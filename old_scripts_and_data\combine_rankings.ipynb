{"cells": [{"cell_type": "markdown", "id": "4e8bfef7", "metadata": {}, "source": ["```\n", "Strategy:\n", "1. Combine BEA data\n", "2. Combine NAICS data\n", "3. Extract first three digit from NAICS code and BEA code.\n", "4. Assign correct BEA code to NAICS code based on three digit mapping. One BEA code will map to multiple NAICS codes.\n", "If we find more than one BEA code for one NAICS code then do the match the industry description and decide which BEA industry code is correct.\n", "```\n"]}, {"cell_type": "markdown", "id": "db1da785", "metadata": {}, "source": ["## Loading the dataframes"]}, {"cell_type": "code", "execution_count": 1, "id": "dbd08f94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['rank', 'seriesID', 'Industry', 'industry_name', 'avg_2023', 'avg_2024',\n", "       'ratio', 'Recent_Growth_%', 'Growth', 'Emp_Score', 'Emp_Growth_Score',\n", "       'Employment_Score'],\n", "      dtype='object')\n", "Index(['Data Type', 'NAICS', 'Year', 'Description', 'Exports1', 'Imports1',\n", "       'Deficit', 'Exports ', 'Imports', 'Trade Balance', 'Imports_Score',\n", "       'Deficit_Score', 'Import_Intensity_Score'],\n", "      dtype='object')\n", "Index(['SeriesCode', 'Industry', 'LineDescription', 'FA_2022', 'FA_2023',\n", "       'Recent_Growth_%', 'Increase in Fixed Assets', 'FA_Score',\n", "       'FA_Growth_Score', 'Fixed_Assets_Score'],\n", "      dtype='object')\n", "Index(['Industry', 'IndustrYDescription', 'GDP_2022', 'GDP_2023', 'GDP_2024',\n", "       'Recent_Growth_%', 'Two_Year_Growth_%', 'Recent_Increase', 'GDP_Score',\n", "       'GDP_Growth_Score', 'Economic_Heft_Score'],\n", "      dtype='object')\n"]}], "source": ["import pandas as pd\n", "from fuzzywuzzy import fuzz, process\n", "\n", "# Load dataframes\n", "emp_df = pd.read_excel('NAICS_Employment_Scores.xlsx')\n", "import_df = pd.read_excel('NAICS_Import_Intensity_Score.xlsx')\n", "fa_df = pd.read_excel('NAICS_Fixed_Assets_Scores.xlsx')\n", "eh_df = pd.read_excel('NAICS_Economic_Heft_Scores.xlsx')\n", "\n", "# Check column names\n", "print(emp_df.columns)\n", "print(import_df.columns)\n", "print(fa_df.columns)\n", "print(eh_df.columns)"]}, {"cell_type": "markdown", "id": "c2092ad6", "metadata": {}, "source": ["## Renaming the column of economic heft and fixed assets dataframes"]}, {"cell_type": "code", "execution_count": 2, "id": "46ea144c", "metadata": {}, "outputs": [], "source": ["fa_df.rename(columns={'LineDescription': 'BEA_description'}, inplace=True)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "cedea576", "metadata": {}, "outputs": [], "source": ["eh_df.rename(columns={'IndustrYDescription': 'BEA_description'}, inplace=True)"]}, {"cell_type": "markdown", "id": "050da5a7", "metadata": {}, "source": ["## Outer join of fa and eh and filling missing values to 0"]}, {"cell_type": "code", "execution_count": 4, "id": "65803049", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19\n", "23\n"]}], "source": ["print(len(eh_df))\n", "print(len(fa_df))"]}, {"cell_type": "code", "execution_count": 5, "id": "5a4ab0b7", "metadata": {}, "outputs": [], "source": ["# Merge the dataframes on 'BEA_description' with outer join\n", "bea_df = pd.merge(\n", "    fa_df[['Industry', 'BEA_description', 'Fixed_Assets_Score']],\n", "    eh_df[['BEA_description', 'Economic_Heft_Score']],\n", "    on='BEA_description',\n", "    how='outer'\n", ").fillna(0.5)"]}, {"cell_type": "code", "execution_count": 6, "id": "4a90774d", "metadata": {}, "outputs": [{"data": {"text/plain": ["23"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(bea_df)"]}, {"cell_type": "code", "execution_count": 7, "id": "f0d1ddd9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>BEA_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>315a</td>\n", "      <td>Apparel and leather and allied products</td>\n", "      <td>0.022472</td>\n", "      <td>0.219149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3250</td>\n", "      <td>Chemical products</td>\n", "      <td>0.734831</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3340</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>0.405752</td>\n", "      <td>0.536735</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3350</td>\n", "      <td>Electrical equipment, appliances, and components</td>\n", "      <td>0.233152</td>\n", "      <td>0.353341</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry                                   BEA_description  \\\n", "0     315a           Apparel and leather and allied products   \n", "1     3120        Beverage and tobacco product manufacturing   \n", "2     3250                                 Chemical products   \n", "3     3340                  Computer and electronic products   \n", "4     3350  Electrical equipment, appliances, and components   \n", "\n", "   Fixed_Assets_Score  Economic_Heft_Score  \n", "0            0.022472             0.219149  \n", "1            0.228889             0.500000  \n", "2            0.734831             0.993617  \n", "3            0.405752             0.536735  \n", "4            0.233152             0.353341  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["bea_df.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "56a80cb1", "metadata": {}, "outputs": [], "source": ["bea_df.rename(columns={'Industry': 'BEA_code'}, inplace=True)"]}, {"cell_type": "markdown", "id": "86564213", "metadata": {}, "source": ["## Combine NAICS data"]}, {"cell_type": "code", "execution_count": 9, "id": "904dcd8a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>seriesID</th>\n", "      <th>Industry</th>\n", "      <th>industry_name</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Growth</th>\n", "      <th>Emp_Score</th>\n", "      <th>Emp_Growth_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>CEU3133640001</td>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "      <td>0.041755</td>\n", "      <td>22.408333</td>\n", "      <td>0.940365</td>\n", "      <td>0.979195</td>\n", "      <td>0.955897</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>16</td>\n", "      <td>CEU3231160001</td>\n", "      <td>3116</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>548.800000</td>\n", "      <td>556.400000</td>\n", "      <td>1.013848</td>\n", "      <td>0.013848</td>\n", "      <td>7.600000</td>\n", "      <td>0.935612</td>\n", "      <td>0.761891</td>\n", "      <td>0.866124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>41</td>\n", "      <td>CEU3232610001</td>\n", "      <td>3261</td>\n", "      <td>Plastics product manufacturing</td>\n", "      <td>602.933333</td>\n", "      <td>592.525000</td>\n", "      <td>0.982737</td>\n", "      <td>-0.017263</td>\n", "      <td>-10.408333</td>\n", "      <td>1.000000</td>\n", "      <td>0.519637</td>\n", "      <td>0.807855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>44</td>\n", "      <td>CEU3133630001</td>\n", "      <td>3363</td>\n", "      <td>Motor vehicle parts manufacturing</td>\n", "      <td>563.783333</td>\n", "      <td>553.316667</td>\n", "      <td>0.981435</td>\n", "      <td>-0.018565</td>\n", "      <td>-10.466667</td>\n", "      <td>0.930116</td>\n", "      <td>0.509496</td>\n", "      <td>0.761868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3</td>\n", "      <td>CEU3231180001</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "      <td>0.039351</td>\n", "      <td>13.266667</td>\n", "      <td>0.568443</td>\n", "      <td>0.960476</td>\n", "      <td>0.725256</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank       seriesID  Industry                              industry_name  \\\n", "0     2  CEU3133640001      3364  Aerospace product and parts manufacturing   \n", "1    16  CEU3231160001      3116         Animal slaughtering and processing   \n", "2    41  CEU3232610001      3261             Plastics product manufacturing   \n", "3    44  CEU3133630001      3363          Motor vehicle parts manufacturing   \n", "4     3  CEU3231180001      3118        Bakeries and tortilla manufacturing   \n", "\n", "     avg_2023    avg_2024     ratio  Recent_Growth_%     Growth  Emp_Score  \\\n", "0  536.658333  559.066667  1.041755         0.041755  22.408333   0.940365   \n", "1  548.800000  556.400000  1.013848         0.013848   7.600000   0.935612   \n", "2  602.933333  592.525000  0.982737        -0.017263 -10.408333   1.000000   \n", "3  563.783333  553.316667  0.981435        -0.018565 -10.466667   0.930116   \n", "4  337.133333  350.400000  1.039351         0.039351  13.266667   0.568443   \n", "\n", "   Emp_Growth_Score  Employment_Score  \n", "0          0.979195          0.955897  \n", "1          0.761891          0.866124  \n", "2          0.519637          0.807855  \n", "3          0.509496          0.761868  \n", "4          0.960476          0.725256  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["emp_df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "cdcf0441", "metadata": {}, "outputs": [], "source": ["emp_df.rename(columns={'Industry': 'NAICS'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 11, "id": "1ca24a55", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Data Type</th>\n", "      <th>NAICS</th>\n", "      <th>Year</th>\n", "      <th>Description</th>\n", "      <th>Exports1</th>\n", "      <th>Imports1</th>\n", "      <th>Deficit</th>\n", "      <th>Exports</th>\n", "      <th>Imports</th>\n", "      <th>Trade Balance</th>\n", "      <th>Imports_Score</th>\n", "      <th>Deficit_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAS Value</td>\n", "      <td>3361</td>\n", "      <td>2024</td>\n", "      <td>MOTOR VEHICLES</td>\n", "      <td>70000797356</td>\n", "      <td>276136328894</td>\n", "      <td>206135531538</td>\n", "      <td>70.000797</td>\n", "      <td>276.136329</td>\n", "      <td>206.135532</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAS Value</td>\n", "      <td>3254</td>\n", "      <td>2024</td>\n", "      <td>PHARMACEUTICALS &amp; MEDICINES</td>\n", "      <td>90872734112</td>\n", "      <td>246094965766</td>\n", "      <td>155222231654</td>\n", "      <td>90.872734</td>\n", "      <td>246.094966</td>\n", "      <td>155.222232</td>\n", "      <td>0.891037</td>\n", "      <td>0.808456</td>\n", "      <td>0.849747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAS Value</td>\n", "      <td>3341</td>\n", "      <td>2024</td>\n", "      <td>COMPUTER EQUIPMENT</td>\n", "      <td>27207942419</td>\n", "      <td>152212075560</td>\n", "      <td>125004133141</td>\n", "      <td>27.207942</td>\n", "      <td>152.212076</td>\n", "      <td>125.004133</td>\n", "      <td>0.550516</td>\n", "      <td>0.694771</td>\n", "      <td>0.622644</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAS Value</td>\n", "      <td>3342</td>\n", "      <td>2024</td>\n", "      <td>COMMUNICATIONS EQUIPMENT</td>\n", "      <td>14984255701</td>\n", "      <td>126284273319</td>\n", "      <td>111300017618</td>\n", "      <td>14.984256</td>\n", "      <td>126.284273</td>\n", "      <td>111.300018</td>\n", "      <td>0.456474</td>\n", "      <td>0.643214</td>\n", "      <td>0.549844</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAS Value</td>\n", "      <td>3363</td>\n", "      <td>2024</td>\n", "      <td>MOTOR VEHICLE PARTS</td>\n", "      <td>49798443457</td>\n", "      <td>141969387660</td>\n", "      <td>92170944203</td>\n", "      <td>49.798443</td>\n", "      <td>141.969388</td>\n", "      <td>92.170944</td>\n", "      <td>0.513365</td>\n", "      <td>0.571247</td>\n", "      <td>0.542306</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Data Type  NAICS  Year                  Description     Exports1  \\\n", "0  FAS Value   3361  2024               MOTOR VEHICLES  70000797356   \n", "1  FAS Value   3254  2024  PHARMACEUTICALS & MEDICINES  90872734112   \n", "2  FAS Value   3341  2024           COMPUTER EQUIPMENT  27207942419   \n", "3  FAS Value   3342  2024     COMMUNICATIONS EQUIPMENT  14984255701   \n", "4  FAS Value   3363  2024          MOTOR VEHICLE PARTS  49798443457   \n", "\n", "       Imports1       Deficit   Exports      Imports  Trade Balance  \\\n", "0  276136328894  206135531538  70.000797  276.136329     206.135532   \n", "1  246094965766  155222231654  90.872734  246.094966     155.222232   \n", "2  152212075560  125004133141  27.207942  152.212076     125.004133   \n", "3  126284273319  111300017618  14.984256  126.284273     111.300018   \n", "4  141969387660   92170944203  49.798443  141.969388      92.170944   \n", "\n", "   Imports_Score  Deficit_Score  Import_Intensity_Score  \n", "0       1.000000       1.000000                1.000000  \n", "1       0.891037       0.808456                0.849747  \n", "2       0.550516       0.694771                0.622644  \n", "3       0.456474       0.643214                0.549844  \n", "4       0.513365       0.571247                0.542306  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import_df.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "a88b620d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(63, 85)\n"]}], "source": ["print(f\"{len(emp_df), len(import_df)}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "97d035de", "metadata": {}, "outputs": [], "source": ["# First merge the dataframes\n", "combined_naic = pd.merge(\n", "    emp_df[['NAICS', 'industry_name', 'Employment_Score']],\n", "    import_df[['NAICS', 'Import_Intensity_Score', 'Description']], \n", "    on='NAICS',\n", "    how='outer'\n", ")\n", "\n", "# Fill numeric columns with 0\n", "combined_naic['Employment_Score'] = combined_naic['Employment_Score'].fillna(0.5)\n", "combined_naic['Import_Intensity_Score'] = combined_naic['Import_Intensity_Score'].fillna(0.5)\n", "\n", "# For description columns, use coalesce to take the first non-null value\n", "combined_naic['industry_description'] = combined_naic['industry_name'].combine_first(combined_naic['Description'])\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "f524fd47", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>industry_name</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Description</th>\n", "      <th>industry_description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>ANIMAL FOODS</td>\n", "      <td>Animal food manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>GRAIN &amp; OILSEED MILLING PRODUCTS</td>\n", "      <td>Grain and oilseed milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>SUGAR &amp; CONFECTIONERY PRODUCTS</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>FRUITS &amp; VEG PRESERVES &amp; SPECIALTY FOODS</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>DAIRY PRODUCTS</td>\n", "      <td>Dairy product manufacturing</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS                                      industry_name  Employment_Score  \\\n", "0   3111                          Animal food manufacturing          0.385857   \n", "1   3112                          Grain and oilseed milling          0.438410   \n", "2   3113      Sugar and confectionery product manufacturing          0.354900   \n", "3   3114  Fruit and vegetable preserving and specialty f...          0.458670   \n", "4   3115                        Dairy product manufacturing          0.458316   \n", "\n", "   Import_Intensity_Score                               Description  \\\n", "0                0.115238                              ANIMAL FOODS   \n", "1                0.159188          GRAIN & OILSEED MILLING PRODUCTS   \n", "2                0.153765            SUGAR & CONFECTIONERY PRODUCTS   \n", "3                0.167181  FRUITS & VEG PRESERVES & SPECIALTY FOODS   \n", "4                0.117253                            DAIRY PRODUCTS   \n", "\n", "                                industry_description  \n", "0                          Animal food manufacturing  \n", "1                          Grain and oilseed milling  \n", "2      Sugar and confectionery product manufacturing  \n", "3  Fruit and vegetable preserving and specialty f...  \n", "4                        Dairy product manufacturing  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_naic.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "c5239b28", "metadata": {}, "outputs": [], "source": ["combined_naic = combined_naic.drop(['industry_name', 'Description'], axis=1)"]}, {"cell_type": "markdown", "id": "9c43d463", "metadata": {}, "source": ["## Extract 3 digit prefixes"]}, {"cell_type": "code", "execution_count": 16, "id": "1be610a0", "metadata": {}, "outputs": [], "source": ["combined_naic['NAICS_3'] = combined_naic['NAICS'].astype(str).str[:3]"]}, {"cell_type": "code", "execution_count": 17, "id": "83028efa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>315a</td>\n", "      <td>Apparel and leather and allied products</td>\n", "      <td>0.022472</td>\n", "      <td>0.219149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3250</td>\n", "      <td>Chemical products</td>\n", "      <td>0.734831</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3340</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>0.405752</td>\n", "      <td>0.536735</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3350</td>\n", "      <td>Electrical equipment, appliances, and components</td>\n", "      <td>0.233152</td>\n", "      <td>0.353341</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3320</td>\n", "      <td>Fabricated metal products</td>\n", "      <td>0.283393</td>\n", "      <td>0.442002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3370</td>\n", "      <td>Furniture and related products</td>\n", "      <td>0.370733</td>\n", "      <td>0.287919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3330</td>\n", "      <td>Machinery</td>\n", "      <td>0.394934</td>\n", "      <td>0.553005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>3380</td>\n", "      <td>Medical equipment and supplies</td>\n", "      <td>0.125378</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>338a</td>\n", "      <td>Miscellaneous manufacturing</td>\n", "      <td>0.157631</td>\n", "      <td>0.521962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>336m</td>\n", "      <td>Motor vehicles, bodies and trailers, and parts</td>\n", "      <td>0.302967</td>\n", "      <td>0.579234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>3270</td>\n", "      <td>Nonmetallic mineral products</td>\n", "      <td>0.423091</td>\n", "      <td>0.338554</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>3390</td>\n", "      <td>Other manufacturing</td>\n", "      <td>0.131942</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>336o</td>\n", "      <td>Other transportation equipment</td>\n", "      <td>0.334159</td>\n", "      <td>0.446049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>3220</td>\n", "      <td>Paper products</td>\n", "      <td>0.311010</td>\n", "      <td>0.195028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>3240</td>\n", "      <td>Petroleum and coal products</td>\n", "      <td>0.323119</td>\n", "      <td>0.194030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>3260</td>\n", "      <td>Plastics and rubber products</td>\n", "      <td>0.264196</td>\n", "      <td>0.343601</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>3310</td>\n", "      <td>Primary metals</td>\n", "      <td>0.450989</td>\n", "      <td>0.146958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3230</td>\n", "      <td>Printing and related support activities</td>\n", "      <td>0.234375</td>\n", "      <td>0.266878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>313t</td>\n", "      <td>Textile mills and textile product mills</td>\n", "      <td>0.156555</td>\n", "      <td>0.262671</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>3210</td>\n", "      <td>Wood products</td>\n", "      <td>0.392267</td>\n", "      <td>0.169042</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   BEA_code                                   BEA_description  \\\n", "0      315a           Apparel and leather and allied products   \n", "1      3120        Beverage and tobacco product manufacturing   \n", "2      3250                                 Chemical products   \n", "3      3340                  Computer and electronic products   \n", "4      3350  Electrical equipment, appliances, and components   \n", "5      3320                         Fabricated metal products   \n", "6      311a            Food and beverage and tobacco products   \n", "7      3110                                Food manufacturing   \n", "8      3370                    Furniture and related products   \n", "9      3330                                         Machinery   \n", "10     3380                    Medical equipment and supplies   \n", "11     338a                       Miscellaneous manufacturing   \n", "12     336m    Motor vehicles, bodies and trailers, and parts   \n", "13     3270                      Nonmetallic mineral products   \n", "14     3390                               Other manufacturing   \n", "15     336o                    Other transportation equipment   \n", "16     3220                                    Paper products   \n", "17     3240                       Petroleum and coal products   \n", "18     3260                      Plastics and rubber products   \n", "19     3310                                    Primary metals   \n", "20     3230           Printing and related support activities   \n", "21     313t           Textile mills and textile product mills   \n", "22     3210                                     Wood products   \n", "\n", "    Fixed_Assets_Score  Economic_Heft_Score  \n", "0             0.022472             0.219149  \n", "1             0.228889             0.500000  \n", "2             0.734831             0.993617  \n", "3             0.405752             0.536735  \n", "4             0.233152             0.353341  \n", "5             0.283393             0.442002  \n", "6             0.492662             0.672787  \n", "7             0.498296             0.500000  \n", "8             0.370733             0.287919  \n", "9             0.394934             0.553005  \n", "10            0.125378             0.500000  \n", "11            0.157631             0.521962  \n", "12            0.302967             0.579234  \n", "13            0.423091             0.338554  \n", "14            0.131942             0.500000  \n", "15            0.334159             0.446049  \n", "16            0.311010             0.195028  \n", "17            0.323119             0.194030  \n", "18            0.264196             0.343601  \n", "19            0.450989             0.146958  \n", "20            0.234375             0.266878  \n", "21            0.156555             0.262671  \n", "22            0.392267             0.169042  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["bea_df.head(23)"]}, {"cell_type": "code", "execution_count": 18, "id": "c72acaef", "metadata": {}, "outputs": [], "source": ["bea_df['BEA_3'] = bea_df['BEA_code'].astype(str).str[:3]\n"]}, {"cell_type": "markdown", "id": "ea1c354a", "metadata": {}, "source": ["## Map NAICS code to BEA code"]}, {"cell_type": "code", "execution_count": 19, "id": "e118eaa7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3116</td>\n", "      <td>0.866124</td>\n", "      <td>0.137305</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3117</td>\n", "      <td>0.352433</td>\n", "      <td>0.127220</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3118</td>\n", "      <td>0.725256</td>\n", "      <td>0.149352</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3119</td>\n", "      <td>0.519237</td>\n", "      <td>0.157582</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3121</td>\n", "      <td>0.659114</td>\n", "      <td>0.206382</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>312</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS  Employment_Score  Import_Intensity_Score  \\\n", "0   3111          0.385857                0.115238   \n", "1   3112          0.438410                0.159188   \n", "2   3113          0.354900                0.153765   \n", "3   3114          0.458670                0.167181   \n", "4   3115          0.458316                0.117253   \n", "5   3116          0.866124                0.137305   \n", "6   3117          0.352433                0.127220   \n", "7   3118          0.725256                0.149352   \n", "8   3119          0.519237                0.157582   \n", "9   3121          0.659114                0.206382   \n", "\n", "                                industry_description NAICS_3  \n", "0                          Animal food manufacturing     311  \n", "1                          Grain and oilseed milling     311  \n", "2      Sugar and confectionery product manufacturing     311  \n", "3  Fruit and vegetable preserving and specialty f...     311  \n", "4                        Dairy product manufacturing     311  \n", "5                 Animal slaughtering and processing     311  \n", "6          Seafood product preparation and packaging     311  \n", "7                Bakeries and tortilla manufacturing     311  \n", "8                           Other food manufacturing     311  \n", "9                             Beverage manufacturing     312  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_naic.head(10)"]}, {"cell_type": "code", "execution_count": 20, "id": "95ecfa10", "metadata": {}, "outputs": [], "source": ["def find_best_bea_match(naics_desc, bea_options):\n", "    \"\"\"\n", "    Finds the closest matching BEA description for a given NAICS description.\n", "    \n", "    Args:\n", "        naics_desc (str): The NAICS industry description to match\n", "        bea_options (list): List of possible BEA descriptions to match against\n", "    \n", "    Returns:\n", "        str: The best matching BEA description\n", "    \"\"\"\n", "    return process.extractOne(\n", "        naics_desc,           \n", "        bea_options,          \n", "        scorer=fuzz.token_sort_ratio  \n", "    )[0]  "]}, {"cell_type": "code", "execution_count": 21, "id": "d3c5721d", "metadata": {}, "outputs": [], "source": ["# Group BEA descriptions by their 3-digit prefix\n", "bea_options_dict = bea_df.groupby('BEA_3')['BEA_description'].apply(list).to_dict()"]}, {"cell_type": "code", "execution_count": 22, "id": "1f10e7af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'311': ['Food and beverage and tobacco products', 'Food manufacturing'], '312': ['Beverage and tobacco product manufacturing'], '313': ['Textile mills and textile product mills'], '315': ['Apparel and leather and allied products'], '321': ['Wood products'], '322': ['Paper products'], '323': ['Printing and related support activities'], '324': ['Petroleum and coal products'], '325': ['Chemical products'], '326': ['Plastics and rubber products'], '327': ['Nonmetallic mineral products'], '331': ['Primary metals'], '332': ['Fabricated metal products'], '333': ['Machinery'], '334': ['Computer and electronic products'], '335': ['Electrical equipment, appliances, and components'], '336': ['Motor vehicles, bodies and trailers, and parts', 'Other transportation equipment'], '337': ['Furniture and related products'], '338': ['Medical equipment and supplies', 'Miscellaneous manufacturing'], '339': ['Other manufacturing']}\n"]}], "source": ["print(bea_options_dict)"]}, {"cell_type": "code", "execution_count": 23, "id": "949ffc3d", "metadata": {}, "outputs": [], "source": ["# Function to assign BEA_code based on NAICS data\n", "def get_bea_code(row):\n", "    naics_3 = row['NAICS_3']\n", "    naics_desc = row['industry_description']\n", "    if naics_3 in bea_options_dict:\n", "        bea_options = bea_options_dict[naics_3]\n", "        if len(bea_options) == 1:\n", "            # If only one option, use it directly\n", "            return bea_df[bea_df['BEA_description'] == bea_options[0]]['BEA_code'].values[0]\n", "        else:\n", "            # Use fuzzy matching to find the best description\n", "            best_match = find_best_bea_match(naics_desc, bea_options)\n", "            return bea_df[bea_df['BEA_description'] == best_match]['BEA_code'].values[0]\n", "    return None"]}, {"cell_type": "code", "execution_count": 24, "id": "f6597aea", "metadata": {}, "outputs": [], "source": ["# Apply the mapping\n", "combined_naic['BEA_code'] = combined_naic.apply(get_bea_code, axis=1)"]}, {"cell_type": "code", "execution_count": 25, "id": "d2ec5424", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>311</td>\n", "      <td>311a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3116</td>\n", "      <td>0.866124</td>\n", "      <td>0.137305</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>311</td>\n", "      <td>311a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3117</td>\n", "      <td>0.352433</td>\n", "      <td>0.127220</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>311</td>\n", "      <td>311a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3118</td>\n", "      <td>0.725256</td>\n", "      <td>0.149352</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3119</td>\n", "      <td>0.519237</td>\n", "      <td>0.157582</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3121</td>\n", "      <td>0.659114</td>\n", "      <td>0.206382</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>312</td>\n", "      <td>3120</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS  Employment_Score  Import_Intensity_Score  \\\n", "0   3111          0.385857                0.115238   \n", "1   3112          0.438410                0.159188   \n", "2   3113          0.354900                0.153765   \n", "3   3114          0.458670                0.167181   \n", "4   3115          0.458316                0.117253   \n", "5   3116          0.866124                0.137305   \n", "6   3117          0.352433                0.127220   \n", "7   3118          0.725256                0.149352   \n", "8   3119          0.519237                0.157582   \n", "9   3121          0.659114                0.206382   \n", "\n", "                                industry_description NAICS_3 BEA_code  \n", "0                          Animal food manufacturing     311     3110  \n", "1                          Grain and oilseed milling     311     311a  \n", "2      Sugar and confectionery product manufacturing     311     3110  \n", "3  Fruit and vegetable preserving and specialty f...     311     3110  \n", "4                        Dairy product manufacturing     311     3110  \n", "5                 Animal slaughtering and processing     311     311a  \n", "6          Seafood product preparation and packaging     311     311a  \n", "7                Bakeries and tortilla manufacturing     311     3110  \n", "8                           Other food manufacturing     311     3110  \n", "9                             Beverage manufacturing     312     3120  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_naic.head(10)"]}, {"cell_type": "code", "execution_count": 26, "id": "a66d4425", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rows with any NaN values:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>3141</td>\n", "      <td>0.147265</td>\n", "      <td>0.167028</td>\n", "      <td>Textile furnishings mills</td>\n", "      <td>314</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>3149</td>\n", "      <td>0.355692</td>\n", "      <td>0.138947</td>\n", "      <td>Other textile product mills</td>\n", "      <td>314</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>3161</td>\n", "      <td>0.500000</td>\n", "      <td>0.112006</td>\n", "      <td>LEATHER &amp; HIDE TANNING</td>\n", "      <td>316</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3162</td>\n", "      <td>0.500000</td>\n", "      <td>0.210280</td>\n", "      <td>FOOTWEAR</td>\n", "      <td>316</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>3169</td>\n", "      <td>0.500000</td>\n", "      <td>0.156348</td>\n", "      <td>OTHER LEATHER PRODUCTS</td>\n", "      <td>316</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    NAICS  Employment_Score  Import_Intensity_Score  \\\n", "14   3141          0.147265                0.167028   \n", "15   3149          0.355692                0.138947   \n", "19   3161          0.500000                0.112006   \n", "20   3162          0.500000                0.210280   \n", "21   3169          0.500000                0.156348   \n", "\n", "           industry_description NAICS_3 BEA_code  \n", "14    Textile furnishings mills     314     None  \n", "15  Other textile product mills     314     None  \n", "19       LEATHER & HIDE TANNING     316     None  \n", "20                     FOOTWEAR     316     None  \n", "21       OTHER LEATHER PRODUCTS     316     None  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["## Checking missing values\n", "# Display rows where any column has NaN\n", "print(\"Rows with any NaN values:\")\n", "combined_naic[combined_naic.isna().any(axis=1)]"]}, {"cell_type": "code", "execution_count": 27, "id": "c8f68ec0", "metadata": {}, "outputs": [], "source": ["# Update BEA codes where they are null and match specific NAICS_3 codes\n", "combined_naic.loc[(combined_naic['BEA_code'].isna()) & (combined_naic['NAICS_3'] == '314'), 'BEA_code'] = '313t'\n", "combined_naic.loc[(combined_naic['BEA_code'].isna()) & (combined_naic['NAICS_3'] == '316'), 'BEA_code'] = '315a'\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "id": "db981a20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated rows:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>3141</td>\n", "      <td>0.147265</td>\n", "      <td>0.167028</td>\n", "      <td>Textile furnishings mills</td>\n", "      <td>314</td>\n", "      <td>313t</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>3149</td>\n", "      <td>0.355692</td>\n", "      <td>0.138947</td>\n", "      <td>Other textile product mills</td>\n", "      <td>314</td>\n", "      <td>313t</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>3161</td>\n", "      <td>0.500000</td>\n", "      <td>0.112006</td>\n", "      <td>LEATHER &amp; HIDE TANNING</td>\n", "      <td>316</td>\n", "      <td>315a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3162</td>\n", "      <td>0.500000</td>\n", "      <td>0.210280</td>\n", "      <td>FOOTWEAR</td>\n", "      <td>316</td>\n", "      <td>315a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>3169</td>\n", "      <td>0.500000</td>\n", "      <td>0.156348</td>\n", "      <td>OTHER LEATHER PRODUCTS</td>\n", "      <td>316</td>\n", "      <td>315a</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    NAICS  Employment_Score  Import_Intensity_Score  \\\n", "14   3141          0.147265                0.167028   \n", "15   3149          0.355692                0.138947   \n", "19   3161          0.500000                0.112006   \n", "20   3162          0.500000                0.210280   \n", "21   3169          0.500000                0.156348   \n", "\n", "           industry_description NAICS_3 BEA_code  \n", "14    Textile furnishings mills     314     313t  \n", "15  Other textile product mills     314     313t  \n", "19       LEATHER & HIDE TANNING     316     315a  \n", "20                     FOOTWEAR     316     315a  \n", "21       OTHER LEATHER PRODUCTS     316     315a  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Verify the changes\n", "print(\"Updated rows:\")\n", "combined_naic[combined_naic['NAICS'].isin([3141, 3149, 3161, 3162, 3169])]"]}, {"cell_type": "code", "execution_count": 29, "id": "7f655814", "metadata": {}, "outputs": [], "source": ["# Merge NAICS and BEA data on 'BEA_code'\n", "combined_df = pd.merge(\n", "    combined_naic,\n", "    bea_df[['BEA_code', 'BEA_description', 'Fixed_Assets_Score', 'Economic_Heft_Score']],\n", "    on='BEA_code',\n", "    how='left'\n", ")"]}, {"cell_type": "code", "execution_count": 30, "id": "bc00e99e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>311</td>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3116</td>\n", "      <td>0.866124</td>\n", "      <td>0.137305</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>311</td>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3117</td>\n", "      <td>0.352433</td>\n", "      <td>0.127220</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>311</td>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3118</td>\n", "      <td>0.725256</td>\n", "      <td>0.149352</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3119</td>\n", "      <td>0.519237</td>\n", "      <td>0.157582</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3121</td>\n", "      <td>0.659114</td>\n", "      <td>0.206382</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>312</td>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS  Employment_Score  Import_Intensity_Score  \\\n", "0   3111          0.385857                0.115238   \n", "1   3112          0.438410                0.159188   \n", "2   3113          0.354900                0.153765   \n", "3   3114          0.458670                0.167181   \n", "4   3115          0.458316                0.117253   \n", "5   3116          0.866124                0.137305   \n", "6   3117          0.352433                0.127220   \n", "7   3118          0.725256                0.149352   \n", "8   3119          0.519237                0.157582   \n", "9   3121          0.659114                0.206382   \n", "\n", "                                industry_description NAICS_3 BEA_code  \\\n", "0                          Animal food manufacturing     311     3110   \n", "1                          Grain and oilseed milling     311     311a   \n", "2      Sugar and confectionery product manufacturing     311     3110   \n", "3  Fruit and vegetable preserving and specialty f...     311     3110   \n", "4                        Dairy product manufacturing     311     3110   \n", "5                 Animal slaughtering and processing     311     311a   \n", "6          Seafood product preparation and packaging     311     311a   \n", "7                Bakeries and tortilla manufacturing     311     3110   \n", "8                           Other food manufacturing     311     3110   \n", "9                             Beverage manufacturing     312     3120   \n", "\n", "                              BEA_description  Fixed_Assets_Score  \\\n", "0                          Food manufacturing            0.498296   \n", "1      Food and beverage and tobacco products            0.492662   \n", "2                          Food manufacturing            0.498296   \n", "3                          Food manufacturing            0.498296   \n", "4                          Food manufacturing            0.498296   \n", "5      Food and beverage and tobacco products            0.492662   \n", "6      Food and beverage and tobacco products            0.492662   \n", "7                          Food manufacturing            0.498296   \n", "8                          Food manufacturing            0.498296   \n", "9  Beverage and tobacco product manufacturing            0.228889   \n", "\n", "   Economic_Heft_Score  \n", "0             0.500000  \n", "1             0.672787  \n", "2             0.500000  \n", "3             0.500000  \n", "4             0.500000  \n", "5             0.672787  \n", "6             0.672787  \n", "7             0.500000  \n", "8             0.500000  \n", "9             0.500000  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head(10)"]}, {"cell_type": "code", "execution_count": 31, "id": "78adfad0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11584\\2245756904.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  final_df.rename(columns={\n"]}], "source": ["# Select and rename columns to match the required format\n", "final_df = combined_df[[\n", "    'BEA_code', 'BEA_description', 'NAICS', 'industry_description',\n", "    'Fixed_Assets_Score', 'Economic_Heft_Score', 'Import_Intensity_Score', 'Employment_Score'\n", "]]\n", "\n", "final_df.rename(columns={\n", "    'BEA_description': 'BEA_industry_description',\n", "    'NAICS': 'NAICS_code',\n", "    'industry_description': 'NAICS_industry_description'\n", "}, inplace=True)"]}, {"cell_type": "code", "execution_count": 32, "id": "b86d964d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3116</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.137305</td>\n", "      <td>0.866124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.127220</td>\n", "      <td>0.352433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.149352</td>\n", "      <td>0.725256</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3119</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.157582</td>\n", "      <td>0.519237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>3121</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.500000</td>\n", "      <td>0.206382</td>\n", "      <td>0.659114</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code                    BEA_industry_description  NAICS_code  \\\n", "0     3110                          Food manufacturing        3111   \n", "1     311a      Food and beverage and tobacco products        3112   \n", "2     3110                          Food manufacturing        3113   \n", "3     3110                          Food manufacturing        3114   \n", "4     3110                          Food manufacturing        3115   \n", "5     311a      Food and beverage and tobacco products        3116   \n", "6     311a      Food and beverage and tobacco products        3117   \n", "7     3110                          Food manufacturing        3118   \n", "8     3110                          Food manufacturing        3119   \n", "9     3120  Beverage and tobacco product manufacturing        3121   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.492662   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "5                 Animal slaughtering and processing            0.492662   \n", "6          Seafood product preparation and packaging            0.492662   \n", "7                Bakeries and tortilla manufacturing            0.498296   \n", "8                           Other food manufacturing            0.498296   \n", "9                             Beverage manufacturing            0.228889   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0             0.500000                0.115238          0.385857  \n", "1             0.672787                0.159188          0.438410  \n", "2             0.500000                0.153765          0.354900  \n", "3             0.500000                0.167181          0.458670  \n", "4             0.500000                0.117253          0.458316  \n", "5             0.672787                0.137305          0.866124  \n", "6             0.672787                0.127220          0.352433  \n", "7             0.500000                0.149352          0.725256  \n", "8             0.500000                0.157582          0.519237  \n", "9             0.500000                0.206382          0.659114  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df.head(10)"]}, {"cell_type": "code", "execution_count": 33, "id": "51a38ed7", "metadata": {}, "outputs": [], "source": ["final_df.to_excel('Combined_NAICS_BEA_Scores.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "a9b95642", "metadata": {}, "source": ["## Adding Factset to NAICS mapping "]}, {"cell_type": "code", "execution_count": 34, "id": "f67b3e9e", "metadata": {}, "outputs": [], "source": ["mapping_df = pd.read_excel('NAICS_to_Factset.xlsx')"]}, {"cell_type": "code", "execution_count": 45, "id": "aa37e1b6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS Title</th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "      <th>Match Rationale</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1111</td>\n", "      <td>Oilseed and Grain Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS involves growing oilseed and grain crops...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1112</td>\n", "      <td>Vegetable and Melon Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS covers growing vegetables and melons; Fa...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1113</td>\n", "      <td>Fruit and Tree Nut Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS involves growing fruit and tree nut crop...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1114</td>\n", "      <td>Greenhouse, Nursery, and Floriculture Production</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS includes growing crops under cover and n...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1119</td>\n", "      <td>Other Crop Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS covers growing crops like tobacco, cotto...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAICS_code                                       NAICS Title  Subgroup Code  \\\n", "0       1111                         Oilseed and Grain Farming         2225.0   \n", "1       1112                       Vegetable and Melon Farming         2225.0   \n", "2       1113                        Fruit and Tree Nut Farming         2225.0   \n", "3       1114  Greenhouse, Nursery, and Floriculture Production         2225.0   \n", "4       1119                                Other Crop Farming         2225.0   \n", "\n", "                     Subgroup Title  \\\n", "0  Agricultural Commodities/Milling   \n", "1  Agricultural Commodities/Milling   \n", "2  Agricultural Commodities/Milling   \n", "3  Agricultural Commodities/Milling   \n", "4  Agricultural Commodities/Milling   \n", "\n", "                                     Match Rationale  \n", "0  NAICS involves growing oilseed and grain crops...  \n", "1  NAICS covers growing vegetables and melons; Fa...  \n", "2  NAICS involves growing fruit and tree nut crop...  \n", "3  NAICS includes growing crops under cover and n...  \n", "4  NAICS covers growing crops like tobacco, cotto...  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["mapping_df.head()"]}, {"cell_type": "code", "execution_count": 41, "id": "1c0d5830", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["object\n"]}], "source": ["print(mapping_df['NAICS Code'].dtype)"]}, {"cell_type": "code", "execution_count": 42, "id": "5cae3234", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["object\n"]}], "source": ["print(final_df['NAICS_code'].dtype)"]}, {"cell_type": "code", "execution_count": 39, "id": "758e12e3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11584\\3515883772.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  final_df['NAICS_code'] = final_df['NAICS_code'].astype(str).str.strip()\n"]}], "source": ["final_df['NAICS_code'] = final_df['NAICS_code'].astype(str).str.strip()"]}, {"cell_type": "code", "execution_count": 40, "id": "a52a67f7", "metadata": {}, "outputs": [], "source": ["mapping_df['NAICS Code'] = mapping_df['NAICS Code'].astype(str).str.strip()"]}, {"cell_type": "code", "execution_count": 44, "id": "0a162ff0", "metadata": {}, "outputs": [], "source": ["mapping_df.rename(columns={'NAICS Code': 'NAICS_code'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 46, "id": "5d3d44ba", "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    final_df,\n", "    mapping_df[['NAICS_code', 'Subgroup Code', 'Subgroup Title']],\n", "    on='NAICS_code',\n", "    how='left'\n", ")"]}, {"cell_type": "code", "execution_count": 51, "id": "0a3423f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3116</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.137305</td>\n", "      <td>0.866124</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.127220</td>\n", "      <td>0.352433</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.149352</td>\n", "      <td>0.725256</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3119</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.157582</td>\n", "      <td>0.519237</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>3121</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.500000</td>\n", "      <td>0.206382</td>\n", "      <td>0.659114</td>\n", "      <td>2420</td>\n", "      <td>Beverages: Non-Alcoholic</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code                    BEA_industry_description NAICS_code  \\\n", "0     3110                          Food manufacturing       3111   \n", "1     311a      Food and beverage and tobacco products       3112   \n", "2     3110                          Food manufacturing       3113   \n", "3     3110                          Food manufacturing       3114   \n", "4     3110                          Food manufacturing       3115   \n", "5     311a      Food and beverage and tobacco products       3116   \n", "6     311a      Food and beverage and tobacco products       3117   \n", "7     3110                          Food manufacturing       3118   \n", "8     3110                          Food manufacturing       3119   \n", "9     3120  Beverage and tobacco product manufacturing       3121   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.492662   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "5                 Animal slaughtering and processing            0.492662   \n", "6          Seafood product preparation and packaging            0.492662   \n", "7                Bakeries and tortilla manufacturing            0.498296   \n", "8                           Other food manufacturing            0.498296   \n", "9                             Beverage manufacturing            0.228889   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0             0.500000                0.115238          0.385857   \n", "1             0.672787                0.159188          0.438410   \n", "2             0.500000                0.153765          0.354900   \n", "3             0.500000                0.167181          0.458670   \n", "4             0.500000                0.117253          0.458316   \n", "5             0.672787                0.137305          0.866124   \n", "6             0.672787                0.127220          0.352433   \n", "7             0.500000                0.149352          0.725256   \n", "8             0.500000                0.157582          0.519237   \n", "9             0.500000                0.206382          0.659114   \n", "\n", "   Factset_code                     Factset_title  \n", "0          2225  Agricultural Commodities/Milling  \n", "1          2225  Agricultural Commodities/Milling  \n", "2          2225  Agricultural Commodities/Milling  \n", "3          2410             Food: Specialty/Candy  \n", "4          2415             Food: Meat/Fish/Dairy  \n", "5          2415             Food: Meat/Fish/Dairy  \n", "6          2415             Food: Meat/Fish/Dairy  \n", "7          2410             Food: Specialty/Candy  \n", "8          2410             Food: Specialty/Candy  \n", "9          2420          Beverages: Non-Alcoholic  "]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df.head(10)"]}, {"cell_type": "code", "execution_count": 48, "id": "d06faa55", "metadata": {}, "outputs": [], "source": ["final_df['Subgroup Code'] = final_df['Subgroup Code'].astype('Int64')"]}, {"cell_type": "code", "execution_count": 50, "id": "d555d515", "metadata": {}, "outputs": [], "source": ["final_df.rename(columns={'Subgroup Code': 'Factset_code', 'Subgroup Title': 'Factset_title' }, inplace=True)"]}, {"cell_type": "code", "execution_count": 53, "id": "d6ce8265", "metadata": {}, "outputs": [], "source": ["final_df.to_excel('Combined_NAICS_BEA_Scores_factset.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "4967c383", "metadata": {}, "source": ["from sentence_transformers import SentenceTransformer, util\n", "\n", "# Load the pre-trained sentence embedding model once (outside the function for efficiency)\n", "model = SentenceTransformer('all-MiniLM-L6-v2')\n", "\n", "# Function to find the best BEA description match using semantic matching\n", "def find_best_bea_match(naics_desc, bea_options):\n", "    # Generate embedding for the NAICS description\n", "    naics_embedding = model.encode(naics_desc, convert_to_tensor=True)\n", "    \n", "    # Generate embeddings for the BEA description options\n", "    bea_embeddings = model.encode(bea_options, convert_to_tensor=True)\n", "    \n", "    # Compute cosine similarities between NAICS and BEA embeddings\n", "    similarities = util.pytorch_cos_sim(naics_embedding, bea_embeddings)[0]\n", "    \n", "    # Find the index of the highest similarity score\n", "    best_idx = similarities.argmax().item()\n", "    \n", "    # Return the best matching BEA description\n", "    return bea_options[best_idx]\n", "\n", "# Group BEA descriptions by their 3-digit prefix\n", "bea_options_dict = bea_df.groupby('BEA_3')['BEA_description'].apply(list).to_dict()\n", "\n", "# Function to assign BEA_code based on NAICS data\n", "def get_bea_code(row):\n", "    naics_3 = row['NAICS_3']\n", "    naics_desc = row['NAICS_description']\n", "    if naics_3 in bea_options_dict:\n", "        bea_options = bea_options_dict[naics_3]\n", "        if len(bea_options) == 1:\n", "            # If only one option, use it directly\n", "            return bea_df[bea_df['BEA_description'] == bea_options[0]]['BEA_code'].values[0]\n", "        else:\n", "            # Use semantic matching to find the best description\n", "            best_match = find_best_bea_match(naics_desc, bea_options)\n", "            return bea_df[bea_df['BEA_description'] == best_match]['BEA_code'].values[0]\n", "    return None\n", "\n", "# Apply the mapping to the NAICS DataFrame\n", "naics_df['BEA_code'] = naics_df.apply(get_bea_code, axis=1)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}