{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ea8e903f", "metadata": {}, "outputs": [], "source": ["from kiteconnect import KiteConnect\n", "import requests"]}, {"cell_type": "code", "execution_count": 2, "id": "cb342462", "metadata": {}, "outputs": [], "source": ["api_key = \"3q0r6l1fb689slhk\"\n", "access_token = \"v7QHUkY06g9i0NbJGMnWgXJA6RcXWRrs\""]}, {"cell_type": "code", "execution_count": 3, "id": "65d7dfe8", "metadata": {}, "outputs": [], "source": ["kite = KiteConnect(api_key=api_key)\n", "kite.set_access_token(access_token)\n"]}, {"cell_type": "markdown", "id": "d0aeb362", "metadata": {}, "source": ["```\n", "Instruments csv column description\n", "https://chatgpt.com/share/68270bf5-9460-8004-ae3c-c41ed503dbd6"]}, {"cell_type": "markdown", "id": "b2b115de", "metadata": {}, "source": ["## fetching the instruments"]}, {"cell_type": "code", "execution_count": 4, "id": "46a89639", "metadata": {}, "outputs": [], "source": ["# Function to fetch instruments\n", "def get_instruments():\n", "    try:\n", "        # API endpoint for instruments\n", "        url = \"https://api.kite.trade/instruments\"\n", "        headers = {\n", "            \"X-Kite-Version\": \"3\",\n", "            \"Authorization\": f\"token {api_key}:{access_token}\"\n", "        }\n", "        \n", "        # Send request\n", "        response = requests.get(url, headers=headers)\n", "        \n", "        # Check if request was successful\n", "        if response.status_code == 200:\n", "            # Print first 100 bytes to check response\n", "            print(\"First 100 bytes of response:\", response.content[:100])\n", "            \n", "            # Save response to a file\n", "            with open(\"instruments.csv\", \"wb\") as file:\n", "                file.write(response.content)\n", "            print(\"Instrument list saved to instruments.csv\")\n", "            \n", "            # Check if response is gzipped\n", "            if response.content.startswith(b'\\x1f\\x8b'):\n", "                print(\"Response is gzipped\")\n", "            else:\n", "                print(\"Response is not gzipped\")\n", "                \n", "            return True\n", "        else:\n", "            print(\"Error: Status code\", response.status_code)\n", "            print(\"Response:\", response.text)\n", "            return False\n", "            \n", "    except Exception as e:\n", "        print(\"Error fetching instruments:\", str(e))\n", "        return False"]}, {"cell_type": "code", "execution_count": 5, "id": "0096c399", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 100 bytes of response: b'instrument_token,exchange_token,tradingsymbol,name,last_price,expiry,strike,tick_size,lot_size,instr'\n", "Instrument list saved to instruments.csv\n", "Response is not gzipped\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["get_instruments()"]}, {"cell_type": "code", "execution_count": 5, "id": "3c14c330", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "instruments_df = pd.read_csv(\"instruments.csv\")"]}, {"cell_type": "code", "execution_count": 6, "id": "8c3f6929", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>*********</td>\n", "      <td>874879</td>\n", "      <td>BANKEX25MAYFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>*********</td>\n", "      <td>1100996</td>\n", "      <td>BANKEX25JUNFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>*********</td>\n", "      <td>1141118</td>\n", "      <td>BANKEX25JULFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-07-29</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>*********</td>\n", "      <td>1104009</td>\n", "      <td>SENSEX25520FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-20</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>*********</td>\n", "      <td>874759</td>\n", "      <td>SENSEX25MAYFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>*********</td>\n", "      <td>1124583</td>\n", "      <td>SENSEX25603FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-03</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>*********</td>\n", "      <td>1136192</td>\n", "      <td>SENSEX25610FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-10</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>*********</td>\n", "      <td>1141074</td>\n", "      <td>SENSEX25617FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-17</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>*********</td>\n", "      <td>1100924</td>\n", "      <td>SENSEX25JUNFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>*********</td>\n", "      <td>821886</td>\n", "      <td>SENSEX25701FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-07-01</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>*********</td>\n", "      <td>1140932</td>\n", "      <td>SENSEX25JULFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-07-29</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>*********</td>\n", "      <td>875289</td>\n", "      <td>SENSEX5025MAYFUT</td>\n", "      <td>SENSEX50</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>60</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>*********</td>\n", "      <td>1101271</td>\n", "      <td>SENSEX5025JUNFUT</td>\n", "      <td>SENSEX50</td>\n", "      <td>0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>60</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>*********</td>\n", "      <td>1140280</td>\n", "      <td>SENSEX5025JULFUT</td>\n", "      <td>SENSEX50</td>\n", "      <td>0</td>\n", "      <td>2025-07-29</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>75</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>*********</td>\n", "      <td>889874</td>\n", "      <td>BANKEX25MAY63000CE</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>63000.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>CE</td>\n", "      <td>BFO-OPT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    instrument_token  exchange_token       tradingsymbol      name  \\\n", "0          *********          874879      BANKEX25MAYFUT    BANKEX   \n", "1          *********         1100996      BANKEX25JUNFUT    BANKEX   \n", "2          *********         1141118      BANKEX25JULFUT    BANKEX   \n", "3          *********         1104009      SENSEX25520FUT    SENSEX   \n", "4          *********          874759      SENSEX25MAYFUT    SENSEX   \n", "5          *********         1124583      SENSEX25603FUT    SENSEX   \n", "6          *********         1136192      SENSEX25610FUT    SENSEX   \n", "7          *********         1141074      SENSEX25617FUT    SENSEX   \n", "8          *********         1100924      SENSEX25JUNFUT    SENSEX   \n", "9          *********          821886      SENSEX25701FUT    SENSEX   \n", "10         *********         1140932      SENSEX25JULFUT    SENSEX   \n", "11         *********          875289    SENSEX5025MAYFUT  SENSEX50   \n", "12         *********         1101271    SENSEX5025JUNFUT  SENSEX50   \n", "13         *********         1140280    SENSEX5025JULFUT  SENSEX50   \n", "14         *********          889874  BANKEX25MAY63000CE    BANKEX   \n", "\n", "    last_price      expiry   strike  tick_size  lot_size instrument_type  \\\n", "0            0  2025-05-27      0.0       0.05        30             FUT   \n", "1            0  2025-06-24      0.0       0.05        30             FUT   \n", "2            0  2025-07-29      0.0       0.05        30             FUT   \n", "3            0  2025-05-20      0.0       0.05        20             FUT   \n", "4            0  2025-05-27      0.0       0.05        20             FUT   \n", "5            0  2025-06-03      0.0       0.05        20             FUT   \n", "6            0  2025-06-10      0.0       0.05        20             FUT   \n", "7            0  2025-06-17      0.0       0.05        20             FUT   \n", "8            0  2025-06-24      0.0       0.05        20             FUT   \n", "9            0  2025-07-01      0.0       0.05        20             FUT   \n", "10           0  2025-07-29      0.0       0.05        20             FUT   \n", "11           0  2025-05-27      0.0       0.05        60             FUT   \n", "12           0  2025-06-24      0.0       0.05        60             FUT   \n", "13           0  2025-07-29      0.0       0.05        75             FUT   \n", "14           0  2025-05-27  63000.0       0.05        30              CE   \n", "\n", "    segment exchange  \n", "0   BFO-FUT      BFO  \n", "1   BFO-FUT      BFO  \n", "2   BFO-FUT      BFO  \n", "3   BFO-FUT      BFO  \n", "4   BFO-FUT      BFO  \n", "5   BFO-FUT      BFO  \n", "6   BFO-FUT      BFO  \n", "7   BFO-FUT      BFO  \n", "8   BFO-FUT      BFO  \n", "9   BFO-FUT      BFO  \n", "10  BFO-FUT      BFO  \n", "11  BFO-FUT      BFO  \n", "12  BFO-FUT      BFO  \n", "13  BFO-FUT      BFO  \n", "14  BFO-OPT      BFO  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["instruments_df.head(15)"]}, {"cell_type": "markdown", "id": "31c0be2a", "metadata": {}, "source": ["## current stock price"]}, {"cell_type": "code", "execution_count": 7, "id": "66be36a1", "metadata": {}, "outputs": [], "source": ["try:\n", "    quote = kite.quote(\"NSE:INFY\") # Infosys market quote data\n", "except Exception as e:\n", "    print(f\"Error fetching quote: {e}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "5610bf80", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'NSE:INFY': {'instrument_token': 408065,\n", "  'timestamp': datetime.datetime(2025, 5, 19, 19, 19, 26),\n", "  'last_trade_time': datetime.datetime(2025, 5, 19, 15, 59, 55),\n", "  'last_price': 1559.8,\n", "  'last_quantity': 20,\n", "  'buy_quantity': 0,\n", "  'sell_quantity': 2674,\n", "  'volume': 8855798,\n", "  'average_price': 1565.44,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 1403.9,\n", "  'upper_circuit_limit': 1715.7,\n", "  'ohlc': {'open': 1576.5, 'high': 1585, 'low': 1555.7, 'close': 1589.9},\n", "  'depth': {'buy': [{'price': 0, 'quantity': 0, 'orders': 0},\n", "    {'price': 0, 'quantity': 0, 'orders': 0},\n", "    {'price': 0, 'quantity': 0, 'orders': 0},\n", "    {'price': 0, 'quantity': 0, 'orders': 0},\n", "    {'price': 0, 'quantity': 0, 'orders': 0}],\n", "   'sell': [{'price': 1559.8, 'quantity': 2674, 'orders': 38},\n", "    {'price': 0, 'quantity': 0, 'orders': 0},\n", "    {'price': 0, 'quantity': 0, 'orders': 0},\n", "    {'price': 0, 'quantity': 0, 'orders': 0},\n", "    {'price': 0, 'quantity': 0, 'orders': 0}]}}}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["quote"]}, {"cell_type": "code", "execution_count": 9, "id": "e5a2c609", "metadata": {}, "outputs": [], "source": ["avg_stock_price_infy = quote['NSE:INFY']['average_price']"]}, {"cell_type": "code", "execution_count": 10, "id": "0fc0f959", "metadata": {}, "outputs": [{"data": {"text/plain": ["1565.44"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["avg_stock_price_infy"]}, {"cell_type": "markdown", "id": "9e233976", "metadata": {}, "source": ["## Picking up call options which are +10% of avg stock price and picking up put options which are -10% of avg stock price expiring in next 15 days."]}, {"cell_type": "code", "execution_count": 11, "id": "7e3f9dd1", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "# Parameters\n", "call_strike_threshold = avg_stock_price_infy * 1.10   # 10% above\n", "put_strike_threshold = avg_stock_price_infy * 0.85    # 10% below\n", "today = pd.Timestamp(datetime.today().date())\n", "expiry_limit = today + pd.<PERSON><PERSON><PERSON>(days=15)\n", "\n", "# Convert expiry to datetime\n", "instruments_df['expiry'] = pd.to_datetime(instruments_df['expiry'], errors='coerce')\n", "\n", "# Filter for INFY options within expiry range\n", "infy_options = instruments_df[\n", "    (instruments_df['name'] == 'INFY') &\n", "    (instruments_df['expiry'].notna()) &\n", "    (instruments_df['expiry'] <= expiry_limit) & (instruments_df['expiry'] >= today)\n", "]\n", "\n", "# Call options: strike  10% above avg\n", "call_options = infy_options[\n", "    (infy_options['instrument_type']=='CE') &\n", "    (infy_options['strike'] >= avg_stock_price_infy) & (infy_options['strike']<= call_strike_threshold)\n", "]\n", "\n", "# Put options: strike ≥ 10% below avg\n", "put_options = infy_options[\n", "    (infy_options['instrument_type']=='PE') &\n", "    (infy_options['strike'] >= put_strike_threshold) & (infy_options['strike']<= avg_stock_price_infy)\n", "]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f316ec63", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "id": "8a6acf11", "metadata": {}, "outputs": [{"data": {"text/plain": ["1721.9840000000002"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["call_strike_threshold"]}, {"cell_type": "code", "execution_count": 13, "id": "00574328", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8, 12)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["call_options.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "634cb010", "metadata": {}, "outputs": [{"data": {"text/plain": ["1330.624"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["put_strike_threshold"]}, {"cell_type": "code", "execution_count": 15, "id": "ed28de59", "metadata": {}, "outputs": [{"data": {"text/plain": ["(12, 12)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "4b359a21", "metadata": {}, "outputs": [], "source": ["infosys_opt = pd.concat([call_options, put_options], axis=0)\n"]}, {"cell_type": "code", "execution_count": 18, "id": "a8e26011", "metadata": {}, "outputs": [{"data": {"text/plain": ["(20, 12)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["infosys_opt.shape"]}, {"cell_type": "code", "execution_count": 19, "id": "6d4b3a21", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>67478</th>\n", "      <td>24357122</td>\n", "      <td>95145</td>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1600.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67480</th>\n", "      <td>27076098</td>\n", "      <td>105766</td>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1580.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67482</th>\n", "      <td>27077890</td>\n", "      <td>105773</td>\n", "      <td>INFY25MAY1620CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1620.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67486</th>\n", "      <td>24357634</td>\n", "      <td>95147</td>\n", "      <td>INFY25MAY1640CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1640.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67490</th>\n", "      <td>27080962</td>\n", "      <td>105785</td>\n", "      <td>INFY25MAY1660CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1660.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67494</th>\n", "      <td>24358146</td>\n", "      <td>95149</td>\n", "      <td>INFY25MAY1680CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1680.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67498</th>\n", "      <td>27081986</td>\n", "      <td>105789</td>\n", "      <td>INFY25MAY1700CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1700.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67502</th>\n", "      <td>24358658</td>\n", "      <td>95151</td>\n", "      <td>INFY25MAY1720CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1720.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67485</th>\n", "      <td>24356866</td>\n", "      <td>95144</td>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1560.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67489</th>\n", "      <td>27074562</td>\n", "      <td>105760</td>\n", "      <td>INFY25MAY1540PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1540.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67493</th>\n", "      <td>24356354</td>\n", "      <td>95142</td>\n", "      <td>INFY25MAY1520PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1520.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67497</th>\n", "      <td>27072258</td>\n", "      <td>105751</td>\n", "      <td>INFY25MAY1500PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1500.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67501</th>\n", "      <td>24355842</td>\n", "      <td>95140</td>\n", "      <td>INFY25MAY1480PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1480.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67505</th>\n", "      <td>27071746</td>\n", "      <td>105749</td>\n", "      <td>INFY25MAY1460PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1460.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67520</th>\n", "      <td>24355330</td>\n", "      <td>95138</td>\n", "      <td>INFY25MAY1440PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1440.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67522</th>\n", "      <td>27071234</td>\n", "      <td>105747</td>\n", "      <td>INFY25MAY1420PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1420.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67524</th>\n", "      <td>24354818</td>\n", "      <td>95136</td>\n", "      <td>INFY25MAY1400PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1400.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67526</th>\n", "      <td>27070722</td>\n", "      <td>105745</td>\n", "      <td>INFY25MAY1380PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67528</th>\n", "      <td>24354306</td>\n", "      <td>95134</td>\n", "      <td>INFY25MAY1360PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1360.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67530</th>\n", "      <td>27054082</td>\n", "      <td>105680</td>\n", "      <td>INFY25MAY1340PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1340.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument_token  exchange_token    tradingsymbol  name  last_price  \\\n", "67478          24357122           95145  INFY25MAY1600CE  INFY           0   \n", "67480          27076098          105766  INFY25MAY1580CE  INFY           0   \n", "67482          27077890          105773  INFY25MAY1620CE  INFY           0   \n", "67486          24357634           95147  INFY25MAY1640CE  INFY           0   \n", "67490          27080962          105785  INFY25MAY1660CE  INFY           0   \n", "67494          24358146           95149  INFY25MAY1680CE  INFY           0   \n", "67498          27081986          105789  INFY25MAY1700CE  INFY           0   \n", "67502          24358658           95151  INFY25MAY1720CE  INFY           0   \n", "67485          24356866           95144  INFY25MAY1560PE  INFY           0   \n", "67489          27074562          105760  INFY25MAY1540PE  INFY           0   \n", "67493          24356354           95142  INFY25MAY1520PE  INFY           0   \n", "67497          27072258          105751  INFY25MAY1500PE  INFY           0   \n", "67501          24355842           95140  INFY25MAY1480PE  INFY           0   \n", "67505          27071746          105749  INFY25MAY1460PE  INFY           0   \n", "67520          24355330           95138  INFY25MAY1440PE  INFY           0   \n", "67522          27071234          105747  INFY25MAY1420PE  INFY           0   \n", "67524          24354818           95136  INFY25MAY1400PE  INFY           0   \n", "67526          27070722          105745  INFY25MAY1380PE  INFY           0   \n", "67528          24354306           95134  INFY25MAY1360PE  INFY           0   \n", "67530          27054082          105680  INFY25MAY1340PE  INFY           0   \n", "\n", "          expiry  strike  tick_size  lot_size instrument_type  segment  \\\n", "67478 2025-05-29  1600.0       0.05       400              CE  NFO-OPT   \n", "67480 2025-05-29  1580.0       0.05       400              CE  NFO-OPT   \n", "67482 2025-05-29  1620.0       0.05       400              CE  NFO-OPT   \n", "67486 2025-05-29  1640.0       0.05       400              CE  NFO-OPT   \n", "67490 2025-05-29  1660.0       0.05       400              CE  NFO-OPT   \n", "67494 2025-05-29  1680.0       0.05       400              CE  NFO-OPT   \n", "67498 2025-05-29  1700.0       0.05       400              CE  NFO-OPT   \n", "67502 2025-05-29  1720.0       0.05       400              CE  NFO-OPT   \n", "67485 2025-05-29  1560.0       0.05       400              PE  NFO-OPT   \n", "67489 2025-05-29  1540.0       0.05       400              PE  NFO-OPT   \n", "67493 2025-05-29  1520.0       0.05       400              PE  NFO-OPT   \n", "67497 2025-05-29  1500.0       0.05       400              PE  NFO-OPT   \n", "67501 2025-05-29  1480.0       0.05       400              PE  NFO-OPT   \n", "67505 2025-05-29  1460.0       0.05       400              PE  NFO-OPT   \n", "67520 2025-05-29  1440.0       0.05       400              PE  NFO-OPT   \n", "67522 2025-05-29  1420.0       0.05       400              PE  NFO-OPT   \n", "67524 2025-05-29  1400.0       0.05       400              PE  NFO-OPT   \n", "67526 2025-05-29  1380.0       0.05       400              PE  NFO-OPT   \n", "67528 2025-05-29  1360.0       0.05       400              PE  NFO-OPT   \n", "67530 2025-05-29  1340.0       0.05       400              PE  NFO-OPT   \n", "\n", "      exchange  \n", "67478      NFO  \n", "67480      NFO  \n", "67482      NFO  \n", "67486      NFO  \n", "67490      NFO  \n", "67494      NFO  \n", "67498      NFO  \n", "67502      NFO  \n", "67485      NFO  \n", "67489      NFO  \n", "67493      NFO  \n", "67497      NFO  \n", "67501      NFO  \n", "67505      NFO  \n", "67520      NFO  \n", "67522      NFO  \n", "67524      NFO  \n", "67526      NFO  \n", "67528      NFO  \n", "67530      NFO  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["infosys_opt.head(20)"]}, {"cell_type": "markdown", "id": "91236076", "metadata": {}, "source": ["Now for each instrument we need LTP, strike, margin and premium"]}, {"cell_type": "code", "execution_count": 22, "id": "279a050f", "metadata": {}, "outputs": [], "source": ["# 3. Define the order(s) whose margin you want to simulate\n", "orders = [\n", "    {\n", "        \"exchange\":       \"NFO\",         \n", "        \"tradingsymbol\":  \"INFY25MAY1760PE\",       \n", "        \"transaction_type\":\"BUY\",        \n", "        \"variety\":        \"regular\",     \n", "        \"product\":        \"MIS\",         \n", "        \"order_type\":     \"MARKET\",      \n", "        \"quantity\":       400,           \n", "        \"price\":          0,             \n", "        \"trigger_price\":  0              \n", "    },\n", "    {\n", "        \"exchange\":       \"NFO\",         \n", "        \"tradingsymbol\":  \"INFY25MAY1340CE\",       \n", "        \"transaction_type\":\"BUY\",        \n", "        \"variety\":        \"regular\",     \n", "        \"product\":        \"MIS\",         \n", "        \"order_type\":     \"MARKET\",      \n", "        \"quantity\":       400,           \n", "        \"price\":          0,             \n", "        \"trigger_price\":  0              \n", "    }\n", "]\n", "\n", "\n", "margins_response = kite.order_margins(params=orders)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "06406e1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1760PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 76400,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 27.144920000000003,\n", "   'sebi_turnover_charge': 0.0764,\n", "   'brokerage': 20,\n", "   'stamp_duty': 2,\n", "   'gst': {'igst': 8.4998376, 'cgst': 0, 'sgst': 0, 'total': 8.4998376},\n", "   'total': 57.721157600000005},\n", "  'total': 76400},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1340CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 94000,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 33.398199999999996,\n", "   'sebi_turnover_charge': 0.094,\n", "   'brokerage': 20,\n", "   'stamp_duty': 3,\n", "   'gst': {'igst': 9.628596, 'cgst': 0, 'sgst': 0, 'total': 9.628596},\n", "   'total': 66.120796},\n", "  'total': 94000}]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response"]}, {"cell_type": "markdown", "id": "4f464ae2", "metadata": {}, "source": ["Preparing the payload to calculate premium"]}, {"cell_type": "code", "execution_count": 20, "id": "7af0abf9", "metadata": {}, "outputs": [], "source": ["# Base order template\n", "base_order = {\n", "    \"exchange\":       \"NFO\",\n", "    \"transaction_type\": \"BUY\",\n", "    \"variety\":        \"regular\",\n", "    \"product\":        \"MIS\",\n", "    \"order_type\":     \"MARKET\",\n", "    \"price\":          0,\n", "    \"trigger_price\":  0\n", "}\n", "\n", "# Build orders list using tradingsymbol and quantity from DataFrame\n", "orders = [\n", "    {\n", "        **base_order,\n", "        \"tradingsymbol\": row[\"tradingsymbol\"],\n", "        \"quantity\": row[\"lot_size\"]  # Use 'lot_size' from each row\n", "    }\n", "    for _, row in infosys_opt.iterrows()\n", "]\n", "\n", "# Now `orders` is ready to be used\n"]}, {"cell_type": "code", "execution_count": 21, "id": "b8f6ca1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(orders)"]}, {"cell_type": "code", "execution_count": 23, "id": "43a57a73", "metadata": {}, "outputs": [], "source": ["margins_response = kite.order_margins(params=orders)"]}, {"cell_type": "code", "execution_count": 24, "id": "7f1f89bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1600CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 4760,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.691228,\n", "   'sebi_turnover_charge': 0.0047599999999999995,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.9052778400000006,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.9052778400000006},\n", "   'total': 25.60126584},\n", "  'total': 4760},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1580CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 7560,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 2.6860679999999997,\n", "   'sebi_turnover_charge': 0.007559999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 4.0848530400000005,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 4.0848530400000005},\n", "   'total': 26.778481040000003},\n", "  'total': 7560},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1620CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 3020,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.073006,\n", "   'sebi_turnover_charge': 0.0030199999999999997,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.7936846799999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.7936846799999997},\n", "   'total': 24.869710679999997},\n", "  'total': 3020},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1640CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 1840,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.6537519999999999,\n", "   'sebi_turnover_charge': 0.0018399999999999996,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.71800656, 'cgst': 0, 'sgst': 0, 'total': 3.71800656},\n", "   'total': 24.37359856},\n", "  'total': 1840},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1660CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 1180,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.419254,\n", "   'sebi_turnover_charge': 0.0011799999999999998,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6756781199999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6756781199999997},\n", "   'total': 24.09611212},\n", "  'total': 1180},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1680CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 780,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.277134,\n", "   'sebi_turnover_charge': 0.00078,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6500245199999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6500245199999997},\n", "   'total': 23.927938519999998},\n", "  'total': 780},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1700CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 600,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.21318,\n", "   'sebi_turnover_charge': 0.0006,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6384803999999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6384803999999997},\n", "   'total': 23.8522604},\n", "  'total': 600},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1720CE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 360,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.127908,\n", "   'sebi_turnover_charge': 0.00035999999999999997,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6230882399999995,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6230882399999995},\n", "   'total': 23.75135624},\n", "  'total': 360},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1560PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 10840,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 3.8514519999999997,\n", "   'sebi_turnover_charge': 0.010839999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 4.29521256, 'cgst': 0, 'sgst': 0, 'total': 4.29521256},\n", "   'total': 28.15750456},\n", "  'total': 10840},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1540PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 7600,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 2.70028,\n", "   'sebi_turnover_charge': 0.0076,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 4.0874184, 'cgst': 0, 'sgst': 0, 'total': 4.0874184},\n", "   'total': 26.7952984},\n", "  'total': 7600},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1520PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 5080,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.8049240000000002,\n", "   'sebi_turnover_charge': 0.0050799999999999994,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.92580072, 'cgst': 0, 'sgst': 0, 'total': 3.92580072},\n", "   'total': 25.735804719999997},\n", "  'total': 5080},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1500PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 3440,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.2222320000000002,\n", "   'sebi_turnover_charge': 0.00344,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.82062096, 'cgst': 0, 'sgst': 0, 'total': 3.82062096},\n", "   'total': 25.046292960000002},\n", "  'total': 3440},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1480PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 2300,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.81719,\n", "   'sebi_turnover_charge': 0.0023,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.7475082000000004,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.7475082000000004},\n", "   'total': 24.5669982},\n", "  'total': 2300},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1460PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 1620,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.575586,\n", "   'sebi_turnover_charge': 0.00162,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.703897079999999,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.703897079999999},\n", "   'total': 24.28110308},\n", "  'total': 1620},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1440PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 1200,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.42636,\n", "   'sebi_turnover_charge': 0.0012,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6769608, 'cgst': 0, 'sgst': 0, 'total': 3.6769608},\n", "   'total': 24.1045208},\n", "  'total': 1200},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1420PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 1000,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.3553,\n", "   'sebi_turnover_charge': 0.001,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6641340000000002,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6641340000000002},\n", "   'total': 24.020434},\n", "  'total': 1000},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1400PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 760,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.27002800000000005,\n", "   'sebi_turnover_charge': 0.0007599999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6487418399999996,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6487418399999996},\n", "   'total': 23.91952984},\n", "  'total': 760},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1380PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 640,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.227392,\n", "   'sebi_turnover_charge': 0.0006399999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6410457599999995,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6410457599999995},\n", "   'total': 23.86907776},\n", "  'total': 640},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1360PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 520,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.184756,\n", "   'sebi_turnover_charge': 0.00052,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6333496800000002,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6333496800000002},\n", "   'total': 23.818625680000004},\n", "  'total': 520},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1340PE',\n", "  'exchange': 'NFO',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 380,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.13501400000000002,\n", "   'sebi_turnover_charge': 0.00037999999999999997,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6243709199999996,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6243709199999996},\n", "   'total': 23.759764920000002},\n", "  'total': 380}]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response"]}, {"cell_type": "code", "execution_count": 25, "id": "99c65398", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["len(margins_response)"]}, {"cell_type": "code", "execution_count": 26, "id": "6af31fe0", "metadata": {}, "outputs": [{"data": {"text/plain": ["4760"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response[0]['option_premium']"]}, {"cell_type": "code", "execution_count": 27, "id": "b832f515", "metadata": {}, "outputs": [], "source": ["premium_df = pd.DataFrame([{'tradingsymbol': item['tradingsymbol'], 'option_premium': item['option_premium']}for item in margins_response])"]}, {"cell_type": "code", "execution_count": 28, "id": "f149d743", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>option_premium</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>4760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>7560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>INFY25MAY1620CE</td>\n", "      <td>3020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>INFY25MAY1640CE</td>\n", "      <td>1840</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>INFY25MAY1660CE</td>\n", "      <td>1180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>INFY25MAY1680CE</td>\n", "      <td>780</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>INFY25MAY1700CE</td>\n", "      <td>600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>INFY25MAY1720CE</td>\n", "      <td>360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>10840</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>INFY25MAY1540PE</td>\n", "      <td>7600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>INFY25MAY1520PE</td>\n", "      <td>5080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>INFY25MAY1500PE</td>\n", "      <td>3440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>INFY25MAY1480PE</td>\n", "      <td>2300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>INFY25MAY1460PE</td>\n", "      <td>1620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>INFY25MAY1440PE</td>\n", "      <td>1200</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      tradingsymbol  option_premium\n", "0   INFY25MAY1600CE            4760\n", "1   INFY25MAY1580CE            7560\n", "2   INFY25MAY1620CE            3020\n", "3   INFY25MAY1640CE            1840\n", "4   INFY25MAY1660CE            1180\n", "5   INFY25MAY1680CE             780\n", "6   INFY25MAY1700CE             600\n", "7   INFY25MAY1720CE             360\n", "8   INFY25MAY1560PE           10840\n", "9   INFY25MAY1540PE            7600\n", "10  INFY25MAY1520PE            5080\n", "11  INFY25MAY1500PE            3440\n", "12  INFY25MAY1480PE            2300\n", "13  INFY25MAY1460PE            1620\n", "14  INFY25MAY1440PE            1200"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["premium_df.head(15)"]}, {"cell_type": "markdown", "id": "e09fbe9a", "metadata": {}, "source": ["Preparing payload to calculate margin (span+exposure) <br>\n", "margin need to be calculated separately for call and put options"]}, {"cell_type": "code", "execution_count": 29, "id": "bafc7778", "metadata": {}, "outputs": [], "source": ["# Base order template\n", "base_order_2 = {\n", "    \"exchange\":       \"NFO\",\n", "    \"transaction_type\": \"SELL\",\n", "    \"variety\":        \"regular\",\n", "    \"product\":        \"MIS\",\n", "    \"order_type\":     \"MARKET\",\n", "    \"price\":          0,\n", "    \"trigger_price\":  0\n", "}\n", "\n", "# Build orders list using tradingsymbol and quantity from DataFrame\n", "orders_2 = [\n", "    {\n", "        **base_order_2,\n", "        \"tradingsymbol\": row[\"tradingsymbol\"],\n", "        \"quantity\": row[\"lot_size\"]  # Use 'lot_size' from each row\n", "    }\n", "    for _, row in call_options.iterrows()\n", "]\n", "\n", "# Now `orders` is ready to be used\n"]}, {"cell_type": "code", "execution_count": 30, "id": "1f069abe", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1600CE',\n", "  'quantity': 400},\n", " {'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1580CE',\n", "  'quantity': 400},\n", " {'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1620CE',\n", "  'quantity': 400},\n", " {'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1640CE',\n", "  'quantity': 400},\n", " {'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1660CE',\n", "  'quantity': 400},\n", " {'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1680CE',\n", "  'quantity': 400},\n", " {'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1700CE',\n", "  'quantity': 400},\n", " {'exchange': 'NFO',\n", "  'transaction_type': 'SELL',\n", "  'variety': 'regular',\n", "  'product': 'MIS',\n", "  'order_type': 'MARKET',\n", "  'price': 0,\n", "  'trigger_price': 0,\n", "  'tradingsymbol': 'INFY25MAY1720CE',\n", "  'quantity': 400}]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["orders_2"]}, {"cell_type": "code", "execution_count": 31, "id": "d07b96d5", "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["len(orders_2)"]}, {"cell_type": "code", "execution_count": 32, "id": "a3546b77", "metadata": {}, "outputs": [], "source": ["margins_response = kite.order_margins(params=orders_2)"]}, {"cell_type": "code", "execution_count": 33, "id": "afe3317f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1600CE',\n", "  'exchange': 'NFO',\n", "  'span': 84676,\n", "  'exposure': 22003.800000000003,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 4.76,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.691228,\n", "   'sebi_turnover_charge': 0.0047599999999999995,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.9052778400000006,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.9052778400000006},\n", "   'total': 30.36126584},\n", "  'total': 106679.8},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1580CE',\n", "  'exchange': 'NFO',\n", "  'span': 92788,\n", "  'exposure': 22101.800000000003,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 7.56,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 2.6860679999999997,\n", "   'sebi_turnover_charge': 0.007559999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 4.0848530400000005,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 4.0848530400000005},\n", "   'total': 34.338481040000005},\n", "  'total': 114889.8},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1620CE',\n", "  'exchange': 'NFO',\n", "  'span': 77104,\n", "  'exposure': 21942.899999999994,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 3.02,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.073006,\n", "   'sebi_turnover_charge': 0.0030199999999999997,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.7936846799999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.7936846799999997},\n", "   'total': 27.889710679999997},\n", "  'total': 99046.9},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1640CE',\n", "  'exchange': 'NFO',\n", "  'span': 69736,\n", "  'exposure': 21901.600000000006,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 1.8399999999999999,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.6537519999999999,\n", "   'sebi_turnover_charge': 0.0018399999999999996,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.71800656, 'cgst': 0, 'sgst': 0, 'total': 3.71800656},\n", "   'total': 26.21359856},\n", "  'total': 91637.6},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1660CE',\n", "  'exchange': 'NFO',\n", "  'span': 62868,\n", "  'exposure': 21878.5,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 1.18,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.419254,\n", "   'sebi_turnover_charge': 0.0011799999999999998,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6756781199999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6756781199999997},\n", "   'total': 25.27611212},\n", "  'total': 84746.5},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1680CE',\n", "  'exchange': 'NFO',\n", "  'span': 56312,\n", "  'exposure': 21864.5,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.78,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.277134,\n", "   'sebi_turnover_charge': 0.00078,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6500245199999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6500245199999997},\n", "   'total': 24.70793852},\n", "  'total': 78176.5},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1700CE',\n", "  'exchange': 'NFO',\n", "  'span': 50048,\n", "  'exposure': 21858.20000000001,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.6,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.21318,\n", "   'sebi_turnover_charge': 0.0006,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6384803999999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6384803999999997},\n", "   'total': 24.4522604},\n", "  'total': 71906.20000000001},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1720CE',\n", "  'exchange': 'NFO',\n", "  'span': 43864,\n", "  'exposure': 21849.79999999999,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.36,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.127908,\n", "   'sebi_turnover_charge': 0.00035999999999999997,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6230882399999995,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6230882399999995},\n", "   'total': 24.11135624},\n", "  'total': 65713.79999999999}]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response"]}, {"cell_type": "code", "execution_count": 34, "id": "b1ef4fa6", "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["len(margins_response)"]}, {"cell_type": "code", "execution_count": 35, "id": "49516327", "metadata": {}, "outputs": [{"data": {"text/plain": ["106679.8"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response[0]['total']"]}, {"cell_type": "code", "execution_count": 36, "id": "97e158f7", "metadata": {}, "outputs": [], "source": ["call_margin_df = pd.DataFrame([{'tradingsymbol': item['tradingsymbol'], 'total_margin': item['total']}for item in margins_response])"]}, {"cell_type": "code", "execution_count": 37, "id": "cf79ea22", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>total_margin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>106679.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>114889.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>INFY25MAY1620CE</td>\n", "      <td>99046.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>INFY25MAY1640CE</td>\n", "      <td>91637.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>INFY25MAY1660CE</td>\n", "      <td>84746.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>INFY25MAY1680CE</td>\n", "      <td>78176.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>INFY25MAY1700CE</td>\n", "      <td>71906.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>INFY25MAY1720CE</td>\n", "      <td>65713.8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     tradingsymbol  total_margin\n", "0  INFY25MAY1600CE      106679.8\n", "1  INFY25MAY1580CE      114889.8\n", "2  INFY25MAY1620CE       99046.9\n", "3  INFY25MAY1640CE       91637.6\n", "4  INFY25MAY1660CE       84746.5\n", "5  INFY25MAY1680CE       78176.5\n", "6  INFY25MAY1700CE       71906.2\n", "7  INFY25MAY1720CE       65713.8"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["call_margin_df.head(15)"]}, {"cell_type": "markdown", "id": "f5cf5953", "metadata": {}, "source": ["getting margin separately for put options"]}, {"cell_type": "code", "execution_count": 38, "id": "89452cb2", "metadata": {}, "outputs": [], "source": ["# Base order template\n", "base_order_3 = {\n", "    \"exchange\":       \"NFO\",\n", "    \"transaction_type\": \"SELL\",\n", "    \"variety\":        \"regular\",\n", "    \"product\":        \"MIS\",\n", "    \"order_type\":     \"MARKET\",\n", "    \"price\":          0,\n", "    \"trigger_price\":  0\n", "}\n", "\n", "# Build orders list using tradingsymbol and quantity from DataFrame\n", "orders_3 = [\n", "    {\n", "        **base_order_3,\n", "        \"tradingsymbol\": row[\"tradingsymbol\"],\n", "        \"quantity\": row[\"lot_size\"]  # Use 'lot_size' from each row\n", "    }\n", "    for _, row in put_options.iterrows()\n", "]\n", "\n", "# Now `orders` is ready to be used\n"]}, {"cell_type": "code", "execution_count": 39, "id": "abc2798e", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["len(orders_3)"]}, {"cell_type": "code", "execution_count": 40, "id": "eb18a7f0", "metadata": {}, "outputs": [], "source": ["margins_response_put = kite.order_margins(params=orders_3)"]}, {"cell_type": "code", "execution_count": 41, "id": "ca1ef0fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1560PE',\n", "  'exchange': 'NFO',\n", "  'span': 75352,\n", "  'exposure': 22216.6,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 10.84,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 3.8514519999999997,\n", "   'sebi_turnover_charge': 0.010839999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 4.29521256, 'cgst': 0, 'sgst': 0, 'total': 4.29521256},\n", "   'total': 38.997504559999996},\n", "  'total': 97568.6},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1540PE',\n", "  'exchange': 'NFO',\n", "  'span': 68220,\n", "  'exposure': 22103.200000000004,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 7.6000000000000005,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 2.70028,\n", "   'sebi_turnover_charge': 0.0076,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 4.0874184, 'cgst': 0, 'sgst': 0, 'total': 4.0874184},\n", "   'total': 34.3952984},\n", "  'total': 90323.20000000001},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1520PE',\n", "  'exchange': 'NFO',\n", "  'span': 61156,\n", "  'exposure': 22015,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 5.08,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.8049240000000002,\n", "   'sebi_turnover_charge': 0.0050799999999999994,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.92580072, 'cgst': 0, 'sgst': 0, 'total': 3.92580072},\n", "   'total': 30.815804719999996},\n", "  'total': 83171},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1500PE',\n", "  'exchange': 'NFO',\n", "  'span': 54172,\n", "  'exposure': 21957.59999999999,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 3.44,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 1.2222320000000002,\n", "   'sebi_turnover_charge': 0.00344,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.82062096, 'cgst': 0, 'sgst': 0, 'total': 3.82062096},\n", "   'total': 28.486292960000004},\n", "  'total': 76129.59999999999},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1480PE',\n", "  'exchange': 'NFO',\n", "  'span': 47360,\n", "  'exposure': 21917.699999999997,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 2.3000000000000003,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.81719,\n", "   'sebi_turnover_charge': 0.0023,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.7475082000000004,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.7475082000000004},\n", "   'total': 26.8669982},\n", "  'total': 69277.7},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1460PE',\n", "  'exchange': 'NFO',\n", "  'span': 40612,\n", "  'exposure': 21893.90000000001,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 1.62,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.575586,\n", "   'sebi_turnover_charge': 0.00162,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.703897079999999,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.703897079999999},\n", "   'total': 25.901103080000002},\n", "  'total': 62505.90000000001},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1440PE',\n", "  'exchange': 'NFO',\n", "  'span': 34176,\n", "  'exposure': 21879.20000000001,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 1.2,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.42636,\n", "   'sebi_turnover_charge': 0.0012,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6769608, 'cgst': 0, 'sgst': 0, 'total': 3.6769608},\n", "   'total': 25.3045208},\n", "  'total': 56055.20000000001},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1420PE',\n", "  'exchange': 'NFO',\n", "  'span': 28156,\n", "  'exposure': 21872.199999999983,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 1,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.3553,\n", "   'sebi_turnover_charge': 0.001,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6641340000000002,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6641340000000002},\n", "   'total': 25.020434},\n", "  'total': 50028.19999999998},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1400PE',\n", "  'exchange': 'NFO',\n", "  'span': 29236,\n", "  'exposure': 21863.800000000017,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.76,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.27002800000000005,\n", "   'sebi_turnover_charge': 0.0007599999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6487418399999996,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6487418399999996},\n", "   'total': 24.67952984},\n", "  'total': 51099.80000000002},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1380PE',\n", "  'exchange': 'NFO',\n", "  'span': 33996,\n", "  'exposure': 21859.600000000006,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.64,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.227392,\n", "   'sebi_turnover_charge': 0.0006399999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6410457599999995,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6410457599999995},\n", "   'total': 24.50907776},\n", "  'total': 55855.600000000006},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1360PE',\n", "  'exchange': 'NFO',\n", "  'span': 31128,\n", "  'exposure': 21855.400000000023,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.52,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.184756,\n", "   'sebi_turnover_charge': 0.00052,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6333496800000002,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6333496800000002},\n", "   'total': 24.338625680000003},\n", "  'total': 52983.40000000002},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'INFY25MAY1340PE',\n", "  'exchange': 'NFO',\n", "  'span': 28300,\n", "  'exposure': 21850.49999999997,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.38,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.13501400000000002,\n", "   'sebi_turnover_charge': 0.00037999999999999997,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6243709199999996,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6243709199999996},\n", "   'total': 24.13976492},\n", "  'total': 50150.49999999997}]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response_put"]}, {"cell_type": "code", "execution_count": 42, "id": "43e8e974", "metadata": {}, "outputs": [], "source": ["put_margin_df = pd.DataFrame([{'tradingsymbol': item['tradingsymbol'], 'total_margin': item['total']}for item in margins_response_put])"]}, {"cell_type": "code", "execution_count": 43, "id": "b11cfd1f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>total_margin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>97568.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>INFY25MAY1540PE</td>\n", "      <td>90323.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>INFY25MAY1520PE</td>\n", "      <td>83171.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>INFY25MAY1500PE</td>\n", "      <td>76129.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>INFY25MAY1480PE</td>\n", "      <td>69277.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>INFY25MAY1460PE</td>\n", "      <td>62505.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>INFY25MAY1440PE</td>\n", "      <td>56055.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>INFY25MAY1420PE</td>\n", "      <td>50028.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>INFY25MAY1400PE</td>\n", "      <td>51099.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>INFY25MAY1380PE</td>\n", "      <td>55855.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>INFY25MAY1360PE</td>\n", "      <td>52983.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>INFY25MAY1340PE</td>\n", "      <td>50150.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      tradingsymbol  total_margin\n", "0   INFY25MAY1560PE       97568.6\n", "1   INFY25MAY1540PE       90323.2\n", "2   INFY25MAY1520PE       83171.0\n", "3   INFY25MAY1500PE       76129.6\n", "4   INFY25MAY1480PE       69277.7\n", "5   INFY25MAY1460PE       62505.9\n", "6   INFY25MAY1440PE       56055.2\n", "7   INFY25MAY1420PE       50028.2\n", "8   INFY25MAY1400PE       51099.8\n", "9   INFY25MAY1380PE       55855.6\n", "10  INFY25MAY1360PE       52983.4\n", "11  INFY25MAY1340PE       50150.5"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["put_margin_df.head(15)"]}, {"cell_type": "code", "execution_count": 44, "id": "5f31b79a", "metadata": {}, "outputs": [], "source": ["margin_df = pd.concat([call_margin_df, put_margin_df], axis=0, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 45, "id": "b22fbbc4", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["len(margin_df)"]}, {"cell_type": "markdown", "id": "88c360a7", "metadata": {}, "source": ["## getting LTP data"]}, {"cell_type": "code", "execution_count": 47, "id": "49e207c4", "metadata": {}, "outputs": [], "source": ["# Generate the list of instrument identifiers\n", "instruments = [\"NFO:\" + ts for ts in infosys_opt[\"tradingsymbol\"]]\n"]}, {"cell_type": "code", "execution_count": 48, "id": "ef401a49", "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["len(instruments)"]}, {"cell_type": "code", "execution_count": 49, "id": "ef95feea", "metadata": {}, "outputs": [], "source": ["# Fetch LTPs for the instruments\n", "ltp_data = kite.ltp(instruments)\n"]}, {"cell_type": "code", "execution_count": 50, "id": "7c7905cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'NFO:INFY25MAY1340PE': {'instrument_token': 27054082, 'last_price': 0.95},\n", " 'NFO:INFY25MAY1360PE': {'instrument_token': 24354306, 'last_price': 1.3},\n", " 'NFO:INFY25MAY1380PE': {'instrument_token': 27070722, 'last_price': 1.6},\n", " 'NFO:INFY25MAY1400PE': {'instrument_token': 24354818, 'last_price': 1.9},\n", " 'NFO:INFY25MAY1420PE': {'instrument_token': 27071234, 'last_price': 2.5},\n", " 'NFO:INFY25MAY1440PE': {'instrument_token': 24355330, 'last_price': 3},\n", " 'NFO:INFY25MAY1460PE': {'instrument_token': 27071746, 'last_price': 4.05},\n", " 'NFO:INFY25MAY1480PE': {'instrument_token': 24355842, 'last_price': 5.75},\n", " 'NFO:INFY25MAY1500PE': {'instrument_token': 27072258, 'last_price': 8.6},\n", " 'NFO:INFY25MAY1520PE': {'instrument_token': 24356354, 'last_price': 12.7},\n", " 'NFO:INFY25MAY1540PE': {'instrument_token': 27074562, 'last_price': 19},\n", " 'NFO:INFY25MAY1560PE': {'instrument_token': 24356866, 'last_price': 27.1},\n", " 'NFO:INFY25MAY1580CE': {'instrument_token': 27076098, 'last_price': 18.9},\n", " 'NFO:INFY25MAY1600CE': {'instrument_token': 24357122, 'last_price': 11.9},\n", " 'NFO:INFY25MAY1620CE': {'instrument_token': 27077890, 'last_price': 7.55},\n", " 'NFO:INFY25MAY1640CE': {'instrument_token': 24357634, 'last_price': 4.6},\n", " 'NFO:INFY25MAY1660CE': {'instrument_token': 27080962, 'last_price': 2.95},\n", " 'NFO:INFY25MAY1680CE': {'instrument_token': 24358146, 'last_price': 1.95},\n", " 'NFO:INFY25MAY1700CE': {'instrument_token': 27081986, 'last_price': 1.5},\n", " 'NFO:INFY25MAY1720CE': {'instrument_token': 24358658, 'last_price': 0.9}}"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["ltp_data"]}, {"cell_type": "code", "execution_count": 51, "id": "cb7ee2bc", "metadata": {}, "outputs": [], "source": ["# Create DataFrame from ltp_data\n", "ltp_df = pd.DataFrame([\n", "    {\n", "        'tradingsymbol': key.split(':')[1],  # Extract symbol from 'NFO:SYMBOL' format\n", "        'instrument_token': ltp_data[key]['instrument_token'],\n", "        'last_price': ltp_data[key]['last_price']\n", "    }\n", "    for key in ltp_data.keys()\n", "])"]}, {"cell_type": "markdown", "id": "53d87f04", "metadata": {}, "source": ["# Merging all the data"]}, {"cell_type": "code", "execution_count": 53, "id": "4037127a", "metadata": {}, "outputs": [], "source": ["# Merge all three dataframes on 'tradingsymbol'\n", "combined_df = premium_df.merge(\n", "    margin_df, \n", "    on='tradingsymbol', \n", "    how='outer'\n", ").merge(\n", "    ltp_df,\n", "    on='tradingsymbol',\n", "    how='outer'\n", ")"]}, {"cell_type": "markdown", "id": "40df8542", "metadata": {}, "source": ["### Adding strike price, expiry date and current avg stock price."]}, {"cell_type": "code", "execution_count": 56, "id": "feaa1058", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>option_premium</th>\n", "      <th>total_margin</th>\n", "      <th>instrument_token</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INFY25MAY1340PE</td>\n", "      <td>380</td>\n", "      <td>50150.5</td>\n", "      <td>27054082</td>\n", "      <td>0.95</td>\n", "      <td>2025-05-29</td>\n", "      <td>1340.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>INFY25MAY1360PE</td>\n", "      <td>520</td>\n", "      <td>52983.4</td>\n", "      <td>24354306</td>\n", "      <td>1.30</td>\n", "      <td>2025-05-29</td>\n", "      <td>1360.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>INFY25MAY1380PE</td>\n", "      <td>640</td>\n", "      <td>55855.6</td>\n", "      <td>27070722</td>\n", "      <td>1.60</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>INFY25MAY1400PE</td>\n", "      <td>760</td>\n", "      <td>51099.8</td>\n", "      <td>24354818</td>\n", "      <td>1.90</td>\n", "      <td>2025-05-29</td>\n", "      <td>1400.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>INFY25MAY1420PE</td>\n", "      <td>1000</td>\n", "      <td>50028.2</td>\n", "      <td>27071234</td>\n", "      <td>2.50</td>\n", "      <td>2025-05-29</td>\n", "      <td>1420.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>INFY25MAY1440PE</td>\n", "      <td>1200</td>\n", "      <td>56055.2</td>\n", "      <td>24355330</td>\n", "      <td>3.00</td>\n", "      <td>2025-05-29</td>\n", "      <td>1440.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>INFY25MAY1460PE</td>\n", "      <td>1620</td>\n", "      <td>62505.9</td>\n", "      <td>27071746</td>\n", "      <td>4.05</td>\n", "      <td>2025-05-29</td>\n", "      <td>1460.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>INFY25MAY1480PE</td>\n", "      <td>2300</td>\n", "      <td>69277.7</td>\n", "      <td>24355842</td>\n", "      <td>5.75</td>\n", "      <td>2025-05-29</td>\n", "      <td>1480.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>INFY25MAY1500PE</td>\n", "      <td>3440</td>\n", "      <td>76129.6</td>\n", "      <td>27072258</td>\n", "      <td>8.60</td>\n", "      <td>2025-05-29</td>\n", "      <td>1500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>INFY25MAY1520PE</td>\n", "      <td>5080</td>\n", "      <td>83171.0</td>\n", "      <td>24356354</td>\n", "      <td>12.70</td>\n", "      <td>2025-05-29</td>\n", "      <td>1520.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>INFY25MAY1540PE</td>\n", "      <td>7600</td>\n", "      <td>90323.2</td>\n", "      <td>27074562</td>\n", "      <td>19.00</td>\n", "      <td>2025-05-29</td>\n", "      <td>1540.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>10840</td>\n", "      <td>97568.6</td>\n", "      <td>24356866</td>\n", "      <td>27.10</td>\n", "      <td>2025-05-29</td>\n", "      <td>1560.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>7560</td>\n", "      <td>114889.8</td>\n", "      <td>27076098</td>\n", "      <td>18.90</td>\n", "      <td>2025-05-29</td>\n", "      <td>1580.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>4760</td>\n", "      <td>106679.8</td>\n", "      <td>24357122</td>\n", "      <td>11.90</td>\n", "      <td>2025-05-29</td>\n", "      <td>1600.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>INFY25MAY1620CE</td>\n", "      <td>3020</td>\n", "      <td>99046.9</td>\n", "      <td>27077890</td>\n", "      <td>7.55</td>\n", "      <td>2025-05-29</td>\n", "      <td>1620.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>INFY25MAY1640CE</td>\n", "      <td>1840</td>\n", "      <td>91637.6</td>\n", "      <td>24357634</td>\n", "      <td>4.60</td>\n", "      <td>2025-05-29</td>\n", "      <td>1640.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>INFY25MAY1660CE</td>\n", "      <td>1180</td>\n", "      <td>84746.5</td>\n", "      <td>27080962</td>\n", "      <td>2.95</td>\n", "      <td>2025-05-29</td>\n", "      <td>1660.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>INFY25MAY1680CE</td>\n", "      <td>780</td>\n", "      <td>78176.5</td>\n", "      <td>24358146</td>\n", "      <td>1.95</td>\n", "      <td>2025-05-29</td>\n", "      <td>1680.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>INFY25MAY1700CE</td>\n", "      <td>600</td>\n", "      <td>71906.2</td>\n", "      <td>27081986</td>\n", "      <td>1.50</td>\n", "      <td>2025-05-29</td>\n", "      <td>1700.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>INFY25MAY1720CE</td>\n", "      <td>360</td>\n", "      <td>65713.8</td>\n", "      <td>24358658</td>\n", "      <td>0.90</td>\n", "      <td>2025-05-29</td>\n", "      <td>1720.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      tradingsymbol  option_premium  total_margin  instrument_token  \\\n", "0   INFY25MAY1340PE             380       50150.5          27054082   \n", "1   INFY25MAY1360PE             520       52983.4          24354306   \n", "2   INFY25MAY1380PE             640       55855.6          27070722   \n", "3   INFY25MAY1400PE             760       51099.8          24354818   \n", "4   INFY25MAY1420PE            1000       50028.2          27071234   \n", "5   INFY25MAY1440<PERSON>E            1200       56055.2          24355330   \n", "6   INFY25MAY1460PE            1620       62505.9          27071746   \n", "7   INFY25MAY1480PE            2300       69277.7          24355842   \n", "8   INFY25MAY1500PE            3440       76129.6          27072258   \n", "9   INFY25MAY1520PE            5080       83171.0          24356354   \n", "10  INFY25MAY1540PE            7600       90323.2          27074562   \n", "11  INFY25MAY1560PE           10840       97568.6          24356866   \n", "12  INFY25MAY1580CE            7560      114889.8          27076098   \n", "13  INFY25MAY1600CE            4760      106679.8          24357122   \n", "14  INFY25MAY1620CE            3020       99046.9          27077890   \n", "15  INFY25MAY1640CE            1840       91637.6          24357634   \n", "16  INFY25MAY1660CE            1180       84746.5          27080962   \n", "17  INFY25MAY1680CE             780       78176.5          24358146   \n", "18  INFY25MAY1700CE             600       71906.2          27081986   \n", "19  INFY25MAY1720CE             360       65713.8          24358658   \n", "\n", "    last_price     expiry  strike  \n", "0         0.95 2025-05-29  1340.0  \n", "1         1.30 2025-05-29  1360.0  \n", "2         1.60 2025-05-29  1380.0  \n", "3         1.90 2025-05-29  1400.0  \n", "4         2.50 2025-05-29  1420.0  \n", "5         3.00 2025-05-29  1440.0  \n", "6         4.05 2025-05-29  1460.0  \n", "7         5.75 2025-05-29  1480.0  \n", "8         8.60 2025-05-29  1500.0  \n", "9        12.70 2025-05-29  1520.0  \n", "10       19.00 2025-05-29  1540.0  \n", "11       27.10 2025-05-29  1560.0  \n", "12       18.90 2025-05-29  1580.0  \n", "13       11.90 2025-05-29  1600.0  \n", "14        7.55 2025-05-29  1620.0  \n", "15        4.60 2025-05-29  1640.0  \n", "16        2.95 2025-05-29  1660.0  \n", "17        1.95 2025-05-29  1680.0  \n", "18        1.50 2025-05-29  1700.0  \n", "19        0.90 2025-05-29  1720.0  "]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df = combined_df.merge(infosys_opt[['tradingsymbol', 'expiry', 'strike']], on='tradingsymbol', how='inner')\n", "\n", "combined_df.head(20)"]}, {"cell_type": "code", "execution_count": 57, "id": "bf2d0e13", "metadata": {}, "outputs": [], "source": ["combined_df['current_avg_stock_price'] = avg_stock_price_infy"]}, {"cell_type": "code", "execution_count": 58, "id": "ffc33e68", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>option_premium</th>\n", "      <th>total_margin</th>\n", "      <th>instrument_token</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>current_avg_stock_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INFY25MAY1340PE</td>\n", "      <td>380</td>\n", "      <td>50150.5</td>\n", "      <td>27054082</td>\n", "      <td>0.95</td>\n", "      <td>2025-05-29</td>\n", "      <td>1340.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>INFY25MAY1360PE</td>\n", "      <td>520</td>\n", "      <td>52983.4</td>\n", "      <td>24354306</td>\n", "      <td>1.30</td>\n", "      <td>2025-05-29</td>\n", "      <td>1360.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>INFY25MAY1380PE</td>\n", "      <td>640</td>\n", "      <td>55855.6</td>\n", "      <td>27070722</td>\n", "      <td>1.60</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>INFY25MAY1400PE</td>\n", "      <td>760</td>\n", "      <td>51099.8</td>\n", "      <td>24354818</td>\n", "      <td>1.90</td>\n", "      <td>2025-05-29</td>\n", "      <td>1400.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>INFY25MAY1420PE</td>\n", "      <td>1000</td>\n", "      <td>50028.2</td>\n", "      <td>27071234</td>\n", "      <td>2.50</td>\n", "      <td>2025-05-29</td>\n", "      <td>1420.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>INFY25MAY1440PE</td>\n", "      <td>1200</td>\n", "      <td>56055.2</td>\n", "      <td>24355330</td>\n", "      <td>3.00</td>\n", "      <td>2025-05-29</td>\n", "      <td>1440.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>INFY25MAY1460PE</td>\n", "      <td>1620</td>\n", "      <td>62505.9</td>\n", "      <td>27071746</td>\n", "      <td>4.05</td>\n", "      <td>2025-05-29</td>\n", "      <td>1460.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>INFY25MAY1480PE</td>\n", "      <td>2300</td>\n", "      <td>69277.7</td>\n", "      <td>24355842</td>\n", "      <td>5.75</td>\n", "      <td>2025-05-29</td>\n", "      <td>1480.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>INFY25MAY1500PE</td>\n", "      <td>3440</td>\n", "      <td>76129.6</td>\n", "      <td>27072258</td>\n", "      <td>8.60</td>\n", "      <td>2025-05-29</td>\n", "      <td>1500.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>INFY25MAY1520PE</td>\n", "      <td>5080</td>\n", "      <td>83171.0</td>\n", "      <td>24356354</td>\n", "      <td>12.70</td>\n", "      <td>2025-05-29</td>\n", "      <td>1520.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>INFY25MAY1540PE</td>\n", "      <td>7600</td>\n", "      <td>90323.2</td>\n", "      <td>27074562</td>\n", "      <td>19.00</td>\n", "      <td>2025-05-29</td>\n", "      <td>1540.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>10840</td>\n", "      <td>97568.6</td>\n", "      <td>24356866</td>\n", "      <td>27.10</td>\n", "      <td>2025-05-29</td>\n", "      <td>1560.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>7560</td>\n", "      <td>114889.8</td>\n", "      <td>27076098</td>\n", "      <td>18.90</td>\n", "      <td>2025-05-29</td>\n", "      <td>1580.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>4760</td>\n", "      <td>106679.8</td>\n", "      <td>24357122</td>\n", "      <td>11.90</td>\n", "      <td>2025-05-29</td>\n", "      <td>1600.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>INFY25MAY1620CE</td>\n", "      <td>3020</td>\n", "      <td>99046.9</td>\n", "      <td>27077890</td>\n", "      <td>7.55</td>\n", "      <td>2025-05-29</td>\n", "      <td>1620.0</td>\n", "      <td>1565.44</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      tradingsymbol  option_premium  total_margin  instrument_token  \\\n", "0   INFY25MAY1340PE             380       50150.5          27054082   \n", "1   INFY25MAY1360PE             520       52983.4          24354306   \n", "2   INFY25MAY1380PE             640       55855.6          27070722   \n", "3   INFY25MAY1400PE             760       51099.8          24354818   \n", "4   INFY25MAY1420PE            1000       50028.2          27071234   \n", "5   INFY25MAY1440<PERSON>E            1200       56055.2          24355330   \n", "6   INFY25MAY1460PE            1620       62505.9          27071746   \n", "7   INFY25MAY1480PE            2300       69277.7          24355842   \n", "8   INFY25MAY1500PE            3440       76129.6          27072258   \n", "9   INFY25MAY1520PE            5080       83171.0          24356354   \n", "10  INFY25MAY1540PE            7600       90323.2          27074562   \n", "11  INFY25MAY1560PE           10840       97568.6          24356866   \n", "12  INFY25MAY1580CE            7560      114889.8          27076098   \n", "13  INFY25MAY1600CE            4760      106679.8          24357122   \n", "14  INFY25MAY1620CE            3020       99046.9          27077890   \n", "\n", "    last_price     expiry  strike  current_avg_stock_price  \n", "0         0.95 2025-05-29  1340.0                  1565.44  \n", "1         1.30 2025-05-29  1360.0                  1565.44  \n", "2         1.60 2025-05-29  1380.0                  1565.44  \n", "3         1.90 2025-05-29  1400.0                  1565.44  \n", "4         2.50 2025-05-29  1420.0                  1565.44  \n", "5         3.00 2025-05-29  1440.0                  1565.44  \n", "6         4.05 2025-05-29  1460.0                  1565.44  \n", "7         5.75 2025-05-29  1480.0                  1565.44  \n", "8         8.60 2025-05-29  1500.0                  1565.44  \n", "9        12.70 2025-05-29  1520.0                  1565.44  \n", "10       19.00 2025-05-29  1540.0                  1565.44  \n", "11       27.10 2025-05-29  1560.0                  1565.44  \n", "12       18.90 2025-05-29  1580.0                  1565.44  \n", "13       11.90 2025-05-29  1600.0                  1565.44  \n", "14        7.55 2025-05-29  1620.0                  1565.44  "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head(15)"]}, {"cell_type": "code", "execution_count": 59, "id": "5ff196f7", "metadata": {}, "outputs": [], "source": ["combined_df = combined_df[['instrument_token', 'tradingsymbol', 'expiry', 'strike', 'option_premium', 'total_margin', 'last_price', 'current_avg_stock_price']]"]}, {"cell_type": "markdown", "id": "40f5dadf", "metadata": {}, "source": ["calculating time to expiry"]}, {"cell_type": "code", "execution_count": 60, "id": "52f4381d", "metadata": {}, "outputs": [], "source": ["\n", "from datetime import datetime\n", "\n", "\n", "combined_df['expiry'] = pd.to_datetime(combined_df['expiry'])  \n", "today = pd.to_datetime(datetime.today().date())  \n", "combined_df['time_to_expiry'] = (combined_df['expiry'] - today).dt.days / 365  \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 62, "id": "b0bed9d5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>option_premium</th>\n", "      <th>total_margin</th>\n", "      <th>last_price</th>\n", "      <th>current_avg_stock_price</th>\n", "      <th>time_to_expiry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>27054082</td>\n", "      <td>INFY25MAY1340PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1340.0</td>\n", "      <td>380</td>\n", "      <td>50150.5</td>\n", "      <td>0.95</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>24354306</td>\n", "      <td>INFY25MAY1360PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1360.0</td>\n", "      <td>520</td>\n", "      <td>52983.4</td>\n", "      <td>1.30</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27070722</td>\n", "      <td>INFY25MAY1380PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>640</td>\n", "      <td>55855.6</td>\n", "      <td>1.60</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>24354818</td>\n", "      <td>INFY25MAY1400PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1400.0</td>\n", "      <td>760</td>\n", "      <td>51099.8</td>\n", "      <td>1.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>27071234</td>\n", "      <td>INFY25MAY1420PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1420.0</td>\n", "      <td>1000</td>\n", "      <td>50028.2</td>\n", "      <td>2.50</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>24355330</td>\n", "      <td>INFY25MAY1440PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1440.0</td>\n", "      <td>1200</td>\n", "      <td>56055.2</td>\n", "      <td>3.00</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>27071746</td>\n", "      <td>INFY25MAY1460PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1460.0</td>\n", "      <td>1620</td>\n", "      <td>62505.9</td>\n", "      <td>4.05</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>24355842</td>\n", "      <td>INFY25MAY1480PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1480.0</td>\n", "      <td>2300</td>\n", "      <td>69277.7</td>\n", "      <td>5.75</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>27072258</td>\n", "      <td>INFY25MAY1500PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1500.0</td>\n", "      <td>3440</td>\n", "      <td>76129.6</td>\n", "      <td>8.60</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>24356354</td>\n", "      <td>INFY25MAY1520PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1520.0</td>\n", "      <td>5080</td>\n", "      <td>83171.0</td>\n", "      <td>12.70</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>27074562</td>\n", "      <td>INFY25MAY1540PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1540.0</td>\n", "      <td>7600</td>\n", "      <td>90323.2</td>\n", "      <td>19.00</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>24356866</td>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1560.0</td>\n", "      <td>10840</td>\n", "      <td>97568.6</td>\n", "      <td>27.10</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>27076098</td>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1580.0</td>\n", "      <td>7560</td>\n", "      <td>114889.8</td>\n", "      <td>18.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>24357122</td>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1600.0</td>\n", "      <td>4760</td>\n", "      <td>106679.8</td>\n", "      <td>11.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>27077890</td>\n", "      <td>INFY25MAY1620CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1620.0</td>\n", "      <td>3020</td>\n", "      <td>99046.9</td>\n", "      <td>7.55</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>24357634</td>\n", "      <td>INFY25MAY1640CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1640.0</td>\n", "      <td>1840</td>\n", "      <td>91637.6</td>\n", "      <td>4.60</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>27080962</td>\n", "      <td>INFY25MAY1660CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1660.0</td>\n", "      <td>1180</td>\n", "      <td>84746.5</td>\n", "      <td>2.95</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>24358146</td>\n", "      <td>INFY25MAY1680CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1680.0</td>\n", "      <td>780</td>\n", "      <td>78176.5</td>\n", "      <td>1.95</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>27081986</td>\n", "      <td>INFY25MAY1700CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1700.0</td>\n", "      <td>600</td>\n", "      <td>71906.2</td>\n", "      <td>1.50</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>24358658</td>\n", "      <td>INFY25MAY1720CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1720.0</td>\n", "      <td>360</td>\n", "      <td>65713.8</td>\n", "      <td>0.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    instrument_token    tradingsymbol     expiry  strike  option_premium  \\\n", "0           27054082  INFY25MAY1340PE 2025-05-29  1340.0             380   \n", "1           24354306  INFY25MAY1360PE 2025-05-29  1360.0             520   \n", "2           27070722  INFY25MAY1380PE 2025-05-29  1380.0             640   \n", "3           24354818  INFY25MAY1400PE 2025-05-29  1400.0             760   \n", "4           27071234  INFY25MAY1420PE 2025-05-29  1420.0            1000   \n", "5           24355330  INFY25MAY1440PE 2025-05-29  1440.0            1200   \n", "6           27071746  INFY25MAY1460PE 2025-05-29  1460.0            1620   \n", "7           24355842  INFY25MAY1480PE 2025-05-29  1480.0            2300   \n", "8           27072258  INFY25MAY1500PE 2025-05-29  1500.0            3440   \n", "9           24356354  INFY25MAY1520PE 2025-05-29  1520.0            5080   \n", "10          27074562  INFY25MAY1540PE 2025-05-29  1540.0            7600   \n", "11          24356866  INFY25MAY1560PE 2025-05-29  1560.0           10840   \n", "12          27076098  INFY25MAY1580CE 2025-05-29  1580.0            7560   \n", "13          24357122  INFY25MAY1600CE 2025-05-29  1600.0            4760   \n", "14          27077890  INFY25MAY1620CE 2025-05-29  1620.0            3020   \n", "15          24357634  INFY25MAY1640CE 2025-05-29  1640.0            1840   \n", "16          27080962  INFY25MAY1660CE 2025-05-29  1660.0            1180   \n", "17          24358146  INFY25MAY1680CE 2025-05-29  1680.0             780   \n", "18          27081986  INFY25MAY1700CE 2025-05-29  1700.0             600   \n", "19          24358658  INFY25MAY1720CE 2025-05-29  1720.0             360   \n", "\n", "    total_margin  last_price  current_avg_stock_price  time_to_expiry  \n", "0        50150.5        0.95                  1565.44        0.027397  \n", "1        52983.4        1.30                  1565.44        0.027397  \n", "2        55855.6        1.60                  1565.44        0.027397  \n", "3        51099.8        1.90                  1565.44        0.027397  \n", "4        50028.2        2.50                  1565.44        0.027397  \n", "5        56055.2        3.00                  1565.44        0.027397  \n", "6        62505.9        4.05                  1565.44        0.027397  \n", "7        69277.7        5.75                  1565.44        0.027397  \n", "8        76129.6        8.60                  1565.44        0.027397  \n", "9        83171.0       12.70                  1565.44        0.027397  \n", "10       90323.2       19.00                  1565.44        0.027397  \n", "11       97568.6       27.10                  1565.44        0.027397  \n", "12      114889.8       18.90                  1565.44        0.027397  \n", "13      106679.8       11.90                  1565.44        0.027397  \n", "14       99046.9        7.55                  1565.44        0.027397  \n", "15       91637.6        4.60                  1565.44        0.027397  \n", "16       84746.5        2.95                  1565.44        0.027397  \n", "17       78176.5        1.95                  1565.44        0.027397  \n", "18       71906.2        1.50                  1565.44        0.027397  \n", "19       65713.8        0.90                  1565.44        0.027397  "]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head(20)"]}, {"cell_type": "code", "execution_count": 63, "id": "9f3167c5", "metadata": {}, "outputs": [], "source": ["combined_df['option_type'] = ['p' if i <= 11 else 'c' for i in combined_df.index]"]}, {"cell_type": "code", "execution_count": 64, "id": "0fc9656e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>option_premium</th>\n", "      <th>total_margin</th>\n", "      <th>last_price</th>\n", "      <th>current_avg_stock_price</th>\n", "      <th>time_to_expiry</th>\n", "      <th>option_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>27054082</td>\n", "      <td>INFY25MAY1340PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1340.0</td>\n", "      <td>380</td>\n", "      <td>50150.5</td>\n", "      <td>0.95</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>24354306</td>\n", "      <td>INFY25MAY1360PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1360.0</td>\n", "      <td>520</td>\n", "      <td>52983.4</td>\n", "      <td>1.30</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27070722</td>\n", "      <td>INFY25MAY1380PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>640</td>\n", "      <td>55855.6</td>\n", "      <td>1.60</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>24354818</td>\n", "      <td>INFY25MAY1400PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1400.0</td>\n", "      <td>760</td>\n", "      <td>51099.8</td>\n", "      <td>1.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>27071234</td>\n", "      <td>INFY25MAY1420PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1420.0</td>\n", "      <td>1000</td>\n", "      <td>50028.2</td>\n", "      <td>2.50</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token    tradingsymbol     expiry  strike  option_premium  \\\n", "0          27054082  INFY25MAY1340PE 2025-05-29  1340.0             380   \n", "1          24354306  INFY25MAY1360PE 2025-05-29  1360.0             520   \n", "2          27070722  INFY25MAY1380PE 2025-05-29  1380.0             640   \n", "3          24354818  INFY25MAY1400PE 2025-05-29  1400.0             760   \n", "4          27071234  INFY25MAY1420PE 2025-05-29  1420.0            1000   \n", "\n", "   total_margin  last_price  current_avg_stock_price  time_to_expiry  \\\n", "0       50150.5        0.95                  1565.44        0.027397   \n", "1       52983.4        1.30                  1565.44        0.027397   \n", "2       55855.6        1.60                  1565.44        0.027397   \n", "3       51099.8        1.90                  1565.44        0.027397   \n", "4       50028.2        2.50                  1565.44        0.027397   \n", "\n", "  option_type  \n", "0           p  \n", "1           p  \n", "2           p  \n", "3           p  \n", "4           p  "]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head()"]}, {"cell_type": "code", "execution_count": 65, "id": "69da10e5", "metadata": {}, "outputs": [], "source": ["from py_vollib.black_scholes import implied_volatility\n", "from py_vollib.black_scholes import black_scholes as bs"]}, {"cell_type": "code", "execution_count": 68, "id": "ea2a656d", "metadata": {}, "outputs": [], "source": ["# Risk-free rate\n", "risk_free_rate = 0.0625  # 6.25%\n", "\n", "# Function to calculate implied volatility\n", "def calculate_implied_volatility(row):\n", "    try:\n", "        option_type = row[\"option_type\"]\n", "        premium = row[\"last_price\"]\n", "        stock_price = row[\"current_avg_stock_price\"]  # Use row-specific stock price\n", "        strike = row[\"strike\"]\n", "        time_to_expiry = row[\"time_to_expiry\"]  # Use row-specific time to expiry\n", "        \n", "        # Calculate IV using py_vollib\n", "        iv = implied_volatility.implied_volatility(\n", "            price=premium,\n", "            S=stock_price,\n", "            K=strike,\n", "            t=time_to_expiry,\n", "            r=risk_free_rate,\n", "            flag=option_type\n", "        )\n", "        return iv * 100  \n", "    except Exception as e:\n", "        print(f\"Error calculating IV for {row['tradingsymbol']}: {e}\")\n", "        return None\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 69, "id": "afa37015", "metadata": {}, "outputs": [], "source": ["# Apply the function to each row\n", "combined_df[\"implied_volatility\"] = combined_df.apply(calculate_implied_volatility, axis=1)"]}, {"cell_type": "code", "execution_count": 70, "id": "56f27c42", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>option_premium</th>\n", "      <th>total_margin</th>\n", "      <th>last_price</th>\n", "      <th>current_avg_stock_price</th>\n", "      <th>time_to_expiry</th>\n", "      <th>option_type</th>\n", "      <th>implied_volatility</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>27054082</td>\n", "      <td>INFY25MAY1340PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1340.0</td>\n", "      <td>380</td>\n", "      <td>50150.5</td>\n", "      <td>0.95</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>47.370887</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>24354306</td>\n", "      <td>INFY25MAY1360PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1360.0</td>\n", "      <td>520</td>\n", "      <td>52983.4</td>\n", "      <td>1.30</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>45.851721</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27070722</td>\n", "      <td>INFY25MAY1380PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>640</td>\n", "      <td>55855.6</td>\n", "      <td>1.60</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>43.466533</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>24354818</td>\n", "      <td>INFY25MAY1400PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1400.0</td>\n", "      <td>760</td>\n", "      <td>51099.8</td>\n", "      <td>1.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>40.715076</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>27071234</td>\n", "      <td>INFY25MAY1420PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1420.0</td>\n", "      <td>1000</td>\n", "      <td>50028.2</td>\n", "      <td>2.50</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>38.707249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>24355330</td>\n", "      <td>INFY25MAY1440PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1440.0</td>\n", "      <td>1200</td>\n", "      <td>56055.2</td>\n", "      <td>3.00</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>35.821614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>27071746</td>\n", "      <td>INFY25MAY1460PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1460.0</td>\n", "      <td>1620</td>\n", "      <td>62505.9</td>\n", "      <td>4.05</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>33.826212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>24355842</td>\n", "      <td>INFY25MAY1480PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1480.0</td>\n", "      <td>2300</td>\n", "      <td>69277.7</td>\n", "      <td>5.75</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>32.243486</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>27072258</td>\n", "      <td>INFY25MAY1500PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1500.0</td>\n", "      <td>3440</td>\n", "      <td>76129.6</td>\n", "      <td>8.60</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>31.288889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>24356354</td>\n", "      <td>INFY25MAY1520PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1520.0</td>\n", "      <td>5080</td>\n", "      <td>83171.0</td>\n", "      <td>12.70</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>30.435327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>27074562</td>\n", "      <td>INFY25MAY1540PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1540.0</td>\n", "      <td>7600</td>\n", "      <td>90323.2</td>\n", "      <td>19.00</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>30.301617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>24356866</td>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1560.0</td>\n", "      <td>10840</td>\n", "      <td>97568.6</td>\n", "      <td>27.10</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>p</td>\n", "      <td>30.056199</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>27076098</td>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1580.0</td>\n", "      <td>7560</td>\n", "      <td>114889.8</td>\n", "      <td>18.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>c</td>\n", "      <td>23.488628</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>24357122</td>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1600.0</td>\n", "      <td>4760</td>\n", "      <td>106679.8</td>\n", "      <td>11.90</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>c</td>\n", "      <td>23.567384</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>27077890</td>\n", "      <td>INFY25MAY1620CE</td>\n", "      <td>2025-05-29</td>\n", "      <td>1620.0</td>\n", "      <td>3020</td>\n", "      <td>99046.9</td>\n", "      <td>7.55</td>\n", "      <td>1565.44</td>\n", "      <td>0.027397</td>\n", "      <td>c</td>\n", "      <td>24.275860</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    instrument_token    tradingsymbol     expiry  strike  option_premium  \\\n", "0           27054082  INFY25MAY1340PE 2025-05-29  1340.0             380   \n", "1           24354306  INFY25MAY1360PE 2025-05-29  1360.0             520   \n", "2           27070722  INFY25MAY1380PE 2025-05-29  1380.0             640   \n", "3           24354818  INFY25MAY1400PE 2025-05-29  1400.0             760   \n", "4           27071234  INFY25MAY1420PE 2025-05-29  1420.0            1000   \n", "5           24355330  INFY25MAY1440PE 2025-05-29  1440.0            1200   \n", "6           27071746  INFY25MAY1460PE 2025-05-29  1460.0            1620   \n", "7           24355842  INFY25MAY1480PE 2025-05-29  1480.0            2300   \n", "8           27072258  INFY25MAY1500PE 2025-05-29  1500.0            3440   \n", "9           24356354  INFY25MAY1520PE 2025-05-29  1520.0            5080   \n", "10          27074562  INFY25MAY1540PE 2025-05-29  1540.0            7600   \n", "11          24356866  INFY25MAY1560PE 2025-05-29  1560.0           10840   \n", "12          27076098  INFY25MAY1580CE 2025-05-29  1580.0            7560   \n", "13          24357122  INFY25MAY1600CE 2025-05-29  1600.0            4760   \n", "14          27077890  INFY25MAY1620CE 2025-05-29  1620.0            3020   \n", "\n", "    total_margin  last_price  current_avg_stock_price  time_to_expiry  \\\n", "0        50150.5        0.95                  1565.44        0.027397   \n", "1        52983.4        1.30                  1565.44        0.027397   \n", "2        55855.6        1.60                  1565.44        0.027397   \n", "3        51099.8        1.90                  1565.44        0.027397   \n", "4        50028.2        2.50                  1565.44        0.027397   \n", "5        56055.2        3.00                  1565.44        0.027397   \n", "6        62505.9        4.05                  1565.44        0.027397   \n", "7        69277.7        5.75                  1565.44        0.027397   \n", "8        76129.6        8.60                  1565.44        0.027397   \n", "9        83171.0       12.70                  1565.44        0.027397   \n", "10       90323.2       19.00                  1565.44        0.027397   \n", "11       97568.6       27.10                  1565.44        0.027397   \n", "12      114889.8       18.90                  1565.44        0.027397   \n", "13      106679.8       11.90                  1565.44        0.027397   \n", "14       99046.9        7.55                  1565.44        0.027397   \n", "\n", "   option_type  implied_volatility  \n", "0            p           47.370887  \n", "1            p           45.851721  \n", "2            p           43.466533  \n", "3            p           40.715076  \n", "4            p           38.707249  \n", "5            p           35.821614  \n", "6            p           33.826212  \n", "7            p           32.243486  \n", "8            p           31.288889  \n", "9            p           30.435327  \n", "10           p           30.301617  \n", "11           p           30.056199  \n", "12           c           23.488628  \n", "13           c           23.567384  \n", "14           c           24.275860  "]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head(15)"]}, {"cell_type": "code", "execution_count": 71, "id": "5d705135", "metadata": {}, "outputs": [], "source": ["combined_df.to_excel('options_sheet.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "32520b56", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}