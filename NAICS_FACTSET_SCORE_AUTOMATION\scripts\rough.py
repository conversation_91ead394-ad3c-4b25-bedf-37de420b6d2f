{"cells": [{"cell_type": "code", "execution_count": null, "id": "a467b8be", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name '__file__' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m Path\n\u001b[1;32m----> 2\u001b[0m project_root \u001b[38;5;241m=\u001b[39m Path(\u001b[38;5;18;43m__file__\u001b[39;49m)\u001b[38;5;241m.\u001b[39mresolve()\u001b[38;5;241m.\u001b[39mparent\u001b[38;5;241m.\u001b[39mparent\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(project_root)\n", "\u001b[1;31mNameError\u001b[0m: name '__file__' is not defined"]}], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}