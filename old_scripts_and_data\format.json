{"format": {"type": "json_schema", "name": "company_scores", "schema": {"type": "object", "properties": {"companies": {"type": "array", "items": {"type": "object", "properties": {"company": {"type": "string", "description": "Name of the company being evaluated."}, "scores": {"type": "object", "properties": {"capacity_expansion_score": {"type": "number", "description": "Score (0–10) based on magnitude of capacity expansion or new plant opening."}, "employment_generation_score": {"type": "number", "description": "Score (0–10) based on the scale of employment generation or hiring."}}, "required": ["capacity_expansion_score", "employment_generation_score"], "additionalProperties": false}, "capacity_expansion_rationale": {"type": "string", "description": "Explanation of how the capacity expansion score was determined, referencing plan details and confidence scores."}, "employment_generation_rationale": {"type": "string", "description": "Explanation of how the employment generation score was determined, referencing plan details and confidence scores."}}, "required": ["company", "scores", "capacity_expansion_rationale", "employment_generation_rationale"], "additionalProperties": false}}}, "required": ["companies"], "additionalProperties": false}}}