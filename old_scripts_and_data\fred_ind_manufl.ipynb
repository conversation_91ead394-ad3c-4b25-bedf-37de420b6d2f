{"cells": [{"cell_type": "code", "execution_count": 15, "id": "a90b08f4", "metadata": {}, "outputs": [], "source": ["fred_api_key = \"51c0e7ec1da836c20ce6d40a9e50f5d1\""]}, {"cell_type": "markdown", "id": "b6b2a3dd", "metadata": {}, "source": ["# Getting Series IDs for Industrial production"]}, {"cell_type": "code", "execution_count": 16, "id": "d2f233c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total series retrieved: 0\n"]}], "source": ["import requests\n", "\n", "API_KEY = fred_api_key\n", "SEARCH_TEXT = 'IPG311'\n", "BASE_URL = 'https://api.stlouisfed.org/fred/series/search'\n", "LIMIT = 1000\n", "offset = 5000\n", "all_series = []\n", "\n", "while True:\n", "    params = {\n", "        'search_text': SEARCH_TEXT,\n", "        'api_key': API_KEY,\n", "        'file_type': 'json',\n", "        'limit': LIMIT,\n", "        'offset': offset\n", "    }\n", "    response = requests.get(BASE_URL, params=params)\n", "    data = response.json()\n", "    series = data.get('seriess', [])\n", "    if not series:\n", "        break\n", "    all_series.extend(series)\n", "    offset += LIMIT\n", "\n", "print(f\"Total series retrieved: {len(all_series)}\")\n"]}, {"cell_type": "code", "execution_count": 12, "id": "e828e4fa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>realtime_start</th>\n", "      <th>realtime_end</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "      <th>notes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INDPRO</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Total Index</td>\n", "      <td>1919-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:27:54-05</td>\n", "      <td>79</td>\n", "      <td>80</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>IPB50001N</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Total Index</td>\n", "      <td>1919-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2025-04-16 08:28:20-05</td>\n", "      <td>39</td>\n", "      <td>80</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>IPB50001SQ</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Total Index</td>\n", "      <td>1919-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Q</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:28:32-05</td>\n", "      <td>24</td>\n", "      <td>80</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>IPB50001NQ</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Total Index</td>\n", "      <td>1919-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Q</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2025-04-16 08:28:27-05</td>\n", "      <td>8</td>\n", "      <td>80</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>IPB50001A</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Total Index</td>\n", "      <td>1919-01-01</td>\n", "      <td>2024-01-01</td>\n", "      <td>Annual</td>\n", "      <td>A</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2025-04-16 08:28:14-05</td>\n", "      <td>7</td>\n", "      <td>80</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           id realtime_start realtime_end                               title  \\\n", "0      INDPRO     2025-05-08   2025-05-08  Industrial Production: Total Index   \n", "1   IPB50001N     2025-05-08   2025-05-08  Industrial Production: Total Index   \n", "2  IPB50001SQ     2025-05-08   2025-05-08  Industrial Production: Total Index   \n", "3  IPB50001NQ     2025-05-08   2025-05-08  Industrial Production: Total Index   \n", "4   IPB50001A     2025-05-08   2025-05-08  Industrial Production: Total Index   \n", "\n", "  observation_start observation_end  frequency frequency_short  \\\n", "0        1919-01-01      2025-03-01    Monthly               M   \n", "1        1919-01-01      2025-03-01    Monthly               M   \n", "2        1919-01-01      2025-01-01  Quarterly               Q   \n", "3        1919-01-01      2025-01-01  Quarterly               Q   \n", "4        1919-01-01      2024-01-01     Annual               A   \n", "\n", "            units     units_short      seasonal_adjustment  \\\n", "0  Index 2017=100  Index 2017=100      Seasonally Adjusted   \n", "1  Index 2017=100  Index 2017=100  Not Seasonally Adjusted   \n", "2  Index 2017=100  Index 2017=100      Seasonally Adjusted   \n", "3  Index 2017=100  Index 2017=100  Not Seasonally Adjusted   \n", "4  Index 2017=100  Index 2017=100  Not Seasonally Adjusted   \n", "\n", "  seasonal_adjustment_short            last_updated  popularity  \\\n", "0                        SA  2025-04-16 08:27:54-05          79   \n", "1                       NSA  2025-04-16 08:28:20-05          39   \n", "2                        SA  2025-04-16 08:28:32-05          24   \n", "3                       NSA  2025-04-16 08:28:27-05           8   \n", "4                       NSA  2025-04-16 08:28:14-05           7   \n", "\n", "   group_popularity                                              notes  \n", "0                80  explanatory notes (https://www.federalreserve....  \n", "1                80  explanatory notes (https://www.federalreserve....  \n", "2                80  explanatory notes (https://www.federalreserve....  \n", "3                80  explanatory notes (https://www.federalreserve....  \n", "4                80  explanatory notes (https://www.federalreserve....  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "series_df = pd.DataFrame(all_series)\n", "series_df.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "cf603f08", "metadata": {}, "outputs": [], "source": ["series_df.to_excel(\"fred_search_results.xlsx\", index=False)"]}, {"cell_type": "markdown", "id": "9bb651d4", "metadata": {}, "source": ["## filtering Industrial production manufacturing data"]}, {"cell_type": "code", "execution_count": 5, "id": "e452ac03", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>realtime_start</th>\n", "      <th>realtime_end</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "      <th>notes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>IPMAN</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:24:14-05</td>\n", "      <td>61</td>\n", "      <td>62</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>IPGMFN</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2025-04-16 08:22:21-05</td>\n", "      <td>23</td>\n", "      <td>62</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>IPGMFSQ</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Q</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:23:30-05</td>\n", "      <td>5</td>\n", "      <td>62</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>IPGMFNQ</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Q</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2025-04-16 08:22:54-05</td>\n", "      <td>5</td>\n", "      <td>62</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>IPGMFA</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2024-01-01</td>\n", "      <td>Annual</td>\n", "      <td>A</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2025-04-16 08:22:01-05</td>\n", "      <td>3</td>\n", "      <td>62</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         id realtime_start realtime_end  \\\n", "7     IPMAN     2025-05-08   2025-05-08   \n", "9    IPGMFN     2025-05-08   2025-05-08   \n", "10  IPGMFSQ     2025-05-08   2025-05-08   \n", "11  IPGMFNQ     2025-05-08   2025-05-08   \n", "12   IPGMFA     2025-05-08   2025-05-08   \n", "\n", "                                           title observation_start  \\\n", "7   Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "9   Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "10  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "11  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "12  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "\n", "   observation_end  frequency frequency_short           units     units_short  \\\n", "7       2025-03-01    Monthly               M  Index 2017=100  Index 2017=100   \n", "9       2025-03-01    Monthly               M  Index 2017=100  Index 2017=100   \n", "10      2025-01-01  Quarterly               Q  Index 2017=100  Index 2017=100   \n", "11      2025-01-01  Quarterly               Q  Index 2017=100  Index 2017=100   \n", "12      2024-01-01     Annual               A  Index 2017=100  Index 2017=100   \n", "\n", "        seasonal_adjustment seasonal_adjustment_short            last_updated  \\\n", "7       Seasonally Adjusted                        SA  2025-04-16 08:24:14-05   \n", "9   Not Seasonally Adjusted                       NSA  2025-04-16 08:22:21-05   \n", "10      Seasonally Adjusted                        SA  2025-04-16 08:23:30-05   \n", "11  Not Seasonally Adjusted                       NSA  2025-04-16 08:22:54-05   \n", "12  Not Seasonally Adjusted                       NSA  2025-04-16 08:22:01-05   \n", "\n", "    popularity  group_popularity  \\\n", "7           61                62   \n", "9           23                62   \n", "10           5                62   \n", "11           5                62   \n", "12           3                62   \n", "\n", "                                                notes  \n", "7   explanatory notes (https://www.federalreserve....  \n", "9   explanatory notes (https://www.federalreserve....  \n", "10  explanatory notes (https://www.federalreserve....  \n", "11  explanatory notes (https://www.federalreserve....  \n", "12  explanatory notes (https://www.federalreserve....  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["prefix_to_filter = \"Industrial Production: Manufacturing\"\n", "starts_with_prefix_mask = series_df['title'].astype(str).str.startswith(prefix_to_filter)\n", "filtered_df = series_df[starts_with_prefix_mask]\n", "filtered_df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "b9112300", "metadata": {}, "outputs": [{"data": {"text/plain": ["40"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(filtered_df)"]}, {"cell_type": "code", "execution_count": 7, "id": "ffad26d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(40, 7)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_columns = ['id', 'title', 'observation_start', 'observation_end', 'frequency', 'units', 'seasonal_adjustment']\n", "manuf_df = filtered_df[selected_columns].copy()\n", "\n", "manuf_df.head()\n", "\n", "manuf_df.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "bb202423", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>units</th>\n", "      <th>seasonal_adjustment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>IPMAN</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Seasonally Adjusted</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>IPGMFN</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>IPGMFSQ</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Seasonally Adjusted</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>IPGMFNQ</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>IPGMFA</td>\n", "      <td>Industrial Production: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2024-01-01</td>\n", "      <td>Annual</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         id                                         title observation_start  \\\n", "7     IPMAN  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "9    IPGMFN  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "10  IPGMFSQ  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "11  IPGMFNQ  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "12   IPGMFA  Industrial Production: Manufacturing (NAICS)        1972-01-01   \n", "\n", "   observation_end  frequency           units      seasonal_adjustment  \n", "7       2025-03-01    Monthly  Index 2017=100      Seasonally Adjusted  \n", "9       2025-03-01    Monthly  Index 2017=100  Not Seasonally Adjusted  \n", "10      2025-01-01  Quarterly  Index 2017=100      Seasonally Adjusted  \n", "11      2025-01-01  Quarterly  Index 2017=100  Not Seasonally Adjusted  \n", "12      2024-01-01     Annual  Index 2017=100  Not Seasonally Adjusted  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["manuf_df.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "f234002c", "metadata": {}, "outputs": [], "source": ["manuf_df.to_excel(\"fred_search_results_manuf_new.xlsx\", index=False)"]}, {"cell_type": "markdown", "id": "b71330de", "metadata": {}, "source": [" manual adjustment"]}, {"cell_type": "code", "execution_count": null, "id": "53ca6f58", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}