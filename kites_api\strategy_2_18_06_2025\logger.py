import logging
import os
import sys


LOG_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_FILE = os.path.join(LOG_DIR, 'trading_app.log')

def setup_logger():
    """
    Sets up a centralized logger for the entire application.
    
    Returns:
        logging.Logger: A configured logger instance.
    """
    logger = logging.getLogger("trading_strategy_app")
    
    # Prevents adding handlers multiple times if the function is called again.
    if logger.hasHandlers():
        return logger

    logger.setLevel(logging.INFO)

    # --- Create Handlers ---
    

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    file_handler = logging.FileHandler(LOG_FILE, mode='a') 
    file_handler.setLevel(logging.INFO)
    
    # --- Create Formatter ---
    # Defines the format of the log messages.
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Set the formatter for both handlers
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    
    # --- Add Handlers to the Logger ---
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger

# Create a single logger instance to be imported by other modules
log = setup_logger()
