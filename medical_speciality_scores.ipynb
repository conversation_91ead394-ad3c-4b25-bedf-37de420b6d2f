{"cells": [{"cell_type": "markdown", "id": "fff1aa8c", "metadata": {}, "source": ["```\n", "1. Date - 16/05/2025\n", "Adding employment details in sec data retrieval prompt.\n", "2. Date - 20-05-2025\n", "Retrieving production data.\n", "3. Date - 28-05-2025\n", "Using for medical speciality companies\n", "```"]}, {"cell_type": "code", "execution_count": 2, "id": "70ee55d1", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 8, "id": "2570c75f", "metadata": {}, "outputs": [], "source": ["api_key = \"********************************************************************************************************************************************************************\""]}, {"cell_type": "code", "execution_count": 9, "id": "09c0703e", "metadata": {}, "outputs": [], "source": ["client = OpenAI(api_key=api_key)"]}, {"cell_type": "markdown", "id": "ae288479", "metadata": {}, "source": ["Giving average confidence score as 0.9 for sec prompt\n"]}, {"cell_type": "markdown", "id": "f7b581ff", "metadata": {}, "source": ["## SEC data retreival"]}, {"cell_type": "code", "execution_count": 10, "id": "4f4e4906", "metadata": {}, "outputs": [], "source": ["sec_data_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You need to extract all the information related to following fields from the SEC filings of the {company_name} and return the output in defined JSON format:\n", "1. Plans regarding manufacturing capacity expansion or new plant openings in the United States as per the SEC filings of the {company_name} from January 1, 2024, through today.\n", "\n", "2. Employment generation or hiring commitments of the {company_name} in the United States as per it's SEC filings from January 1, 2024, through today.\n", "\n", "3. Actual production output (units or volumes produced) per quarter of the {company_name} in the United States as reported in it's SEC filings from January 1, 2024, through today.\n", "\n", "While answering, please adhere strictly to these rules:\n", "\n", "1. Web search:\n", "   - Search only the Official SEC filings (10-K/10-Q/8-K) of the target company to gather authoritative sources.\n", "   - Do not search any other source apart from the official SEC filings.\n", "\n", "2. Plan Identification:\n", "   2a. Identify Capacity-Expansion Plans: \n", "         - Locate statements or paragraphs in the SEC filings describing intentions, plans, or commitments to:\n", "            - Expand existing factories in the United States\n", "            - Build or commission new manufacturing facilities in the United States.\n", "         - Quantify Every Plan:\n", "            - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the SEC filings. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "   2b. Identify Employment-Generation Plans:  \n", "         - Locate any statements in the SEC filings describing hiring targets, job-creation commitments, or intended workforce increases in the United States.  \n", "         - Quantify by citing numeric metrics if available in the SEC filings.(e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "   2c. Identify Actual Production Output:\n", "         - Locate quarterly production output metrics in the United States, disclosed in the SEC filings from January 1, 2024, through today.\n", "         - Extract details such as number of units produced, volume of goods manufactured, or any other production quantity disclosed on a per-quarter basis.\n", "\n", "3. Time-<PERSON><PERSON>:\n", "   - Include only those sources dated on or after January 1, 2024, up to the current date.\n", "   - Exclude any sources from earlier periods.\n", "\n", "4. Output Structure:\n", "   - Present your findings in JSON format with following fields:\n", "     - `company`: name of the target company.\n", "     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, publishing_agency, title, section, url).\n", "     - `employment_generation_plans`: list of objects with `plan_details` regarding employment generation, `source` (publishing_date, publishing_agency, title, section, url).\n", "     - `production_output`: list of objects with `output_details` (e.g., units/volume produced), `source` (publishing_date, publishing_agency, title, section, url).\n", "     - `summary`: a concise overview of `plan_details`from both `capacity_expansion_plans` and `employment_generation_plans`, and  `output_details` from `production_output`.\n", "   - Order `plan_details` chronologically (earliest → latest).\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 12, "id": "93251a32", "metadata": {}, "outputs": [], "source": ["output_format_sec = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"company\":{\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Name of the company about which the information is being asked by the user.\"\n", "                },\n", "                \"capacity_expansion_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Manufacturing capacity expansion or new plant opening plan details of the company in the United States as per the SEC filings (10-K/10-Q/8-K).\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the SEC filing (10-K/10-Q/8-K) was filed by the company (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the publishing agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the SEC filing (10-K/10-Q/8-K)\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the SEC filing where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"employment_generation_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Employment generation plan details of the company in the United States as per the SEC filings (10-K/10-Q/8-K).\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the SEC filing was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the publishing agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the SEC filing (10-K/10-Q/8-K)\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the SEC filing where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"production_output\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"output_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Actual production output (units or volumes produced) per quarter of the company in the United States as reported in SEC filings.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the SEC filing was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the publishing agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the SEC filing (10-K/10-Q/8-K)\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the SEC filing where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"output_details\",\n", "                            \"source\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"summary\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Short overview of all plan details and output details.\"\n", "                }\n", "            },\n", "            \"required\": [\"company\",\"capacity_expansion_plans\",\"employment_generation_plans\", \"production_output\",\n", "                \"summary\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "markdown", "id": "a55d010b", "metadata": {}, "source": ["## company press release, news articles and other relevant websites."]}, {"cell_type": "code", "execution_count": 13, "id": "4abd9102", "metadata": {}, "outputs": [], "source": ["user_prompt_other_sources = \"\"\"\\\n", "You are a research assistant with access to web-search.\n", "The company of interest is {company_name}. You need to extract all information related to following fields from relevant web sources and return the output in defined JSON format:\n", "1. Plans regarding manufacturing capacity expansion or new plant openings of the {company_name} in the United States from relevant web sources, covering the period from January 1, 2024, to the present.\n", "\n", "2. Employment generation or hiring commitments of the {company_name} in the United States from relevant web sources, covering the period from January 1, 2024, to the present.\n", "\n", "3. Actual production output (units or volumes produced) per quarter of the {company_name} in the United States as per the relevant web sources, covering the period from January 1, 2024, to the present.\n", " \n", "When responding, adhere strictly to these rules:\n", "\n", "1. Web search: \n", "   - Search the company's press releases, relevant websites, news articles etc. to gather authoritative sources.\n", "\n", "2. Plan Identification:\n", "   2a. Identify Capacity-Expansion Plans: \n", "         - Locate statements or paragraphs describing intentions, plans, or commitments to:\n", "            - Expand existing factories in the United States.\n", "            - Build or commission new manufacturing facilities in the United States.\n", "         - Quantify Every Plan:\n", "            - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "   2b. Identify Employment-Generation Plans:  \n", "         - Locate any statements in the sources describing hiring targets, job-creation commitments, or intended workforce increases in the United States.  \n", "         - Quantify by citing numeric metrics (e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "   2c. Identify Actual Production Output:\n", "         - Locate reported or disclosed production output metrics by the company per quarter in the United States, covering the period from January 1, 2024, to the present.\n", "         - Extract information such as number of units produced, volume of goods manufactured, or other quarterly production quantity data disclosed in press releases, earnings calls or news articles etc.\n", "\n", "4. Time-<PERSON><PERSON>: \n", "   - Include only sources dated on or after 2024-01-01, up to today's date.  \n", "   - Exclude any sources published before 2024-01-01.\n", "\n", "5. Restrict to Company's Own Data: \n", "   - Only include target company's own plans to increase its manufacturing capacity or opening a new plant, employment generation plans and production output.\\\n", "     Do not include any supplier, vendor, or third‑party manufacturing partner plans or information.\n", "\n", "6. Output Structure:\n", "   - Return results in JSON with these fields:\n", "     - `company`: name of the target company.\n", "     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, publishing_agency, title, section, url), and `confidence_score`.\n", "     - `employment_generation_plans`: list of objects with `plan_details` regarding employment generation, `source` (publishing_date, publishing_agency, title, section, url), and `confidence_score`.  \n", "     - `production_output`: list of objects with `output_details` (e.g., units/volume produced), `source` (publishing_date, publishing_agency, title, section, url) and `confidence_score`.\n", "     - `summary`: a concise overview of `plan_details`from both `capacity_expansion_plans` and `employment_generation_plans`, and `output_details` from `production_output`.\n", "   - Order `plan_details` chronologically (earliest → latest).\n", "\n", "7. Confidence Score Calculation: \n", "   - A score between 0 and 0.9 suggesting confidence in the information given in plan details and source, calculated as follows:  \n", "      - Source Reliability (0.0–0.4):  \n", "         0.4=Company's official press release; 0.3=Tier-1 industry press; 0.2=Blog/aggregator; 0.0=Unverified forum/social media  \n", "      - Specificity Score(0.0–0.5):  \n", "         0.5=Direct management quote with numeric metric; 0.4=Exact excerpt paraphrased; 0.3=Summary statement without numbers; 0.2=Analyst commentary; 0.0= pure Speculation  \n", "      - `confidence_score = Source Reliability + Specificity Score`\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 14, "id": "61301811", "metadata": {}, "outputs": [], "source": ["output_format_other_sources = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"company\":{\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Name of the company about which the information is being asked by the user.\"\n", "                },\n", "                \"capacity_expansion_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Capacity expansion or new plant opening plan details of the company under consideration in the United States as per the found sources.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the source was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the document or article\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"confidence_score\": {\n", "                                \"type\": \"number\",\n", "                                \"description\": \"A score between 0 and 0.9 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\",\n", "                            \"confidence_score\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"employment_generation_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Employment generation plan details of the company under consideration in the United States as per the found sources.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the source was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the document or article\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"confidence_score\": {\n", "                                \"type\": \"number\",\n", "                                \"description\": \"A score between 0 and 1 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\",\n", "                            \"confidence_score\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"production_output\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"output_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Actual production output (units or volumes produced) per quarter of the company in the United States as reported in relevant web sources.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the source was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the document or article\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"confidence_score\": {\n", "                                \"type\": \"number\",\n", "                                \"description\": \"A score between 0 and 1 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"output_details\",\n", "                            \"source\",\n", "                            \"confidence_score\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"summary\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Short overview of all plan details and production output details.\"\n", "                }\n", "            },\n", "            \"required\": [\"company\",\"capacity_expansion_plans\",\"employment_generation_plans\", \"production_output\",\n", "                \"summary\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "markdown", "id": "6f360bb3", "metadata": {}, "source": ["## retrieving data for all the companies"]}, {"cell_type": "code", "execution_count": 15, "id": "70bac86f", "metadata": {}, "outputs": [], "source": ["def all_company_details(company_names:list[str]) -> tuple[list[dict], float, float, float]:\n", "    company_results = []\n", "    INPUT_COST_PER_TOKEN = 2.00 / 1_000_000\n", "    OUTPUT_COST_PER_TOKEN = 8.00 / 1_000_000\n", "    WEB_SEARCH_COST_PER_CALL = 50.00 / 1_000  \n", "    sec_cost = 0\n", "    other_cost = 0\n", "    for company_name in company_names:\n", "        # SEC data retrieval\n", "        response_sec = client.responses.create(\n", "            model=\"gpt-4.1\",\n", "            tools=[{\n", "                    \"type\": \"web_search_preview\",\n", "                    \"search_context_size\": \"high\",\n", "                }],\n", "            input=sec_data_retrieval_prompt.format(company_name = company_name),\n", "            text=output_format_sec\n", "        )\n", "        # Cost calculation\n", "        input_tokens_sec = response_sec.usage.input_tokens\n", "        output_tokens_sec = response_sec.usage.output_tokens\n", "        token_cost_sec = (input_tokens_sec * INPUT_COST_PER_TOKEN) + (output_tokens_sec * OUTPUT_COST_PER_TOKEN)\n", "        total_cost_sec = token_cost_sec + WEB_SEARCH_COST_PER_CALL\n", "        sec_cost += total_cost_sec\n", "\n", "        data_sec = json.loads(response_sec.output_text)\n", "        # Assinging confidence score of 0.9\n", "        for plan in data_sec[\"capacity_expansion_plans\"]:\n", "            plan[\"confidence_score\"] = 0.9\n", "\n", "        for plan in data_sec[\"employment_generation_plans\"]:\n", "            plan[\"confidence_score\"] = 0.9\n", "\n", "        for output in data_sec[\"production_output\"]:\n", "            output[\"confidence_score\"] = 0.9\n", "        \n", "        # Other sources data retrieval\n", "        response_other = client.responses.create(\n", "            model=\"gpt-4.1\",\n", "            tools=[{\n", "                    \"type\": \"web_search_preview\",\n", "                    \"search_context_size\": \"high\",\n", "            }],\n", "            input=user_prompt_other_sources.format(company_name = company_name),\n", "            text=output_format_other_sources\n", "        )\n", "        # Cost calculation\n", "        input_tokens_other = response_other.usage.input_tokens\n", "        output_tokens_other = response_other.usage.output_tokens\n", "        token_cost_other = (input_tokens_other * INPUT_COST_PER_TOKEN) + (output_tokens_other * OUTPUT_COST_PER_TOKEN)\n", "        total_cost_other = token_cost_other + WEB_SEARCH_COST_PER_CALL\n", "        other_cost += total_cost_other\n", "\n", "        data_other = json.loads(response_other.output_text)\n", "        # appending other sources data to SEC data to make final data\n", "        for plan in data_other[\"capacity_expansion_plans\"]:\n", "            data_sec[\"capacity_expansion_plans\"].append(plan)\n", "\n", "        for plan in data_other[\"employment_generation_plans\"]:\n", "            data_sec[\"employment_generation_plans\"].append(plan)\n", "\n", "        for output in data_other[\"production_output\"]:\n", "            data_sec[\"production_output\"].append(output)\n", "\n", "        data_sec[\"summary\"]=data_sec[\"summary\"]+' '+data_other[\"summary\"]\n", "        # final data\n", "        company_results.append(data_sec)\n", "    return company_results, sec_cost, other_cost, sec_cost + other_cost\n"]}, {"cell_type": "markdown", "id": "32386f55", "metadata": {}, "source": ["## loading company names"]}, {"cell_type": "code", "execution_count": 4, "id": "f7788b03", "metadata": {}, "outputs": [], "source": ["medical_spc_df = pd.read_excel(\"medical_specialties_df.xlsx\")"]}, {"cell_type": "code", "execution_count": 5, "id": "d1724390", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "      <th>Cluster</th>\n", "      <th>SizeCategory</th>\n", "      <th>QuantileCluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Abbott Laboratories</td>\n", "      <td>219756.111897</td>\n", "      <td>12.300278</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Thermo Fisher Scientific, Inc.</td>\n", "      <td>196949.204829</td>\n", "      <td>12.190706</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Intuitive Surgical, Inc.</td>\n", "      <td>173562.531943</td>\n", "      <td>12.064299</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Danaher Corp.</td>\n", "      <td>150617.968231</td>\n", "      <td>11.922509</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Boston Scientific Corp.</td>\n", "      <td>143706.465119</td>\n", "      <td>11.875535</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Entity_Name  Company_Mcap_USD    LogMCap  Cluster  \\\n", "0             Abbott Laboratories     219756.111897  12.300278        2   \n", "1  Thermo Fisher Scientific, Inc.     196949.204829  12.190706        2   \n", "2        Intuitive Surgical, Inc.     173562.531943  12.064299        2   \n", "3                   Danaher Corp.     150617.968231  11.922509        2   \n", "4         Boston Scientific Corp.     143706.465119  11.875535        2   \n", "\n", "  SizeCategory QuantileCluster  \n", "0        Large           Large  \n", "1        Large           Large  \n", "2        Large           Large  \n", "3        Large           Large  \n", "4        Large           Large  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_spc_df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "c895ca5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["28\n"]}], "source": ["small_medical_q = medical_spc_df[medical_spc_df['QuantileCluster'] == 'Small']['Entity_Name'].tolist()\n", "print(len(small_medical_q))"]}, {"cell_type": "code", "execution_count": 7, "id": "3bdcc67d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["38\n"]}], "source": ["small_medical_k = medical_spc_df[medical_spc_df['SizeCategory'] == 'Small']['Entity_Name'].tolist()\n", "print(len(small_medical_k))"]}, {"cell_type": "code", "execution_count": 16, "id": "c7e0a272", "metadata": {}, "outputs": [], "source": ["manuf_comp_data, sec_cost, other_cost, total_cost = all_company_details(small_medical_q)"]}, {"cell_type": "code", "execution_count": 17, "id": "84f885e1", "metadata": {}, "outputs": [{"data": {"text/plain": ["28"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["len(manuf_comp_data)"]}, {"cell_type": "code", "execution_count": 18, "id": "0eea90a8", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"medical_company_small_q.json\", \"w\") as f:\n", "    json.dump(manuf_comp_data, f, indent=4)\n", "\n"]}, {"cell_type": "markdown", "id": "95d5d91f", "metadata": {}, "source": ["```\n", "potential improvemnt\n", "sec_sum = data_sec.get(\"summary\", \"\")\n", "other_sum = data_other.get(\"summary\", \"\")\n", "data_sec[\"summary\"] = f\"{sec_sum} {other_sum}\".strip()\n", "```"]}, {"cell_type": "markdown", "id": "79430d09", "metadata": {}, "source": ["## Final data"]}, {"cell_type": "markdown", "id": "7fe2c478", "metadata": {}, "source": ["## Different scores"]}, {"cell_type": "code", "execution_count": 19, "id": "30d0f40d", "metadata": {}, "outputs": [], "source": ["scores_system_prompt = \"\"\"\\\n", "You are an expert research analyst evaluating and scoring manufacturing companies based on their manufacturing capacity expansion plans or new plant opening plans, employment generation plans, and actual production output in the United States. You have been provided with a structured JSON list containing details for multiple companies.\n", "\n", "For each company:\n", "1. <PERSON>oughly analyze the `capacity_expansion_plans`, `employment_generation_plans`, and `production_output` fields in in the input structured JSON.\n", "2. Consider all `plan_details` from `capacity_expansion_plans`, `employment_generation_plans`, `output_details` from `production_output`, and their corresponding `confidence_score`s while assessing each company.\n", "\n", "Assign the following scores to each company in a comparative manner on a scale from 0 to 10:\n", "- `capacity_expansion_score`: Based on the magnitude of manufacturing capacity expansion or new manufacturing facilities of companies. The magnitude can be dollar value or in terms of units of production or other relevant figures. Give more weightage to specific, quantifiable plans as compared to vague, non-specific information.\n", "- `employment_generation_score`: Based on the number of new job creation or hiring plans of the respective companies.\n", "- `production_output_score`: Based on the level and trend of actual production output (in units or volume) over the quarters of respective companies. Use the following rules while assigning production_output_score to companies:\n", "    a. If production output data is available for only one, two or three quarters, make an educated estimate of the annual output based on the available data, assuming consistent production unless indicated otherwise.\n", "    b. When comparing companies for production output scoring, compare only similar types of commodities within the same primary product category. Focus on the company's core manufacturing output — i.e., its primary product line. For instance, Company X's annual car production can be compared with Company Y's motorcycle output, as both fall under the broad 'vehicle' category. However, X's energy storage production should not be compared with vehicles or any other unrelated product categories from other companies. Always align comparisons based on the principal manufactured product of each company.\n", "\n", "Output structure:\n", "- Return the output in json format with following schema:\n", "    - \"companies\": list of objects with following fields:\n", "        - \"company\": name of the company.\n", "        - \"scores\": list of objects with \"capacity_expansion_score\", \"employment_generation_score\", \"production_output_score\".\n", "        - \"Rationale\": <PERSON><PERSON><PERSON> explaining the assigned scores.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 21, "id": "97c14d0a", "metadata": {}, "outputs": [], "source": ["scores_output_format = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_scores\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"companies\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"company\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Name of the company being evaluated.\"\n", "                            },\n", "                            \"scores\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"capacity_expansion_score\": {\n", "                                        \"type\": \"number\",\n", "                                        \"description\": \"Score (0–10) based on magnitude of capacity expansion or new plant opening.\"\n", "                                    },\n", "                                    \"employment_generation_score\": {\n", "                                        \"type\": \"number\",\n", "                                        \"description\": \"Score (0–10) based on the scale of employment generation or hiring.\"\n", "                                    },\n", "                                    \"production_output_score\": {\n", "                                        \"type\": \"number\",\n", "                                        \"description\": \"Score (0–10) based on the level and trend of actual production output.\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"capacity_expansion_score\",\n", "                                    \"employment_generation_score\",\n", "                                    \"production_output_score\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"capacity_expansion_rationale\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Explanation of how the capacity_expansion_score was determined, referencing plan details and confidence scores.\"\n", "                            },\n", "                            \"employment_generation_rationale\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Explanation of how the employment_generation_score was determined, referencing plan details and confidence scores.\"\n", "                            },\n", "                            \"production_output_rationale\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Explanation of how the production_output_score was determined, referencing output details and confidence scores.\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"company\",\n", "                            \"scores\",\n", "                            \"capacity_expansion_rationale\",\n", "                            \"employment_generation_rationale\",\n", "                            \"production_output_rationale\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                }\n", "            },\n", "            \"required\": [\n", "                \"companies\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 13, "id": "2e7d8af0", "metadata": {}, "outputs": [], "source": ["with open(\"manuf_company_results.json\", \"r\") as f:\n", "    manuf_comp_data_old = json.load(f)"]}, {"cell_type": "code", "execution_count": 16, "id": "3ac0f15c", "metadata": {}, "outputs": [{"data": {"text/plain": ["list"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["type(manuf_comp_data_old)"]}, {"cell_type": "code", "execution_count": null, "id": "f483728a", "metadata": {}, "outputs": [], "source": ["quarter_urls = json.dumps"]}, {"cell_type": "code", "execution_count": 22, "id": "00c33053", "metadata": {}, "outputs": [], "source": ["response = client.responses.create(\n", "            model=\"o4-mini\",\n", "            reasoning={\"effort\": \"high\"},\n", "            input=[\n", "                {\"role\": \"system\", \"content\": scores_system_prompt},\n", "                {\"role\": \"user\", \"content\": json.dumps(manuf_comp_data) }\n", "            ],\n", "            text=scores_output_format\n", "        )\n", "\n", "INPUT_COST_PER_TOKEN = 1.10 / 1_000_000\n", "OUTPUT_COST_PER_TOKEN = 4.40 / 1_000_000\n", "\n", "input_tokens = response.usage.input_tokens\n", "output_tokens = response.usage.output_tokens\n", "total_scoring_cost = (input_tokens * INPUT_COST_PER_TOKEN) + (output_tokens * OUTPUT_COST_PER_TOKEN)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "id": "3ff99e4c", "metadata": {}, "outputs": [{"data": {"text/plain": ["24215"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["input_tokens"]}, {"cell_type": "code", "execution_count": 24, "id": "0882a786", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"companies\": [\n", "        {\n", "            \"company\": \"BioLife Solutions, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 3\n", "            },\n", "            \"capacity_expansion_rationale\": \"Input shows an empty capacity_expansion_plans list and no related disclosures in multiple SEC filings (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans provided or disclosed in filings (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly revenue data ($31.7 M in Q1, $28.3 M in Q2, $30.6 M in Q3; $82.3 M full-year 2024) indicate moderate scale and stability in the cell processing business relative to peers => score 3.\"\n", "        },\n", "        {\n", "            \"company\": \"Alphatec Holdings, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 5\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed in SEC filings (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed in filings (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly revenues rising from $138 M in Q1 to $177 M in Q4 2024, driven by 23\\u201330% surgical volume growth, show strong upward trend in core surgical device output => score 5.\"\n", "        },\n", "        {\n", "            \"company\": \"MiMedx Group, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"Empty capacity_expansion_plans with no related disclosures (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans or hiring commitments found (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly net sales of $85 M, $87 M, $84 M, $93 M show consistent year-over-year growth (3\\u201318%) in tissue products => moderate scale and trend => score 4.\"\n", "        },\n", "        {\n", "            \"company\": \"Paragon 28, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 5\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed in filings (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"Quarterly net revenues rising from $61.1 M in Q1 to a projected ~$256 M full-year 2024, with 17\\u201318% growth, indicate moderate-high output scale in orthopedic implants => score 5.\"\n", "        },\n", "        {\n", "            \"company\": \"Kestra Medical Technologies Ltd.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 2,\n", "                \"production_output_score\": 2\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed in filings (confidence ~0.8) => score 0.\",\n", "            \"employment_generation_rationale\": \"Two plan announcements to expand commercial headcount post\\u2013$196 M financing and IPO (confidence ~0.8), but without specific hiring targets => minimal quantified impact => score 2.\",\n", "            \"production_output_rationale\": \"Q3 FY25 revenue of $15.1 M (82% YoY growth) from ASSURE\\u00ae prescriptions (confidence ~0.9) indicates small absolute scale => score 2.\"\n", "        },\n", "        {\n", "            \"company\": \"CareDx, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans found (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) testing services revenues of $53.8 M in Q1 rising to ~$72.3 M in Q3 and 47,100 patient results in Q1 2025 show solid growth but moderate absolute scale relative to high-volume diagnostics peers => score 4.\"\n", "        },\n", "        {\n", "            \"company\": \"Pulse Biosciences, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"Leased 2,000 ft\\u00b2 corporate HQ but no manufacturing capacity expansion disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"No production_output data provided in filings => score 0.\"\n", "        },\n", "        {\n", "            \"company\": \"Artivion, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed in filings (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly revenues near $96\\u201399 M across Q1 2024\\u2013Q1 2025 show stable mid-scale performance in vascular grafts => score 4.\"\n", "        },\n", "        {\n", "            \"company\": \"GRAIL, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 5\n", "            },\n", "            \"capacity_expansion_rationale\": \"No new plant or capacity_expansion_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"Announced a 30% headcount reduction (confidence ~0.9), indicating contraction rather than growth => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) sale of 137,000 Galleri\\u00ae tests for $108.6 M U.S. revenue (45% YoY growth) shows significant and growing clinical output => score 5.\"\n", "        },\n", "        {\n", "            \"company\": \"Myriad Genetics, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 1,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 6\n", "            },\n", "            \"capacity_expansion_rationale\": \"Plan to consolidate IPG laboratory operations into existing Salt Lake City facility by end-2024 (confidence ~0.8) is rationalization, not net expansion => minimal score 1.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) test volumes of 380,675 in Q1 2024 (+9% YoY) and ~374,000 in Q4 2024 (+4% YoY), with revenues of $211\\u2013213 M/quarter, represent large-scale diagnostics throughput => score 6.\"\n", "        },\n", "        {\n", "            \"company\": \"STAAR Surgical Co.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 1\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans or new facilities disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"Plans to lay off 115 employees in California (confidence ~0.7) indicate contraction => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) U.S. sales peaked at $5.5 M in Q2 2024 but then declined to $4.0 M in Q1 2025, showing downward volume trend => score 1.\"\n", "        },\n", "        {\n", "            \"company\": \"Bioventus, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 6\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) U.S. net sales of $135.2 M in Q4 2024 and Q1 2025 (+13.8% YoY) demonstrate strong scale in orthobiologics => score 6.\"\n", "        },\n", "        {\n", "            \"company\": \"Axogen, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 8,\n", "                \"employment_generation_score\": 5,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"Opened 107,000 ft\\u00b2 Axogen Processing Center in Vandalia, OH, tripling previous processing capacity (confidence 0.9) => score 8.\",\n", "            \"employment_generation_rationale\": \"Facility employs ~100 professionals with plans to expand headcount as production scales (confidence 0.9) => moderate impact => score 5.\",\n", "            \"production_output_rationale\": \"Reported $38.2 M in Q2 2024 revenue (+11% YoY, confidence ~0.7) indicates modest core revenue scale => score 4.\"\n", "        },\n", "        {\n", "            \"company\": \"Embecta Corp.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 8\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"Reported $1,123 M in FY 2024 revenue (+0.2% YoY) and $261.9 M in Q1 2025 (confidence ~0.9), reflecting very large, stable medical device output => score 8.\"\n", "        },\n", "        {\n", "            \"company\": \"Ceribell, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 5,\n", "                \"production_output_score\": 3\n", "            },\n", "            \"capacity_expansion_rationale\": \"No manufacturing capacity_expansion_plans disclosed in filings (confidence ~0.7) => score 0.\",\n", "            \"employment_generation_rationale\": \"Plans to grow direct sales organization beyond ~70 reps and expand R&D headcount post-IPO (confidence ~0.7) indicate moderate hiring activity => score 5.\",\n", "            \"production_output_rationale\": \"Q3 2024 revenue of $17.2 M, Q4 $18.5 M, Q1 2025 $20.5 M (confidence ~0.9) show early-stage growth at modest absolute scale => score 3.\"\n", "        },\n", "        {\n", "            \"company\": \"Owens & Minor, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 10\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans or new plant openings disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"Consistent high-confidence (0.9) quarterly net revenues of $2.6\\u20132.7 billion across all quarters of 2024 demonstrate top-tier distribution scale => score 10.\"\n", "        },\n", "        {\n", "            \"company\": \"Mesa Laboratories, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed (confidence ~0.7) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.7) => score 0.\",\n", "            \"production_output_rationale\": \"Revenue of $58.2 M in Q1 2025 and $62.8 M in Q3 2025 (confidence ~0.7) shows moderate growth in lab and calibrations market => score 4.\"\n", "        },\n", "        {\n", "            \"company\": \"IRadimed Corp.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 7,\n", "                \"employment_generation_score\": 7,\n", "                \"production_output_score\": 3\n", "            },\n", "            \"capacity_expansion_rationale\": \"Invested $8.8 M in new manufacturing facility and plans additional $5.5 M; constructing 60,000 ft\\u00b2, $20 M production HQ in Orlando by May 2025 (confidence 0.9) => score 7.\",\n", "            \"employment_generation_rationale\": \"Orlando expansion projected to create 160 new jobs at average $85 K salary (confidence 0.9) => score 7.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly revenues of $17.6\\u201319.5 M in 2024\\u2013Q1 2025 indicate modest medical device output scale => score 3.\"\n", "        },\n", "        {\n", "            \"company\": \"Avanos Medical, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 6\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly net sales of $166\\u2013179 M in 2024 (+2.5\\u20134.3% YoY) reflect large-scale infusion and respiratory device production => score 6.\"\n", "        },\n", "        {\n", "            \"company\": \"Butterfly Network, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"No manufacturing capacity plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"No production_output data provided in filings => score 0.\"\n", "        },\n", "        {\n", "            \"company\": \"Orthofix Medical, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 6\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly net sales of $188.6\\u2013215.7 M in 2024 (+6\\u20137.7% YoY) across spine and orthopedics signal strong sustained output => score 6.\"\n", "        },\n", "        {\n", "            \"company\": \"Organogenesis Holdings, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 8,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 5\n", "            },\n", "            \"capacity_expansion_rationale\": \"Leased 122,000 ft\\u00b2 manufacturing facility in Smithfield, RI (delivery space in 2027) with long-term term and purchase rights (confidence 0.9) => significant future capacity => score 8.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed in filings (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) Q4 2024 net revenue of $126.7 M in wound care and surgical products indicates moderate output scale => score 5.\"\n", "        },\n", "        {\n", "            \"company\": \"Beta Bionics, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 2\n", "            },\n", "            \"capacity_expansion_rationale\": \"No manufacturing capacity plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) net sales of $20.4 M in Q4 2024 and $17.6 M in Q1 2025 (+36\\u2013145% YoY but small absolute base) => low absolute scale => score 2.\"\n", "        },\n", "        {\n", "            \"company\": \"SI-BONE, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 5\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans or new plants disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) worldwide revenue growth from $37.9 M in Q1 2024 to $49.0 M in Q4 2024 and $47.3 M in Q1 2025 (16\\u201326% YoY) indicates solid mid-scale momentum in sacroiliac fusion products => score 5.\"\n", "        },\n", "        {\n", "            \"company\": \"OrthoPediatrics Corp.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"Clinic openings in four U.S. markets expand service network but no new manufacturing facilities disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) quarterly revenues rising from $44.7 M in Q1 to ~$54.6 M in Q3 2024 (33\\u201341% YoY) show moderate pediatric implant output scale => score 4.\"\n", "        },\n", "        {\n", "            \"company\": \"Castle Biosciences, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"Groundbreaking for 80,000 ft\\u00b2 corporate HQ in Friendswood, TX (office space only) with no biomanufacturing capacity described (confidence 0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence 0.9) => score 0.\",\n", "            \"production_output_rationale\": \"High-confidence (0.9) delivery of 96,071 total test reports in 2024 (+36% YoY) demonstrates moderate diagnostics output scale => score 4.\"\n", "        },\n", "        {\n", "            \"company\": \"Cytek Biosciences, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 2,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"ISO 13485 certification of San Diego reagent facility (confidence 0.9) enables clinical manufacturing but is not a new plant opening => minimal quantified expansion => score 2.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence 0.9) => score 0.\",\n", "            \"production_output_rationale\": \"No production_output data provided in filings => score 0.\"\n", "        },\n", "        {\n", "            \"company\": \"Varex Imaging Corp.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity_expansion_plans or new manufacturing facilities disclosed (confidence ~0.9) => score 0.\",\n", "            \"employment_generation_rationale\": \"No employment_generation_plans disclosed (confidence ~0.9) => score 0.\",\n", "            \"production_output_rationale\": \"No production_output data provided in filings => score 0.\"\n", "        }\n", "    ]\n", "}\n"]}], "source": ["final_result = json.loads(response.output_text)\n", "print(json.dumps(final_result, indent=4))"]}, {"cell_type": "code", "execution_count": 25, "id": "cd1d3f3e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "companies_data = final_result[\"companies\"]\n", "\n", "df = pd.DataFrame([\n", "    {\n", "        \"company\": entry[\"company\"],\n", "        \"capacity_expansion_score\": entry[\"scores\"][\"capacity_expansion_score\"],\n", "        \"employment_generation_score\": entry[\"scores\"][\"employment_generation_score\"],\n", "        \"production_output_score\": entry[\"scores\"][\"production_output_score\"],\n", "        \"capacity_expansion_rationale\": entry[\"capacity_expansion_rationale\"],\n", "        \"employment_generation_rationale\": entry[\"employment_generation_rationale\"],\n", "        \"production_output_rationale\": entry[\"production_output_rationale\"]\n", "    }\n", "    for entry in companies_data\n", "])\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "id": "33bc63a7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>company</th>\n", "      <th>capacity_expansion_score</th>\n", "      <th>employment_generation_score</th>\n", "      <th>production_output_score</th>\n", "      <th>capacity_expansion_rationale</th>\n", "      <th>employment_generation_rationale</th>\n", "      <th>production_output_rationale</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Te<PERSON></td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "      <td>8</td>\n", "      <td>Tesla announced a $3.6 billion Gigafactory Nev...</td>\n", "      <td>Tesla’s plans include 3 000 jobs from the Neva...</td>\n", "      <td>Fremont produced ~560 000 vehicles in 2023 and...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>General Motors</td>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>10</td>\n", "      <td>GM is investing &gt;$3 billion with Samsung SDI f...</td>\n", "      <td>GM projects “thousands” of jobs at the new bat...</td>\n", "      <td>GM reported 2.7 million US vehicle sales in 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Ford Motor Company</td>\n", "      <td>8</td>\n", "      <td>9</td>\n", "      <td>9</td>\n", "      <td>Ford’s US investments include $3.5 billion for...</td>\n", "      <td>Ford plans ~6 200 new factory jobs (EVs and ne...</td>\n", "      <td>Ford wholesaled ~526 000 vehicles per quarter ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON>ian <PERSON>motive</td>\n", "      <td>7</td>\n", "      <td>9</td>\n", "      <td>3</td>\n", "      <td>Rivian expanded Normal, IL capacity to 215 000...</td>\n", "      <td><PERSON><PERSON><PERSON>’s Georgia facility is projected to empl...</td>\n", "      <td><PERSON><PERSON><PERSON> produced ~49 500 vehicles in 2024 and ~...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Lucid Group</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>Lucid expanded AMP-1 by 3 million sq ft to boo...</td>\n", "      <td>No specific US job-creation figures disclosed ...</td>\n", "      <td>Lucid produced ~9 000 vehicles in 2024 (full y...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Oshkosh Corporation</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>Oshkosh invested $281 million in 2024 capex to...</td>\n", "      <td>Plans to add ~300 jobs in Jefferson County, TN...</td>\n", "      <td>No unit or volume output data provided. Score ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Harley-Davidson</td>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>Received an $89 million DOE grant to expand it...</td>\n", "      <td>Plans to retrain 1 300 union employees and hir...</td>\n", "      <td>Retail sales of ~94 930 motorcycles in 2024 (c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>REV Group</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>Only plan cited is a 2022 Decatur, IN facility...</td>\n", "      <td>The Decatur facility will create ~120–140 jobs...</td>\n", "      <td>No production unit or volume data provided. Sc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Blue Bird Corp.</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>Converting a 600 000 sq ft Georgia site ($160 ...</td>\n", "      <td>Fort Valley conversion creates &gt;400 jobs and P...</td>\n", "      <td>Sold 9 000 buses in FY 2024 (+6%) including 70...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Miller Industries</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>Acquired Southern Hydraulic Cylinder and added...</td>\n", "      <td>No new US job-creation figures disclosed for 2...</td>\n", "      <td>Q1–Q3 2024 net sales grew 14–24% then Q4 fell ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               company  capacity_expansion_score  employment_generation_score  \\\n", "0                Tesla                        10                           10   \n", "1       General Motors                         9                            7   \n", "2   Ford Motor Company                         8                            9   \n", "3    Rivian Automotive                         7                            9   \n", "4          Lucid Group                         6                            0   \n", "5  Oshkosh Corporation                         4                            3   \n", "6      Harley-Davidson                         5                            3   \n", "7            REV Group                         2                            2   \n", "8      Blue Bird Corp.                         6                            5   \n", "9    Miller Industries                         3                            0   \n", "\n", "   production_output_score                       capacity_expansion_rationale  \\\n", "0                        8  Tesla announced a $3.6 billion Gigafactory Nev...   \n", "1                       10  GM is investing >$3 billion with Samsung SDI f...   \n", "2                        9  Ford’s US investments include $3.5 billion for...   \n", "3                        3  Rivian expanded Normal, IL capacity to 215 000...   \n", "4                        1  Lucid expanded AMP-1 by 3 million sq ft to boo...   \n", "5                        0  Oshkosh invested $281 million in 2024 capex to...   \n", "6                        5  Received an $89 million DOE grant to expand it...   \n", "7                        0  Only plan cited is a 2022 Decatur, IN facility...   \n", "8                        5  Converting a 600 000 sq ft Georgia site ($160 ...   \n", "9                        3  Acquired Southern Hydraulic Cylinder and added...   \n", "\n", "                     employment_generation_rationale  \\\n", "0  Tesla’s plans include 3 000 jobs from the Neva...   \n", "1  GM projects “thousands” of jobs at the new bat...   \n", "2  Ford plans ~6 200 new factory jobs (EVs and ne...   \n", "3  Rivian’s Georgia facility is projected to empl...   \n", "4  No specific US job-creation figures disclosed ...   \n", "5  Plans to add ~300 jobs in Jefferson County, TN...   \n", "6  Plans to retrain 1 300 union employees and hir...   \n", "7  The Decatur facility will create ~120–140 jobs...   \n", "8  Fort Valley conversion creates >400 jobs and P...   \n", "9  No new US job-creation figures disclosed for 2...   \n", "\n", "                         production_output_rationale  \n", "0  Fremont produced ~560 000 vehicles in 2023 and...  \n", "1  GM reported 2.7 million US vehicle sales in 20...  \n", "2  Ford wholesaled ~526 000 vehicles per quarter ...  \n", "3  Rivian produced ~49 500 vehicles in 2024 and ~...  \n", "4  Lucid produced ~9 000 vehicles in 2024 (full y...  \n", "5  No unit or volume output data provided. Score ...  \n", "6  Retail sales of ~94 930 motorcycles in 2024 (c...  \n", "7  No production unit or volume data provided. Sc...  \n", "8  Sold 9 000 buses in FY 2024 (+6%) including 70...  \n", "9  Q1–Q3 2024 net sales grew 14–24% then Q4 fell ...  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(10)"]}, {"cell_type": "code", "execution_count": 26, "id": "009cac99", "metadata": {}, "outputs": [], "source": ["df.to_excel(\"medical_small_q_score_new.xlsx\", index=False)"]}, {"cell_type": "markdown", "id": "58579239", "metadata": {}, "source": ["## Cost Analysis"]}, {"cell_type": "code", "execution_count": 27, "id": "610f25af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sec_data_cost: 47.712121120000006,\n", "       \n", " other_data_cost: 51.656816639999995,\n", "       \n", " total_data_cost: 99.36893776000001,\n", "        \n", "total_scoring_cost: 3.4355094400000006\n"]}], "source": ["print(f\"\"\"sec_data_cost: {sec_cost*85.52},\n", "       \\n other_data_cost: {other_cost*85.52},\n", "       \\n total_data_cost: {total_cost*85.52},\n", "        \\ntotal_scoring_cost: {total_scoring_cost*85.52}\"\"\")"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}