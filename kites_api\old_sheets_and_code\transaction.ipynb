{"cells": [{"cell_type": "markdown", "id": "71ebe3d6", "metadata": {}, "source": ["``` \n", "columns in transaction sheet\n", "intsrument token, exchange token, tradingsymbol, name, , instrument type, expiry, strike, lot size, current stock price,ITM/OTM premium paid, premium received,profit, margin, profit %"]}, {"cell_type": "code", "execution_count": 3, "id": "34a9ca81", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from kiteconnect import KiteConnect\n", "import requests"]}, {"cell_type": "code", "execution_count": 4, "id": "b29e5e65", "metadata": {}, "outputs": [], "source": ["api_key = \"3q0r6l1fb689slhk\"\n", "access_token = \"J4L2gokb0CNBVjeoKqT1p11i61s1q0kl\"\n", "\n", "kite = KiteConnect(api_key=api_key)\n", "kite.set_access_token(access_token)"]}, {"cell_type": "code", "execution_count": 7, "id": "7c3d3762", "metadata": {}, "outputs": [], "source": ["put_options_df = pd.read_excel('put_options_final.xlsx')"]}, {"cell_type": "code", "execution_count": 8, "id": "793c3433", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>average_price</th>\n", "      <th>target_strike</th>\n", "      <th>last_price</th>\n", "      <th>premium</th>\n", "      <th>total_margin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1202.64</td>\n", "      <td>1142.5080</td>\n", "      <td>0.95</td>\n", "      <td>593.75</td>\n", "      <td>105724.53125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1927.84</td>\n", "      <td>1831.4480</td>\n", "      <td>1.10</td>\n", "      <td>605.00</td>\n", "      <td>141766.76250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1445.26</td>\n", "      <td>1372.9970</td>\n", "      <td>0.85</td>\n", "      <td>595.00</td>\n", "      <td>134309.52500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>2086.16</td>\n", "      <td>1981.8520</td>\n", "      <td>1.30</td>\n", "      <td>520.00</td>\n", "      <td>117610.90000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "      <td>220</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>230.88</td>\n", "      <td>219.3360</td>\n", "      <td>0.15</td>\n", "      <td>450.00</td>\n", "      <td>178330.50000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>2092.87</td>\n", "      <td>1988.2265</td>\n", "      <td>4.25</td>\n", "      <td>1168.75</td>\n", "      <td>108467.56250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>760</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>790.21</td>\n", "      <td>750.6995</td>\n", "      <td>0.75</td>\n", "      <td>562.50</td>\n", "      <td>83071.50000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  average_price  \\\n", "0 2025-05-29    1150       625              PE      NFO        1202.64   \n", "1 2025-05-29    1840       550              PE      NFO        1927.84   \n", "2 2025-05-29    1380       700              PE      NFO        1445.26   \n", "3 2025-05-29    2000       400              PE      NFO        2086.16   \n", "4 2025-05-29     220      3000              PE      NFO         230.88   \n", "5 2025-05-29    2000       275              PE      NFO        2092.87   \n", "6 2025-05-29     760       750              PE      NFO         790.21   \n", "\n", "   target_strike  last_price  premium  total_margin  \n", "0      1142.5080        0.95   593.75  105724.53125  \n", "1      1831.4480        1.10   605.00  141766.76250  \n", "2      1372.9970        0.85   595.00  134309.52500  \n", "3      1981.8520        1.30   520.00  117610.90000  \n", "4       219.3360        0.15   450.00  178330.50000  \n", "5      1988.2265        4.25  1168.75  108467.56250  \n", "6       750.6995        0.75   562.50   83071.50000  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options_df.head(8)"]}, {"cell_type": "code", "execution_count": 9, "id": "4fd3bf43", "metadata": {}, "outputs": [], "source": ["transaction_sheet = put_options_df[['instrument_token', 'exchange_token', 'tradingsymbol', 'name', 'expiry', 'strike', 'lot_size', 'instrument_type', 'premium', 'total_margin']]\n", "\n"]}, {"cell_type": "markdown", "id": "8c308fc0", "metadata": {}, "source": ["## Current stock price"]}, {"cell_type": "code", "execution_count": 10, "id": "9f3df883", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NSE:AXISBANK', 'NSE:HDFCBANK', 'NSE:ICICIBANK', 'NSE:KOTAKBANK', 'NSE:MANAPPURAM', 'NSE:MUTHOOTFIN', 'NSE:SBIN']\n"]}], "source": ["instruments_list = transaction_sheet.apply(\n", "    lambda row: f\"NSE:{row['name']}\", axis=1\n", ").to_list()\n", "\n", "print(instruments_list)"]}, {"cell_type": "code", "execution_count": 11, "id": "ad9d0ad7", "metadata": {}, "outputs": [], "source": ["try:\n", "    quote = kite.quote(instruments_list) \n", "except Exception as e:\n", "    print(f\"Error fetching quote: {e}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "a155baa7", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["len(quote)"]}, {"cell_type": "code", "execution_count": 14, "id": "55dd5cc9", "metadata": {}, "outputs": [], "source": ["stk_price_df = pd.DataFrame([\n", "    {\n", "      'name': instrument.split(':')[1],\n", "      'average_price': quote[instrument]['average_price'] \n", "    }\n", "    for instrument in quote.keys()\n", "])"]}, {"cell_type": "code", "execution_count": 15, "id": "5188096a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>average_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AXISBANK</td>\n", "      <td>1218.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HDFCBANK</td>\n", "      <td>1940.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ICICIBANK</td>\n", "      <td>1463.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KOTAKBANK</td>\n", "      <td>2098.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MANAPPURAM</td>\n", "      <td>230.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2105.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SBIN</td>\n", "      <td>793.83</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         name  average_price\n", "0    AXISBANK        1218.36\n", "1    HDFCBANK        1940.43\n", "2   ICICIBANK        1463.08\n", "3   KOTAKBANK        2098.30\n", "4  MANAPPURAM         230.79\n", "5  MUTHOOTFIN        2105.39\n", "6        SBIN         793.83"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_price_df.head(8)"]}, {"cell_type": "code", "execution_count": 16, "id": "6ab57886", "metadata": {}, "outputs": [], "source": ["transaction_sheet = transaction_sheet.merge(stk_price_df, on='name', how='inner')"]}, {"cell_type": "code", "execution_count": 21, "id": "e22acaa4", "metadata": {}, "outputs": [], "source": ["transaction_sheet.rename(columns={'average_price': 'stock_price_on_exp', 'premium': 'premium_received'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 24, "id": "25eb82b5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>593.75</td>\n", "      <td>105724.53125</td>\n", "      <td>1218.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>605.00</td>\n", "      <td>141766.76250</td>\n", "      <td>1940.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>595.00</td>\n", "      <td>134309.52500</td>\n", "      <td>1463.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>520.00</td>\n", "      <td>117610.90000</td>\n", "      <td>2098.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "      <td>220</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>450.00</td>\n", "      <td>178330.50000</td>\n", "      <td>230.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>1168.75</td>\n", "      <td>108467.56250</td>\n", "      <td>2105.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>760</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>562.50</td>\n", "      <td>83071.50000</td>\n", "      <td>793.83</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "      expiry  strike  lot_size instrument_type  premium_received  \\\n", "0 2025-05-29    1150       625              PE            593.75   \n", "1 2025-05-29    1840       550              PE            605.00   \n", "2 2025-05-29    1380       700              PE            595.00   \n", "3 2025-05-29    2000       400              PE            520.00   \n", "4 2025-05-29     220      3000              PE            450.00   \n", "5 2025-05-29    2000       275              PE           1168.75   \n", "6 2025-05-29     760       750              PE            562.50   \n", "\n", "   total_margin  stock_price_on_exp  \n", "0  105724.53125             1218.36  \n", "1  141766.76250             1940.43  \n", "2  134309.52500             1463.08  \n", "3  117610.90000             2098.30  \n", "4  178330.50000              230.79  \n", "5  108467.56250             2105.39  \n", "6   83071.50000              793.83  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["transaction_sheet.head(8)"]}, {"cell_type": "markdown", "id": "77ad8c2a", "metadata": {}, "source": ["## ITM/OTM flagging"]}, {"cell_type": "code", "execution_count": 25, "id": "11b4b97e", "metadata": {}, "outputs": [], "source": ["# ITM and OTM flagging\n", "def determine_itm_otm(row):\n", "    if row['instrument_type'] == 'CE':\n", "        return 'ITM' if row['strike'] < row['stock_price_on_exp'] else 'OTM'\n", "    elif row['instrument_type'] == 'PE':\n", "        return 'ITM' if row['strike'] > row['stock_price_on_exp'] else 'OTM'\n", "    else:\n", "        return 'N/A'\n"]}, {"cell_type": "code", "execution_count": 26, "id": "b9ac94f3", "metadata": {}, "outputs": [], "source": ["transaction_sheet['ITM/OTM'] = transaction_sheet.apply(determine_itm_otm, axis=1)"]}, {"cell_type": "code", "execution_count": 27, "id": "e9c8eb7a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>ITM/OTM</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>593.75</td>\n", "      <td>105724.53125</td>\n", "      <td>1218.36</td>\n", "      <td>OTM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>605.00</td>\n", "      <td>141766.76250</td>\n", "      <td>1940.43</td>\n", "      <td>OTM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>595.00</td>\n", "      <td>134309.52500</td>\n", "      <td>1463.08</td>\n", "      <td>OTM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>520.00</td>\n", "      <td>117610.90000</td>\n", "      <td>2098.30</td>\n", "      <td>OTM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "      <td>220</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>450.00</td>\n", "      <td>178330.50000</td>\n", "      <td>230.79</td>\n", "      <td>OTM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>1168.75</td>\n", "      <td>108467.56250</td>\n", "      <td>2105.39</td>\n", "      <td>OTM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>760</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>562.50</td>\n", "      <td>83071.50000</td>\n", "      <td>793.83</td>\n", "      <td>OTM</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "      expiry  strike  lot_size instrument_type  premium_received  \\\n", "0 2025-05-29    1150       625              PE            593.75   \n", "1 2025-05-29    1840       550              PE            605.00   \n", "2 2025-05-29    1380       700              PE            595.00   \n", "3 2025-05-29    2000       400              PE            520.00   \n", "4 2025-05-29     220      3000              PE            450.00   \n", "5 2025-05-29    2000       275              PE           1168.75   \n", "6 2025-05-29     760       750              PE            562.50   \n", "\n", "   total_margin  stock_price_on_exp ITM/OTM  \n", "0  105724.53125             1218.36     OTM  \n", "1  141766.76250             1940.43     OTM  \n", "2  134309.52500             1463.08     OTM  \n", "3  117610.90000             2098.30     OTM  \n", "4  178330.50000              230.79     OTM  \n", "5  108467.56250             2105.39     OTM  \n", "6   83071.50000              793.83     OTM  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["transaction_sheet.head(8)"]}, {"cell_type": "markdown", "id": "5d0ba095", "metadata": {}, "source": ["## Premium Paid for square off"]}, {"cell_type": "code", "execution_count": 28, "id": "74d36958", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NFO:AXISBANK25MAY1150PE', 'NFO:HDFCBANK25MAY1840PE', 'NFO:ICICIBANK25MAY1380PE', 'NFO:KOTAKBANK25MAY2000PE', 'NFO:MANAPPURAM25MAY220PE', 'NFO:MUTHOOTFIN25MAY2000PE', 'NFO:SBIN25MAY760PE']\n"]}], "source": ["# premium calculation\n", "put_instruments_list = transaction_sheet.apply(\n", "    lambda row: f\"NFO:{row['tradingsymbol']}\", axis=1\n", ").to_list()\n", "\n", "print(put_instruments_list)"]}, {"cell_type": "code", "execution_count": 29, "id": "d5125ea4", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["quote_data = kite.quote(put_instruments_list)\n", "\n", "len(quote_data)"]}, {"cell_type": "code", "execution_count": 30, "id": "53b5f619", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AXISBANK25MAY1150PE\n"]}], "source": ["instur = 'NFO:AXISBANK25MAY1150PE'\n", "print(instur.split(':')[1])"]}, {"cell_type": "code", "execution_count": 31, "id": "cebc8c32", "metadata": {}, "outputs": [], "source": ["quote_df = pd.DataFrame([\n", "    {\n", "        'tradingsymbol': instrument_key.split(':')[1],\n", "        'last_price': quote_data[instrument_key]['last_price']\n", "    }\n", "    for instrument_key in quote_data.keys()\n", "])"]}, {"cell_type": "code", "execution_count": 32, "id": "9ad11314", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>last_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>0.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>1.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>0.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>2.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           tradingsymbol  last_price\n", "0    AXISBANK25MAY1150PE        0.50\n", "1    HDFCBANK25MAY1840PE        0.75\n", "2   ICICIBANK25MAY1380PE        0.55\n", "3   KOTAKBANK25MAY2000PE        1.05\n", "4   MANAPPURAM25MAY220PE        0.10\n", "5  MUTHOOTFIN25MAY2000PE        2.15\n", "6         SBIN25MAY760PE        0.45"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["quote_df.head(8)"]}, {"cell_type": "code", "execution_count": 33, "id": "897305b4", "metadata": {}, "outputs": [], "source": ["transaction_sheet = transaction_sheet.merge(quote_df, on='tradingsymbol', how='inner')"]}, {"cell_type": "code", "execution_count": 35, "id": "74b131a9", "metadata": {}, "outputs": [], "source": ["transaction_sheet['premium_paid'] = transaction_sheet['last_price'] * transaction_sheet['lot_size']"]}, {"cell_type": "code", "execution_count": 36, "id": "c0a59e86", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>ITM/OTM</th>\n", "      <th>last_price</th>\n", "      <th>premium_paid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>593.75</td>\n", "      <td>105724.53125</td>\n", "      <td>1218.36</td>\n", "      <td>OTM</td>\n", "      <td>0.50</td>\n", "      <td>312.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>605.00</td>\n", "      <td>141766.76250</td>\n", "      <td>1940.43</td>\n", "      <td>OTM</td>\n", "      <td>0.75</td>\n", "      <td>412.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>595.00</td>\n", "      <td>134309.52500</td>\n", "      <td>1463.08</td>\n", "      <td>OTM</td>\n", "      <td>0.55</td>\n", "      <td>385.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>520.00</td>\n", "      <td>117610.90000</td>\n", "      <td>2098.30</td>\n", "      <td>OTM</td>\n", "      <td>1.05</td>\n", "      <td>420.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "      <td>220</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>450.00</td>\n", "      <td>178330.50000</td>\n", "      <td>230.79</td>\n", "      <td>OTM</td>\n", "      <td>0.10</td>\n", "      <td>300.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>1168.75</td>\n", "      <td>108467.56250</td>\n", "      <td>2105.39</td>\n", "      <td>OTM</td>\n", "      <td>2.15</td>\n", "      <td>591.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>760</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>562.50</td>\n", "      <td>83071.50000</td>\n", "      <td>793.83</td>\n", "      <td>OTM</td>\n", "      <td>0.45</td>\n", "      <td>337.50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "      expiry  strike  lot_size instrument_type  premium_received  \\\n", "0 2025-05-29    1150       625              PE            593.75   \n", "1 2025-05-29    1840       550              PE            605.00   \n", "2 2025-05-29    1380       700              PE            595.00   \n", "3 2025-05-29    2000       400              PE            520.00   \n", "4 2025-05-29     220      3000              PE            450.00   \n", "5 2025-05-29    2000       275              PE           1168.75   \n", "6 2025-05-29     760       750              PE            562.50   \n", "\n", "   total_margin  stock_price_on_exp ITM/OTM  last_price  premium_paid  \n", "0  105724.53125             1218.36     OTM        0.50        312.50  \n", "1  141766.76250             1940.43     OTM        0.75        412.50  \n", "2  134309.52500             1463.08     OTM        0.55        385.00  \n", "3  117610.90000             2098.30     OTM        1.05        420.00  \n", "4  178330.50000              230.79     OTM        0.10        300.00  \n", "5  108467.56250             2105.39     OTM        2.15        591.25  \n", "6   83071.50000              793.83     OTM        0.45        337.50  "]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["transaction_sheet.head(8)"]}, {"cell_type": "markdown", "id": "60577ec1", "metadata": {}, "source": ["## Profit and yield percentage"]}, {"cell_type": "code", "execution_count": 37, "id": "7162357c", "metadata": {}, "outputs": [], "source": ["transaction_sheet['profit_loss'] = transaction_sheet['premium_received'] - transaction_sheet['premium_paid']\n", "\n", "transaction_sheet['yield'] = transaction_sheet['profit_loss'] / transaction_sheet['total_margin']"]}, {"cell_type": "code", "execution_count": 38, "id": "b3c575bb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>ITM/OTM</th>\n", "      <th>last_price</th>\n", "      <th>premium_paid</th>\n", "      <th>profit_loss</th>\n", "      <th>yield</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>593.75</td>\n", "      <td>105724.53125</td>\n", "      <td>1218.36</td>\n", "      <td>OTM</td>\n", "      <td>0.50</td>\n", "      <td>312.50</td>\n", "      <td>281.25</td>\n", "      <td>0.002660</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>605.00</td>\n", "      <td>141766.76250</td>\n", "      <td>1940.43</td>\n", "      <td>OTM</td>\n", "      <td>0.75</td>\n", "      <td>412.50</td>\n", "      <td>192.50</td>\n", "      <td>0.001358</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>595.00</td>\n", "      <td>134309.52500</td>\n", "      <td>1463.08</td>\n", "      <td>OTM</td>\n", "      <td>0.55</td>\n", "      <td>385.00</td>\n", "      <td>210.00</td>\n", "      <td>0.001564</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>520.00</td>\n", "      <td>117610.90000</td>\n", "      <td>2098.30</td>\n", "      <td>OTM</td>\n", "      <td>1.05</td>\n", "      <td>420.00</td>\n", "      <td>100.00</td>\n", "      <td>0.000850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "      <td>220</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>450.00</td>\n", "      <td>178330.50000</td>\n", "      <td>230.79</td>\n", "      <td>OTM</td>\n", "      <td>0.10</td>\n", "      <td>300.00</td>\n", "      <td>150.00</td>\n", "      <td>0.000841</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>1168.75</td>\n", "      <td>108467.56250</td>\n", "      <td>2105.39</td>\n", "      <td>OTM</td>\n", "      <td>2.15</td>\n", "      <td>591.25</td>\n", "      <td>577.50</td>\n", "      <td>0.005324</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>760</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>562.50</td>\n", "      <td>83071.50000</td>\n", "      <td>793.83</td>\n", "      <td>OTM</td>\n", "      <td>0.45</td>\n", "      <td>337.50</td>\n", "      <td>225.00</td>\n", "      <td>0.002709</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "      expiry  strike  lot_size instrument_type  premium_received  \\\n", "0 2025-05-29    1150       625              PE            593.75   \n", "1 2025-05-29    1840       550              PE            605.00   \n", "2 2025-05-29    1380       700              PE            595.00   \n", "3 2025-05-29    2000       400              PE            520.00   \n", "4 2025-05-29     220      3000              PE            450.00   \n", "5 2025-05-29    2000       275              PE           1168.75   \n", "6 2025-05-29     760       750              PE            562.50   \n", "\n", "   total_margin  stock_price_on_exp ITM/OTM  last_price  premium_paid  \\\n", "0  105724.53125             1218.36     OTM        0.50        312.50   \n", "1  141766.76250             1940.43     OTM        0.75        412.50   \n", "2  134309.52500             1463.08     OTM        0.55        385.00   \n", "3  117610.90000             2098.30     OTM        1.05        420.00   \n", "4  178330.50000              230.79     OTM        0.10        300.00   \n", "5  108467.56250             2105.39     OTM        2.15        591.25   \n", "6   83071.50000              793.83     OTM        0.45        337.50   \n", "\n", "   profit_loss     yield  \n", "0       281.25  0.002660  \n", "1       192.50  0.001358  \n", "2       210.00  0.001564  \n", "3       100.00  0.000850  \n", "4       150.00  0.000841  \n", "5       577.50  0.005324  \n", "6       225.00  0.002709  "]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["transaction_sheet.head(8)"]}, {"cell_type": "code", "execution_count": 39, "id": "fae46f79", "metadata": {}, "outputs": [], "source": ["transaction_sheet.to_excel('transaction_sheet.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": 40, "id": "f3229873", "metadata": {}, "outputs": [], "source": ["itm_otm_sheet = transaction_sheet[['name', 'ITM/OTM']]"]}, {"cell_type": "code", "execution_count": 42, "id": "fd586066", "metadata": {}, "outputs": [], "source": ["itm_otm_sheet.to_excel('itm_otm_sheet.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "bb68dd0e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}