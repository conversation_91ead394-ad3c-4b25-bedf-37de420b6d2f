import pandas as pd
import numpy as np
from datetime import datetime
import kite_utils 

from config import TARGET_BANKS


def get_target_equities(instruments_df):
    """
    Filters the master instruments DataFrame for target equities.
    """
    if instruments_df is None or instruments_df.empty:
        return pd.DataFrame()
        
    target_equities_df = instruments_df[
        (instruments_df['instrument_type'] == 'EQ') &
        (instruments_df['segment'] == 'NSE') &
        (instruments_df['name'].isin(TARGET_BANKS)) 
    ].copy()
    print(f"Found {len(target_equities_df)} target equities.")
    return target_equities_df

def get_current_stock_prices(target_equities_df):
    """
    Fetches current market prices for the given target equities.

    Args:
        target_equities_df (pd.DataFrame): DataFrame of target equity instruments.
                                           Expected columns: 'exchange', 'tradingsymbol'.

    Returns:
        pd.DataFrame: DataFrame with 'underlying_symbol' and 'current_stock_price',
                      or an empty DataFrame if fetching fails or no equities provided.
    """
    if target_equities_df.empty:
        print("No target equities provided to fetch prices.")
        return pd.DataFrame()

    equity_instrument_keys = [f"{row['exchange']}:{row['tradingsymbol']}" for _, row in target_equities_df.iterrows()]
    
    print(f"Fetching current prices for: {equity_instrument_keys}")
    current_quotes_dict = kite_utils.get_quotes(equity_instrument_keys)

    if not current_quotes_dict:
        print("Failed to fetch current stock prices for target equities.")
        return pd.DataFrame()
    
    stock_prices_df = pd.DataFrame([
    {
      'instrument': instrument,
      'name': instrument.split(':')[1],
      'average_price': current_quotes_dict[instrument]['average_price'] 
    }
    for instrument in current_quotes_dict.keys()
    ])
    print(f"Shape of stock_prices_df: {stock_prices_df.shape}")

    return stock_prices_df


def get_all_tradable_options_for_targets(instruments_df, target_underlying_symbols):
    """
    Filters instruments_df for all CALL and PUT options for the target underlying symbols
    that have a valid future expiry.

    Args:
        instruments_df (pd.DataFrame): Master instruments DataFrame.
        target_underlying_symbols (list): List of stock symbols (e.g., ['SBIN', 'HDFCBANK']).

    Returns:
        pd.DataFrame: DataFrame of all relevant CALL and PUT options with future nearest expiries.
    """
    if instruments_df is None or instruments_df.empty or not target_underlying_symbols:
        return pd.DataFrame()
    
    options_df = instruments_df[
        instruments_df['name'].isin(target_underlying_symbols) &
        instruments_df['instrument_type'].isin(['CE', 'PE'])
    ].copy()
    print(f"Shape of options_df: {options_df.shape}")

    if options_df.empty:
        print(f"No options found for target symbols: {target_underlying_symbols}")
        return pd.DataFrame()

    # Convert 'expiry' to datetime and filter for future expiries
    options_df['expiry'] = pd.to_datetime(options_df['expiry'], errors='coerce')
    
    current_date = pd.to_datetime(datetime.today().date())
    tradable_options_df = options_df[options_df['expiry'] >= current_date].copy()
    nearest_expiry = tradable_options_df.groupby('name')['expiry'].min().reset_index()
    print(nearest_expiry.head())
    print(f"Shape of nearest_expiry: {nearest_expiry.shape}")
    nearest_expiry_options_df = tradable_options_df.merge(nearest_expiry, on=['name', 'expiry'], how='inner')
    print(f"Found {len(nearest_expiry_options_df)} tradable CE/PE options with nearest expiry.")
    return nearest_expiry_options_df

def find_closest_strike(group):
    instr_type = group['instrument_type'].iloc[0]

    if instr_type == 'CE':
        valid_strikes = group[
            (group['strike'] >= group['target_strike'])
        ]
        if not valid_strikes.empty:
            return valid_strikes.loc[valid_strikes['strike'].idxmin()]
    
    elif instr_type == 'PE':
        valid_strikes = group[
            (group['strike'] <= group['target_strike'])
        ]
        if not valid_strikes.empty:
            return valid_strikes.loc[valid_strikes['strike'].idxmax()]
    
    return pd.Series()


def filter_option_by_strike(nearest_expiry_options_df, current_stock_prices_df, delta=0.05):
    """
    Filters the nearest expiry options DataFrame on the basis of target strike price.
    Target strike price is 5% below the current stock price for PUT options and 5% above for CALL options.
    """
    nearest_expiry_options_df = nearest_expiry_options_df.merge(current_stock_prices_df, on='name', how='inner')
    nearest_expiry_options_df['target_strike'] = np.where(
    nearest_expiry_options_df['instrument_type'] == 'PE',
    nearest_expiry_options_df['average_price'] * (1 - delta),
    nearest_expiry_options_df['average_price'] * (1 + delta)
    )
    print(f"Shape of nearest_expiry_options_df: {nearest_expiry_options_df.shape}")
    filtered_options = nearest_expiry_options_df.groupby(
    ['name', 'instrument_type'], group_keys=False
).apply(find_closest_strike).reset_index(drop=True)
    
    filtered_options.drop(columns=['last_price', 'tick_size', 'segment', 'instrument'], inplace=True)
    print("filtered options after finding closest strike:")
    print(filtered_options.head())
    print(f"Shape of filtered_options: {filtered_options.shape}")

    return filtered_options

def select_options_by_item_otm(filtered_options_df, itm_otm_mapping=None):
    """
    Selects one option (CE or PE) per company based on ITM/OTM mapping.
    
    Args:
        filtered_options_df (pd.DataFrame): DataFrame with both CE and PE rows per 'name'.
        itm_otm_mapping (dict): Optional dict with {'name': 'ITM' or 'OTM'}.
                                   If None, defaults to picking PE for all.

    Returns:
        pd.DataFrame: Filtered DataFrame with one option per company based on moneyness rule.
    """
    def select_row(group):
        name = group['name'].iloc[0]
        preference = itm_otm_mapping.get(name, 'OTM') if itm_otm_mapping else 'OTM'

        if preference == 'ITM':
            return group[group['instrument_type'] == 'CE'].iloc[0]
        else:
            return group[group['instrument_type'] == 'PE'].iloc[0]

    result_df = (
        filtered_options_df
        .groupby('name', group_keys=False)
        .apply(select_row)
        .reset_index(drop=True)
    )

    return result_df


def get_premium_and_margin_details(filtered_options_df):
    """
    Fetches live quotes and calculates premium and margin for the filtered options.
    """
    kite = kite_utils.init_kite()
    # Step 1: Build instrument key
    filtered_options_df = filtered_options_df.copy()
    filtered_options_df['instrument_key'] = filtered_options_df.apply(
        lambda row: f"{row['exchange']}:{row['tradingsymbol']}", axis=1
    )
    
    # Step 2: Fetch live quotes
    instrument_keys = filtered_options_df['instrument_key'].tolist()
    quote_data = kite.quote(instrument_keys)
    
    quote_df = pd.DataFrame([
        {
            'instrument_key': key,
            'last_price': quote_data[key]['last_price']
        }
        for key in quote_data
    ])
    print(f"Shape of quote_df: {quote_df.shape}")
    print(f"shape of filtered_options_df: {filtered_options_df.shape}")
    
    # Step 3: Merge quotes and calculate premium
    filtered_options_df = filtered_options_df.merge(quote_df, on='instrument_key', how='inner')
    filtered_options_df['premium_received'] = filtered_options_df['last_price'] * filtered_options_df['lot_size']
    
    # Step 4: Build orders list for margin API
    base_order = {
        "exchange": "NFO",
        "transaction_type": "SELL",
        "variety": "regular",
        "product": "NRML",
        "order_type": "MARKET",
        "price": 0,
        "trigger_price": 0
    }
    ce_df = filtered_options_df[filtered_options_df['instrument_type'] == 'CE']
    pe_df = filtered_options_df[filtered_options_df['instrument_type'] == 'PE']
    
    orders_ce = [
        {
            **base_order,
            "tradingsymbol": row["tradingsymbol"],
            "quantity": row["lot_size"]
        }
        for _, row in ce_df.iterrows()
    ]

    orders_pe = [
        {
            **base_order,
            "tradingsymbol": row["tradingsymbol"],
            "quantity": row["lot_size"]
        }
        for _, row in pe_df.iterrows()
    ]
    
    # Step 5: Fetch margin info
    margins_response_ce = kite.order_margins(params=orders_ce) if orders_ce else []
    margins_response_pe = kite.order_margins(params=orders_pe) if orders_pe else []
    margins_response = margins_response_ce + margins_response_pe
    
    margin_df = pd.DataFrame([
        {
            'tradingsymbol': item['tradingsymbol'],
            'total_margin': item['total'],
            'span': item.get('span'),
            'exposure': item.get('exposure')
        }
        for item in margins_response
    ])
    print(f"Shape of margin_df: {margin_df.shape}")
    
    # Step 6: Merge margin info and return
    filtered_options_df = filtered_options_df.merge(margin_df[['tradingsymbol', 'total_margin']], on='tradingsymbol', how='inner')
    filtered_options_df.drop(columns=['instrument_key'], inplace=True)

    return filtered_options_df


if __name__ == "__main__":
    instruments_df = kite_utils.update_instruments()
    target_equities_df = get_target_equities(instruments_df)
    current_stock_prices_df = get_current_stock_prices(target_equities_df)
    target_underlying_symbols = current_stock_prices_df['name'].tolist()
    print("target underlying symbols: ", target_underlying_symbols)
    nearest_expiry_options_df = get_all_tradable_options_for_targets(instruments_df, target_underlying_symbols)
    filtered_options_df = filter_option_by_strike(nearest_expiry_options_df, current_stock_prices_df)
    #filtered_options_df = select_options_by_item_otm(filtered_options_df)
    final_options_df = get_premium_and_margin_details(filtered_options_df)
    final_options_df.to_csv('final_options.csv', index=False)
    print(final_options_df.head(7))




