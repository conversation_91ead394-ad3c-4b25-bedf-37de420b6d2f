from openai import OpenAI
import json
import pandas as pd
import numpy as np

api_key = "********************************************************************************************************************************************************************"

client = OpenAI(api_key=api_key)

sec_data_retrieval_prompt = """\
You are an expert in extracting information from SEC filings of companies.
The company of interest is {company_name}. You need to extract all the information related to following fields from the SEC filings of the {company_name} and return the output in defined JSON format:
1. Plans regarding manufacturing capacity expansion or new plant openings in the United States as per the SEC filings of the {company_name} from January 1, 2024, through today.

2. Employment generation or hiring commitments of the {company_name} in the United States as per it's SEC filings from January 1, 2024, through today.

3. Actual production output (units or volumes produced) per quarter of the {company_name} in the United States as reported in it's SEC filings from January 1, 2024, through today.

While answering, please adhere strictly to these rules:

1. Web search:
   - Search only the Official SEC filings (10-K/10-Q/8-K) of the target company to gather authoritative sources.
   - Do not search any other source apart from the official SEC filings.

2. Plan Identification:
   2a. Identify Capacity-Expansion Plans: 
         - Locate statements or paragraphs in the SEC filings describing intentions, plans, or commitments to:
            - Expand existing factories in the United States
            - Build or commission new manufacturing facilities in the United States.
         - Quantify Every Plan:
            - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the SEC filings. (e.g., "...investing $X billion to expand factory", "...setting up a new Y sq ft facility" etc.)
   2b. Identify Employment-Generation Plans:  
         - Locate any statements in the SEC filings describing hiring targets, job-creation commitments, or intended workforce increases in the United States.  
         - Quantify by citing numeric metrics if available in the SEC filings.(e.g., "1,000 new employees", "2,500 jobs over three years" etc.).
   2c. Identify Actual Production Output:
         - Locate quarterly production output metrics in the United States, disclosed in the SEC filings from January 1, 2024, through today.
         - Extract details such as number of units produced, volume of goods manufactured, or any other production quantity disclosed on a per-quarter basis.

3. Time-Frame Filter:
   - Include only those sources dated on or after January 1, 2024, up to the current date.
   - Exclude any sources from earlier periods.

4. Output Structure:
   - Present your findings in JSON format with following fields:
     - `company`: name of the target company.
     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, publishing_agency, title, section, url).
     - `employment_generation_plans`: list of objects with `plan_details` regarding employment generation, `source` (publishing_date, publishing_agency, title, section, url).
     - `production_output`: list of objects with `output_details` (e.g., units/volume produced), `source` (publishing_date, publishing_agency, title, section, url).
     - `summary`: a concise overview of `plan_details`from both `capacity_expansion_plans` and `employment_generation_plans`, and  `output_details` from `production_output`.
   - Order `plan_details` chronologically (earliest → latest).
"""

output_format_sec = {
    "format": {
        "type": "json_schema",
        "name": "company_expansion_details",
        "schema": {
            "type": "object",
            "properties": {
                "company":{
                    "type": "string",
                    "description": "Name of the company about which the information is being asked by the user."
                },
                "capacity_expansion_plans": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "plan_details": {
                                "type": "string",
                                "description": "Manufacturing capacity expansion or new plant opening plan details of the company in the United States as per the SEC filings (10-K/10-Q/8-K)."
                            },
                            "source": {
                                "type": "object",
                                "properties": {
                                    "publishing_date": {
                                        "type": "string",
                                        "description": "Date when the SEC filing (10-K/10-Q/8-K) was filed by the company (YYYY-MM-DD)"
                                    },
                                    "publishing_agency": {
                                        "type": "string",
                                        "description": "Name of the publishing agency or website"
                                    },
                                    "title": {
                                        "type": "string",
                                        "description": "Title of the SEC filing (10-K/10-Q/8-K)"
                                    },
                                    "section": {
                                        "type": "string",
                                        "description": "Section or heading within the SEC filing where the excerpt is found."
                                    },
                                    "url": {
                                        "type": "string",
                                        "description": "URL of the source"
                                    }
                                },
                                "required": [
                                    "publishing_date",
                                    "publishing_agency",
                                    "title",
                                    "section",
                                    "url"
                                ],
                                "additionalProperties": False
                            }
                        },
                        "required": [
                            "plan_details",
                            "source"
                        ],
                        "additionalProperties": False
                    }
                },
                "employment_generation_plans": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "plan_details": {
                                "type": "string",
                                "description": "Employment generation plan details of the company in the United States as per the SEC filings (10-K/10-Q/8-K)."
                            },
                            "source": {
                                "type": "object",
                                "properties": {
                                    "publishing_date": {
                                        "type": "string",
                                        "description": "Date when the SEC filing was published (YYYY-MM-DD)"
                                    },
                                    "publishing_agency": {
                                        "type": "string",
                                        "description": "Name of the publishing agency or website"
                                    },
                                    "title": {
                                        "type": "string",
                                        "description": "Title of the SEC filing (10-K/10-Q/8-K)"
                                    },
                                    "section": {
                                        "type": "string",
                                        "description": "Section or heading within the SEC filing where the excerpt is found."
                                    },
                                    "url": {
                                        "type": "string",
                                        "description": "URL of the source"
                                    }
                                },
                                "required": [
                                    "publishing_date",
                                    "publishing_agency",
                                    "title",
                                    "section",
                                    "url"
                                ],
                                "additionalProperties": False
                            }
                        },
                        "required": [
                            "plan_details",
                            "source"
                        ],
                        "additionalProperties": False
                    }
                },
                "production_output": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "output_details": {
                                "type": "string",
                                "description": "Actual production output (units or volumes produced) per quarter of the company in the United States as reported in SEC filings."
                            },
                            "source": {
                                "type": "object",
                                "properties": {
                                    "publishing_date": {
                                        "type": "string",
                                        "description": "Date when the SEC filing was published (YYYY-MM-DD)"
                                    },
                                    "publishing_agency": {
                                        "type": "string",
                                        "description": "Name of the publishing agency or website"
                                    },
                                    "title": {
                                        "type": "string",
                                        "description": "Title of the SEC filing (10-K/10-Q/8-K)"
                                    },
                                    "section": {
                                        "type": "string",
                                        "description": "Section or heading within the SEC filing where the excerpt is found."
                                    },
                                    "url": {
                                        "type": "string",
                                        "description": "URL of the source"
                                    }
                                },
                                "required": [
                                    "publishing_date",
                                    "publishing_agency",
                                    "title",
                                    "section",
                                    "url"
                                ],
                                "additionalProperties": False
                            }
                        },
                        "required": [
                            "output_details",
                            "source"
                        ],
                        "additionalProperties": False
                    }
                },
                "summary": {
                    "type": "string",
                    "description": "Short overview of all plan details and output details."
                }
            },
            "required": ["company","capacity_expansion_plans","employment_generation_plans", "production_output",
                "summary"
            ],
            "additionalProperties": False
        }
    }
}

user_prompt_other_sources = """\
You are a research assistant with access to web-search.
The company of interest is {company_name}. You need to extract all information related to following fields from relevant web sources and return the output in defined JSON format:
1. Plans regarding manufacturing capacity expansion or new plant openings of the {company_name} in the United States from relevant web sources, covering the period from January 1, 2024, to the present.

2. Employment generation or hiring commitments of the {company_name} in the United States from relevant web sources, covering the period from January 1, 2024, to the present.

3. Actual production output (units or volumes produced) per quarter of the {company_name} in the United States as per the relevant web sources, covering the period from January 1, 2024, to the present.
 
When responding, adhere strictly to these rules:

1. Web search: 
   - Search the company's press releases, relevant websites, news articles etc. to gather authoritative sources.

2. Plan Identification:
   2a. Identify Capacity-Expansion Plans: 
         - Locate statements or paragraphs describing intentions, plans, or commitments to:
            - Expand existing factories in the United States.
            - Build or commission new manufacturing facilities in the United States.
         - Quantify Every Plan:
            - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures. (e.g., "...investing $X billion to expand factory", "...setting up a new Y sq ft facility" etc.)
   2b. Identify Employment-Generation Plans:  
         - Locate any statements in the sources describing hiring targets, job-creation commitments, or intended workforce increases in the United States.  
         - Quantify by citing numeric metrics (e.g., "1,000 new employees", "2,500 jobs over three years" etc.).
   2c. Identify Actual Production Output:
         - Locate reported or disclosed production output metrics by the company per quarter in the United States, covering the period from January 1, 2024, to the present.
         - Extract information such as number of units produced, volume of goods manufactured, or other quarterly production quantity data disclosed in press releases, earnings calls or news articles etc.

4. Time-Frame Filter: 
   - Include only sources dated on or after 2024-01-01, up to today's date.  
   - Exclude any sources published before 2024-01-01.

5. Restrict to Company's Own Data: 
   - Only include target company's own plans to increase its manufacturing capacity or opening a new plant, employment generation plans and production output.\
     Do not include any supplier, vendor, or third‑party manufacturing partner plans or information.

6. Output Structure:
   - Return results in JSON with these fields:
     - `company`: name of the target company.
     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, publishing_agency, title, section, url), and `confidence_score`.
     - `employment_generation_plans`: list of objects with `plan_details` regarding employment generation, `source` (publishing_date, publishing_agency, title, section, url), and `confidence_score`.  
     - `production_output`: list of objects with `output_details` (e.g., units/volume produced), `source` (publishing_date, publishing_agency, title, section, url) and `confidence_score`.
     - `summary`: a concise overview of `plan_details`from both `capacity_expansion_plans` and `employment_generation_plans`, and `output_details` from `production_output`.
   - Order `plan_details` chronologically (earliest → latest).

7. Confidence Score Calculation: 
   - A score between 0 and 0.9 suggesting confidence in the information given in plan details and source, calculated as follows:  
      - Source Reliability (0.0–0.4):  
         0.4=Company's official press release; 0.3=Tier-1 industry press; 0.2=Blog/aggregator; 0.0=Unverified forum/social media  
      - Specificity Score(0.0–0.5):  
         0.5=Direct management quote with numeric metric; 0.4=Exact excerpt paraphrased; 0.3=Summary statement without numbers; 0.2=Analyst commentary; 0.0= pure Speculation  
      - `confidence_score = Source Reliability + Specificity Score`
"""

output_format_other_sources = {
    "format": {
        "type": "json_schema",
        "name": "company_expansion_details",
        "schema": {
            "type": "object",
            "properties": {
                "company":{
                    "type": "string",
                    "description": "Name of the company about which the information is being asked by the user."
                },
                "capacity_expansion_plans": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "plan_details": {
                                "type": "string",
                                "description": "Capacity expansion or new plant opening plan details of the company under consideration in the United States as per the found sources."
                            },
                            "source": {
                                "type": "object",
                                "properties": {
                                    "publishing_date": {
                                        "type": "string",
                                        "description": "Date when the source was published (YYYY-MM-DD)"
                                    },
                                    "publishing_agency": {
                                        "type": "string",
                                        "description": "Name of the agency or website"
                                    },
                                    "title": {
                                        "type": "string",
                                        "description": "Title of the document or article"
                                    },
                                    "section": {
                                        "type": "string",
                                        "description": "Section or heading within the source where the excerpt is found."
                                    },
                                    "url": {
                                        "type": "string",
                                        "description": "URL of the source"
                                    }
                                },
                                "required": [
                                    "publishing_date",
                                    "publishing_agency",
                                    "title",
                                    "section",
                                    "url"
                                ],
                                "additionalProperties": False
                            },
                            "confidence_score": {
                                "type": "number",
                                "description": "A score between 0 and 0.9 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5)."
                            }
                        },
                        "required": [
                            "plan_details",
                            "source",
                            "confidence_score"
                        ],
                        "additionalProperties": False
                    }
                },
                "employment_generation_plans": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "plan_details": {
                                "type": "string",
                                "description": "Employment generation plan details of the company under consideration in the United States as per the found sources."
                            },
                            "source": {
                                "type": "object",
                                "properties": {
                                    "publishing_date": {
                                        "type": "string",
                                        "description": "Date when the source was published (YYYY-MM-DD)"
                                    },
                                    "publishing_agency": {
                                        "type": "string",
                                        "description": "Name of the agency or website"
                                    },
                                    "title": {
                                        "type": "string",
                                        "description": "Title of the document or article"
                                    },
                                    "section": {
                                        "type": "string",
                                        "description": "Section or heading within the source where the excerpt is found."
                                    },
                                    "url": {
                                        "type": "string",
                                        "description": "URL of the source"
                                    }
                                },
                                "required": [
                                    "publishing_date",
                                    "publishing_agency",
                                    "title",
                                    "section",
                                    "url"
                                ],
                                "additionalProperties": False
                            },
                            "confidence_score": {
                                "type": "number",
                                "description": "A score between 0 and 1 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5)."
                            }
                        },
                        "required": [
                            "plan_details",
                            "source",
                            "confidence_score"
                        ],
                        "additionalProperties": False
                    }
                },
                "production_output": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "output_details": {
                                "type": "string",
                                "description": "Actual production output (units or volumes produced) per quarter of the company in the United States as reported in relevant web sources."
                            },
                            "source": {
                                "type": "object",
                                "properties": {
                                    "publishing_date": {
                                        "type": "string",
                                        "description": "Date when the source was published (YYYY-MM-DD)"
                                    },
                                    "publishing_agency": {
                                        "type": "string",
                                        "description": "Name of the agency or website"
                                    },
                                    "title": {
                                        "type": "string",
                                        "description": "Title of the document or article"
                                    },
                                    "section": {
                                        "type": "string",
                                        "description": "Section or heading within the source where the excerpt is found."
                                    },
                                    "url": {
                                        "type": "string",
                                        "description": "URL of the source"
                                    }
                                },
                                "required": [
                                    "publishing_date",
                                    "publishing_agency",
                                    "title",
                                    "section",
                                    "url"
                                ],
                                "additionalProperties": False
                            },
                            "confidence_score": {
                                "type": "number",
                                "description": "A score between 0 and 1 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5)."
                            }
                        },
                        "required": [
                            "output_details",
                            "source",
                            "confidence_score"
                        ],
                        "additionalProperties": False
                    }
                },
                "summary": {
                    "type": "string",
                    "description": "Short overview of all plan details and production output details."
                }
            },
            "required": ["company","capacity_expansion_plans","employment_generation_plans", "production_output",
                "summary"
            ],
            "additionalProperties": False
        }
    }
}

response_other = client.responses.create(
            model="gpt-4.1",
            tools=[{
                    "type": "web_search_preview",
                    "search_context_size": "high",
            }],
            input=user_prompt_other_sources.format(company_name = "TESLA"),
            text=output_format_other_sources
        )

data_other = json.loads(response_other.output_text)

print(json.dumps(data_other, indent=4))

def all_company_details(company_names:list[str]) -> tuple[list[dict], float, float, float]:
    company_results = []
    INPUT_COST_PER_TOKEN = 2.00 / 1_000_000
    OUTPUT_COST_PER_TOKEN = 8.00 / 1_000_000
    WEB_SEARCH_COST_PER_CALL = 50.00 / 1_000  
    sec_cost = 0
    other_cost = 0
    for company_name in company_names:
        # SEC data retrieval
        response_sec = client.responses.create(
            model="gpt-4.1",
            tools=[{
                    "type": "web_search_preview",
                    "search_context_size": "high",
                }],
            input=sec_data_retrieval_prompt.format(company_name = company_name),
            text=output_format_sec
        )
        # Cost calculation
        input_tokens_sec = response_sec.usage.input_tokens
        output_tokens_sec = response_sec.usage.output_tokens
        token_cost_sec = (input_tokens_sec * INPUT_COST_PER_TOKEN) + (output_tokens_sec * OUTPUT_COST_PER_TOKEN)
        total_cost_sec = token_cost_sec + WEB_SEARCH_COST_PER_CALL
        sec_cost += total_cost_sec

        data_sec = json.loads(response_sec.output_text)
        # Assinging confidence score of 0.9
        for plan in data_sec["capacity_expansion_plans"]:
            plan["confidence_score"] = 0.9

        for plan in data_sec["employment_generation_plans"]:
            plan["confidence_score"] = 0.9

        for output in data_sec["production_output"]:
            output["confidence_score"] = 0.9
        
        # Other sources data retrieval
        response_other = client.responses.create(
            model="gpt-4.1",
            tools=[{
                    "type": "web_search_preview",
                    "search_context_size": "high",
            }],
            input=user_prompt_other_sources.format(company_name = company_name),
            text=output_format_other_sources
        )
        # Cost calculation
        input_tokens_other = response_other.usage.input_tokens
        output_tokens_other = response_other.usage.output_tokens
        token_cost_other = (input_tokens_other * INPUT_COST_PER_TOKEN) + (output_tokens_other * OUTPUT_COST_PER_TOKEN)
        total_cost_other = token_cost_other + WEB_SEARCH_COST_PER_CALL
        other_cost += total_cost_other

        data_other = json.loads(response_other.output_text)
        # appending other sources data to SEC data to make final data
        for plan in data_other["capacity_expansion_plans"]:
            data_sec["capacity_expansion_plans"].append(plan)

        for plan in data_other["employment_generation_plans"]:
            data_sec["employment_generation_plans"].append(plan)

        for output in data_other["production_output"]:
            data_sec["production_output"].append(output)

        data_sec["summary"]=data_sec["summary"]+' '+data_other["summary"]
        # final data
        company_results.append(data_sec)
    return company_results, sec_cost, other_cost, sec_cost + other_cost


manufac_companies = ['Tesla', 'General Motors', 'Ford Motors', 'Rivian Automotive', 'Lucid Group', 'Oshkosh Corp.', 'Harley-Davidson', 'REV Group', 'Blue Bird Corp.', 'Miller Industries']

manuf_comp_data, sec_cost, other_cost, total_cost = all_company_details(manufac_companies)

len(manuf_comp_data)

import json

with open("manuf_company_results_2.json", "w") as f:
    json.dump(manuf_comp_data, f, indent=4)



scores_system_prompt = """\
You are an expert research analyst evaluating and scoring manufacturing companies based on their manufacturing capacity expansion plans or new plant opening plans, employment generation plans, and actual production output in the United States. You have been provided with a structured JSON list containing details for multiple companies.

For each company:
1. Thoroughly analyze the `capacity_expansion_plans`, `employment_generation_plans`, and `production_output` fields in in the input structured JSON.
2. Consider all `plan_details` from `capacity_expansion_plans`, `employment_generation_plans`, `output_details` from `production_output`, and their corresponding `confidence_score`s while assessing each company.

Assign the following scores to each company in a comparative manner on a scale from 0 to 10:
- `capacity_expansion_score`: Based on the magnitude of manufacturing capacity expansion or new manufacturing facilities of companies. The magnitude can be dollar value or in terms of units of production or other relevant figures. Give more weightage to specific, quantifiable plans as compared to vague, non-specific information.
- `employment_generation_score`: Based on the number of new job creation or hiring plans of the respective companies.
- `production_output_score`: Based on the level and trend of actual production output (in units or volume) over the quarters of respective companies. Use the following rules while assigning production_output_score to companies:
    a. If production output data is available for only one, two or three quarters, make an educated estimate of the annual output based on the available data, assuming consistent production unless indicated otherwise.
    b. When comparing companies for production output scoring, compare only similar types of commodities within the same primary product category. Focus on the company's core manufacturing output — i.e., its primary product line. For instance, Company X's annual car production can be compared with Company Y's motorcycle output, as both fall under the broad 'vehicle' category. However, X's energy storage production should not be compared with vehicles or any other unrelated product categories from other companies. Always align comparisons based on the principal manufactured product of each company.

Output structure:
- Return the output in json format with following schema:
    - "companies": list of objects with following fields:
        - "company": name of the company.
        - "scores": list of objects with "capacity_expansion_score", "employment_generation_score", "production_output_score".
        - "Rationale": Rationale explaining the assigned scores.
"""


scores_output_format = {
    "format": {
        "type": "json_schema",
        "name": "company_scores",
        "schema": {
            "type": "object",
            "properties": {
                "companies": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "company": {
                                "type": "string",
                                "description": "Name of the company being evaluated."
                            },
                            "scores": {
                                "type": "object",
                                "properties": {
                                    "capacity_expansion_score": {
                                        "type": "number",
                                        "description": "Score (0–10) based on magnitude of capacity expansion or new plant opening."
                                    },
                                    "employment_generation_score": {
                                        "type": "number",
                                        "description": "Score (0–10) based on the scale of employment generation or hiring."
                                    },
                                    "production_output_score": {
                                        "type": "number",
                                        "description": "Score (0–10) based on the level and trend of actual production output."
                                    }
                                },
                                "required": [
                                    "capacity_expansion_score",
                                    "employment_generation_score",
                                    "production_output_score"
                                ],
                                "additionalProperties": False
                            },
                            "capacity_expansion_rationale": {
                                "type": "string",
                                "description": "Explanation of how the capacity_expansion_score was determined, referencing plan details and confidence scores."
                            },
                            "employment_generation_rationale": {
                                "type": "string",
                                "description": "Explanation of how the employment_generation_score was determined, referencing plan details and confidence scores."
                            },
                            "production_output_rationale": {
                                "type": "string",
                                "description": "Explanation of how the production_output_score was determined, referencing output details and confidence scores."
                            }
                        },
                        "required": [
                            "company",
                            "scores",
                            "capacity_expansion_rationale",
                            "employment_generation_rationale",
                            "production_output_rationale"
                        ],
                        "additionalProperties": False
                    }
                }
            },
            "required": [
                "companies"
            ],
            "additionalProperties": False
        }
    }
}

with open("manuf_company_results.json", "r") as f:
    manuf_comp_data_old = json.load(f)



len(manuf_comp_data_old)

response = client.responses.create(
            model="gpt-4.1",
            # reasoning={"effort": "high"},
            input=[
                {"role": "system", "content": scores_system_prompt},
                {"role": "user", "content": json.dumps(manuf_comp_data_old) }
            ],
            temperature=0,
            text=scores_output_format
        )

INPUT_COST_PER_TOKEN = 1.10 / 1_000_000
OUTPUT_COST_PER_TOKEN = 4.40 / 1_000_000

input_tokens = response.usage.input_tokens
output_tokens = response.usage.output_tokens
total_scoring_cost = (input_tokens * INPUT_COST_PER_TOKEN) + (output_tokens * OUTPUT_COST_PER_TOKEN)




input_tokens

final_result = json.loads(response.output_text)
print(json.dumps(final_result, indent=4))

import pandas as pd

companies_data = final_result["companies"]

df = pd.DataFrame([
    {
        "company": entry["company"],
        "capacity_expansion_score": entry["scores"]["capacity_expansion_score"],
        "employment_generation_score": entry["scores"]["employment_generation_score"],
        "production_output_score": entry["scores"]["production_output_score"],
        "capacity_expansion_rationale": entry["capacity_expansion_rationale"],
        "employment_generation_rationale": entry["employment_generation_rationale"],
        "production_output_rationale": entry["production_output_rationale"]
    }
    for entry in companies_data
])



df.head(10)

df.to_excel("manuf_score_4.1_turn3.xlsx", index=False)

print(f"""sec_data_cost: {sec_cost*85.52},
       \n other_data_cost: {other_cost*85.52},
       \n total_data_cost: {total_cost*85.52},
        \ntotal_scoring_cost: {total_scoring_cost*85.52}""")