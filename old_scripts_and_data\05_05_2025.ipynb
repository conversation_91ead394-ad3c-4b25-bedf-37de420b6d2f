{"cells": [{"cell_type": "code", "execution_count": 1, "id": "eac3e855", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Load the FactSet to NAICS mapping\n", "factset_to_naics = pd.read_excel('Factset_to_Naics.xlsx')\n", "\n", "# Load the NAICS to BEA scores\n", "naics_bea_scores = pd.read_excel('Combined_NAICS_BEA_Scores_new.xlsx')"]}, {"cell_type": "code", "execution_count": 2, "id": "5a99e64f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "      <th>NAICS Code</th>\n", "      <th>NAICS Title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3311</td>\n", "      <td>Iron and Steel Mills and Ferroalloy Manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3315</td>\n", "      <td>Foundries</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3312</td>\n", "      <td>Steel Product Manufacturing from Purchased S...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>3313</td>\n", "      <td>Alumina and Aluminum Production and Processing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>2122</td>\n", "      <td>Metal Ore Mining</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Subgroup Code   Subgroup Title  NAICS Code  \\\n", "0           1105            Steel        3311   \n", "1           1105            Steel        3315   \n", "2           1105            Steel        3312   \n", "3           1115         Aluminum        3313   \n", "4           1120  Precious Metals        2122   \n", "\n", "                                         NAICS Title  \n", "0  Iron and Steel Mills and Ferroalloy Manufacturing  \n", "1                                          Foundries  \n", "2    Steel Product Manufacturing from Purchased S...  \n", "3     Alumina and Aluminum Production and Processing  \n", "4                                   Metal Ore Mining  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["factset_to_naics.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "34c28ee5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.498296   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0                  0.5                0.115238          0.385857  \n", "1                  0.5                0.159188          0.438410  \n", "2                  0.5                0.153765          0.354900  \n", "3                  0.5                0.167181          0.458670  \n", "4                  0.5                0.117253          0.458316  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["naics_bea_scores.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "4b40b6bf", "metadata": {}, "outputs": [], "source": ["factset_to_naics.rename(columns={'NAICS Code': 'NAICS_code'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "490d642d", "metadata": {}, "outputs": [], "source": ["# Merge DataFrames\n", "merged_df = pd.merge(factset_to_naics, naics_bea_scores, \n", "                     on='NAICS_code', \n", "                     how='inner')\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "cb9a281c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS Title</th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3311</td>\n", "      <td>Iron and Steel Mills and Ferroalloy Manufacturing</td>\n", "      <td>3311IS</td>\n", "      <td>Iron and steel mills and manufacturing from pu...</td>\n", "      <td>Iron and steel mills and ferroalloy manufacturing</td>\n", "      <td>0.5</td>\n", "      <td>0.031477</td>\n", "      <td>0.215054</td>\n", "      <td>0.346703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3315</td>\n", "      <td>Foundries</td>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>Foundries</td>\n", "      <td>0.5</td>\n", "      <td>0.024801</td>\n", "      <td>0.114993</td>\n", "      <td>0.308566</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3312</td>\n", "      <td>Steel Product Manufacturing from Purchased S...</td>\n", "      <td>3311IS</td>\n", "      <td>Iron and steel mills and manufacturing from pu...</td>\n", "      <td>Steel product manufacturing from purchased steel</td>\n", "      <td>0.5</td>\n", "      <td>0.031477</td>\n", "      <td>0.123062</td>\n", "      <td>0.301470</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>3313</td>\n", "      <td>Alumina and Aluminum Production and Processing</td>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>ALUMINA &amp; ALUMINUM  &amp; PROCESSING</td>\n", "      <td>0.5</td>\n", "      <td>0.024801</td>\n", "      <td>0.165602</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>3314</td>\n", "      <td>Nonferrous Metal (except Aluminum) Production ...</td>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>NONFERROUS (EXC ALUM) &amp; PROCESSING</td>\n", "      <td>0.5</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Subgroup Code   Subgroup Title  NAICS_code  \\\n", "0           1105            Steel        3311   \n", "1           1105            Steel        3315   \n", "2           1105            Steel        3312   \n", "3           1115         Aluminum        3313   \n", "4           1120  Precious Metals        3314   \n", "\n", "                                         NAICS Title BEA_code  \\\n", "0  Iron and Steel Mills and Ferroalloy Manufacturing   3311IS   \n", "1                                          Foundries   3313NF   \n", "2    Steel Product Manufacturing from Purchased S...   3311IS   \n", "3     Alumina and Aluminum Production and Processing   3313NF   \n", "4  Nonferrous Metal (except Aluminum) Production ...   3313NF   \n", "\n", "                            BEA_industry_description  \\\n", "0  Iron and steel mills and manufacturing from pu...   \n", "1  Nonferrous metal production and processing and...   \n", "2  Iron and steel mills and manufacturing from pu...   \n", "3  Nonferrous metal production and processing and...   \n", "4  Nonferrous metal production and processing and...   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0  Iron and steel mills and ferroalloy manufacturing                 0.5   \n", "1                                          Foundries                 0.5   \n", "2   Steel product manufacturing from purchased steel                 0.5   \n", "3                   ALUMINA & ALUMINUM  & PROCESSING                 0.5   \n", "4                 NONFERROUS (EXC ALUM) & PROCESSING                 0.5   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0             0.031477                0.215054          0.346703  \n", "1             0.024801                0.114993          0.308566  \n", "2             0.031477                0.123062          0.301470  \n", "3             0.024801                0.165602          0.500000  \n", "4             0.024801                0.312600          0.500000  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display the first few rows to verify\n", "\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "f170e989", "metadata": {}, "outputs": [{"data": {"text/plain": ["89"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(merged_df)"]}, {"cell_type": "code", "execution_count": null, "id": "f35f1411", "metadata": {}, "outputs": [], "source": ["#merged_df.to_excel('Factset_to_naics_BEA_Scores.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "55dfc1f4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS Title</th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3311</td>\n", "      <td>Iron and Steel Mills and Ferroalloy Manufacturing</td>\n", "      <td>3311IS</td>\n", "      <td>Iron and steel mills and manufacturing from pu...</td>\n", "      <td>0.5</td>\n", "      <td>0.031477</td>\n", "      <td>0.215054</td>\n", "      <td>0.346703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3315</td>\n", "      <td>Foundries</td>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>0.5</td>\n", "      <td>0.024801</td>\n", "      <td>0.114993</td>\n", "      <td>0.308566</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>3312</td>\n", "      <td>Steel Product Manufacturing from Purchased S...</td>\n", "      <td>3311IS</td>\n", "      <td>Iron and steel mills and manufacturing from pu...</td>\n", "      <td>0.5</td>\n", "      <td>0.031477</td>\n", "      <td>0.123062</td>\n", "      <td>0.301470</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>3313</td>\n", "      <td>Alumina and Aluminum Production and Processing</td>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>0.5</td>\n", "      <td>0.024801</td>\n", "      <td>0.165602</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>3314</td>\n", "      <td>Nonferrous Metal (except Aluminum) Production ...</td>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>0.5</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Subgroup Code   Subgroup Title  NAICS_code  \\\n", "0           1105            Steel        3311   \n", "1           1105            Steel        3315   \n", "2           1105            Steel        3312   \n", "3           1115         Aluminum        3313   \n", "4           1120  Precious Metals        3314   \n", "\n", "                                         NAICS Title BEA_code  \\\n", "0  Iron and Steel Mills and Ferroalloy Manufacturing   3311IS   \n", "1                                          Foundries   3313NF   \n", "2    Steel Product Manufacturing from Purchased S...   3311IS   \n", "3     Alumina and Aluminum Production and Processing   3313NF   \n", "4  Nonferrous Metal (except Aluminum) Production ...   3313NF   \n", "\n", "                            BEA_industry_description  Fixed_Assets_Score  \\\n", "0  Iron and steel mills and manufacturing from pu...                 0.5   \n", "1  Nonferrous metal production and processing and...                 0.5   \n", "2  Iron and steel mills and manufacturing from pu...                 0.5   \n", "3  Nonferrous metal production and processing and...                 0.5   \n", "4  Nonferrous metal production and processing and...                 0.5   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0             0.031477                0.215054          0.346703  \n", "1             0.024801                0.114993          0.308566  \n", "2             0.031477                0.123062          0.301470  \n", "3             0.024801                0.165602          0.500000  \n", "4             0.024801                0.312600          0.500000  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df = merged_df.drop(columns=['NAICS_industry_description'])\n", "\n", "merged_df.head()\n"]}, {"cell_type": "code", "execution_count": 9, "id": "876d0b3e", "metadata": {}, "outputs": [], "source": ["score_columns = ['Fixed_Assets_Score', 'Economic_Heft_Score', \n", "                 'Import_Intensity_Score', 'Employment_Score']\n", "\n", "factset_scores = merged_df.groupby(['Subgroup Code', 'Subgroup Title'])[score_columns].mean().reset_index()"]}, {"cell_type": "code", "execution_count": 10, "id": "86a59695", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>0.500000</td>\n", "      <td>0.029251</td>\n", "      <td>0.151036</td>\n", "      <td>0.318913</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.165602</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>0.392267</td>\n", "      <td>0.169042</td>\n", "      <td>0.133481</td>\n", "      <td>0.267790</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Subgroup Code         Subgroup Title  Fixed_Assets_Score  \\\n", "0           1105                  Steel            0.500000   \n", "1           1115               Aluminum            0.500000   \n", "2           1120        Precious Metals            0.500000   \n", "3           1125  Other Metals/Minerals            0.500000   \n", "4           1130        Forest Products            0.392267   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0             0.029251                0.151036          0.318913  \n", "1             0.024801                0.165602          0.500000  \n", "2             0.024801                0.312600          0.500000  \n", "3             0.024801                0.312600          0.500000  \n", "4             0.169042                0.133481          0.267790  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["factset_scores.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "03c8b053", "metadata": {}, "outputs": [{"data": {"text/plain": ["53"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(factset_scores)"]}, {"cell_type": "code", "execution_count": 12, "id": "f8f4e55c", "metadata": {}, "outputs": [], "source": ["def get_naics_info(group):\n", "    # Get unique NAICS code and title pairs\n", "    naics_list = group[['NAICS_code', 'NAICS Title']].drop_duplicates().values\n", "    # Format each pair as \"NAICS code, title\"\n", "    naics_strings = [f\"{code}, {title}\" for code, title in naics_list]\n", "    # Join with semicolons\n", "    return '; '.join(naics_strings)"]}, {"cell_type": "code", "execution_count": 13, "id": "f1be7292", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13376\\1821853730.py:1: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  naics_info = merged_df.groupby(['Subgroup Code', 'Subgroup Title']).apply(get_naics_info).reset_index(name='NAICS_Info')\n"]}], "source": ["naics_info = merged_df.groupby(['Subgroup Code', 'Subgroup Title']).apply(get_naics_info).reset_index(name='NAICS_Info')"]}, {"cell_type": "code", "execution_count": 14, "id": "e65129c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["53"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(naics_info)"]}, {"cell_type": "code", "execution_count": 15, "id": "ef59a3e3", "metadata": {}, "outputs": [], "source": ["factset_scores = pd.merge(factset_scores, naics_info, on=['Subgroup Code', 'Subgroup Title'], how='left')"]}, {"cell_type": "code", "execution_count": 16, "id": "2b1a53fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["53"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["len(factset_scores)\n"]}, {"cell_type": "code", "execution_count": 17, "id": "51b14e28", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>NAICS_Info</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>0.500000</td>\n", "      <td>0.029251</td>\n", "      <td>0.151036</td>\n", "      <td>0.318913</td>\n", "      <td>3311, Iron and Steel Mills and Ferroalloy Manu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.165602</td>\n", "      <td>0.500000</td>\n", "      <td>3313, Alumina and Aluminum Production and Proc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>0.392267</td>\n", "      <td>0.169042</td>\n", "      <td>0.133481</td>\n", "      <td>0.267790</td>\n", "      <td>3211, Sawmills and Wood Preservation</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Subgroup Code         Subgroup Title  Fixed_Assets_Score  \\\n", "0           1105                  Steel            0.500000   \n", "1           1115               Aluminum            0.500000   \n", "2           1120        Precious Metals            0.500000   \n", "3           1125  Other Metals/Minerals            0.500000   \n", "4           1130        Forest Products            0.392267   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0             0.029251                0.151036          0.318913   \n", "1             0.024801                0.165602          0.500000   \n", "2             0.024801                0.312600          0.500000   \n", "3             0.024801                0.312600          0.500000   \n", "4             0.169042                0.133481          0.267790   \n", "\n", "                                          NAICS_Info  \n", "0  3311, Iron and Steel Mills and Ferroalloy Manu...  \n", "1  3313, Alumina and Aluminum Production and Proc...  \n", "2  3314, Nonferrous Metal (except Aluminum) Produ...  \n", "3  3314, Nonferrous Metal (except Aluminum) Produ...  \n", "4               3211, Sawmills and Wood Preservation  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["factset_scores.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "1e801909", "metadata": {}, "outputs": [], "source": ["factset_scores.rename(columns={'Subgroup Code': 'Factset_code', 'Subgroup Title': 'Factset_title' }, inplace=True)"]}, {"cell_type": "code", "execution_count": 19, "id": "69c5d698", "metadata": {}, "outputs": [], "source": ["factset_scores.to_excel('Factset_scores.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "72f27867", "metadata": {}, "source": ["## Factset code for which scores are not obtained"]}, {"cell_type": "code", "execution_count": 21, "id": "5ebf61a7", "metadata": {}, "outputs": [], "source": ["unmatched_naics = factset_to_naics[~factset_to_naics['NAICS_code'].isin(naics_bea_scores['NAICS_code'])]"]}, {"cell_type": "code", "execution_count": 25, "id": "54e92142", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS Title</th>\n", "      <th>Match Rationale</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>2122</td>\n", "      <td>Metal Ore Mining</td>\n", "      <td>Includes mining of precious metals like gold a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>2122</td>\n", "      <td>Metal Ore Mining</td>\n", "      <td>Covers mining of ferrous and non-ferrous metal...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>2123</td>\n", "      <td>Nonmetallic Mineral Mining and Quarrying</td>\n", "      <td>Includes mining of nonmetallic minerals (e.g.,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>1131</td>\n", "      <td>Timber Tract Operations</td>\n", "      <td>Includes growing and managing timber tracts, a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>1132</td>\n", "      <td>Forest Nurseries and Gathering of Forest Products</td>\n", "      <td>Covers gathering forest products and operating...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Factset_code          Factset_title  NAICS_code  \\\n", "3          1120        Precious Metals        2122   \n", "5          1125  Other Metals/Minerals        2122   \n", "6          1125  Other Metals/Minerals        2123   \n", "7          1130        Forest Products        1131   \n", "8          1130        Forest Products        1132   \n", "\n", "                                         NAICS Title  \\\n", "3                                   Metal Ore Mining   \n", "5                                   Metal Ore Mining   \n", "6           Nonmetallic Mineral Mining and Quarrying   \n", "7                            Timber Tract Operations   \n", "8  Forest Nurseries and Gathering of Forest Products   \n", "\n", "                                     Match Rationale  \n", "3  Includes mining of precious metals like gold a...  \n", "5  Covers mining of ferrous and non-ferrous metal...  \n", "6  Includes mining of nonmetallic minerals (e.g.,...  \n", "7  Includes growing and managing timber tracts, a...  \n", "8  Covers gathering forest products and operating...  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["unmatched_naics.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "5186d26a", "metadata": {}, "outputs": [{"data": {"text/plain": ["16"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["len(unmatched_naics)"]}, {"cell_type": "code", "execution_count": 24, "id": "62928c3a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9640\\3110721080.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  unmatched_naics.rename(columns={'Subgroup Code': 'Factset_code', 'Subgroup Title': 'Factset_title' }, inplace=True)\n"]}], "source": ["unmatched_naics.rename(columns={'Subgroup Code': 'Factset_code', 'Subgroup Title': 'Factset_title' }, inplace=True)"]}, {"cell_type": "code", "execution_count": 26, "id": "6a898c3a", "metadata": {}, "outputs": [], "source": ["unmatched_naics.to_excel('Unmatched_Factset.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "96e7353d", "metadata": {}, "source": ["## Combined scores\n", "1. Import Substitution Intensity (50%) \n", "2. Economic Heft (15%)\n", "3. Fixed Assets Investment (15%)\n", "4. Employment (20%)"]}, {"cell_type": "code", "execution_count": 20, "id": "5e5bdafd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>NAICS_Info</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>0.500000</td>\n", "      <td>0.029251</td>\n", "      <td>0.151036</td>\n", "      <td>0.318913</td>\n", "      <td>3311, Iron and Steel Mills and Ferroalloy Manu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.165602</td>\n", "      <td>0.500000</td>\n", "      <td>3313, Alumina and Aluminum Production and Proc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>0.392267</td>\n", "      <td>0.169042</td>\n", "      <td>0.133481</td>\n", "      <td>0.267790</td>\n", "      <td>3211, Sawmills and Wood Preservation</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Factset_code          Factset_title  Fixed_Assets_Score  \\\n", "0          1105                  Steel            0.500000   \n", "1          1115               Aluminum            0.500000   \n", "2          1120        Precious Metals            0.500000   \n", "3          1125  Other Metals/Minerals            0.500000   \n", "4          1130        Forest Products            0.392267   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0             0.029251                0.151036          0.318913   \n", "1             0.024801                0.165602          0.500000   \n", "2             0.024801                0.312600          0.500000   \n", "3             0.024801                0.312600          0.500000   \n", "4             0.169042                0.133481          0.267790   \n", "\n", "                                          NAICS_Info  \n", "0  3311, Iron and Steel Mills and Ferroalloy Manu...  \n", "1  3313, Alumina and Aluminum Production and Proc...  \n", "2  3314, Nonferrous Metal (except Aluminum) Produ...  \n", "3  3314, Nonferrous Metal (except Aluminum) Produ...  \n", "4               3211, Sawmills and Wood Preservation  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["factset_scores.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "acc879aa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>NAICS_Info</th>\n", "      <th>Combined_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>0.500000</td>\n", "      <td>0.029251</td>\n", "      <td>0.151036</td>\n", "      <td>0.318913</td>\n", "      <td>3311, Iron and Steel Mills and Ferroalloy Manu...</td>\n", "      <td>0.218688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.165602</td>\n", "      <td>0.500000</td>\n", "      <td>3313, Alumina and Aluminum Production and Proc...</td>\n", "      <td>0.261521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "      <td>0.335020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>0.500000</td>\n", "      <td>0.024801</td>\n", "      <td>0.312600</td>\n", "      <td>0.500000</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "      <td>0.335020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>0.392267</td>\n", "      <td>0.169042</td>\n", "      <td>0.133481</td>\n", "      <td>0.267790</td>\n", "      <td>3211, Sawmills and Wood Preservation</td>\n", "      <td>0.204495</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Factset_code          Factset_title  Fixed_Assets_Score  \\\n", "0          1105                  Steel            0.500000   \n", "1          1115               Aluminum            0.500000   \n", "2          1120        Precious Metals            0.500000   \n", "3          1125  Other Metals/Minerals            0.500000   \n", "4          1130        Forest Products            0.392267   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0             0.029251                0.151036          0.318913   \n", "1             0.024801                0.165602          0.500000   \n", "2             0.024801                0.312600          0.500000   \n", "3             0.024801                0.312600          0.500000   \n", "4             0.169042                0.133481          0.267790   \n", "\n", "                                          NAICS_Info  Combined_Score  \n", "0  3311, Iron and Steel Mills and Ferroalloy Manu...        0.218688  \n", "1  3313, Alumina and Aluminum Production and Proc...        0.261521  \n", "2  3314, Nonferrous Metal (except Aluminum) Produ...        0.335020  \n", "3  3314, Nonferrous Metal (except Aluminum) Produ...        0.335020  \n", "4               3211, Sawmills and Wood Preservation        0.204495  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Calculate combined score with specified weights\n", "factset_scores['Combined_Score'] = (\n", "    0.5 * factset_scores['Import_Intensity_Score'] + \n", "    0.15 * factset_scores['Economic_Heft_Score'] + \n", "    0.15 * factset_scores['Fixed_Assets_Score'] + \n", "    0.20 * factset_scores['Employment_Score']\n", ")\n", "\n", "factset_scores.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "2811196d", "metadata": {}, "outputs": [], "source": ["factset_scores.to_excel('Factset_scores_new.xlsx', index=False, float_format='%.2f')"]}, {"cell_type": "code", "execution_count": 3, "id": "a9943484", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 4, "id": "ef6b2fd3", "metadata": {}, "outputs": [], "source": ["us_data = pd.read_excel(\"US_Data.xlsx\")"]}, {"cell_type": "code", "execution_count": 5, "id": "61269164", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Fsym_ID</th>\n", "      <th>Fsym_Security_ID</th>\n", "      <th>Ticker</th>\n", "      <th>ISIN</th>\n", "      <th>SEDOL</th>\n", "      <th>Entity_ID</th>\n", "      <th>Entity_Name</th>\n", "      <th>Security_Name</th>\n", "      <th>Exchange_Code</th>\n", "      <th>Exchange_Name</th>\n", "      <th>...</th>\n", "      <th>TD_Eligibility_6M</th>\n", "      <th>Days_Traded_3M</th>\n", "      <th>Exchange_Open_3M</th>\n", "      <th>TD_Eligibility_3M</th>\n", "      <th>First_Trade_Date</th>\n", "      <th>Last_Trade_Date</th>\n", "      <th>ADTV_6M</th>\n", "      <th>ADTV_3M</th>\n", "      <th>Security_Mcap_USD</th>\n", "      <th>Business Description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MH33D6-R</td>\n", "      <td>R85KLC-S</td>\n", "      <td>AAPL-US</td>\n", "      <td>US0378331005</td>\n", "      <td>2046251</td>\n", "      <td>000C7F-E</td>\n", "      <td>Apple, Inc.</td>\n", "      <td>Apple Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>60</td>\n", "      <td>60</td>\n", "      <td>Yes</td>\n", "      <td>1984-11-05</td>\n", "      <td>2025-03-20</td>\n", "      <td>11780.832445</td>\n", "      <td>12683.983288</td>\n", "      <td>3.207062e+06</td>\n", "      <td>Apple Inc. designs, manufactures and markets s...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>K7TPSX-R</td>\n", "      <td>QDYJZC-S</td>\n", "      <td>NVDA-US</td>\n", "      <td>US67066G1040</td>\n", "      <td>2379504</td>\n", "      <td>00208X-E</td>\n", "      <td>NVIDIA Corp.</td>\n", "      <td>NVIDIA Corporation</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>60</td>\n", "      <td>60</td>\n", "      <td>Yes</td>\n", "      <td>1999-01-22</td>\n", "      <td>2025-03-20</td>\n", "      <td>33115.052807</td>\n", "      <td>34618.656093</td>\n", "      <td>2.968748e+06</td>\n", "      <td>NVIDIA Corporation is a full-stack computing i...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>P8R3C2-R</td>\n", "      <td>DF3K29-S</td>\n", "      <td>MSFT-US</td>\n", "      <td>US5949181045</td>\n", "      <td>2588173</td>\n", "      <td>000Q07-E</td>\n", "      <td>Microsoft Corp.</td>\n", "      <td>Microsoft Corporation</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>60</td>\n", "      <td>60</td>\n", "      <td>Yes</td>\n", "      <td>1986-03-13</td>\n", "      <td>2025-03-20</td>\n", "      <td>9109.686450</td>\n", "      <td>9574.951682</td>\n", "      <td>2.888548e+06</td>\n", "      <td>Microsoft Corporation is a technology company....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MCNYYL-R</td>\n", "      <td>RMCVZ9-S</td>\n", "      <td>AMZN-US</td>\n", "      <td>US0231351067</td>\n", "      <td>2000019</td>\n", "      <td>001MF1-E</td>\n", "      <td>Amazon.com, Inc.</td>\n", "      <td>Amazon.com, Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>60</td>\n", "      <td>60</td>\n", "      <td>Yes</td>\n", "      <td>1997-05-15</td>\n", "      <td>2025-03-20</td>\n", "      <td>8074.030556</td>\n", "      <td>8506.250917</td>\n", "      <td>2.097821e+06</td>\n", "      <td>Amazon.com, Inc. provides a range of products ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HTM0LK-R</td>\n", "      <td>XF9TK6-S</td>\n", "      <td>GOOGL-US</td>\n", "      <td>US02079K3059</td>\n", "      <td>BYVY8G0</td>\n", "      <td>0FPWZZ-E</td>\n", "      <td>Alphabet, Inc.</td>\n", "      <td>Alphabet Inc. Class A</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>60</td>\n", "      <td>60</td>\n", "      <td>Yes</td>\n", "      <td>2004-08-19</td>\n", "      <td>2025-03-20</td>\n", "      <td>5015.869916</td>\n", "      <td>5556.433458</td>\n", "      <td>9.653032e+05</td>\n", "      <td>Alphabet Inc. is a holding company. The Compan...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 40 columns</p>\n", "</div>"], "text/plain": ["    Fsym_ID Fsym_Security_ID    Ticker          ISIN    SEDOL Entity_ID  \\\n", "0  MH33D6-R         R85KLC-S   AAPL-US  US0378331005  2046251  000C7F-E   \n", "1  K7TPSX-R         QDYJZC-S   NVDA-US  US67066G1040  2379504  00208X-E   \n", "2  P8R3C2-R         DF3K29-S   MSFT-US  US5949181045  2588173  000Q07-E   \n", "3  MCNYYL-<PERSON>         RMCVZ9-S   AMZN-US  US0231351067  2000019  001MF1-E   \n", "4  HTM0LK-R         XF9TK6-S  GOOGL-US  US02079K3059  BYVY8G0  0FPWZZ-E   \n", "\n", "        Entity_Name          Security_Name Exchange_Code Exchange_Name  ...  \\\n", "0       Apple, Inc.             Apple Inc.           NAS        NASDAQ  ...   \n", "1      NVIDIA Corp.     NVIDIA Corporation           NAS        NASDAQ  ...   \n", "2   Microsoft Corp.  Microsoft Corporation           NAS        NASDAQ  ...   \n", "3  Amazon.com, Inc.       Amazon.com, Inc.           NAS        NASDAQ  ...   \n", "4    Alphabet, Inc.  Alphabet Inc. Class A           NAS        NASDAQ  ...   \n", "\n", "  TD_Eligibility_6M Days_Traded_3M Exchange_Open_3M TD_Eligibility_3M  \\\n", "0               Yes             60               60               Yes   \n", "1               Yes             60               60               Yes   \n", "2               Yes             60               60               Yes   \n", "3               Yes             60               60               Yes   \n", "4               Yes             60               60               Yes   \n", "\n", "  First_Trade_Date Last_Trade_Date       ADTV_6M       ADTV_3M  \\\n", "0       1984-11-05      2025-03-20  11780.832445  12683.983288   \n", "1       1999-01-22      2025-03-20  33115.052807  34618.656093   \n", "2       1986-03-13      2025-03-20   9109.686450   9574.951682   \n", "3       1997-05-15      2025-03-20   8074.030556   8506.250917   \n", "4       2004-08-19      2025-03-20   5015.869916   5556.433458   \n", "\n", "  Security_Mcap_USD                               Business Description  \n", "0      3.207062e+06  Apple Inc. designs, manufactures and markets s...  \n", "1      2.968748e+06  NVIDIA Corporation is a full-stack computing i...  \n", "2      2.888548e+06  Microsoft Corporation is a technology company....  \n", "3      2.097821e+06  Amazon.com, Inc. provides a range of products ...  \n", "4      9.653032e+05  Alphabet Inc. is a holding company. The Compan...  \n", "\n", "[5 rows x 40 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["us_data.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "1241a367", "metadata": {}, "outputs": [], "source": ["factset_scores = pd.read_excel(\"Factset_scores_new.xlsx\")"]}, {"cell_type": "code", "execution_count": 7, "id": "7f8eae62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>NAICS_Info</th>\n", "      <th>Combined_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>0.50</td>\n", "      <td>0.03</td>\n", "      <td>0.15</td>\n", "      <td>0.32</td>\n", "      <td>3311, Iron and Steel Mills and Ferroalloy Manu...</td>\n", "      <td>0.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>0.50</td>\n", "      <td>0.02</td>\n", "      <td>0.17</td>\n", "      <td>0.50</td>\n", "      <td>3313, Alumina and Aluminum Production and Proc...</td>\n", "      <td>0.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>0.50</td>\n", "      <td>0.02</td>\n", "      <td>0.31</td>\n", "      <td>0.50</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "      <td>0.34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>0.50</td>\n", "      <td>0.02</td>\n", "      <td>0.31</td>\n", "      <td>0.50</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "      <td>0.34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>0.39</td>\n", "      <td>0.17</td>\n", "      <td>0.13</td>\n", "      <td>0.27</td>\n", "      <td>3211, Sawmills and Wood Preservation</td>\n", "      <td>0.20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Factset_code          Factset_title  Fixed_Assets_Score  \\\n", "0          1105                  Steel                0.50   \n", "1          1115               Aluminum                0.50   \n", "2          1120        Precious Metals                0.50   \n", "3          1125  Other Metals/Minerals                0.50   \n", "4          1130        Forest Products                0.39   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0                 0.03                    0.15              0.32   \n", "1                 0.02                    0.17              0.50   \n", "2                 0.02                    0.31              0.50   \n", "3                 0.02                    0.31              0.50   \n", "4                 0.17                    0.13              0.27   \n", "\n", "                                          NAICS_Info  Combined_Score  \n", "0  3311, Iron and Steel Mills and Ferroalloy Manu...            0.22  \n", "1  3313, Alumina and Aluminum Production and Proc...            0.26  \n", "2  3314, Nonferrous Metal (except Aluminum) Produ...            0.34  \n", "3  3314, Nonferrous Metal (except Aluminum) Produ...            0.34  \n", "4               3211, Sawmills and Wood Preservation            0.20  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["factset_scores.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "9692a9e6", "metadata": {}, "outputs": [], "source": ["factset_scores.rename(columns = {'Factset_title': 'Factset_Industry'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "646082a4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_Industry</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>NAICS_Info</th>\n", "      <th>Combined_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1105</td>\n", "      <td>Steel</td>\n", "      <td>0.50</td>\n", "      <td>0.03</td>\n", "      <td>0.15</td>\n", "      <td>0.32</td>\n", "      <td>3311, Iron and Steel Mills and Ferroalloy Manu...</td>\n", "      <td>0.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1115</td>\n", "      <td>Aluminum</td>\n", "      <td>0.50</td>\n", "      <td>0.02</td>\n", "      <td>0.17</td>\n", "      <td>0.50</td>\n", "      <td>3313, Alumina and Aluminum Production and Proc...</td>\n", "      <td>0.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1120</td>\n", "      <td>Precious Metals</td>\n", "      <td>0.50</td>\n", "      <td>0.02</td>\n", "      <td>0.31</td>\n", "      <td>0.50</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "      <td>0.34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1125</td>\n", "      <td>Other Metals/Minerals</td>\n", "      <td>0.50</td>\n", "      <td>0.02</td>\n", "      <td>0.31</td>\n", "      <td>0.50</td>\n", "      <td>3314, Nonferrous Metal (except Aluminum) Produ...</td>\n", "      <td>0.34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1130</td>\n", "      <td>Forest Products</td>\n", "      <td>0.39</td>\n", "      <td>0.17</td>\n", "      <td>0.13</td>\n", "      <td>0.27</td>\n", "      <td>3211, Sawmills and Wood Preservation</td>\n", "      <td>0.20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Factset_code       Factset_Industry  Fixed_Assets_Score  \\\n", "0          1105                  Steel                0.50   \n", "1          1115               Aluminum                0.50   \n", "2          1120        Precious Metals                0.50   \n", "3          1125  Other Metals/Minerals                0.50   \n", "4          1130        Forest Products                0.39   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0                 0.03                    0.15              0.32   \n", "1                 0.02                    0.17              0.50   \n", "2                 0.02                    0.31              0.50   \n", "3                 0.02                    0.31              0.50   \n", "4                 0.17                    0.13              0.27   \n", "\n", "                                          NAICS_Info  Combined_Score  \n", "0  3311, Iron and Steel Mills and Ferroalloy Manu...            0.22  \n", "1  3313, Alumina and Aluminum Production and Proc...            0.26  \n", "2  3314, Nonferrous Metal (except Aluminum) Produ...            0.34  \n", "3  3314, Nonferrous Metal (except Aluminum) Produ...            0.34  \n", "4               3211, Sawmills and Wood Preservation            0.20  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["factset_scores.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "ebf249e0", "metadata": {}, "outputs": [], "source": ["matched_data = us_data.merge(\n", "    factset_scores[['Factset_code','Factset_Industry', 'Fixed_Assets_Score', 'Economic_Heft_Score', \n", "                    'Import_Intensity_Score', 'Employment_Score', 'Combined_Score']],\n", "    on='Factset_Industry',\n", "    how='inner'\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "e866d144", "metadata": {}, "outputs": [{"data": {"text/plain": ["809"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(matched_data)"]}, {"cell_type": "code", "execution_count": 12, "id": "ce0ef261", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Fsym_ID</th>\n", "      <th>Fsym_Security_ID</th>\n", "      <th>Ticker</th>\n", "      <th>ISIN</th>\n", "      <th>SEDOL</th>\n", "      <th>Entity_ID</th>\n", "      <th>Entity_Name</th>\n", "      <th>Security_Name</th>\n", "      <th>Exchange_Code</th>\n", "      <th>Exchange_Name</th>\n", "      <th>...</th>\n", "      <th>ADTV_6M</th>\n", "      <th>ADTV_3M</th>\n", "      <th>Security_Mcap_USD</th>\n", "      <th>Business Description</th>\n", "      <th>Factset_code</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Combined_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MH33D6-R</td>\n", "      <td>R85KLC-S</td>\n", "      <td>AAPL-US</td>\n", "      <td>US0378331005</td>\n", "      <td>2046251</td>\n", "      <td>000C7F-E</td>\n", "      <td>Apple, Inc.</td>\n", "      <td>Apple Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>11780.832445</td>\n", "      <td>12683.983288</td>\n", "      <td>3.207062e+06</td>\n", "      <td>Apple Inc. designs, manufactures and markets s...</td>\n", "      <td>1320</td>\n", "      <td>0.5</td>\n", "      <td>0.02</td>\n", "      <td>0.55</td>\n", "      <td>0.22</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>K7TPSX-R</td>\n", "      <td>QDYJZC-S</td>\n", "      <td>NVDA-US</td>\n", "      <td>US67066G1040</td>\n", "      <td>2379504</td>\n", "      <td>00208X-E</td>\n", "      <td>NVIDIA Corp.</td>\n", "      <td>NVIDIA Corporation</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>33115.052807</td>\n", "      <td>34618.656093</td>\n", "      <td>2.968748e+06</td>\n", "      <td>NVIDIA Corporation is a full-stack computing i...</td>\n", "      <td>1305</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>B81TLL-R</td>\n", "      <td>MDX5ZL-S</td>\n", "      <td>AVGO-US</td>\n", "      <td>US11135F1012</td>\n", "      <td>BDZ78H9</td>\n", "      <td>0JCLYY-E</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>5828.494482</td>\n", "      <td>7560.204036</td>\n", "      <td>9.194190e+05</td>\n", "      <td>Broadcom Inc. is a global technology firm that...</td>\n", "      <td>1305</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>WWDPYB-S</td>\n", "      <td>TSLA-US</td>\n", "      <td>US88160R1014</td>\n", "      <td>B616C79</td>\n", "      <td>006XY7-E</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>29106.313789</td>\n", "      <td>32312.138650</td>\n", "      <td>8.040649e+05</td>\n", "      <td>Tesla, Inc. designs, develops, manufactures, s...</td>\n", "      <td>1405</td>\n", "      <td>0.3</td>\n", "      <td>0.58</td>\n", "      <td>1.00</td>\n", "      <td>0.64</td>\n", "      <td>0.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>B19ST9-R</td>\n", "      <td>W38FV3-S</td>\n", "      <td>LLY-US</td>\n", "      <td>US5324571083</td>\n", "      <td>2516152</td>\n", "      <td>000P56-E</td>\n", "      <td>Eli Lilly &amp; Co.</td>\n", "      <td>Eli Lilly and Company</td>\n", "      <td>NYS</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>...</td>\n", "      <td>3030.989148</td>\n", "      <td>3017.401099</td>\n", "      <td>7.711702e+05</td>\n", "      <td>Eli Lilly and Company is a medicine company. T...</td>\n", "      <td>2305</td>\n", "      <td>0.5</td>\n", "      <td>0.28</td>\n", "      <td>0.85</td>\n", "      <td>0.62</td>\n", "      <td>0.67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 46 columns</p>\n", "</div>"], "text/plain": ["    Fsym_ID Fsym_Security_ID   Ticker          ISIN    SEDOL Entity_ID  \\\n", "0  MH33D6-R         R85KLC-S  AAPL-US  US0378331005  2046251  000C7F-E   \n", "1  K7TPSX-R         QDYJZC-S  NVDA-US  US67066G1040  2379504  00208X-E   \n", "2  B81TLL-R         MDX5ZL-S  AVGO-US  US11135F1012  BDZ78H9  0JCLYY-E   \n", "3  Q2YN1N-R         WWDPYB-S  TSLA-US  US88160R1014  B616C79  006XY7-E   \n", "4  B19ST9-R         W38FV3-S   LLY-US  US5324571083  2516152  000P56-E   \n", "\n", "       Entity_Name          Security_Name Exchange_Code  \\\n", "0      Apple, Inc.             Apple Inc.           NAS   \n", "1     NVIDIA Corp.     NVIDIA Corporation           NAS   \n", "2    Broadcom Inc.          Broadcom Inc.           NAS   \n", "3      Tesla, Inc.            Tesla, Inc.           NAS   \n", "4  Eli Lilly & Co.  Eli Lilly and Company           NYS   \n", "\n", "             Exchange_Name  ...       ADTV_6M       ADTV_3M Security_Mcap_USD  \\\n", "0                   NASDAQ  ...  11780.832445  12683.983288      3.207062e+06   \n", "1                   NASDAQ  ...  33115.052807  34618.656093      2.968748e+06   \n", "2                   NASDAQ  ...   5828.494482   7560.204036      9.194190e+05   \n", "3                   NASDAQ  ...  29106.313789  32312.138650      8.040649e+05   \n", "4  New York Stock Exchange  ...   3030.989148   3017.401099      7.711702e+05   \n", "\n", "                                Business Description Factset_code  \\\n", "0  Apple Inc. designs, manufactures and markets s...         1320   \n", "1  NVIDIA Corporation is a full-stack computing i...         1305   \n", "2  Broadcom Inc. is a global technology firm that...         1305   \n", "3  Tesla, Inc. designs, develops, manufactures, s...         1405   \n", "4  Eli Lilly and Company is a medicine company. T...         2305   \n", "\n", "  Fixed_Assets_Score Economic_Heft_Score Import_Intensity_Score  \\\n", "0                0.5                0.02                   0.55   \n", "1                0.5                0.11                   0.53   \n", "2                0.5                0.11                   0.53   \n", "3                0.3                0.58                   1.00   \n", "4                0.5                0.28                   0.85   \n", "\n", "  Employment_Score Combined_Score  \n", "0             0.22           0.40  \n", "1             0.62           0.48  \n", "2             0.62           0.48  \n", "3             0.64           0.76  \n", "4             0.62           0.67  \n", "\n", "[5 rows x 46 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["matched_data.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "983863da", "metadata": {}, "outputs": [], "source": ["unmatched_data = us_data[~us_data['Factset_Industry'].isin(factset_scores['Factset_Industry'])]"]}, {"cell_type": "code", "execution_count": 14, "id": "1fe7b4fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["1416"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(unmatched_data)"]}, {"cell_type": "code", "execution_count": 15, "id": "32585945", "metadata": {}, "outputs": [{"data": {"text/plain": ["2225"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["len(us_data)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "68b48ea4", "metadata": {}, "outputs": [], "source": ["matched_data.to_excel(\"US_Data_Matched_Scores.xlsx\", index=False)\n", "unmatched_data.to_excel(\"US_Data_Unmatched.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}