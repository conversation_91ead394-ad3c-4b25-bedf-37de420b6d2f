{"cells": [{"cell_type": "code", "execution_count": 2, "id": "8f4b708b", "metadata": {}, "outputs": [], "source": ["from kiteconnect import KiteConnect\n", "import requests\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "id": "ee484bde", "metadata": {}, "outputs": [], "source": ["api_key = \"3q0r6l1fb689slhk\"\n", "access_token = \"X3tzT7qYg6lW4923ix7sxUqHzITxKGGW\"\n", "\n", "kite = KiteConnect(api_key=api_key)\n", "kite.set_access_token(access_token)"]}, {"cell_type": "markdown", "id": "baebdf92", "metadata": {}, "source": ["# Initialize Dataframes"]}, {"cell_type": "code", "execution_count": 70, "id": "ae69aa2b", "metadata": {}, "outputs": [], "source": ["active_positions_columns = [\n", "    'instrument_token', 'exchange_token', 'tradingsymbol', 'name', 'expiry',\n", "    'strike', 'lot_size', 'instrument_type', 'exchange',\n", "    'last_trading_price_of_stock', 'Intrinsic_value', 'last_price_of_option',\n", "    'premium_received', 'total_margin', 'premium_to_margin_percentage'\n", "]\n", "active_positions = pd.DataFrame(columns=active_positions_columns)"]}, {"cell_type": "code", "execution_count": 95, "id": "9e0af19f", "metadata": {}, "outputs": [], "source": ["transaction_log_columns = [\n", "    'instrument_token', 'exchange_token', 'tradingsymbol', 'name', 'expiry',\n", "    'strike', 'lot_size', 'instrument_type', 'premium_received', 'total_margin',\n", "    'stock_price_on_exp', 'itm_otm', 'option_price_on_expiry', 'loss_incase_of _itm','premium_paid',\n", "    'next_month_premium', 'stocks_hold', 'stocks_delivered', 'stock_transaction'\n", "]\n", "transaction_log = pd.DataFrame(columns=transaction_log_columns)"]}, {"cell_type": "markdown", "id": "901540ba", "metadata": {}, "source": ["# Getting instruments"]}, {"cell_type": "code", "execution_count": null, "id": "170f1af1", "metadata": {}, "outputs": [], "source": ["instruments = kite.instruments('NSE')\n"]}, {"cell_type": "code", "execution_count": 8, "id": "69d699b4", "metadata": {}, "outputs": [], "source": ["instruments_df = pd.DataFrame(instruments)\n", "instruments_df.to_csv('instruments.csv', index=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "2e4640cc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>*********</td>\n", "      <td>1100996</td>\n", "      <td>BANKEX25JUNFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>*********</td>\n", "      <td>1141118</td>\n", "      <td>BANKEX25JULFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-07-29</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>*********</td>\n", "      <td>826846</td>\n", "      <td>BANKEX25AUGFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-08-26</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>*********</td>\n", "      <td>1141074</td>\n", "      <td>SENSEX25617FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-17</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>*********</td>\n", "      <td>1100924</td>\n", "      <td>SENSEX25JUNFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token   tradingsymbol    name  last_price  \\\n", "0         *********        1100996  BANKEX25JUNFUT  BANKEX         0.0   \n", "1         *********        1141118  BANKEX25JULFUT  BANKEX         0.0   \n", "2         *********         826846  BANKEX25AUGFUT  BANKEX         0.0   \n", "3         *********        1141074  SENSEX25617FUT  SENSEX         0.0   \n", "4         *********        1100924  SENSEX25JUNFUT  SENSEX         0.0   \n", "\n", "       expiry  strike  tick_size  lot_size instrument_type  segment exchange  \n", "0  2025-06-24     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "1  2025-07-29     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "2  2025-08-26     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "3  2025-06-17     0.0       0.05        20             FUT  BFO-FUT      BFO  \n", "4  2025-06-24     0.0       0.05        20             FUT  BFO-FUT      BFO  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["instruments_df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "ef7d1595", "metadata": {}, "outputs": [], "source": ["target_banks = [\n", "    'ICICI BANK',\n", "    'HDFC BANK',\n", "    'STATE BANK OF INDIA'\n", "]"]}, {"cell_type": "code", "execution_count": 11, "id": "f81b4ce3", "metadata": {}, "outputs": [], "source": ["bank_equities = instruments_df[\n", "    (instruments_df['instrument_type'] == 'EQ') & (instruments_df['segment'] == 'NSE') &\n", "    (instruments_df['name'].isin(target_banks))].copy()"]}, {"cell_type": "code", "execution_count": 12, "id": "94b01fbd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NSE:HDFCBANK', 'NSE:SBIN', 'NSE:ICICIBANK']\n"]}], "source": ["instruments_list = bank_equities.apply(\n", "    lambda row: f\"{row['exchange']}:{row['tradingsymbol']}\", axis=1\n", ").to_list()\n", "print(instruments_list)"]}, {"cell_type": "code", "execution_count": 13, "id": "31caa179", "metadata": {}, "outputs": [], "source": ["try:\n", "    quote = kite.quote(instruments_list) \n", "except Exception as e:\n", "    print(f\"Error fetching quote: {e}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "ab6a5f2f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'NSE:HDFCBANK': {'instrument_token': 341249,\n", "  'timestamp': datetime.datetime(2025, 6, 17, 12, 47, 26),\n", "  'last_trade_time': datetime.datetime(2025, 6, 17, 12, 47, 25),\n", "  'last_price': 1925.7,\n", "  'last_quantity': 57,\n", "  'buy_quantity': 317328,\n", "  'sell_quantity': 450493,\n", "  'volume': 3666233,\n", "  'average_price': 1926.32,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 1741.9,\n", "  'upper_circuit_limit': 2128.9,\n", "  'ohlc': {'open': 1937, 'high': 1941.6, 'low': 1919.1, 'close': 1935.4},\n", "  'depth': {'buy': [{'price': 1925.6, 'quantity': 7, 'orders': 1},\n", "    {'price': 1925.5, 'quantity': 32, 'orders': 1},\n", "    {'price': 1925.4, 'quantity': 38, 'orders': 2},\n", "    {'price': 1925.3, 'quantity': 1, 'orders': 1},\n", "    {'price': 1925.1, 'quantity': 223, 'orders': 4}],\n", "   'sell': [{'price': 1925.7, 'quantity': 15, 'orders': 1},\n", "    {'price': 1925.8, 'quantity': 230, 'orders': 6},\n", "    {'price': 1925.9, 'quantity': 469, 'orders': 7},\n", "    {'price': 1926, 'quantity': 1465, 'orders': 17},\n", "    {'price': 1926.1, 'quantity': 549, 'orders': 10}]}},\n", " 'NSE:ICICIBANK': {'instrument_token': 1270529,\n", "  'timestamp': datetime.datetime(2025, 6, 17, 12, 47, 26),\n", "  'last_trade_time': datetime.datetime(2025, 6, 17, 12, 47, 25),\n", "  'last_price': 1420.9,\n", "  'last_quantity': 1,\n", "  'buy_quantity': 291329,\n", "  'sell_quantity': 440587,\n", "  'volume': 2945625,\n", "  'average_price': 1424.93,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 1284.3,\n", "  'upper_circuit_limit': 1569.5,\n", "  'ohlc': {'open': 1426.9, 'high': 1429.8, 'low': 1420.6, 'close': 1426.9},\n", "  'depth': {'buy': [{'price': 1420.7, 'quantity': 27, 'orders': 1},\n", "    {'price': 1420.6, 'quantity': 195, 'orders': 2},\n", "    {'price': 1420.5, 'quantity': 808, 'orders': 14},\n", "    {'price': 1420.4, 'quantity': 2058, 'orders': 9},\n", "    {'price': 1420.3, 'quantity': 947, 'orders': 11}],\n", "   'sell': [{'price': 1420.9, 'quantity': 123, 'orders': 4},\n", "    {'price': 1421, 'quantity': 386, 'orders': 9},\n", "    {'price': 1421.1, 'quantity': 696, 'orders': 8},\n", "    {'price': 1421.2, 'quantity': 972, 'orders': 8},\n", "    {'price': 1421.3, 'quantity': 740, 'orders': 7}]}},\n", " 'NSE:SBIN': {'instrument_token': 779521,\n", "  'timestamp': datetime.datetime(2025, 6, 17, 12, 47, 25),\n", "  'last_trade_time': datetime.datetime(2025, 6, 17, 12, 47, 24),\n", "  'last_price': 792.65,\n", "  'last_quantity': 1,\n", "  'buy_quantity': 557061,\n", "  'sell_quantity': 856831,\n", "  'volume': 4680752,\n", "  'average_price': 795.05,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 713.25,\n", "  'upper_circuit_limit': 871.75,\n", "  'ohlc': {'open': 794, 'high': 798.85, 'low': 792, 'close': 792.5},\n", "  'depth': {'buy': [{'price': 792.5, 'quantity': 191, 'orders': 3},\n", "    {'price': 792.45, 'quantity': 187, 'orders': 1},\n", "    {'price': 792.4, 'quantity': 464, 'orders': 7},\n", "    {'price': 792.35, 'quantity': 628, 'orders': 8},\n", "    {'price': 792.3, 'quantity': 1741, 'orders': 18}],\n", "   'sell': [{'price': 792.65, 'quantity': 141, 'orders': 3},\n", "    {'price': 792.75, 'quantity': 365, 'orders': 4},\n", "    {'price': 792.8, 'quantity': 488, 'orders': 6},\n", "    {'price': 792.85, 'quantity': 874, 'orders': 11},\n", "    {'price': 792.9, 'quantity': 3994, 'orders': 17}]}}}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["quote"]}, {"cell_type": "code", "execution_count": 16, "id": "e3701c87", "metadata": {}, "outputs": [], "source": ["stk_price_df = pd.DataFrame([\n", "    {\n", "      'instrument': instrument,\n", "      'name': instrument.split(':')[1],\n", "      'last_trading_price': quote[instrument]['last_price'] \n", "    }\n", "    for instrument in quote.keys()\n", "])"]}, {"cell_type": "code", "execution_count": 17, "id": "62ba2973", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument</th>\n", "      <th>name</th>\n", "      <th>last_trading_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NSE:HDFCBANK</td>\n", "      <td>HDFCBANK</td>\n", "      <td>1925.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NSE:ICICIBANK</td>\n", "      <td>ICICIBANK</td>\n", "      <td>1420.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NSE:SBIN</td>\n", "      <td>SBIN</td>\n", "      <td>792.65</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      instrument       name  last_trading_price\n", "0   NSE:HDFCBANK   HDFCBANK             1925.70\n", "1  NSE:ICICIBANK  ICICIBANK             1420.90\n", "2       NSE:SBIN       SBIN              792.65"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_price_df.head()"]}, {"cell_type": "markdown", "id": "0acd5409", "metadata": {}, "source": ["# Picking Up Put options to sell"]}, {"cell_type": "code", "execution_count": 18, "id": "b742edb6", "metadata": {}, "outputs": [], "source": ["bank_symbols = stk_price_df['name'].tolist()\n", "put_options = instruments_df[\n", "    (instruments_df['instrument_type'] == 'PE') & \n", "    (instruments_df['name'].isin(bank_symbols))\n", "].copy()"]}, {"cell_type": "code", "execution_count": 19, "id": "e8da67b1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>64758</th>\n", "      <td>********</td>\n", "      <td>95042</td>\n", "      <td>HDFCBANK25JUN1940PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>1940.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64760</th>\n", "      <td>********</td>\n", "      <td>94107</td>\n", "      <td>HDFCBANK25JUN1960PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>1960.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64762</th>\n", "      <td>********</td>\n", "      <td>94090</td>\n", "      <td>HDFCBANK25JUN1920PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>1920.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64764</th>\n", "      <td>********</td>\n", "      <td>95044</td>\n", "      <td>HDFCBANK25JUN1980PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>1980.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64766</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument_token exchange_token        tradingsymbol      name  \\\n", "64758          ********          95042  HDFCBANK25JUN1940PE  HDFCBANK   \n", "64760          ********          94107  HDFCBANK25JUN1960PE  HDFCBANK   \n", "64762          ********          94090  HDFCBANK25JUN1920PE  HDFCBANK   \n", "64764          ********          95044  HDFCBANK25JUN1980PE  HDFCBANK   \n", "64766          ********          95040  HDFCBANK25JUN1900PE  HDFCBANK   \n", "\n", "       last_price      expiry  strike  tick_size  lot_size instrument_type  \\\n", "64758         0.0  2025-06-26  1940.0       0.05       550              PE   \n", "64760         0.0  2025-06-26  1960.0       0.05       550              PE   \n", "64762         0.0  2025-06-26  1920.0       0.05       550              PE   \n", "64764         0.0  2025-06-26  1980.0       0.05       550              PE   \n", "64766         0.0  2025-06-26  1900.0       0.05       550              PE   \n", "\n", "       segment exchange  \n", "64758  NFO-OPT      NFO  \n", "64760  NFO-OPT      NFO  \n", "64762  NFO-OPT      NFO  \n", "64764  NFO-OPT      NFO  \n", "64766  NFO-OPT      NFO  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "01aff826", "metadata": {}, "outputs": [{"data": {"text/plain": ["(221, 12)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options.shape"]}, {"cell_type": "code", "execution_count": 21, "id": "14846b03", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "put_options['expiry'] = pd.to_datetime(put_options['expiry'], errors='coerce')\n", "current_date = pd.to_datetime(datetime.today())\n", "valid_expiries = put_options[put_options['expiry'] > current_date]\n", "nearest_expiry = valid_expiries.groupby('name')['expiry'].min().reset_index()"]}, {"cell_type": "code", "execution_count": 23, "id": "51db32b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(91, 12)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["nearest_expiry_put_options = put_options.merge(nearest_expiry, on=['name', 'expiry'], how='inner')\n", "nearest_expiry_put_options.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "d398828c", "metadata": {}, "outputs": [], "source": ["intrinsic_values ={\n", "    'HDFCBANK': 1900,\n", "    'ICICIBANK': 1400, \n", "    'SBIN': 750  \n", "}"]}, {"cell_type": "code", "execution_count": 26, "id": "********", "metadata": {}, "outputs": [], "source": ["selected_puts = []\n", "\n", "for stock, intrinsic in intrinsic_values.items():\n", "    stock_puts = put_options[put_options['name'] == stock]\n", "    \n", "    \n", "    valid_puts = stock_puts[stock_puts['strike'] <= intrinsic]\n", "    \n", "    if not valid_puts.empty:\n", "        \n", "        selected = valid_puts.loc[valid_puts['strike'].idxmax()]\n", "        selected_puts.append(selected)\n", "\n", "\n", "selected_puts_df = pd.DataFrame(selected_puts).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 27, "id": "de41b175", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>0.05</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>125306</td>\n", "      <td>SBIN25JUN750PE</td>\n", "      <td>SBIN</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-26</td>\n", "      <td>750.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "2          ********         125306        SBIN25JUN750PE       SBIN   \n", "\n", "   last_price     expiry  strike  tick_size  lot_size instrument_type  \\\n", "0         0.0 2025-06-26  1900.0       0.05       550              PE   \n", "1         0.0 2025-06-26  1400.0       0.05       700              PE   \n", "2         0.0 2025-06-26   750.0       0.05       750              PE   \n", "\n", "   segment exchange  \n", "0  NFO-OPT      NFO  \n", "1  NFO-OPT      NFO  \n", "2  NFO-OPT      NFO  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_puts_df.head()"]}, {"cell_type": "markdown", "id": "68d82dfc", "metadata": {}, "source": ["## Premium Calculation"]}, {"cell_type": "code", "execution_count": 28, "id": "e1b36b98", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NFO:HDFCBANK25JUN1900PE', 'NFO:ICICIBANK25JUN1400PE', 'NFO:SBIN25JUN750PE']\n"]}], "source": ["selected_puts_df['instrument_key'] = selected_puts_df.apply(\n", "    lambda row: f\"{row['exchange']}:{row['tradingsymbol']}\", axis=1\n", ")\n", "\n", "put_instrument_list = selected_puts_df['instrument_key'].tolist()\n", "print(put_instrument_list)"]}, {"cell_type": "code", "execution_count": 13, "id": "71c8c136", "metadata": {}, "outputs": [], "source": ["l = ['NFO:HDFCBANK25JUN1900PE', 'NFO:ICICIBANK25JUN1400PE', 'NFO:SBIN25JUN750PE']"]}, {"cell_type": "code", "execution_count": 14, "id": "0e888ec7", "metadata": {}, "outputs": [], "source": ["quote_data = kite.quote(l)"]}, {"cell_type": "code", "execution_count": 16, "id": "2e9d78bf", "metadata": {}, "outputs": [{"data": {"text/plain": ["6.7"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["quote_data['NFO:HDFCBANK25JUN1900PE']['last_price']"]}, {"cell_type": "code", "execution_count": 29, "id": "a02f33fa", "metadata": {}, "outputs": [], "source": ["quote_data = kite.quote(put_instrument_list)"]}, {"cell_type": "code", "execution_count": 30, "id": "5405f611", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["len(quote_data)"]}, {"cell_type": "code", "execution_count": 36, "id": "abf5139b", "metadata": {}, "outputs": [], "source": ["quote_df = pd.DataFrame([\n", "    {\n", "        'instrument_key': instrument_key,\n", "        'last_price_of_option': quote_data[instrument_key]['last_price']\n", "    }\n", "    for instrument_key in quote_data.keys()\n", "])"]}, {"cell_type": "code", "execution_count": 37, "id": "d4808184", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_key</th>\n", "      <th>last_price_of_option</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NFO:HDFCBANK25JUN1900PE</td>\n", "      <td>8.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NFO:ICICIBANK25JUN1400PE</td>\n", "      <td>6.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NFO:SBIN25JUN750PE</td>\n", "      <td>0.90</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             instrument_key  last_price_of_option\n", "0   NFO:HDFCBANK25JUN1900PE                  8.95\n", "1  NFO:ICICIBANK25JUN1400PE                  6.45\n", "2        NFO:SBIN25JUN750PE                  0.90"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["quote_df.head()"]}, {"cell_type": "code", "execution_count": 38, "id": "6d2edfe6", "metadata": {}, "outputs": [], "source": ["selected_puts_df = selected_puts_df.drop(columns=['last_price'], errors='ignore')\n", "\n", "selected_puts_df = selected_puts_df.merge(quote_df, on='instrument_key', how='inner')"]}, {"cell_type": "code", "execution_count": 39, "id": "83c4c791", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "      <th>instrument_key</th>\n", "      <th>last_price_of_option</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:HDFCBANK25JUN1900PE</td>\n", "      <td>8.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>0.05</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:ICICIBANK25JUN1400PE</td>\n", "      <td>6.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>125306</td>\n", "      <td>SBIN25JUN750PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-06-26</td>\n", "      <td>750.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:SBIN25JUN750PE</td>\n", "      <td>0.90</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "2          ********         125306        SBIN25JUN750PE       SBIN   \n", "\n", "      expiry  strike  tick_size  lot_size instrument_type  segment exchange  \\\n", "0 2025-06-26  1900.0       0.05       550              PE  NFO-OPT      NFO   \n", "1 2025-06-26  1400.0       0.05       700              PE  NFO-OPT      NFO   \n", "2 2025-06-26   750.0       0.05       750              PE  NFO-OPT      NFO   \n", "\n", "             instrument_key  last_price_of_option  \n", "0   NFO:HDFCBANK25JUN1900PE                  8.95  \n", "1  NFO:ICICIBANK25JUN1400PE                  6.45  \n", "2        NFO:SBIN25JUN750PE                  0.90  "]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_puts_df.head()"]}, {"cell_type": "code", "execution_count": 40, "id": "61757e59", "metadata": {}, "outputs": [], "source": ["selected_puts_df['premium_received'] = selected_puts_df['last_price_of_option'] * selected_puts_df['lot_size']"]}, {"cell_type": "code", "execution_count": 41, "id": "0813413b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "      <th>instrument_key</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:HDFCBANK25JUN1900PE</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>0.05</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:ICICIBANK25JUN1400PE</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>125306</td>\n", "      <td>SBIN25JUN750PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-06-26</td>\n", "      <td>750.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:SBIN25JUN750PE</td>\n", "      <td>0.90</td>\n", "      <td>675.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "2          ********         125306        SBIN25JUN750PE       SBIN   \n", "\n", "      expiry  strike  tick_size  lot_size instrument_type  segment exchange  \\\n", "0 2025-06-26  1900.0       0.05       550              PE  NFO-OPT      NFO   \n", "1 2025-06-26  1400.0       0.05       700              PE  NFO-OPT      NFO   \n", "2 2025-06-26   750.0       0.05       750              PE  NFO-OPT      NFO   \n", "\n", "             instrument_key  last_price_of_option  premium_received  \n", "0   NFO:HDFCBANK25JUN1900PE                  8.95            4922.5  \n", "1  NFO:ICICIBANK25JUN1400PE                  6.45            4515.0  \n", "2        NFO:SBIN25JUN750PE                  0.90             675.0  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_puts_df.head()\n", "\n"]}, {"cell_type": "markdown", "id": "b521059b", "metadata": {}, "source": ["## Margin Calculation"]}, {"cell_type": "code", "execution_count": 42, "id": "59c0a8a7", "metadata": {}, "outputs": [], "source": ["base_order = {\n", "    \"exchange\":       \"NFO\",\n", "    \"transaction_type\": \"SELL\",\n", "    \"variety\":        \"regular\",\n", "    \"product\":        \"NRML\",\n", "    \"order_type\":     \"MARKET\",\n", "    \"price\":          0,\n", "    \"trigger_price\":  0\n", "}\n", "\n", "# Build orders list using tradingsymbol and quantity from DataFrame\n", "orders = [\n", "    {\n", "        **base_order,\n", "        \"tradingsymbol\": row[\"tradingsymbol\"],\n", "        \"quantity\": row[\"lot_size\"]  # Use 'lot_size' from each row\n", "    }\n", "    for _, row in selected_puts_df.iterrows()\n", "]"]}, {"cell_type": "code", "execution_count": 44, "id": "8612605c", "metadata": {}, "outputs": [], "source": ["margins_response = kite.order_margins(params=orders)"]}, {"cell_type": "code", "execution_count": 46, "id": "746cf53c", "metadata": {}, "outputs": [], "source": ["margin_df = pd.DataFrame([\n", "    {\n", "        'tradingsymbol': item['tradingsymbol'],\n", "        'total_margin': item['total']\n", "    }for item in margins_response\n", "])"]}, {"cell_type": "code", "execution_count": 47, "id": "9a2488e1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>total_margin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>163513.2125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>153305.7750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SBIN25JUN750PE</td>\n", "      <td>72196.1250</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          tradingsymbol  total_margin\n", "0   HDFCBANK25JUN1900PE   163513.2125\n", "1  ICICIBANK25JUN1400PE   153305.7750\n", "2        SBIN25JUN750PE    72196.1250"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["margin_df.head()\n"]}, {"cell_type": "code", "execution_count": 48, "id": "6c8aabac", "metadata": {}, "outputs": [], "source": ["selected_puts_df = selected_puts_df.merge(margin_df, on='tradingsymbol', how='inner')"]}, {"cell_type": "code", "execution_count": 53, "id": "cb65ed68", "metadata": {}, "outputs": [], "source": ["selected_puts_df['premium_to_margin_percentage'] = selected_puts_df['premium_received'] / selected_puts_df['total_margin']"]}, {"cell_type": "code", "execution_count": 54, "id": "8988305d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "      <th>instrument_key</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:HDFCBANK25JUN1900PE</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>0.05</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:ICICIBANK25JUN1400PE</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>125306</td>\n", "      <td>SBIN25JUN750PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-06-26</td>\n", "      <td>750.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:SBIN25JUN750PE</td>\n", "      <td>0.90</td>\n", "      <td>675.0</td>\n", "      <td>72196.1250</td>\n", "      <td>0.009350</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "2          ********         125306        SBIN25JUN750PE       SBIN   \n", "\n", "      expiry  strike  tick_size  lot_size instrument_type  segment exchange  \\\n", "0 2025-06-26  1900.0       0.05       550              PE  NFO-OPT      NFO   \n", "1 2025-06-26  1400.0       0.05       700              PE  NFO-OPT      NFO   \n", "2 2025-06-26   750.0       0.05       750              PE  NFO-OPT      NFO   \n", "\n", "             instrument_key  last_price_of_option  premium_received  \\\n", "0   NFO:HDFCBANK25JUN1900PE                  8.95            4922.5   \n", "1  NFO:ICICIBANK25JUN1400PE                  6.45            4515.0   \n", "2        NFO:SBIN25JUN750PE                  0.90             675.0   \n", "\n", "   total_margin  premium_to_margin_percentage  \n", "0   163513.2125                      0.030105  \n", "1   153305.7750                      0.029451  \n", "2    72196.1250                      0.009350  "]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_puts_df.head()"]}, {"cell_type": "code", "execution_count": 55, "id": "0851556a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "      <th>instrument_key</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:HDFCBANK25JUN1900PE</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>0.05</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NFO:ICICIBANK25JUN1400PE</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  tick_size  lot_size instrument_type  segment exchange  \\\n", "0 2025-06-26  1900.0       0.05       550              PE  NFO-OPT      NFO   \n", "1 2025-06-26  1400.0       0.05       700              PE  NFO-OPT      NFO   \n", "\n", "             instrument_key  last_price_of_option  premium_received  \\\n", "0   NFO:HDFCBANK25JUN1900PE                  8.95            4922.5   \n", "1  NFO:ICICIBANK25JUN1400PE                  6.45            4515.0   \n", "\n", "   total_margin  premium_to_margin_percentage  \n", "0   163513.2125                      0.030105  \n", "1   153305.7750                      0.029451  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions_temp = selected_puts_df[selected_puts_df['premium_to_margin_percentage'] >= 0.01]\n", "\n", "active_positions_temp.head()\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 57, "id": "3947c8df", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_trading_price_of_stock</th>\n", "      <th>target_strike</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [instrument_token, exchange_token, tradingsymbol, name, expiry, strike, lot_size, instrument_type, exchange, last_trading_price_of_stock, target_strike, last_price_of_option, premium_received, total_margin, premium_to_margin_percentage]\n", "Index: []"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions.head()"]}, {"cell_type": "code", "execution_count": 58, "id": "e426ad73", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11404\\328130511.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  active_positions_temp.drop(columns=['tick_size', 'segment', 'instrument_key'], inplace=True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  last_price_of_option  \\\n", "0 2025-06-26  1900.0       550              PE      NFO                  8.95   \n", "1 2025-06-26  1400.0       700              PE      NFO                  6.45   \n", "\n", "   premium_received  total_margin  premium_to_margin_percentage  \n", "0            4922.5   163513.2125                      0.030105  \n", "1            4515.0   153305.7750                      0.029451  "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions_temp.drop(columns=['tick_size', 'segment', 'instrument_key'], inplace=True)\n", "\n", "active_positions_temp.head()"]}, {"cell_type": "code", "execution_count": 60, "id": "899aa9df", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_trading_price_of_stock</th>\n", "      <th>target_strike</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [instrument_token, exchange_token, tradingsymbol, name, expiry, strike, lot_size, instrument_type, exchange, last_trading_price_of_stock, target_strike, last_price_of_option, premium_received, total_margin, premium_to_margin_percentage]\n", "Index: []"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions.head()"]}, {"cell_type": "code", "execution_count": 64, "id": "0c9a8cdd", "metadata": {}, "outputs": [], "source": ["stk_price_df.rename(columns = {'last_trading_price': 'last_trading_price_of_stock'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 68, "id": "f74cee01", "metadata": {}, "outputs": [], "source": ["active_positions_temp.drop(columns=['last_trading_price_of_stock', 'Intrinsic_value'], inplace=True)"]}, {"cell_type": "code", "execution_count": 71, "id": "efa86d5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument</th>\n", "      <th>name</th>\n", "      <th>last_trading_price_of_stock</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NSE:HDFCBANK</td>\n", "      <td>HDFCBANK</td>\n", "      <td>1925.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NSE:ICICIBANK</td>\n", "      <td>ICICIBANK</td>\n", "      <td>1420.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NSE:SBIN</td>\n", "      <td>SBIN</td>\n", "      <td>792.65</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      instrument       name  last_trading_price_of_stock\n", "0   NSE:HDFCBANK   HDFCBANK                      1925.70\n", "1  NSE:ICICIBANK  ICICIBANK                      1420.90\n", "2       NSE:SBIN       SBIN                       792.65"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_price_df.head()"]}, {"cell_type": "code", "execution_count": 72, "id": "ff9e1a08", "metadata": {}, "outputs": [], "source": ["# adding last stock price data\n", "active_positions_temp = active_positions_temp.merge(stk_price_df[['name', 'last_trading_price_of_stock']], on='name', how='inner')"]}, {"cell_type": "code", "execution_count": 73, "id": "6eed822e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "      <th>last_trading_price_of_stock</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "      <td>1925.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "      <td>1420.9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  last_price_of_option  \\\n", "0 2025-06-26  1900.0       550              PE      NFO                  8.95   \n", "1 2025-06-26  1400.0       700              PE      NFO                  6.45   \n", "\n", "   premium_received  total_margin  premium_to_margin_percentage  \\\n", "0            4922.5   163513.2125                      0.030105   \n", "1            4515.0   153305.7750                      0.029451   \n", "\n", "   last_trading_price_of_stock  \n", "0                       1925.7  \n", "1                       1420.9  "]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions_temp.head()"]}, {"cell_type": "code", "execution_count": 75, "id": "4d292b7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'HDFCBANK': 1900, 'ICICIBANK': 1400, 'SBIN': 750}"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["intrinsic_values"]}, {"cell_type": "code", "execution_count": 76, "id": "ca86d115", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>Intrinsic_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HDFCBANK</td>\n", "      <td>1900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ICICIBANK</td>\n", "      <td>1400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SBIN</td>\n", "      <td>750</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        name  Intrinsic_value\n", "0   HDFCBANK             1900\n", "1  ICICIBANK             1400\n", "2       SBIN              750"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["# adding_instrisic value\n", "intrisic_df = pd.DataFrame(list(intrinsic_values.items()), columns=['name', 'Intrinsic_value'])\n", "intrisic_df.head()"]}, {"cell_type": "code", "execution_count": 77, "id": "6ecdc7e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "      <th>last_trading_price_of_stock</th>\n", "      <th>Intrinsic_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "      <td>1925.7</td>\n", "      <td>1900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "      <td>1420.9</td>\n", "      <td>1400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  last_price_of_option  \\\n", "0 2025-06-26  1900.0       550              PE      NFO                  8.95   \n", "1 2025-06-26  1400.0       700              PE      NFO                  6.45   \n", "\n", "   premium_received  total_margin  premium_to_margin_percentage  \\\n", "0            4922.5   163513.2125                      0.030105   \n", "1            4515.0   153305.7750                      0.029451   \n", "\n", "   last_trading_price_of_stock  Intrinsic_value  \n", "0                       1925.7             1900  \n", "1                       1420.9             1400  "]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions_temp = active_positions_temp.merge(intrisic_df, on='name', how='inner')\n", "\n", "active_positions_temp.head()"]}, {"cell_type": "code", "execution_count": 78, "id": "38c4f5bd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_trading_price_of_stock</th>\n", "      <th>Intrinsic_value</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1925.7</td>\n", "      <td>1900</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1420.9</td>\n", "      <td>1400</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  \\\n", "0 2025-06-26  1900.0       550              PE      NFO   \n", "1 2025-06-26  1400.0       700              PE      NFO   \n", "\n", "   last_trading_price_of_stock  Intrinsic_value  last_price_of_option  \\\n", "0                       1925.7             1900                  8.95   \n", "1                       1420.9             1400                  6.45   \n", "\n", "   premium_received  total_margin  premium_to_margin_percentage  \n", "0            4922.5   163513.2125                      0.030105  \n", "1            4515.0   153305.7750                      0.029451  "]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions_temp = active_positions_temp.reindex(columns=active_positions.columns)\n", "\n", "active_positions_temp.head()"]}, {"cell_type": "code", "execution_count": 79, "id": "239f633f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11404\\3175951941.py:1: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  active_positions = pd.concat([active_positions, active_positions_temp], ignore_index=True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_trading_price_of_stock</th>\n", "      <th>Intrinsic_value</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1925.7</td>\n", "      <td>1900</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1420.9</td>\n", "      <td>1400</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  instrument_token exchange_token         tradingsymbol       name     expiry  \\\n", "0         ********          95040   HDFCBANK25JUN1900PE   HDFCBANK 2025-06-26   \n", "1         ********         100147  ICICIBANK25JUN1400PE  ICICIBANK 2025-06-26   \n", "\n", "   strike lot_size instrument_type exchange  last_trading_price_of_stock  \\\n", "0  1900.0      550              PE      NFO                       1925.7   \n", "1  1400.0      700              PE      NFO                       1420.9   \n", "\n", "  Intrinsic_value  last_price_of_option  premium_received  total_margin  \\\n", "0            1900                  8.95            4922.5   163513.2125   \n", "1            1400                  6.45            4515.0   153305.7750   \n", "\n", "   premium_to_margin_percentage  \n", "0                      0.030105  \n", "1                      0.029451  "]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions = pd.concat([active_positions, active_positions_temp], ignore_index=True)\n", "\n", "active_positions.head()"]}, {"cell_type": "markdown", "id": "f2a0ab05", "metadata": {}, "source": ["# active positions"]}, {"cell_type": "code", "execution_count": 80, "id": "b273625d", "metadata": {}, "outputs": [], "source": ["active_positions.to_csv('active_positions_strat2.csv', index=False)"]}, {"cell_type": "code", "execution_count": 81, "id": "c900c511", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>last_option_price</th>\n", "      <th>premium_paid</th>\n", "      <th>next_month_premium</th>\n", "      <th>stocks_hold</th>\n", "      <th>stocks_delivered</th>\n", "      <th>stock_transaction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [instrument_token, exchange_token, tradingsymbol, name, expiry, strike, lot_size, instrument_type, premium_received, total_margin, stock_price_on_exp, itm_otm, last_option_price, premium_paid, next_month_premium, stocks_hold, stocks_delivered, stock_transaction]\n", "Index: []"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["transaction_log.head()"]}, {"cell_type": "code", "execution_count": 82, "id": "e317c385", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>last_trading_price_of_stock</th>\n", "      <th>Intrinsic_value</th>\n", "      <th>last_price_of_option</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>premium_to_margin_percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1925.7</td>\n", "      <td>1900</td>\n", "      <td>8.95</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>0.030105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1420.9</td>\n", "      <td>1400</td>\n", "      <td>6.45</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>0.029451</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  \\\n", "0 2025-06-26  1900.0       550              PE      NFO   \n", "1 2025-06-26  1400.0       700              PE      NFO   \n", "\n", "   last_trading_price_of_stock  Intrinsic_value  last_price_of_option  \\\n", "0                       1925.7             1900                  8.95   \n", "1                       1420.9             1400                  6.45   \n", "\n", "   premium_received  total_margin  premium_to_margin_percentage  \n", "0            4922.5   163513.2125                      0.030105  \n", "1            4515.0   153305.7750                      0.029451  "]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["active_positions_temp.head()"]}, {"cell_type": "code", "execution_count": 84, "id": "7ba00378", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   total_margin  \n", "0   163513.2125  \n", "1   153305.7750  "]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log = active_positions_temp.copy()\n", "temp_transaction_log.drop(columns=['premium_to_margin_percentage', 'last_trading_price_of_stock', 'Intrinsic_value', 'last_price_of_option'], inplace=True)\n", "\n", "temp_transaction_log.head()\n"]}, {"cell_type": "markdown", "id": "9dc6897d", "metadata": {}, "source": ["# Expiry day calculations"]}, {"cell_type": "code", "execution_count": 96, "id": "9000b514", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>option_price_on_expiry</th>\n", "      <th>loss_incase_of _itm</th>\n", "      <th>premium_paid</th>\n", "      <th>next_month_premium</th>\n", "      <th>stocks_hold</th>\n", "      <th>stocks_delivered</th>\n", "      <th>stock_transaction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [instrument_token, exchange_token, tradingsymbol, name, expiry, strike, lot_size, instrument_type, premium_received, total_margin, stock_price_on_exp, itm_otm, option_price_on_expiry, loss_incase_of _itm, premium_paid, next_month_premium, stocks_hold, stocks_delivered, stock_transaction]\n", "Index: []"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["transaction_log.head()"]}, {"cell_type": "code", "execution_count": 86, "id": "6cfd811a", "metadata": {}, "outputs": [], "source": ["# dummy stock price on expiry\n", "stock_price_exp_df = pd.DataFrame({\n", "    'name': ['HDFCBANK', 'ICICIBANK'],\n", "    'stock_price_on_exp': [1850, 1350]\n", "})"]}, {"cell_type": "code", "execution_count": 88, "id": "17f209ce", "metadata": {}, "outputs": [], "source": ["temp_transaction_log = temp_transaction_log.merge(stock_price_exp_df, on='name', how='inner')"]}, {"cell_type": "code", "execution_count": 89, "id": "63067e48", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>1850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>1350</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   total_margin  stock_price_on_exp  \n", "0   163513.2125                1850  \n", "1   153305.7750                1350  "]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log.head()"]}, {"cell_type": "code", "execution_count": 90, "id": "9cd67270", "metadata": {}, "outputs": [], "source": ["# itm/OTM flagging\n", "temp_transaction_log['itm_otm'] =  np.where(\n", "    temp_transaction_log['stock_price_on_exp'] < temp_transaction_log['strike'],\n", "    'ITM',\n", "    'OTM'\n", ")"]}, {"cell_type": "code", "execution_count": 92, "id": "25aed72e", "metadata": {}, "outputs": [], "source": ["# dummy option price on expiry\n", "option_price_exp_df = pd.DataFrame({\n", "    'name': ['HDFCBANK', 'ICICIBANK'],\n", "    'option_price_on_expiry': [12, 9]\n", "})"]}, {"cell_type": "code", "execution_count": 93, "id": "ab629bb8", "metadata": {}, "outputs": [], "source": ["temp_transaction_log = temp_transaction_log.merge(option_price_exp_df, on='name', how='inner')\n", "\n"]}, {"cell_type": "code", "execution_count": 94, "id": "30762c9c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>option_price_on_expiry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>1850</td>\n", "      <td>ITM</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>1350</td>\n", "      <td>ITM</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   total_margin  stock_price_on_exp itm_otm  option_price_on_expiry  \n", "0   163513.2125                1850     ITM                      12  \n", "1   153305.7750                1350     ITM                       9  "]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fa09ef6e", "metadata": {}, "outputs": [], "source": ["#loss calculation\n", "temp_transaction_log['loss_incase_of _itm'] = (temp_transaction_log['strike'] - temp_transaction_log['stock_price_on_exp']) * temp_transaction_log['lot_size'] - temp_transaction_log['premium_received']"]}, {"cell_type": "code", "execution_count": 102, "id": "b8c900cc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>option_price_on_expiry</th>\n", "      <th>loss_incase_of _itm</th>\n", "      <th>premium_paid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>1850</td>\n", "      <td>ITM</td>\n", "      <td>12</td>\n", "      <td>22577.5</td>\n", "      <td>6600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>1350</td>\n", "      <td>ITM</td>\n", "      <td>9</td>\n", "      <td>30485.0</td>\n", "      <td>6300</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   total_margin  stock_price_on_exp itm_otm  option_price_on_expiry  \\\n", "0   163513.2125                1850     ITM                      12   \n", "1   153305.7750                1350     ITM                       9   \n", "\n", "   loss_incase_of _itm  premium_paid  \n", "0              22577.5          6600  \n", "1              30485.0          6300  "]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log.head()"]}, {"cell_type": "code", "execution_count": 101, "id": "b6eeaf71", "metadata": {}, "outputs": [], "source": ["# premium paid\n", "temp_transaction_log['premium_paid'] = temp_transaction_log['option_price_on_expiry'] * temp_transaction_log['lot_size']"]}, {"cell_type": "code", "execution_count": 103, "id": "0f4ee57e", "metadata": {}, "outputs": [], "source": ["# next month pemium of same put\n", "import re\n", "\n", "def replace_month_in_symbol(symbol, from_month='JUN', to_month='JUL'):\n", "    # Replace only the 3-letter month that follows 2-digit day (e.g., 25JUN → 25JUL)\n", "    return re.sub(r'(\\d{2})' + from_month, r'\\1' + to_month, symbol)"]}, {"cell_type": "code", "execution_count": 116, "id": "34ec157a", "metadata": {}, "outputs": [], "source": ["temp_transaction_log['next_month_symbol'] = temp_transaction_log['tradingsymbol'].apply(\n", "    lambda s: replace_month_in_symbol(s, from_month='JUN', to_month='JUL')\n", ")"]}, {"cell_type": "code", "execution_count": 117, "id": "ad10199b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>option_price_on_expiry</th>\n", "      <th>loss_incase_of _itm</th>\n", "      <th>premium_paid</th>\n", "      <th>next_month_symbol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>1850</td>\n", "      <td>ITM</td>\n", "      <td>12</td>\n", "      <td>22577.5</td>\n", "      <td>6600</td>\n", "      <td>HDFCBANK25JUL1900PE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>1350</td>\n", "      <td>ITM</td>\n", "      <td>9</td>\n", "      <td>30485.0</td>\n", "      <td>6300</td>\n", "      <td>ICICIBANK25JUL1400PE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   total_margin  stock_price_on_exp itm_otm  option_price_on_expiry  \\\n", "0   163513.2125                1850     ITM                      12   \n", "1   153305.7750                1350     ITM                       9   \n", "\n", "   loss_incase_of _itm  premium_paid     next_month_symbol  \n", "0              22577.5          6600   HDFCBANK25JUL1900PE  \n", "1              30485.0          6300  ICICIBANK25JUL1400PE  "]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log.head()"]}, {"cell_type": "code", "execution_count": 121, "id": "6804c603", "metadata": {}, "outputs": [], "source": ["next_month_instruments = temp_transaction_log.apply(\n", "    lambda row: f\"{row['exchange']}:{row['tradingsymbol']}\", axis=1\n", ").to_list()"]}, {"cell_type": "code", "execution_count": 122, "id": "151af181", "metadata": {}, "outputs": [{"data": {"text/plain": ["['NFO:HDFCBANK25JUN1900PE', 'NFO:ICICIBANK25JUN1400PE']"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "source": ["next_month_instruments"]}, {"cell_type": "code", "execution_count": 123, "id": "a2329c95", "metadata": {}, "outputs": [], "source": ["quote_data = kite.quote(next_month_instruments)"]}, {"cell_type": "code", "execution_count": 125, "id": "d5a48b44", "metadata": {}, "outputs": [], "source": ["new_quote_df = pd.DataFrame([\n", "    {\n", "        'tradingsymbol': instrument_key.split(':')[1],\n", "        'option_price_next_month': quote_data[instrument_key]['last_price']\n", "    }\n", "    for instrument_key in quote_data.keys()\n", "])"]}, {"cell_type": "code", "execution_count": 126, "id": "01f78b4a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>option_price_next_month</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>7.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>6.20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          tradingsymbol  option_price_next_month\n", "0   HDFCBANK25JUN1900PE                     7.75\n", "1  ICICIBANK25JUN1400PE                     6.20"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["new_quote_df.head()"]}, {"cell_type": "code", "execution_count": 127, "id": "5069c6f1", "metadata": {}, "outputs": [], "source": ["temp_transaction_log = temp_transaction_log.merge(new_quote_df, on='tradingsymbol', how='inner')\n"]}, {"cell_type": "code", "execution_count": 130, "id": "fd015ca2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>option_price_on_expiry</th>\n", "      <th>loss_incase_of _itm</th>\n", "      <th>premium_paid</th>\n", "      <th>next_month_symbol</th>\n", "      <th>option_price_next_month</th>\n", "      <th>next_month_premium</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>163513.2125</td>\n", "      <td>1850</td>\n", "      <td>ITM</td>\n", "      <td>12</td>\n", "      <td>22577.5</td>\n", "      <td>6600</td>\n", "      <td>HDFCBANK25JUL1900PE</td>\n", "      <td>7.75</td>\n", "      <td>4262.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>153305.7750</td>\n", "      <td>1350</td>\n", "      <td>ITM</td>\n", "      <td>9</td>\n", "      <td>30485.0</td>\n", "      <td>6300</td>\n", "      <td>ICICIBANK25JUL1400PE</td>\n", "      <td>6.20</td>\n", "      <td>4340.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   total_margin  stock_price_on_exp itm_otm  option_price_on_expiry  \\\n", "0   163513.2125                1850     ITM                      12   \n", "1   153305.7750                1350     ITM                       9   \n", "\n", "   loss_incase_of _itm  premium_paid     next_month_symbol  \\\n", "0              22577.5          6600   HDFCBANK25JUL1900PE   \n", "1              30485.0          6300  ICICIBANK25JUL1400PE   \n", "\n", "   option_price_next_month  next_month_premium  \n", "0                     7.75              4262.5  \n", "1                     6.20              4340.0  "]}, "execution_count": 130, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log.head()"]}, {"cell_type": "code", "execution_count": 129, "id": "29861a68", "metadata": {}, "outputs": [], "source": ["# next month premium\n", "temp_transaction_log['next_month_premium'] = temp_transaction_log['option_price_next_month'] * temp_transaction_log['lot_size']\n"]}, {"cell_type": "code", "execution_count": 131, "id": "35647a6e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>premium_received</th>\n", "      <th>total_margin</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>option_price_on_expiry</th>\n", "      <th>loss_incase_of _itm</th>\n", "      <th>premium_paid</th>\n", "      <th>next_month_premium</th>\n", "      <th>stocks_hold</th>\n", "      <th>stocks_delivered</th>\n", "      <th>stock_transaction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [instrument_token, exchange_token, tradingsymbol, name, expiry, strike, lot_size, instrument_type, premium_received, total_margin, stock_price_on_exp, itm_otm, option_price_on_expiry, loss_incase_of _itm, premium_paid, next_month_premium, stocks_hold, stocks_delivered, stock_transaction]\n", "Index: []"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["transaction_log.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7b8a1ab5", "metadata": {}, "outputs": [], "source": ["# next month premium < loss in case of itm hence no rollover.\n", "temp_transaction_log['stocks_hold'] = temp_transaction_log['lot_size']\n", "temp_transaction_log['stocks_delivered'] = 0\n", "\n"]}, {"cell_type": "code", "execution_count": 133, "id": "561b908e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>...</th>\n", "      <th>stock_price_on_exp</th>\n", "      <th>itm_otm</th>\n", "      <th>option_price_on_expiry</th>\n", "      <th>loss_incase_of _itm</th>\n", "      <th>premium_paid</th>\n", "      <th>next_month_symbol</th>\n", "      <th>option_price_next_month</th>\n", "      <th>next_month_premium</th>\n", "      <th>stocks_hold</th>\n", "      <th>stocks_delivered</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>...</td>\n", "      <td>1850</td>\n", "      <td>ITM</td>\n", "      <td>12</td>\n", "      <td>22577.5</td>\n", "      <td>6600</td>\n", "      <td>HDFCBANK25JUL1900PE</td>\n", "      <td>7.75</td>\n", "      <td>4262.5</td>\n", "      <td>550</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>...</td>\n", "      <td>1350</td>\n", "      <td>ITM</td>\n", "      <td>9</td>\n", "      <td>30485.0</td>\n", "      <td>6300</td>\n", "      <td>ICICIBANK25JUL1400PE</td>\n", "      <td>6.20</td>\n", "      <td>4340.0</td>\n", "      <td>700</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   ...  stock_price_on_exp  itm_otm option_price_on_expiry  \\\n", "0  ...                1850      ITM                     12   \n", "1  ...                1350      ITM                      9   \n", "\n", "   loss_incase_of _itm  premium_paid     next_month_symbol  \\\n", "0              22577.5          6600   HDFCBANK25JUL1900PE   \n", "1              30485.0          6300  ICICIBANK25JUL1400PE   \n", "\n", "  option_price_next_month  next_month_premium  stocks_hold  stocks_delivered  \n", "0                    7.75              4262.5          550                 0  \n", "1                    6.20              4340.0          700                 0  \n", "\n", "[2 rows x 21 columns]"]}, "execution_count": 133, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log.head()"]}, {"cell_type": "code", "execution_count": 134, "id": "4c519954", "metadata": {}, "outputs": [], "source": ["temp_transaction_log['stock_hold_value'] = temp_transaction_log['stocks_hold'] * temp_transaction_log['strike']\n", "temp_transaction_log['stock_delivered_value'] = temp_transaction_log['stocks_delivered'] * temp_transaction_log['strike']"]}, {"cell_type": "code", "execution_count": 135, "id": "6afdea44", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>premium_received</th>\n", "      <th>...</th>\n", "      <th>option_price_on_expiry</th>\n", "      <th>loss_incase_of _itm</th>\n", "      <th>premium_paid</th>\n", "      <th>next_month_symbol</th>\n", "      <th>option_price_next_month</th>\n", "      <th>next_month_premium</th>\n", "      <th>stocks_hold</th>\n", "      <th>stocks_delivered</th>\n", "      <th>stock_hold_value</th>\n", "      <th>stock_delivered_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>95040</td>\n", "      <td>HDFCBANK25JUN1900PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1900.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4922.5</td>\n", "      <td>...</td>\n", "      <td>12</td>\n", "      <td>22577.5</td>\n", "      <td>6600</td>\n", "      <td>HDFCBANK25JUL1900PE</td>\n", "      <td>7.75</td>\n", "      <td>4262.5</td>\n", "      <td>550</td>\n", "      <td>0</td>\n", "      <td>1045000.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>100147</td>\n", "      <td>ICICIBANK25JUN1400PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-06-26</td>\n", "      <td>1400.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>4515.0</td>\n", "      <td>...</td>\n", "      <td>9</td>\n", "      <td>30485.0</td>\n", "      <td>6300</td>\n", "      <td>ICICIBANK25JUL1400PE</td>\n", "      <td>6.20</td>\n", "      <td>4340.0</td>\n", "      <td>700</td>\n", "      <td>0</td>\n", "      <td>980000.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 23 columns</p>\n", "</div>"], "text/plain": ["   instrument_token exchange_token         tradingsymbol       name  \\\n", "0          ********          95040   HDFCBANK25JUN1900PE   HDFCBANK   \n", "1          ********         100147  ICICIBANK25JUN1400PE  ICICIBANK   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  premium_received  \\\n", "0 2025-06-26  1900.0       550              PE      NFO            4922.5   \n", "1 2025-06-26  1400.0       700              PE      NFO            4515.0   \n", "\n", "   ...  option_price_on_expiry  loss_incase_of _itm premium_paid  \\\n", "0  ...                      12              22577.5         6600   \n", "1  ...                       9              30485.0         6300   \n", "\n", "      next_month_symbol  option_price_next_month  next_month_premium  \\\n", "0   HDFCBANK25JUL1900PE                     7.75              4262.5   \n", "1  ICICIBANK25JUL1400PE                     6.20              4340.0   \n", "\n", "  stocks_hold  stocks_delivered  stock_hold_value  stock_delivered_value  \n", "0         550                 0         1045000.0                    0.0  \n", "1         700                 0          980000.0                    0.0  \n", "\n", "[2 rows x 23 columns]"]}, "execution_count": 135, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_transaction_log.head()"]}, {"cell_type": "code", "execution_count": 136, "id": "43b066e6", "metadata": {}, "outputs": [], "source": ["temp_transaction_log.to_csv('transaction_log_strat2.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}