{"cells": [{"cell_type": "code", "execution_count": null, "id": "e3d7a804", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "\n", "# Assuming instruments_df and stk_price_df are already loaded\n", "# instruments_df is created from the provided instruments.csv\n", "# stk_price_df contains the average stock prices as shown\n", "\n", "# Step 1: Filter for put options (PE) of target companies\n", "target_banks = [\n", "    'ICICI BANK',\n", "    'HDFC BANK',\n", "    'STATE BANK OF INDIA',\n", "    'AXIS BANK',\n", "    'KOTAK MAHINDRA BANK',\n", "    'MUTHOOT FINANCE',\n", "    'MANAPPURAM FINANCE'\n", "]\n", "\n", "# Standardize company names to match tradingsymbol format\n", "symbol_mapping = {\n", "    'ICICI BANK': 'ICICIBANK',\n", "    'HDFC BANK': 'HDFCBANK',\n", "    'STATE BANK OF INDIA': 'SBIN',\n", "    'AXIS BANK': 'AXISBANK',\n", "    'KOTAK MAHINDRA BANK': 'KOTAKBANK',\n", "    'MUTHOOT FINANCE': 'MUTHOOTFIN',\n", "    'MANAPPURAM FINANCE': 'MANAPPURAM'\n", "}\n", "\n", "# Filter for put options (PE) of target companies\n", "put_options = instruments_df[\n", "    (instruments_df['instrument_type'] == 'PE') & \n", "    (instruments_df['name'].isin(target_banks))\n", "].copy()\n", "\n", "# Step 2: Find the nearest expiry date for each company\n", "put_options['expiry'] = pd.to_datetime(put_options['expiry'])\n", "current_date = pd.to_datetime('2025-05-23')  # Current date as per context\n", "put_options['days_to_expiry'] = (put_options['expiry'] - current_date).dt.days\n", "\n", "# Group by company name and find the minimum expiry date\n", "nearest_expiry = put_options.groupby('name')['expiry'].min().reset_index()\n", "\n", "# Step 3: Filter put options with nearest expiry and select strike price ~5% below current price\n", "# Merge with stk_price_df to get average prices\n", "stk_price_df['name'] = stk_price_df['symbol'].map(symbol_mapping)\n", "merged_df = put_options.merge(stk_price_df[['name', 'average_price']], on='name', how='left')\n", "\n", "# Filter for nearest expiry date\n", "merged_df = merged_df.merge(nearest_expiry, on=['name', 'expiry'], how='inner')\n", "\n", "# Calculate target strike price (5% below average price)\n", "merged_df['target_strike'] = merged_df['average_price'] * 0.95\n", "\n", "# Find the option with strike price closest to 5% below average price, within 0-5% range\n", "def find_closest_strike(group):\n", "    # Filter strikes within 0-5% below average price\n", "    valid_strikes = group[\n", "        (group['strike'] <= group['average_price']) & \n", "        (group['strike'] >= group['average_price'] * 0.95)\n", "    ]\n", "    if not valid_strikes.empty:\n", "        # Find the minimum strike price in the valid range\n", "        return valid_strikes.loc[valid_strikes['strike'].idxmin()]\n", "    return pd.Series()\n", "\n", "result = merged_df.groupby('name').apply(find_closest_strike).reset_index()\n", "\n", "# Select relevant columns for output\n", "result = result[['name', 'tradingsymbol', 'expiry', 'strike', 'average_price', 'target_strike']]\n", "\n", "# Format expiry date\n", "result['expiry'] = result['expiry'].dt.strftime('%Y-%m-%d')\n", "\n", "# Display the result\n", "print(result)"]}, {"cell_type": "code", "execution_count": 1, "id": "6b940362", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b\n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame({\n", "    'strike': [22500, 22000, 23000],\n", "    'price': [100, 120, 90]\n", "}, index=['a', 'b', 'c'])\n", "\n", "print(df['strike'].idxmin())  # Output: 'b'\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6d8efc89", "metadata": {}, "outputs": [{"data": {"text/plain": ["strike    22000\n", "price       120\n", "Name: b, dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df['strike'].idxmin()]"]}, {"cell_type": "code", "execution_count": 1, "id": "dc20d2e4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from kiteconnect import KiteConnect"]}, {"cell_type": "code", "execution_count": 2, "id": "acd0236a", "metadata": {}, "outputs": [], "source": ["api_key = \"3q0r6l1fb689slhk\"\n", "access_token = \"J4L2gokb0CNBVjeoKqT1p11i61s1q0kl\"\n", "\n", "kite = KiteConnect(api_key=api_key)\n", "kite.set_access_token(access_token)"]}, {"cell_type": "code", "execution_count": 3, "id": "bc760c4c", "metadata": {}, "outputs": [], "source": ["instruments = kite.instruments()\n", "import pandas as pd\n", "instruments_df = pd.DataFrame(instruments)"]}, {"cell_type": "code", "execution_count": 4, "id": "3f9d6cc5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>*********</td>\n", "      <td>874879</td>\n", "      <td>BANKEX25MAYFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>*********</td>\n", "      <td>1100996</td>\n", "      <td>BANKEX25JUNFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>*********</td>\n", "      <td>1141118</td>\n", "      <td>BANKEX25JULFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-07-29</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>*********</td>\n", "      <td>874759</td>\n", "      <td>SENSEX25MAYFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>*********</td>\n", "      <td>1124583</td>\n", "      <td>SENSEX25603FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-03</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>*********</td>\n", "      <td>1136192</td>\n", "      <td>SENSEX25610FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-10</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>*********</td>\n", "      <td>1141074</td>\n", "      <td>SENSEX25617FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-17</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>*********</td>\n", "      <td>1100924</td>\n", "      <td>SENSEX25JUNFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0.0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token exchange_token   tradingsymbol    name  last_price  \\\n", "0         *********         874879  BANKEX25MAYFUT  BANKEX         0.0   \n", "1         *********        1100996  BANKEX25JUNFUT  BANKEX         0.0   \n", "2         *********        1141118  BANKEX25JULFUT  BANKEX         0.0   \n", "3         *********         874759  SENSEX25MAYFUT  SENSEX         0.0   \n", "4         *********        1124583  SENSEX25603FUT  SENSEX         0.0   \n", "5         *********        1136192  SENSEX25610FUT  SENSEX         0.0   \n", "6         *********        1141074  SENSEX25617FUT  SENSEX         0.0   \n", "7         *********        1100924  SENSEX25JUNFUT  SENSEX         0.0   \n", "\n", "       expiry  strike  tick_size  lot_size instrument_type  segment exchange  \n", "0  2025-05-27     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "1  2025-06-24     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "2  2025-07-29     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "3  2025-05-27     0.0       0.05        20             FUT  BFO-FUT      BFO  \n", "4  2025-06-03     0.0       0.05        20             FUT  BFO-FUT      BFO  \n", "5  2025-06-10     0.0       0.05        20             FUT  BFO-FUT      BFO  \n", "6  2025-06-17     0.0       0.05        20             FUT  BFO-FUT      BFO  \n", "7  2025-06-24     0.0       0.05        20             FUT  BFO-FUT      BFO  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["instruments_df.head(8)"]}, {"cell_type": "code", "execution_count": 5, "id": "68743629", "metadata": {}, "outputs": [{"data": {"text/plain": ["(94747, 12)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["instruments_df.shape"]}, {"cell_type": "code", "execution_count": 2, "id": "11397682", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": ["from datetime import datetime, timedelta\n", "today = datetime.today()\n", "expiry = \"29-05-2025\"\n", "differnce = datetime.strptime(expiry, '%d-%m-%Y') - today\n", "print(differnce.days)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d48b568e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{1: 2, 3: 4}, {5: 2, 6: 4}]\n"]}], "source": ["a= [{1:2, 3:4}]\n", "b= [{5:2, 6:4}]\n", "c = a+b\n", "print(c)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}