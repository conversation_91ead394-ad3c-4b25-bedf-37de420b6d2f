{"cells": [{"cell_type": "markdown", "id": "5bcb70ed", "metadata": {}, "source": ["## Loading the industry code file"]}, {"cell_type": "code", "execution_count": 1, "id": "939cdfaa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Load the uploaded file\n", "file_path = \"ce.industry\"\n", "\n", "df = pd.read_csv(file_path, sep='\\t')\n"]}, {"cell_type": "code", "execution_count": 2, "id": "399da45f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>publishing_status</th>\n", "      <th>industry_name</th>\n", "      <th>display_level</th>\n", "      <th>selectable</th>\n", "      <th>sort_sequence</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>B</td>\n", "      <td>Total nonfarm</td>\n", "      <td>0</td>\n", "      <td>T</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5000000</td>\n", "      <td>-</td>\n", "      <td>A</td>\n", "      <td>Total private</td>\n", "      <td>1</td>\n", "      <td>T</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6000000</td>\n", "      <td>-</td>\n", "      <td>A</td>\n", "      <td>Goods-producing</td>\n", "      <td>1</td>\n", "      <td>T</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7000000</td>\n", "      <td>-</td>\n", "      <td>B</td>\n", "      <td>Service-providing</td>\n", "      <td>1</td>\n", "      <td>T</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8000000</td>\n", "      <td>-</td>\n", "      <td>A</td>\n", "      <td>Private service-providing</td>\n", "      <td>1</td>\n", "      <td>T</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>10000000</td>\n", "      <td>-</td>\n", "      <td>A</td>\n", "      <td>Mining and logging</td>\n", "      <td>2</td>\n", "      <td>T</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>10113300</td>\n", "      <td>1133</td>\n", "      <td>A</td>\n", "      <td>Logging</td>\n", "      <td>5</td>\n", "      <td>T</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10210000</td>\n", "      <td>21</td>\n", "      <td>A</td>\n", "      <td>Mining, quarrying, and oil and gas extraction</td>\n", "      <td>3</td>\n", "      <td>T</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>10211000</td>\n", "      <td>211</td>\n", "      <td>A</td>\n", "      <td>Oil and gas extraction</td>\n", "      <td>4</td>\n", "      <td>T</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10212000</td>\n", "      <td>212</td>\n", "      <td>A</td>\n", "      <td>Mining (except oil and gas)</td>\n", "      <td>4</td>\n", "      <td>T</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>10212100</td>\n", "      <td>2121</td>\n", "      <td>B</td>\n", "      <td>Coal mining</td>\n", "      <td>5</td>\n", "      <td>T</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>10212114</td>\n", "      <td>212114</td>\n", "      <td>C</td>\n", "      <td>Surface coal mining</td>\n", "      <td>7</td>\n", "      <td>T</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>10212115</td>\n", "      <td>212115</td>\n", "      <td>C</td>\n", "      <td>Underground coal mining</td>\n", "      <td>7</td>\n", "      <td>T</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>10212200</td>\n", "      <td>2122</td>\n", "      <td>B</td>\n", "      <td>Metal ore mining</td>\n", "      <td>5</td>\n", "      <td>T</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>10212220</td>\n", "      <td>21222</td>\n", "      <td>C</td>\n", "      <td>Gold ore and silver ore mining</td>\n", "      <td>6</td>\n", "      <td>T</td>\n", "      <td>15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    industry_code naics_code publishing_status  \\\n", "0               0          -                 B   \n", "1         5000000          -                 A   \n", "2         6000000          -                 A   \n", "3         7000000          -                 B   \n", "4         8000000          -                 A   \n", "5        10000000          -                 A   \n", "6        10113300       1133                 A   \n", "7        10210000         21                 A   \n", "8        10211000        211                 A   \n", "9        10212000        212                 A   \n", "10       10212100       2121                 B   \n", "11       10212114     212114                 C   \n", "12       10212115     212115                 C   \n", "13       10212200       2122                 B   \n", "14       10212220      21222                 C   \n", "\n", "                                    industry_name  display_level selectable  \\\n", "0                                   Total nonfarm              0          T   \n", "1                                   Total private              1          T   \n", "2                                 Goods-producing              1          T   \n", "3                               Service-providing              1          T   \n", "4                       Private service-providing              1          T   \n", "5                              Mining and logging              2          T   \n", "6                                         Logging              5          T   \n", "7   Mining, quarrying, and oil and gas extraction              3          T   \n", "8                          Oil and gas extraction              4          T   \n", "9                     Mining (except oil and gas)              4          T   \n", "10                                    Coal mining              5          T   \n", "11                            Surface coal mining              7          T   \n", "12                        Underground coal mining              7          T   \n", "13                               Metal ore mining              5          T   \n", "14                 Gold ore and silver ore mining              6          T   \n", "\n", "    sort_sequence  \n", "0               1  \n", "1               2  \n", "2               3  \n", "3               4  \n", "4               5  \n", "5               6  \n", "6               7  \n", "7               8  \n", "8               9  \n", "9              10  \n", "10             11  \n", "11             12  \n", "12             13  \n", "13             14  \n", "14             15  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(15)"]}, {"cell_type": "markdown", "id": "fef730f6", "metadata": {}, "source": ["## Filetering rows with four digit NAICS code starting from 31"]}, {"cell_type": "code", "execution_count": 3, "id": "496049e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>32311100</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>32311200</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>32311300</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>32311400</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>32311500</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     industry_code naics_code  \\\n", "211       32311100       3111   \n", "212       32311200       3112   \n", "213       32311300       3113   \n", "215       32311400       3114   \n", "220       32311500       3115   \n", "\n", "                                         industry_name  \n", "211                          Animal food manufacturing  \n", "212                          Grain and oilseed milling  \n", "213      Sugar and confectionery product manufacturing  \n", "215  Fruit and vegetable preserving and specialty f...  \n", "220                        Dairy product manufacturing  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter for rows where the NAICS code starts with '31' and is 4 digits long\n", "filtered_df = df[df['naics_code'].str.match(r'^31\\d{2}$', na=False)]\n", "\n", "filtered_df = filtered_df[['industry_code', 'naics_code', 'industry_name']]\n", "\n", "filtered_df.head()\n"]}, {"cell_type": "code", "execution_count": 4, "id": "62acfa4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13\n"]}], "source": ["print(len(filtered_df))\n"]}, {"cell_type": "markdown", "id": "d80cef18", "metadata": {}, "source": ["## Filtering rows for NAICS code starting from 32"]}, {"cell_type": "code", "execution_count": 5, "id": "7433d78d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>31321100</td>\n", "      <td>3211</td>\n", "      <td>Sawmills and wood preservation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>31321200</td>\n", "      <td>3212</td>\n", "      <td>Veneer, plywood, and engineered wood product m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>31321900</td>\n", "      <td>3219</td>\n", "      <td>Other wood product manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>31327100</td>\n", "      <td>3271</td>\n", "      <td>Clay product and refractory manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>31327200</td>\n", "      <td>3272</td>\n", "      <td>Glass and glass product manufacturing</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    industry_code naics_code  \\\n", "82       31321100       3211   \n", "83       31321200       3212   \n", "84       31321900       3219   \n", "91       31327100       3271   \n", "92       31327200       3272   \n", "\n", "                                        industry_name  \n", "82                     Sawmills and wood preservation  \n", "83  Veneer, plywood, and engineered wood product m...  \n", "84                   Other wood product manufacturing  \n", "91          Clay product and refractory manufacturing  \n", "92              Glass and glass product manufacturing  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df_32 = df[df['naics_code'].str.match(r'^32\\d{2}$', na=False)]\n", "\n", "filtered_df_32 = filtered_df_32[['industry_code', 'naics_code', 'industry_name']]\n", "\n", "filtered_df_32.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "e6a36310", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["17\n"]}], "source": ["print(len(filtered_df_32))\n"]}, {"cell_type": "markdown", "id": "ee1e1df1", "metadata": {}, "source": ["## filtering for NAICS code starting with 33"]}, {"cell_type": "code", "execution_count": 7, "id": "03d9a280", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>31331100</td>\n", "      <td>3311</td>\n", "      <td>Iron and steel mills and ferroalloy manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>31331200</td>\n", "      <td>3312</td>\n", "      <td>Steel product manufacturing from purchased steel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>31331500</td>\n", "      <td>3315</td>\n", "      <td>Foundries</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>31332100</td>\n", "      <td>3321</td>\n", "      <td>Forging and stamping</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>31332300</td>\n", "      <td>3323</td>\n", "      <td>Architectural and structural metals manufacturing</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     industry_code naics_code  \\\n", "98        31331100       3311   \n", "99        31331200       3312   \n", "101       31331500       3315   \n", "105       31332100       3321   \n", "106       31332300       3323   \n", "\n", "                                         industry_name  \n", "98   Iron and steel mills and ferroalloy manufacturing  \n", "99    Steel product manufacturing from purchased steel  \n", "101                                          Foundries  \n", "105                               Forging and stamping  \n", "106  Architectural and structural metals manufacturing  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df_33 = df[df['naics_code'].str.match(r'^33\\d{2}$', na=False)]\n", "\n", "filtered_df_33 = filtered_df_33[['industry_code', 'naics_code', 'industry_name']]\n", "\n", "filtered_df_33.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "306a7c33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["33\n"]}], "source": ["print(len(filtered_df_33))"]}, {"cell_type": "markdown", "id": "e127a301", "metadata": {}, "source": ["## Combining the three dataframe"]}, {"cell_type": "code", "execution_count": 9, "id": "ae0475e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of rows: 63\n"]}], "source": ["# Combine the three dataframes\n", "combined_df = pd.concat([filtered_df, filtered_df_32, filtered_df_33], ignore_index=True)\n", "\n", "print(f\"Total number of rows: {len(combined_df)}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "dad63190", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>32311100</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>32311200</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32311300</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>32311400</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32311500</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   industry_code naics_code                                      industry_name\n", "0       32311100       3111                          Animal food manufacturing\n", "1       32311200       3112                          Grain and oilseed milling\n", "2       32311300       3113      Sugar and confectionery product manufacturing\n", "3       32311400       3114  Fruit and vegetable preserving and specialty f...\n", "4       32311500       3115                        Dairy product manufacturing"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f4211923", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "      <th>series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>32311100</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>32311100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>32311200</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>32311200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32311300</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>32311300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>32311400</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>32311400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32311500</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>32311500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   industry_code naics_code  \\\n", "0       32311100       3111   \n", "1       32311200       3112   \n", "2       32311300       3113   \n", "3       32311400       3114   \n", "4       32311500       3115   \n", "\n", "                                       industry_name  series_id  \n", "0                          Animal food manufacturing   32311100  \n", "1                          Grain and oilseed milling   32311200  \n", "2      Sugar and confectionery product manufacturing   32311300  \n", "3  Fruit and vegetable preserving and specialty f...   32311400  \n", "4                        Dairy product manufacturing   32311500  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Add new column 'series id' with values from 'industry_code'\n", "combined_df['series_id'] = combined_df['industry_code']\n", "\n", "combined_df.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "c86cacce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of missing values in each column:\n", "industry_code    0\n", "naics_code       0\n", "industry_name    0\n", "series_id        0\n", "dtype: int64\n"]}], "source": ["print(\"Number of missing values in each column:\")\n", "print(combined_df.isnull().sum())"]}, {"cell_type": "markdown", "id": "dbb20656", "metadata": {}, "source": ["## Adding \"CEU\" Prefix to Series id"]}, {"cell_type": "code", "execution_count": 13, "id": "530c3908", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "      <th>series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>32311100</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>CEU32311100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>32311200</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>CEU32311200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32311300</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>CEU32311300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>32311400</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>CEU32311400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32311500</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>CEU32311500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   industry_code naics_code  \\\n", "0       32311100       3111   \n", "1       32311200       3112   \n", "2       32311300       3113   \n", "3       32311400       3114   \n", "4       32311500       3115   \n", "\n", "                                       industry_name    series_id  \n", "0                          Animal food manufacturing  CEU32311100  \n", "1                          Grain and oilseed milling  CEU32311200  \n", "2      Sugar and confectionery product manufacturing  CEU32311300  \n", "3  Fruit and vegetable preserving and specialty f...  CEU32311400  \n", "4                        Dairy product manufacturing  CEU32311500  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df['series_id'] = 'CEU' + combined_df['series_id'].astype(str)\n", "\n", "combined_df.head()"]}, {"cell_type": "markdown", "id": "9a439b2f", "metadata": {}, "source": ["## Adding \"01\" to the end of each series id"]}, {"cell_type": "code", "execution_count": 14, "id": "3a66d70a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "      <th>series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>32311100</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>CEU3231110001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>32311200</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>CEU3231120001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32311300</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>CEU3231130001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>32311400</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>CEU3231140001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32311500</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>CEU3231150001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   industry_code naics_code  \\\n", "0       32311100       3111   \n", "1       32311200       3112   \n", "2       32311300       3113   \n", "3       32311400       3114   \n", "4       32311500       3115   \n", "\n", "                                       industry_name      series_id  \n", "0                          Animal food manufacturing  CEU3231110001  \n", "1                          Grain and oilseed milling  CEU3231120001  \n", "2      Sugar and confectionery product manufacturing  CEU3231130001  \n", "3  Fruit and vegetable preserving and specialty f...  CEU3231140001  \n", "4                        Dairy product manufacturing  CEU3231150001  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df['series_id'] = combined_df['series_id'] + \"01\"\n", "\n", "combined_df.head()"]}, {"cell_type": "markdown", "id": "f9033367", "metadata": {}, "source": ["## Extracting list of series id"]}, {"cell_type": "code", "execution_count": 15, "id": "22e183a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 series ids:\n", "['CEU3231110001', 'CEU3231120001', 'CEU3231130001', 'CEU3231140001', 'CEU3231150001']\n", "\n", "Total number of series ids: 63\n"]}], "source": ["# Convert series_code column to list of strings\n", "series_id_list = combined_df['series_id'].tolist()\n", "\n", "print(\"First 5 series ids:\")\n", "print(series_id_list[:5])\n", "\n", "print(f\"\\nTotal number of series ids: {len(series_id_list)}\")"]}, {"cell_type": "markdown", "id": "46ed288a", "metadata": {}, "source": ["## Getting the data"]}, {"cell_type": "markdown", "id": "f172053f", "metadata": {}, "source": ["### for first 25 series ids."]}, {"cell_type": "code", "execution_count": 16, "id": "361cb100", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "headers = {'Content-type': 'application/json'}\n", "data_1 = json.dumps({\"seriesid\": series_id_list[:25],\"startyear\":\"2023\", \"endyear\":\"2024\"})\n", "#print(data_1)\n", "p = requests.post('https://api.bls.gov/publicAPI/v1/timeseries/data/', data=data_1, headers=headers)\n", "json_data = json.loads(p.text)"]}, {"cell_type": "code", "execution_count": 1, "id": "f930dd12", "metadata": {}, "outputs": [], "source": ["ids =['CES3231120001',\n", "'CES3133640001',\n", "'CES3231180001',\n", "'CES3231170001',\n", "'CES3133530001',\n", "'CES3133610001']\n"]}, {"cell_type": "code", "execution_count": null, "id": "94993b4a", "metadata": {}, "outputs": [], "source": ["CEU3231210001"]}, {"cell_type": "code", "execution_count": 2, "id": "31f29b35", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "headers = {'Content-type': 'application/json'}\n", "data_1 = json.dumps({\"seriesid\": ids,\"startyear\":\"2023\", \"endyear\":\"2025\"})\n", "#print(data_1)\n", "p = requests.post('https://api.bls.gov/publicAPI/v1/timeseries/data/', data=data_1, headers=headers)\n", "json_data = json.loads(p.text)"]}, {"cell_type": "code", "execution_count": 3, "id": "7e7eca8d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"status\": \"REQUEST_SUCCEEDED\",\n", "    \"responseTime\": 129,\n", "    \"message\": [],\n", "    \"Results\": {\n", "        \"series\": [\n", "            {\n", "                \"seriesID\": \"CES3231120001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"68.2\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"68.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"68.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"68.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"68.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"68.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"68.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"68.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"67.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"67.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"66.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"67.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"66.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"66.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"66.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"66.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"65.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"65.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"65.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"64.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"64.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"64.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"64.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"64.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"64.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"64.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"64.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"63.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133640001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"552.8\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"552.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"552.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"554.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"558.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"558.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"524.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"563.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"562.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"564.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"563.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"564.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"565.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"562.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"561.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"559.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"556.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"553.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"549.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"543.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"542.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"539.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"536.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"529.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"524.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"524.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"522.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"517.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3231180001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"353.0\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"352.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"352.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"351.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"352.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"353.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"355.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"355.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"353.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"351.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"350.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"349.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"347.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"346.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"345.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"344.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"341.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"340.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"338.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"336.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"338.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"337.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"337.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"336.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"334.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"335.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"334.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"334.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3231170001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"27.5\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"27.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"28.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"28.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"29.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"31.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"32.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"32.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"32.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"32.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"31.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"31.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"31.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"31.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"30.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"29.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"29.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"28.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"29.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"28.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"30.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"31.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"33.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"32.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"31.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133530001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"150.8\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"151.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"150.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"150.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"150.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"151.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"152.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"152.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"152.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"153.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"153.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"154.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"152.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"151.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"151.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"150.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"150.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"149.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"146.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"148.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"148.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"146.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"147.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"147.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"147.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133610001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"297.5\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"297.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"299.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"294.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"302.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"305.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"305.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"305.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"306.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"310.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"302.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"300.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"296.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"295.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"295.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"296.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"297.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"296.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"275.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"297.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"294.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"296.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"294.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"294.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"296.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"288.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"294.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"294.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    }\n", "}\n"]}], "source": ["import json\n", "print(json.dumps(json_data, indent=4))"]}, {"cell_type": "code", "execution_count": 4, "id": "1b16cdef", "metadata": {}, "outputs": [], "source": ["all_data_1 = []\n", "\n", "for series in json_data['Results']['series']:\n", "    series_id = series['seriesID']\n", "    for entry in series['data']:\n", "        all_data_1.append({\n", "            'seriesID': series_id,\n", "            'year': int(entry['year']),\n", "            'period': entry['period'],\n", "            'periodName': entry['periodName'],\n", "            'value': float(entry['value'])\n", "        })\n", "\n", "#print(all_data_1)"]}, {"cell_type": "code", "execution_count": 6, "id": "fd05e3d6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "employment_df = pd.DataFrame(all_data_1)"]}, {"cell_type": "code", "execution_count": 7, "id": "762c11f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seriesID</th>\n", "      <th>year</th>\n", "      <th>period</th>\n", "      <th>periodName</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CES3231120001</td>\n", "      <td>2024</td>\n", "      <td>M12</td>\n", "      <td>December</td>\n", "      <td>68.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CES3231120001</td>\n", "      <td>2024</td>\n", "      <td>M11</td>\n", "      <td>November</td>\n", "      <td>68.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CES3231120001</td>\n", "      <td>2024</td>\n", "      <td>M10</td>\n", "      <td>October</td>\n", "      <td>68.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CES3231120001</td>\n", "      <td>2024</td>\n", "      <td>M09</td>\n", "      <td>September</td>\n", "      <td>68.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CES3231120001</td>\n", "      <td>2024</td>\n", "      <td>M08</td>\n", "      <td>August</td>\n", "      <td>67.8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seriesID  year period periodName  value\n", "0  CES3231120001  2024    M12   December   68.4\n", "1  CES3231120001  2024    M11   November   68.1\n", "2  CES3231120001  2024    M10    October   68.4\n", "3  CES3231120001  2024    M09  September   68.0\n", "4  CES3231120001  2024    M08     August   67.8"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["employment_df.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "8427dd2a", "metadata": {}, "outputs": [{"data": {"text/plain": ["600"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(employment_df)"]}, {"cell_type": "code", "execution_count": 22, "id": "117abdf7", "metadata": {}, "outputs": [], "source": ["yearly_avg_1 = (\n", "    employment_df.groupby(['seriesID', 'year'])['value']\n", "      .mean()\n", "      .unstack()  # Pivots years to columns\n", "      .rename(columns={2023: 'avg_2023', 2024: 'avg_2024'})\n", ")"]}, {"cell_type": "code", "execution_count": 23, "id": "112c7c76", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>year</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "    </tr>\n", "    <tr>\n", "      <th>seriesID</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CEU3132110001</th>\n", "      <td>92.308333</td>\n", "      <td>90.616667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132120001</th>\n", "      <td>82.833333</td>\n", "      <td>82.150000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132190001</th>\n", "      <td>243.766667</td>\n", "      <td>244.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132710001</th>\n", "      <td>35.566667</td>\n", "      <td>34.533333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132720001</th>\n", "      <td>81.850000</td>\n", "      <td>77.991667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["year             avg_2023    avg_2024\n", "seriesID                             \n", "CEU3132110001   92.308333   90.616667\n", "CEU3132120001   82.833333   82.150000\n", "CEU3132190001  243.766667  244.250000\n", "CEU3132710001   35.566667   34.533333\n", "CEU3132720001   81.850000   77.991667"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["yearly_avg_1.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "52bd64d5", "metadata": {}, "outputs": [], "source": ["yearly_avg_1['ratio'] = yearly_avg_1['avg_2024'] / yearly_avg_1['avg_2023']"]}, {"cell_type": "code", "execution_count": 25, "id": "d0409776", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>year</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "    <tr>\n", "      <th>seriesID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CEU3132110001</th>\n", "      <td>92.308333</td>\n", "      <td>90.616667</td>\n", "      <td>0.981674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132120001</th>\n", "      <td>82.833333</td>\n", "      <td>82.150000</td>\n", "      <td>0.991751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132190001</th>\n", "      <td>243.766667</td>\n", "      <td>244.250000</td>\n", "      <td>1.001983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132710001</th>\n", "      <td>35.566667</td>\n", "      <td>34.533333</td>\n", "      <td>0.970947</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3132720001</th>\n", "      <td>81.850000</td>\n", "      <td>77.991667</td>\n", "      <td>0.952861</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["year             avg_2023    avg_2024     ratio\n", "seriesID                                       \n", "CEU3132110001   92.308333   90.616667  0.981674\n", "CEU3132120001   82.833333   82.150000  0.991751\n", "CEU3132190001  243.766667  244.250000  1.001983\n", "CEU3132710001   35.566667   34.533333  0.970947\n", "CEU3132720001   81.850000   77.991667  0.952861"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["yearly_avg_1.head()"]}, {"cell_type": "markdown", "id": "405f0f15", "metadata": {}, "source": ["## for next 25 series ids"]}, {"cell_type": "code", "execution_count": 26, "id": "212b8ede", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "headers = {'Content-type': 'application/json'}\n", "data_2 = json.dumps({\"seriesid\": series_id_list[25:50],\"startyear\":\"2023\", \"endyear\":\"2024\"})\n", "#print(data_1)\n", "p = requests.post('https://api.bls.gov/publicAPI/v1/timeseries/data/', data=data_2, headers=headers)\n", "json_data = json.loads(p.text)"]}, {"cell_type": "code", "execution_count": 27, "id": "6bb7c892", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 65.5}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 65.1}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 64.9}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 64.5}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 65.4}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 64.7}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 64.7}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 65.6}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 65.3}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 65.9}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 65.3}, {'seriesID': 'CEU3232550001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 64.2}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 64.2}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 65.1}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 64.6}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 65.1}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 65.1}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 65.1}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 65.4}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 65.2}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 65.9}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 66.1}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 73.9}, {'seriesID': 'CEU3232550001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 66.9}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 113.9}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 113.4}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 112.4}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 112.4}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 113.5}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 114.1}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 114.6}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 113.7}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 114.0}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 114.1}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 113.5}, {'seriesID': 'CEU3232560001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 113.7}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 112.8}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 112.3}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 112.9}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 113.4}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 113.6}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 114.7}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 116.1}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 116.5}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 115.7}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 115.6}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 115.6}, {'seriesID': 'CEU3232560001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 115.7}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 80.9}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 80.3}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 80.4}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 80.9}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 81.6}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 82.6}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 81.9}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 80.8}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 80.5}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 79.8}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 79.9}, {'seriesID': 'CEU3232590001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 79.8}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 81.4}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 81.6}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 81.2}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 81.6}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 80.3}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 80.9}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 82.7}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 81.7}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 82.2}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 81.7}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 81.5}, {'seriesID': 'CEU3232590001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 81.1}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 588.6}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 586.9}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 586.6}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 588.3}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 593.0}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 594.7}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 598.1}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 597.2}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 595.4}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 594.1}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 594.3}, {'seriesID': 'CEU3232610001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 593.1}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 594.8}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 596.7}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 598.0}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 602.5}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 600.8}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 604.1}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 607.7}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 605.2}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 603.9}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 606.3}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 606.8}, {'seriesID': 'CEU3232610001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 608.4}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 131.7}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 131.2}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 131.4}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 132.1}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 132.5}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 133.2}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 133.7}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 132.6}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 132.2}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 132.9}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 133.6}, {'seriesID': 'CEU3232620001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 134.4}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 135.1}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 134.2}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 134.4}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 135.1}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 136.0}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 136.4}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 136.3}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 135.2}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 135.4}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 135.3}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 136.1}, {'seriesID': 'CEU3232620001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 136.9}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 85.2}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 84.7}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 84.9}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 84.6}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 86.3}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 86.0}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 86.7}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 86.6}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 87.0}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 85.7}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 85.3}, {'seriesID': 'CEU3133110001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 83.8}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 85.2}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 83.7}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 86.4}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 85.2}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 86.3}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 86.4}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 85.5}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 84.3}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 84.3}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 84.3}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 83.3}, {'seriesID': 'CEU3133110001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 83.0}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 59.1}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 59.2}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 59.6}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 59.7}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 59.4}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 60.4}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 60.4}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 60.2}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 59.8}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 59.6}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 59.6}, {'seriesID': 'CEU3133120001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 60.3}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 61.1}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 60.3}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 60.0}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 60.8}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 61.0}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 59.9}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 59.5}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 58.4}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 59.4}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 59.1}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 58.4}, {'seriesID': 'CEU3133120001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 57.2}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 106.8}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 106.1}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 105.7}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 106.0}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 106.3}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 106.5}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 106.6}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 106.3}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 105.7}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 105.3}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 105.4}, {'seriesID': 'CEU3133150001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 104.9}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 105.6}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 104.6}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 104.5}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 106.4}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 107.5}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 108.8}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 108.9}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 108.7}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 106.8}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 107.8}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 107.8}, {'seriesID': 'CEU3133150001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 107.7}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 91.1}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 90.6}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 90.5}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 91.2}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 91.5}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 90.5}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 92.1}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 91.7}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 90.3}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 89.8}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 90.3}, {'seriesID': 'CEU3133210001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 90.5}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 90.9}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 90.2}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 90.3}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 91.1}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 90.9}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 90.8}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 90.2}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 90.1}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 90.6}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 91.1}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 92.1}, {'seriesID': 'CEU3133210001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 92.0}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 413.2}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 413.5}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 413.2}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 412.7}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 414.5}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 415.0}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 415.5}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 414.2}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 412.0}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 413.6}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 416.6}, {'seriesID': 'CEU3133230001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 412.9}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 412.7}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 413.8}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 410.4}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 409.7}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 407.1}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 407.2}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 410.4}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 405.9}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 405.1}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 405.4}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 406.1}, {'seriesID': 'CEU3133230001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 404.7}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 96.9}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 97.5}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 97.5}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 98.3}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 98.2}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 97.7}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 98.4}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 97.6}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 97.2}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 97.8}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 97.6}, {'seriesID': 'CEU3133240001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 96.8}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 97.2}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 95.6}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 94.3}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 93.8}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 93.9}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 93.6}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 96.3}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 95.4}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 95.2}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 94.7}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 96.1}, {'seriesID': 'CEU3133240001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 95.6}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 329.8}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 328.9}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 328.8}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 327.7}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 331.0}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 334.1}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 334.7}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 334.5}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 336.2}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 336.9}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 337.7}, {'seriesID': 'CEU3133270001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 337.8}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 340.8}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 341.0}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 340.7}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 341.4}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 344.0}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 345.6}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 345.3}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 342.2}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 344.4}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 342.0}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 342.6}, {'seriesID': 'CEU3133270001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 342.1}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 128.7}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 128.5}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 128.7}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 130.1}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 130.2}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 130.7}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 129.9}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 129.6}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 129.8}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 130.5}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 129.8}, {'seriesID': 'CEU3133280001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 129.2}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 130.4}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 130.0}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 129.7}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 129.0}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 129.6}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 129.1}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 129.2}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 130.6}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 131.2}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 131.6}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 130.4}, {'seriesID': 'CEU3133280001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 130.7}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 313.4}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 315.4}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 313.0}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 314.0}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 312.6}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 315.7}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 314.8}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 314.7}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 315.3}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 315.3}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 315.5}, {'seriesID': 'CEU3133290001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 314.9}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 313.7}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 312.1}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 314.2}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 315.3}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 318.9}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 321.0}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 319.7}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 316.8}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 314.6}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 314.8}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 315.7}, {'seriesID': 'CEU3133290001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 314.6}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 210.9}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 210.9}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 211.0}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 213.2}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 214.0}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 217.8}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 218.0}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 218.4}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 218.4}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 218.4}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 217.5}, {'seriesID': 'CEU3133310001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 218.5}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 219.7}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 219.0}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 218.1}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 218.1}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 220.7}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 221.8}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 222.5}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 221.2}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 218.9}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 219.0}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 218.0}, {'seriesID': 'CEU3133310001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 218.7}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 135.0}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 133.7}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 132.8}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 132.2}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 133.0}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 133.3}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 133.4}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 133.0}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 133.2}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 131.6}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 132.7}, {'seriesID': 'CEU3133320001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 132.2}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 131.9}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 132.3}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 132.1}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 132.7}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 133.8}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 133.6}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 134.6}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 133.5}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 133.9}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 134.9}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 135.0}, {'seriesID': 'CEU3133320001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 135.0}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 89.6}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 89.1}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 88.9}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 88.5}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 88.6}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 89.0}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 88.1}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 88.5}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 88.6}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 88.2}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 88.5}, {'seriesID': 'CEU3133330001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 89.2}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 90.0}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 89.7}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 89.7}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 88.8}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 90.5}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 91.1}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 92.1}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 90.2}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 90.1}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 90.1}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 90.5}, {'seriesID': 'CEU3133330001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 89.9}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 149.4}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 148.3}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 146.9}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 146.3}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 147.0}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 146.7}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 145.7}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 145.4}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 144.3}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 144.0}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 144.4}, {'seriesID': 'CEU3133340001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 146.7}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 146.3}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 146.1}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 145.8}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 145.0}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 145.1}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 146.5}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 146.6}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 142.7}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 145.5}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 145.1}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 146.3}, {'seriesID': 'CEU3133340001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 145.4}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 154.2}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 155.6}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 154.7}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 155.4}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 156.6}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 157.2}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 158.8}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 158.7}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 158.3}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 157.9}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 159.6}, {'seriesID': 'CEU3133350001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 158.4}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 161.0}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 159.0}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 159.8}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 159.8}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 161.6}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 162.7}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 163.6}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 162.8}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 162.6}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 163.0}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 162.5}, {'seriesID': 'CEU3133350001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 162.5}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 93.0}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 92.9}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 92.7}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 91.9}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 91.9}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 92.3}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 92.6}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 93.1}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 93.2}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 92.8}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 93.8}, {'seriesID': 'CEU3133360001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 93.7}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 94.1}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 90.8}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 91.2}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 95.3}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 95.2}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 94.4}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 93.6}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 92.1}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 92.3}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 91.9}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 91.1}, {'seriesID': 'CEU3133360001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 90.1}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 282.4}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 283.3}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 283.6}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 285.2}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 285.8}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 287.7}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 289.3}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 285.8}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 284.4}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 285.0}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 285.5}, {'seriesID': 'CEU3133390001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 285.6}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 288.8}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 289.2}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 287.4}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 288.0}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 290.5}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 289.6}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 289.0}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 285.9}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 284.9}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 285.3}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 283.1}, {'seriesID': 'CEU3133390001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 283.1}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 111.9}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 110.5}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 110.6}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 110.0}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 110.4}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 110.6}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 110.0}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 110.0}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 109.2}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 108.0}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 110.3}, {'seriesID': 'CEU3133410001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 112.1}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 113.8}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 111.9}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 112.3}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 113.0}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 114.9}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 116.2}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 116.3}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 115.0}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 115.7}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 115.6}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 116.2}, {'seriesID': 'CEU3133410001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 116.8}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 81.9}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 82.1}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 81.9}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 82.0}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 82.6}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 83.3}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 83.9}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 83.5}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 82.5}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 83.3}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 84.1}, {'seriesID': 'CEU3133420001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 84.4}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 85.4}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 85.5}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 86.1}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 85.2}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 85.5}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 86.9}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 87.1}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 85.5}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 85.2}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 85.1}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 84.8}, {'seriesID': 'CEU3133420001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 84.5}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 382.8}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 385.6}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 388.1}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 391.9}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 395.4}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 398.2}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 397.3}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 393.4}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 392.4}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 393.6}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 394.1}, {'seriesID': 'CEU3133440001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 394.3}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 395.0}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 392.2}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 390.8}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 391.4}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 396.6}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 398.7}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 398.7}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 395.0}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 395.6}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 395.6}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 398.8}, {'seriesID': 'CEU3133440001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 397.7}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 410.1}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 409.3}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 410.9}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 411.0}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 411.5}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 415.3}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 414.2}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 411.0}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 412.1}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 413.1}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 415.2}, {'seriesID': 'CEU3133450001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 416.3}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 420.2}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 420.4}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 421.8}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 424.2}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 429.4}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 432.3}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 431.6}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 428.0}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 429.0}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 428.4}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 428.6}, {'seriesID': 'CEU3133450001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 425.3}]\n"]}], "source": ["all_data_2 = []\n", "\n", "for series in json_data['Results']['series']:\n", "    series_id = series['seriesID']\n", "    for entry in series['data']:\n", "        all_data_2.append({\n", "            'seriesID': series_id,\n", "            'year': int(entry['year']),\n", "            'period': entry['period'],\n", "            'periodName': entry['periodName'],\n", "            'value': float(entry['value'])\n", "        })\n", "\n", "print(all_data_2)"]}, {"cell_type": "code", "execution_count": 28, "id": "b776a2ee", "metadata": {}, "outputs": [], "source": ["employment_df_2 = pd.DataFrame(all_data_2)"]}, {"cell_type": "code", "execution_count": 29, "id": "23cdbc3b", "metadata": {}, "outputs": [{"data": {"text/plain": ["600"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(employment_df_2)"]}, {"cell_type": "code", "execution_count": 30, "id": "8b3f494b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seriesID</th>\n", "      <th>year</th>\n", "      <th>period</th>\n", "      <th>periodName</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CEU3232550001</td>\n", "      <td>2024</td>\n", "      <td>M12</td>\n", "      <td>December</td>\n", "      <td>65.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CEU3232550001</td>\n", "      <td>2024</td>\n", "      <td>M11</td>\n", "      <td>November</td>\n", "      <td>65.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CEU3232550001</td>\n", "      <td>2024</td>\n", "      <td>M10</td>\n", "      <td>October</td>\n", "      <td>64.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CEU3232550001</td>\n", "      <td>2024</td>\n", "      <td>M09</td>\n", "      <td>September</td>\n", "      <td>64.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CEU3232550001</td>\n", "      <td>2024</td>\n", "      <td>M08</td>\n", "      <td>August</td>\n", "      <td>65.4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seriesID  year period periodName  value\n", "0  CEU3232550001  2024    M12   December   65.5\n", "1  CEU3232550001  2024    M11   November   65.1\n", "2  CEU3232550001  2024    M10    October   64.9\n", "3  CEU3232550001  2024    M09  September   64.5\n", "4  CEU3232550001  2024    M08     August   65.4"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["employment_df_2.head()"]}, {"cell_type": "code", "execution_count": 31, "id": "cc294296", "metadata": {}, "outputs": [], "source": ["yearly_avg_2 = (\n", "    employment_df_2.groupby(['seriesID', 'year'])['value']\n", "      .mean()\n", "      .unstack()  # Pivots years to columns\n", "      .rename(columns={2023: 'avg_2023', 2024: 'avg_2024'})\n", ")"]}, {"cell_type": "code", "execution_count": 32, "id": "53fd1f50", "metadata": {}, "outputs": [], "source": ["yearly_avg_2['ratio'] = yearly_avg_2['avg_2024'] / yearly_avg_2['avg_2023']"]}, {"cell_type": "code", "execution_count": 33, "id": "78b48f3c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>year</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "    <tr>\n", "      <th>seriesID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CEU3133110001</th>\n", "      <td>84.825000</td>\n", "      <td>85.566667</td>\n", "      <td>1.008743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133120001</th>\n", "      <td>59.591667</td>\n", "      <td>59.775000</td>\n", "      <td>1.003076</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133150001</th>\n", "      <td>107.091667</td>\n", "      <td>105.966667</td>\n", "      <td>0.989495</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133210001</th>\n", "      <td>90.858333</td>\n", "      <td>90.841667</td>\n", "      <td>0.999817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133230001</th>\n", "      <td>408.208333</td>\n", "      <td>413.908333</td>\n", "      <td>1.013963</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["year             avg_2023    avg_2024     ratio\n", "seriesID                                       \n", "CEU3133110001   84.825000   85.566667  1.008743\n", "CEU3133120001   59.591667   59.775000  1.003076\n", "CEU3133150001  107.091667  105.966667  0.989495\n", "CEU3133210001   90.858333   90.841667  0.999817\n", "CEU3133230001  408.208333  413.908333  1.013963"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["yearly_avg_2.head()"]}, {"cell_type": "markdown", "id": "74171011", "metadata": {}, "source": ["## for remaining series ids"]}, {"cell_type": "code", "execution_count": 34, "id": "b490939e", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "headers = {'Content-type': 'application/json'}\n", "data_3 = json.dumps({\"seriesid\": series_id_list[50:],\"startyear\":\"2023\", \"endyear\":\"2024\"})\n", "#print(data_3)\n", "p = requests.post('https://api.bls.gov/publicAPI/v1/timeseries/data/', data=data_3, headers=headers)\n", "json_data = json.loads(p.text)"]}, {"cell_type": "code", "execution_count": 35, "id": "a4668648", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 33.3}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 33.2}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 33.1}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 33.2}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 33.6}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 34.8}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 35.0}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 35.3}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 35.6}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 36.0}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 35.7}, {'seriesID': 'CEU3133510001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 35.6}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 36.6}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 36.3}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 37.1}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 37.3}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 38.0}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 37.5}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 37.6}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 38.1}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 37.9}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 38.5}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 38.8}, {'seriesID': 'CEU3133510001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 38.7}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 59.4}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 59.4}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 59.5}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 59.5}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 59.8}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 59.1}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 59.1}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 59.7}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 60.9}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 61.3}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 62.2}, {'seriesID': 'CEU3133520001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 61.5}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 63.2}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 65.1}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 65.1}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 65.3}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 64.7}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 66.0}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 64.0}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 64.4}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 63.6}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 64.4}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 65.0}, {'seriesID': 'CEU3133520001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 64.9}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 150.9}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 151.7}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 152.3}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 152.4}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 152.2}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 154.1}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 154.2}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 153.8}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 154.4}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 154.1}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 154.0}, {'seriesID': 'CEU3133530001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 152.3}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 152.1}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 150.8}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 150.2}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 149.5}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 149.0}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 147.4}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 149.0}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 147.6}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 146.8}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 147.6}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 147.0}, {'seriesID': 'CEU3133530001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 147.5}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 163.8}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 164.5}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 164.4}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 164.0}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 165.1}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 166.8}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 166.7}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 166.5}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 167.1}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 166.8}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 166.2}, {'seriesID': 'CEU3133590001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 166.5}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 167.2}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 167.2}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 168.0}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 167.6}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 168.5}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 167.8}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 166.9}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 166.3}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 165.1}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 164.1}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 163.2}, {'seriesID': 'CEU3133590001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 162.6}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 309.6}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 308.0}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 306.0}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 307.9}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 308.1}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 301.2}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 303.7}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 299.7}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 299.0}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 295.5}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 293.4}, {'seriesID': 'CEU3133610001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 291.8}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 304.5}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 297.9}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 276.4}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 300.6}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 295.6}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 286.7}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 296.7}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 295.0}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 299.0}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 289.9}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 292.3}, {'seriesID': 'CEU3133610001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 289.2}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 163.4}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 162.0}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 160.9}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 162.3}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 163.2}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 163.1}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 165.2}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 163.6}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 164.3}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 164.4}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 164.5}, {'seriesID': 'CEU3133620001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 164.9}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 165.1}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 166.2}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 165.3}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 167.1}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 168.1}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 164.2}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 168.6}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 169.1}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 168.9}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 169.7}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 169.6}, {'seriesID': 'CEU3133620001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 167.4}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 546.7}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 545.2}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 543.9}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 547.5}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 550.1}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 552.6}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 560.4}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 557.3}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 557.3}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 558.7}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 558.0}, {'seriesID': 'CEU3133630001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 562.1}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 569.2}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 564.4}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 556.4}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 564.6}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 563.6}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 558.9}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 565.8}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 564.2}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 564.9}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 565.8}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 564.8}, {'seriesID': 'CEU3133630001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 562.8}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 559.1}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 558.3}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 523.0}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 564.5}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 565.6}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 569.5}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 567.1}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 561.1}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 562.0}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 560.5}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 560.1}, {'seriesID': 'CEU3133640001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 558.0}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 556.9}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 553.0}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 548.2}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 545.0}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 545.4}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 544.7}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 539.4}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 526.2}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 521.6}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 522.6}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 521.0}, {'seriesID': 'CEU3133640001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 515.9}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 147.8}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 148.2}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 148.3}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 148.8}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 148.6}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 150.6}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 152.7}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 152.1}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 152.3}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 152.2}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 151.8}, {'seriesID': 'CEU3133660001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 152.0}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 153.6}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 153.4}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 152.8}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 152.6}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 154.4}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 154.8}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 155.0}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 154.6}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 154.5}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 154.7}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 153.3}, {'seriesID': 'CEU3133660001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 152.2}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 214.3}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 215.4}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 216.1}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 215.6}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 218.0}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 219.4}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 219.8}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 217.9}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 219.0}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 221.5}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 221.4}, {'seriesID': 'CEU3133710001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 220.7}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 222.8}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 224.0}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 223.2}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 223.6}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 226.3}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 227.3}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 228.5}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 226.9}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 230.3}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 231.4}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 231.1}, {'seriesID': 'CEU3133710001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 231.7}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 124.6}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 124.3}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 122.0}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 123.1}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 124.8}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 124.0}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 126.3}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 126.7}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 126.1}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 127.1}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 125.8}, {'seriesID': 'CEU3133720001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 125.5}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 129.2}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 128.8}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 128.2}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 127.9}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 130.5}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 130.5}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 132.6}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 131.7}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 131.7}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 132.1}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 132.8}, {'seriesID': 'CEU3133720001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 133.7}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 332.5}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 329.8}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 329.0}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 328.3}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 329.2}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 330.0}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 330.1}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 329.3}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 329.6}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 328.8}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 330.7}, {'seriesID': 'CEU3133910001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 328.8}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 333.2}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 331.7}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 332.0}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 333.0}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 332.8}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 336.2}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 335.4}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 331.9}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 334.2}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 334.6}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 336.8}, {'seriesID': 'CEU3133910001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 335.7}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M12', 'periodName': 'December', 'value': 283.2}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M11', 'periodName': 'November', 'value': 282.3}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M10', 'periodName': 'October', 'value': 282.1}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M09', 'periodName': 'September', 'value': 280.9}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M08', 'periodName': 'August', 'value': 284.3}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M07', 'periodName': 'July', 'value': 285.9}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M06', 'periodName': 'June', 'value': 286.5}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M05', 'periodName': 'May', 'value': 287.5}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M04', 'periodName': 'April', 'value': 288.2}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M03', 'periodName': 'March', 'value': 289.9}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M02', 'periodName': 'February', 'value': 289.8}, {'seriesID': 'CEU3133990001', 'year': 2024, 'period': 'M01', 'periodName': 'January', 'value': 288.8}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M12', 'periodName': 'December', 'value': 290.9}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M11', 'periodName': 'November', 'value': 290.5}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M10', 'periodName': 'October', 'value': 290.0}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M09', 'periodName': 'September', 'value': 290.5}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M08', 'periodName': 'August', 'value': 294.5}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M07', 'periodName': 'July', 'value': 294.2}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M06', 'periodName': 'June', 'value': 293.3}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M05', 'periodName': 'May', 'value': 292.5}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M04', 'periodName': 'April', 'value': 293.0}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M03', 'periodName': 'March', 'value': 292.9}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M02', 'periodName': 'February', 'value': 292.7}, {'seriesID': 'CEU3133990001', 'year': 2023, 'period': 'M01', 'periodName': 'January', 'value': 292.8}]\n"]}], "source": ["all_data_3 = []\n", "\n", "for series in json_data['Results']['series']:\n", "    series_id = series['seriesID']\n", "    for entry in series['data']:\n", "        all_data_3.append({\n", "            'seriesID': series_id,\n", "            'year': int(entry['year']),\n", "            'period': entry['period'],\n", "            'periodName': entry['periodName'],\n", "            'value': float(entry['value'])\n", "        })\n", "\n", "print(all_data_3)"]}, {"cell_type": "code", "execution_count": 36, "id": "3989f297", "metadata": {}, "outputs": [], "source": ["employment_df_3 = pd.DataFrame(all_data_3)"]}, {"cell_type": "code", "execution_count": 37, "id": "a38b9b1f", "metadata": {}, "outputs": [], "source": ["yearly_avg_3 = (\n", "    employment_df_3.groupby(['seriesID', 'year'])['value']\n", "      .mean()\n", "      .unstack()  # Pivots years to columns\n", "      .rename(columns={2023: 'avg_2023', 2024: 'avg_2024'})\n", ")\n", "\n", "yearly_avg_3['ratio'] = yearly_avg_3['avg_2024'] / yearly_avg_3['avg_2023']"]}, {"cell_type": "code", "execution_count": 38, "id": "017725d8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>year</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "    <tr>\n", "      <th>seriesID</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CEU3133510001</th>\n", "      <td>37.700000</td>\n", "      <td>34.533333</td>\n", "      <td>0.916004</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133520001</th>\n", "      <td>64.641667</td>\n", "      <td>60.116667</td>\n", "      <td>0.929999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133530001</th>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133590001</th>\n", "      <td>166.208333</td>\n", "      <td>165.700000</td>\n", "      <td>0.996942</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CEU3133610001</th>\n", "      <td>293.650000</td>\n", "      <td>301.991667</td>\n", "      <td>1.028407</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["year             avg_2023    avg_2024     ratio\n", "seriesID                                       \n", "CEU3133510001   37.700000   34.533333  0.916004\n", "CEU3133520001   64.641667   60.116667  0.929999\n", "CEU3133530001  148.708333  153.033333  1.029084\n", "CEU3133590001  166.208333  165.700000  0.996942\n", "CEU3133610001  293.650000  301.991667  1.028407"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["yearly_avg_3.head()"]}, {"cell_type": "code", "execution_count": 43, "id": "04b1d77a", "metadata": {}, "outputs": [], "source": ["yearly_avg_1_reset = yearly_avg_1.reset_index()"]}, {"cell_type": "code", "execution_count": 45, "id": "52147859", "metadata": {}, "outputs": [], "source": ["yearly_avg_2_reset = yearly_avg_2.reset_index()\n", "yearly_avg_3_reset = yearly_avg_3.reset_index()\n", "\n", "# Combine all three dataframes\n", "combined_emp_df = pd.concat([yearly_avg_1_reset, yearly_avg_2_reset, yearly_avg_3_reset], \n", "                              ignore_index=True)"]}, {"cell_type": "code", "execution_count": 46, "id": "7bae4ab2", "metadata": {}, "outputs": [{"data": {"text/plain": ["63"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["len(combined_emp_df)"]}, {"cell_type": "markdown", "id": "04a51255", "metadata": {}, "source": ["Combined avg employment data"]}, {"cell_type": "code", "execution_count": 47, "id": "e3ec56af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>year</th>\n", "      <th>seriesID</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CEU3132110001</td>\n", "      <td>92.308333</td>\n", "      <td>90.616667</td>\n", "      <td>0.981674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CEU3132120001</td>\n", "      <td>82.833333</td>\n", "      <td>82.150000</td>\n", "      <td>0.991751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CEU3132190001</td>\n", "      <td>243.766667</td>\n", "      <td>244.250000</td>\n", "      <td>1.001983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CEU3132710001</td>\n", "      <td>35.566667</td>\n", "      <td>34.533333</td>\n", "      <td>0.970947</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CEU3132720001</td>\n", "      <td>81.850000</td>\n", "      <td>77.991667</td>\n", "      <td>0.952861</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["year       seriesID    avg_2023    avg_2024     ratio\n", "0     CEU3132110001   92.308333   90.616667  0.981674\n", "1     CEU3132120001   82.833333   82.150000  0.991751\n", "2     CEU3132190001  243.766667  244.250000  1.001983\n", "3     CEU3132710001   35.566667   34.533333  0.970947\n", "4     CEU3132720001   81.850000   77.991667  0.952861"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_emp_df.head()"]}, {"cell_type": "code", "execution_count": 48, "id": "8437a7ac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>year</th>\n", "      <th>seriesID</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>CEU3231120001</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>CEU3133640001</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>CEU3231180001</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CEU3231170001</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>CEU3133530001</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["year       seriesID    avg_2023    avg_2024     ratio\n", "7     CEU3231120001   64.525000   67.391667  1.044427\n", "57    CEU3133640001  536.658333  559.066667  1.041755\n", "13    CEU3231180001  337.133333  350.400000  1.039351\n", "12    CEU3231170001   30.583333   31.475000  1.029155\n", "52    CEU3133530001  148.708333  153.033333  1.029084"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["# Sort combined_emp_df by ratio in descending order\n", "ranked_df = combined_emp_df.sort_values(by='ratio', ascending=False)\n", "ranked_df.head()"]}, {"cell_type": "code", "execution_count": 53, "id": "30272a58", "metadata": {}, "outputs": [], "source": ["ranked_df = ranked_df.rename_axis(None, axis=0) \n"]}, {"cell_type": "code", "execution_count": 54, "id": "719f3777", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>year</th>\n", "      <th>seriesID</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>CEU3231120001</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>CEU3133640001</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>CEU3231180001</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CEU3231170001</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>CEU3133530001</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["year       seriesID    avg_2023    avg_2024     ratio\n", "7     CEU3231120001   64.525000   67.391667  1.044427\n", "57    CEU3133640001  536.658333  559.066667  1.041755\n", "13    CEU3231180001  337.133333  350.400000  1.039351\n", "12    CEU3231170001   30.583333   31.475000  1.029155\n", "52    CEU3133530001  148.708333  153.033333  1.029084"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["ranked_df.head()"]}, {"cell_type": "code", "execution_count": 55, "id": "7849250c", "metadata": {}, "outputs": [], "source": ["ranked_df.index.name = None"]}, {"cell_type": "code", "execution_count": 57, "id": "dccf78a4", "metadata": {}, "outputs": [], "source": ["ranked_df = ranked_df.rename_axis(None, axis=1) "]}, {"cell_type": "code", "execution_count": 58, "id": "2275e33d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seriesID</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>CEU3231120001</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>CEU3133640001</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>CEU3231180001</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CEU3231170001</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>CEU3133530001</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         seriesID    avg_2023    avg_2024     ratio\n", "7   CEU3231120001   64.525000   67.391667  1.044427\n", "57  CEU3133640001  536.658333  559.066667  1.041755\n", "13  CEU3231180001  337.133333  350.400000  1.039351\n", "12  CEU3231170001   30.583333   31.475000  1.029155\n", "52  CEU3133530001  148.708333  153.033333  1.029084"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["ranked_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7973d9b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["        seriesID    avg_2023    avg_2024     ratio\n", "0  CEU3231120001   64.525000   67.391667  1.044427\n", "1  CEU3133640001  536.658333  559.066667  1.041755\n", "2  CEU3231180001  337.133333  350.400000  1.039351\n", "3  CEU3231170001   30.583333   31.475000  1.029155\n", "4  CEU3133530001  148.708333  153.033333  1.029084\n"]}], "source": ["# Reset index if needed and then select columns\n", "ranked_df = ranked_df.reset_index(drop=True)[['seriesID', 'avg_2023', 'avg_2024', 'ratio']]\n", "\n", "# Display the result\n", "ranked_df.head()"]}, {"cell_type": "code", "execution_count": 61, "id": "229b6b5c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seriesID</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "      <th>rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CEU3231120001</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CEU3133640001</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CEU3231180001</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CEU3231170001</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CEU3133530001</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seriesID    avg_2023    avg_2024     ratio  rank\n", "0  CEU3231120001   64.525000   67.391667  1.044427     1\n", "1  CEU3133640001  536.658333  559.066667  1.041755     2\n", "2  CEU3231180001  337.133333  350.400000  1.039351     3\n", "3  CEU3231170001   30.583333   31.475000  1.029155     4\n", "4  CEU3133530001  148.708333  153.033333  1.029084     5"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["ranked_df['rank'] = range(1, len(ranked_df) + 1)\n", "ranked_df.head()"]}, {"cell_type": "code", "execution_count": 63, "id": "3a2064b1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>industry_code</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "      <th>series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>32311100</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>CEU3231110001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>32311200</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>CEU3231120001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32311300</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>CEU3231130001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>32311400</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>CEU3231140001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32311500</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>CEU3231150001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   industry_code naics_code  \\\n", "0       32311100       3111   \n", "1       32311200       3112   \n", "2       32311300       3113   \n", "3       32311400       3114   \n", "4       32311500       3115   \n", "\n", "                                       industry_name      series_id  \n", "0                          Animal food manufacturing  CEU3231110001  \n", "1                          Grain and oilseed milling  CEU3231120001  \n", "2      Sugar and confectionery product manufacturing  CEU3231130001  \n", "3  Fruit and vegetable preserving and specialty f...  CEU3231140001  \n", "4                        Dairy product manufacturing  CEU3231150001  "]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head()"]}, {"cell_type": "code", "execution_count": 64, "id": "97572d5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   industry_code naics_code  \\\n", "0       32311100       3111   \n", "1       32311200       3112   \n", "2       32311300       3113   \n", "3       32311400       3114   \n", "4       32311500       3115   \n", "\n", "                                       industry_name       seriesID  \n", "0                          Animal food manufacturing  CEU3231110001  \n", "1                          Grain and oilseed milling  CEU3231120001  \n", "2      Sugar and confectionery product manufacturing  CEU3231130001  \n", "3  Fruit and vegetable preserving and specialty f...  CEU3231140001  \n", "4                        Dairy product manufacturing  CEU3231150001  \n"]}], "source": ["combined_df = combined_df.rename(columns={'series_id': 'seriesID'})\n", "\n", "print(combined_df.head())"]}, {"cell_type": "code", "execution_count": 65, "id": "fa18ceab", "metadata": {}, "outputs": [{"data": {"text/plain": ["63"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["len(combined_df)"]}, {"cell_type": "markdown", "id": "da57732f", "metadata": {}, "source": ["## Final ranking table"]}, {"cell_type": "code", "execution_count": 66, "id": "855df6db", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>seriesID</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>CEU3231120001</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>CEU3133640001</td>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>CEU3231180001</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>CEU3231170001</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>CEU3133530001</td>\n", "      <td>3353</td>\n", "      <td>Electrical equipment manufacturing</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank       seriesID naics_code                              industry_name  \\\n", "0     1  CEU3231120001       3112                  Grain and oilseed milling   \n", "1     2  CEU3133640001       3364  Aerospace product and parts manufacturing   \n", "2     3  CEU3231180001       3118        Bakeries and tortilla manufacturing   \n", "3     4  CEU3231170001       3117  Seafood product preparation and packaging   \n", "4     5  CEU3133530001       3353         Electrical equipment manufacturing   \n", "\n", "     avg_2023    avg_2024     ratio  \n", "0   64.525000   67.391667  1.044427  \n", "1  536.658333  559.066667  1.041755  \n", "2  337.133333  350.400000  1.039351  \n", "3   30.583333   31.475000  1.029155  \n", "4  148.708333  153.033333  1.029084  "]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["# Merge ranked_df with combined_df to get naics_code and industry_name\n", "ranked_df = ranked_df.merge(\n", "    combined_df[['seriesID', 'naics_code', 'industry_name']], \n", "    on='seriesID',\n", "    how='left'\n", ")\n", "\n", "# Reorder columns \n", "ranked_df = ranked_df[['rank', 'seriesID', 'naics_code', 'industry_name', 'avg_2023', 'avg_2024', 'ratio']]\n", "\n", "ranked_df.head()\n", "\n"]}, {"cell_type": "code", "execution_count": 68, "id": "198286d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Missing values in each column:\n", "rank             0\n", "seriesID         0\n", "naics_code       0\n", "industry_name    0\n", "avg_2023         0\n", "avg_2024         0\n", "ratio            0\n", "dtype: int64\n"]}], "source": ["print(\"\\nMissing values in each column:\")\n", "print(ranked_df.isnull().sum())"]}, {"cell_type": "code", "execution_count": 70, "id": "dec96b10", "metadata": {}, "outputs": [], "source": ["ranked_df.to_csv('manufacturing_employment_rankings.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}