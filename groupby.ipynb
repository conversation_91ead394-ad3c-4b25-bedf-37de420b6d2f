{"cells": [{"cell_type": "code", "execution_count": 21, "id": "e4254c70", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 22, "id": "4647535a", "metadata": {}, "outputs": [], "source": ["companies_df = pd.read_excel(\"US_Data_Matched_new.xlsx\")"]}, {"cell_type": "code", "execution_count": 23, "id": "52dc8d93", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Fsym_ID</th>\n", "      <th>Fsym_Security_ID</th>\n", "      <th>Ticker</th>\n", "      <th>ISIN</th>\n", "      <th>SEDOL</th>\n", "      <th>Entity_ID</th>\n", "      <th>Entity_Name</th>\n", "      <th>Security_Name</th>\n", "      <th>Exchange_Code</th>\n", "      <th>Exchange_Name</th>\n", "      <th>...</th>\n", "      <th>Last_Trade_Date</th>\n", "      <th>ADTV_6M</th>\n", "      <th>ADTV_3M</th>\n", "      <th>Security_Mcap_USD</th>\n", "      <th>Business Description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Combined_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MH33D6-R</td>\n", "      <td>R85KLC-S</td>\n", "      <td>AAPL-US</td>\n", "      <td>US0378331005</td>\n", "      <td>2046251</td>\n", "      <td>000C7F-E</td>\n", "      <td>Apple, Inc.</td>\n", "      <td>Apple Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>11780.832445</td>\n", "      <td>12683.983288</td>\n", "      <td>3.207062e+06</td>\n", "      <td>Apple Inc. designs, manufactures and markets s...</td>\n", "      <td>0.5</td>\n", "      <td>0.02</td>\n", "      <td>0.55</td>\n", "      <td>0.22</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>K7TPSX-R</td>\n", "      <td>QDYJZC-S</td>\n", "      <td>NVDA-US</td>\n", "      <td>US67066G1040</td>\n", "      <td>2379504</td>\n", "      <td>00208X-E</td>\n", "      <td>NVIDIA Corp.</td>\n", "      <td>NVIDIA Corporation</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>33115.052807</td>\n", "      <td>34618.656093</td>\n", "      <td>2.968748e+06</td>\n", "      <td>NVIDIA Corporation is a full-stack computing i...</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>B81TLL-R</td>\n", "      <td>MDX5ZL-S</td>\n", "      <td>AVGO-US</td>\n", "      <td>US11135F1012</td>\n", "      <td>BDZ78H9</td>\n", "      <td>0JCLYY-E</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>5828.494482</td>\n", "      <td>7560.204036</td>\n", "      <td>9.194190e+05</td>\n", "      <td>Broadcom Inc. is a global technology firm that...</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>WWDPYB-S</td>\n", "      <td>TSLA-US</td>\n", "      <td>US88160R1014</td>\n", "      <td>B616C79</td>\n", "      <td>006XY7-E</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>29106.313789</td>\n", "      <td>32312.138650</td>\n", "      <td>8.040649e+05</td>\n", "      <td>Tesla, Inc. designs, develops, manufactures, s...</td>\n", "      <td>0.3</td>\n", "      <td>0.58</td>\n", "      <td>1.00</td>\n", "      <td>0.64</td>\n", "      <td>0.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>B19ST9-R</td>\n", "      <td>W38FV3-S</td>\n", "      <td>LLY-US</td>\n", "      <td>US5324571083</td>\n", "      <td>2516152</td>\n", "      <td>000P56-E</td>\n", "      <td>Eli Lilly &amp; Co.</td>\n", "      <td>Eli Lilly and Company</td>\n", "      <td>NYS</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>3030.989148</td>\n", "      <td>3017.401099</td>\n", "      <td>7.711702e+05</td>\n", "      <td>Eli Lilly and Company is a medicine company. T...</td>\n", "      <td>0.5</td>\n", "      <td>0.28</td>\n", "      <td>0.85</td>\n", "      <td>0.62</td>\n", "      <td>0.67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 46 columns</p>\n", "</div>"], "text/plain": ["    Fsym_ID Fsym_Security_ID   Ticker          ISIN    SEDOL Entity_ID  \\\n", "0  MH33D6-R         R85KLC-S  AAPL-US  US0378331005  2046251  000C7F-E   \n", "1  K7TPSX-R         QDYJZC-S  NVDA-US  US67066G1040  2379504  00208X-E   \n", "2  B81TLL-R         MDX5ZL-S  AVGO-US  US11135F1012  BDZ78H9  0JCLYY-E   \n", "3  Q2YN1N-R         WWDPYB-S  TSLA-US  US88160R1014  B616C79  006XY7-E   \n", "4  B19ST9-R         W38FV3-S   LLY-US  US5324571083  2516152  000P56-E   \n", "\n", "       Entity_Name          Security_Name Exchange_Code  \\\n", "0      Apple, Inc.             Apple Inc.           NAS   \n", "1     NVIDIA Corp.     NVIDIA Corporation           NAS   \n", "2    Broadcom Inc.          Broadcom Inc.           NAS   \n", "3      Tesla, Inc.            Tesla, Inc.           NAS   \n", "4  Eli Lilly & Co.  Eli Lilly and Company           NYS   \n", "\n", "             Exchange_Name  ... Last_Trade_Date       ADTV_6M       ADTV_3M  \\\n", "0                   NASDAQ  ...      2025-03-20  11780.832445  12683.983288   \n", "1                   NASDAQ  ...      2025-03-20  33115.052807  34618.656093   \n", "2                   NASDAQ  ...      2025-03-20   5828.494482   7560.204036   \n", "3                   NASDAQ  ...      2025-03-20  29106.313789  32312.138650   \n", "4  New York Stock Exchange  ...      2025-03-20   3030.989148   3017.401099   \n", "\n", "  Security_Mcap_USD                               Business Description  \\\n", "0      3.207062e+06  Apple Inc. designs, manufactures and markets s...   \n", "1      2.968748e+06  NVIDIA Corporation is a full-stack computing i...   \n", "2      9.194190e+05  Broadcom Inc. is a global technology firm that...   \n", "3      8.040649e+05  Tesla, Inc. designs, develops, manufactures, s...   \n", "4      7.711702e+05  Eli Lilly and Company is a medicine company. T...   \n", "\n", "  Fixed_Assets_Score Economic_Heft_Score  Import_Intensity_Score  \\\n", "0                0.5                0.02                    0.55   \n", "1                0.5                0.11                    0.53   \n", "2                0.5                0.11                    0.53   \n", "3                0.3                0.58                    1.00   \n", "4                0.5                0.28                    0.85   \n", "\n", "  Employment_Score Combined_Score  \n", "0             0.22           0.40  \n", "1             0.62           0.48  \n", "2             0.62           0.48  \n", "3             0.64           0.76  \n", "4             0.62           0.67  \n", "\n", "[5 rows x 46 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["companies_df.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "b27da375", "metadata": {}, "outputs": [], "source": ["industry_companies = companies_df[['Entity_Name', 'Factset_Industry']].sort_values('Factset_Industry')"]}, {"cell_type": "code", "execution_count": 25, "id": "18692b5f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Factset_Industry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>Leonardo DRS, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Honeywell International, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>Crane Co.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>365</th>\n", "      <td>Kratos Defense &amp; Security Solutions, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>The Boeing Co.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>366</th>\n", "      <td>Hexcel Corp.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>Lockheed Martin Corp.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>HEICO Corp.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>400</th>\n", "      <td>Spirit AeroSystems Holdings, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>244</th>\n", "      <td>Rocket Lab USA, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   Entity_Name     Factset_Industry\n", "243                         Leonardo DRS, Inc.  Aerospace & Defense\n", "30               Honeywell International, Inc.  Aerospace & Defense\n", "238                                  Crane Co.  Aerospace & Defense\n", "365  Kratos Defense & Security Solutions, Inc.  Aerospace & Defense\n", "34                              The Boeing Co.  Aerospace & Defense\n", "366                               Hexcel Corp.  Aerospace & Defense\n", "37                       Lockheed Martin Corp.  Aerospace & Defense\n", "102                                HEICO Corp.  Aerospace & Defense\n", "400          Spirit AeroSystems Holdings, Inc.  Aerospace & Defense\n", "244                       Rocket Lab USA, Inc.  Aerospace & Defense"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["industry_companies.head(10)"]}, {"cell_type": "code", "execution_count": 26, "id": "3153c8d4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Factset_Industry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Leonardo DRS, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Honeywell International, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Crane Co.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Kratos Defense &amp; Security Solutions, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>The Boeing Co.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Hexcel Corp.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Lockheed Martin Corp.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>HEICO Corp.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Spirit AeroSystems Holdings, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Rocket Lab USA, Inc.</td>\n", "      <td>Aerospace &amp; Defense</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Entity_Name     Factset_Industry\n", "0                         Leonardo DRS, Inc.  Aerospace & Defense\n", "1              Honeywell International, Inc.  Aerospace & Defense\n", "2                                  Crane Co.  Aerospace & Defense\n", "3  Kratos Defense & Security Solutions, Inc.  Aerospace & Defense\n", "4                             The Boeing Co.  Aerospace & Defense\n", "5                               Hexcel Corp.  Aerospace & Defense\n", "6                      Lockheed Martin Corp.  Aerospace & Defense\n", "7                                HEICO Corp.  Aerospace & Defense\n", "8          Spirit AeroSystems Holdings, Inc.  Aerospace & Defense\n", "9                       Rocket Lab USA, Inc.  Aerospace & Defense"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["industry_companies.reset_index(drop=True, inplace=True)\n", "\n", "industry_companies.head(10)"]}, {"cell_type": "code", "execution_count": 27, "id": "31d9905a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(809, 2)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["industry_companies.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "31a59dd8", "metadata": {}, "outputs": [{"data": {"text/plain": ["126"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# no of unique industrys\n", "industry_companies['Factset_Industry'].nunique()"]}, {"cell_type": "code", "execution_count": 32, "id": "d5e8aa43", "metadata": {}, "outputs": [], "source": ["industry_companies.to_excel(\"industry_companies.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": 16, "id": "e2eddb69", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.read_excel(\"industry_companies.xlsx\")"]}, {"cell_type": "code", "execution_count": 17, "id": "b2a1bfdc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Factset_Industry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>QuinStreet, Inc.</td>\n", "      <td>Advertising/Marketing Services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>National CineMedia, Inc.</td>\n", "      <td>Advertising/Marketing Services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Clear Channel Outdoor Holdings, Inc.</td>\n", "      <td>Advertising/Marketing Services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Trump Media &amp; Technology Group Corp.</td>\n", "      <td>Advertising/Marketing Services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Roku, Inc.</td>\n", "      <td>Advertising/Marketing Services</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            Entity_Name                Factset_Industry\n", "0                      QuinStreet, Inc.  Advertising/Marketing Services\n", "1              National CineMedia, Inc.  Advertising/Marketing Services\n", "2  Clear Channel Outdoor Holdings, Inc.  Advertising/Marketing Services\n", "3  Trump Media & Technology Group Corp.  Advertising/Marketing Services\n", "4                            Roku, Inc.  Advertising/Marketing Services"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 28, "id": "ba8316c2", "metadata": {}, "outputs": [], "source": ["industry_counts = industry_companies.groupby(\"Factset_Industry\")[\"Entity_Name\"].count().reset_index()"]}, {"cell_type": "code", "execution_count": 29, "id": "09f03b2b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Factset_Industry</th>\n", "      <th>Entity_Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Aerospace &amp; Defense</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Aluminum</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Apparel/Footwear</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Auto Parts: OEM</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Automotive Aftermarket</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Beverages: Alcoholic</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Beverages: Non-Alcoholic</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Biotechnology</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Building Products</td>\n", "      <td>18</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Factset_Industry  Entity_Name\n", "0               Aerospace & Defense           42\n", "1  Agricultural Commodities/Milling           10\n", "2                          Aluminum            3\n", "3                  Apparel/Footwear           18\n", "4                   Auto Parts: OEM           16\n", "5            Automotive Aftermarket            5\n", "6              Beverages: Alcoholic            4\n", "7          Beverages: Non-Alcoholic            8\n", "8                     Biotechnology           76\n", "9                 Building Products           18"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["industry_counts.head(10)"]}, {"cell_type": "code", "execution_count": 30, "id": "9004696f", "metadata": {}, "outputs": [], "source": ["industry_counts.to_excel(\"industry_counts.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}