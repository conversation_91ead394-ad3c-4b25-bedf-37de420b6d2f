{"cells": [{"cell_type": "markdown", "id": "f4962f35", "metadata": {}, "source": ["## URL Context"]}, {"cell_type": "code", "execution_count": 2, "id": "70306889", "metadata": {}, "outputs": [], "source": ["from google import genai\n", "from google.genai.types import Tool, GenerateContentConfig, GoogleSearch\n", "import google.genai.types as types\n", "from sec_api import QueryApi, ExtractorApi\n", "from typing import Any, Dict, List, Optional, Tuple\n", "from requests import HTTPError\n", "import json\n", "from enum import Enum\n", "import os\n", "import sys\n", "\n"]}, {"cell_type": "code", "execution_count": 70, "id": "3c29de39", "metadata": {}, "outputs": [], "source": ["client = genai.Client(api_key=\"AIzaSyBqrAc1A0b-xGbYBdgNYVQEnuQQDk4MO3E\")\n", "model_id = \"gemini-2.5-flash-preview-05-20\""]}, {"cell_type": "code", "execution_count": 86, "id": "18bf895e", "metadata": {}, "outputs": [], "source": ["SEC_API_KEY = \"3c4defb7a3cd524cf604e5f75c7c14290ab4b3c0f1e2fa7cac9dfb621342f938\""]}, {"cell_type": "code", "execution_count": null, "id": "229a31e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NVIDIA's gross profit for the fiscal year ended January 26, 2025, was $97,858 million. This represents a gross margin of 75.0%. The increase in gross margin was primarily driven by a higher mix of Data Center revenue.\n", "url_metadata=[UrlMetadata(retrieved_url='https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97', url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>)]\n"]}], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "#tools.append(Tool(google_search=types.GoogleSearch))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=\"summarize this and tell me about the gross profit of this year https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97\",\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")\n", "\n", "for each in response.candidates[0].content.parts:\n", "    print(each.text)\n", "# get URLs retrieved for context\n", "print(response.candidates[0].url_context_metadata)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c04c3c0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NVIDIA's cash and cash equivalents for the fiscal year ended January 28, 2024, were $7,280 million.\n", "url_metadata=[UrlMetadata(retrieved_url='https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97', url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>)]\n"]}], "source": ["# targeted query 2\n", "tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "#tools.append(Tool(google_search=types.GoogleSearch))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=\"what is the cash and cash equivalents of this company for last year? https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97\",\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")\n", "\n", "for each in response.candidates[0].content.parts:\n", "    print(each.text)\n", "# get URLs retrieved for context\n", "print(response.candidates[0].url_context_metadata)\n"]}, {"cell_type": "markdown", "id": "810d9c83", "metadata": {}, "source": ["```\n", "Pick the SEC filings of companies whose data is not available\n", "put those links in the url context \n", "ask questions\n"]}, {"cell_type": "code", "execution_count": 54, "id": "6dbe584f", "metadata": {}, "outputs": [], "source": ["class SecEdgerImportantInfo(str, Enum):\n", "    COMPANY_NAME = 'companyName'\n", "    # STOCK_SYMBOL = 'ticker'\n", "    # CIK = 'cik'\n", "    FORM_TYPE = 'formType'\n", "    FISCAL_DATE_ENDING = 'periodOfReport'\n", "    # FILING_DATE = 'filedAt'\n", "    FILING_URL = 'linkToFilingDetails'\n", "    # DATA_FILES = 'dataFiles'\n", "\n", "    def __repr__(self) -> str:\n", "        return str.__repr__(self)\n", "    \n", "    def __str__(self) -> str:\n", "        return str.__str__(self)\n", "\n", "    @staticmethod\n", "    def get_all_important_info() -> List[str]:\n", "        return [col.value for col in SecEdgerImportantInfo]"]}, {"cell_type": "code", "execution_count": 55, "id": "9c0592d4", "metadata": {}, "outputs": [], "source": ["def get_filing_info(stock_symbol, file_type: str = \"10-Q\", date_range: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:\n", "    try:\n", "        query_api = QueryApi(api_key=SEC_API_KEY)\n", "        if date_range is None:\n", "            query = f'ticker: {stock_symbol} AND formType: \"{file_type}\"'\n", "        else:\n", "            query = f\"ticker: {stock_symbol} AND formType: \\\"{file_type}\\\" AND periodOfReport: [{date_range['from']} TO {date_range['upto']}]\"\n", "        query_params = {\n", "            \"query\": query,\n", "            \"sort\": [{\"periodOfReport\": {\"order\": \"desc\"}}]\n", "        }\n", "        filings = query_api.get_filings(query=query_params)[\"filings\"]\n", "        report_info = [\n", "            {field: filing[field] for field in SecEdgerImportantInfo.get_all_important_info()} for filing in filings \n", "            if SecEdgerImportantInfo.FISCAL_DATE_ENDING in filing\n", "        ]\n", "        return report_info\n", "    \n", "    except Exception as e:\n", "        raise HTTPError(f'Error occurred while running query_api -> {e}')"]}, {"cell_type": "code", "execution_count": 24, "id": "ed48c076", "metadata": {}, "outputs": [], "source": ["result = get_filing_info(file_type=\"10-Q\", stock_symbol=\"NVDA\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-04-16\"})"]}, {"cell_type": "code", "execution_count": 13, "id": "14582871", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"companyName\": \"NVIDIA CORP\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-10-27\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"NVIDIA CORP\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-07-28\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"NVIDIA CORP\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-04-28\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm\"\n", "    }\n", "]\n"]}], "source": ["print(json.dumps(result, indent=4))"]}, {"cell_type": "code", "execution_count": 14, "id": "6d507ca7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filing URL: https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm\n"]}], "source": ["filing_url = result[0][SecEdgerImportantInfo.FILING_URL]\n", "print(f\"Filing URL: {filing_url}\")"]}, {"cell_type": "markdown", "id": "feae02cf", "metadata": {}, "source": ["## trying for all 10 Q urls of two companies"]}, {"cell_type": "markdown", "id": "06fe2fc8", "metadata": {}, "source": ["Enerpac Tool Group Corp.(EPAC), Worthington Enterprises, Inc.(WOR)"]}, {"cell_type": "code", "execution_count": 10, "id": "7e071435", "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Optional\n", "from collections import defaultdict\n", "\n", "def get_10Q_url(stock_symbols: List[str], date_range: Optional[Dict[str, str]] = None):\n", "    filing_urls = []\n", "    for stock_symbol in stock_symbols:\n", "        url_list = get_filing_info(stock_symbol, date_range=date_range)\n", "        company_dict = defaultdict(dict)\n", "\n", "        for url in url_list:\n", "            company_name = url[SecEdgerImportantInfo.COMPANY_NAME]\n", "            period = url[SecEdgerImportantInfo.FISCAL_DATE_ENDING]\n", "            filing_url = url[SecEdgerImportantInfo.FILING_URL]\n", "\n", "            company_dict[company_name][period] = filing_url\n", "\n", "        filing_urls.append(dict(company_dict))\n", "        \n", "    return filing_urls\n"]}, {"cell_type": "code", "execution_count": 11, "id": "f667e6f2", "metadata": {}, "outputs": [], "source": ["url_list = get_10Q_url([\"EPAC\", \"WOR\"], date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-29\"})"]}, {"cell_type": "code", "execution_count": 12, "id": "582d7c32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"ENERPAC TOOL GROUP CORP\": {\n", "            \"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695525000008/epac-20250228.htm\",\n", "            \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\",\n", "            \"2024-05-31\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000042/epac-20240531.htm\",\n", "            \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000037/epac-20240229.htm\"\n", "        }\n", "    },\n", "    {\n", "        \"WORTHINGTON ENTERPRISES, INC.\": {\n", "            \"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", "            \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", "            \"2024-08-31\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", "            \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "        }\n", "    }\n", "]\n"]}], "source": ["print(json.dumps(url_list, indent=4))"]}, {"cell_type": "code", "execution_count": 24, "id": "115a7776", "metadata": {}, "outputs": [], "source": ["system_instruction_sec = \"\"\"\\\n", "You are an expert in extracting structured information from official SEC filings of companies.\n", "\n", "You are provided with a list of dictionaries. In each dictionary:\n", "- The key is a company name.\n", "- The value is another dictionary, where:\n", "  - Keys are fiscal quarter ending dates (in YYYY-MM-DD format).\n", "  - Values are URLs to the company's 10-Q filings for those quarters.\n", "\n", "All filings are from the period January 1, 2024 to the present. Your task is to extract relevant United states specific information from each filing under the following three categories and return the output strictly as per the described JSON format.\n", "\n", "Categories of Interest:\n", "\n", "1. Manufacturing Capacity Expansion\n", "   - Extract any statements related to expansion of existing manufacturing facilities or plans to establish new plants/factories in the United States.\n", "   - Include numeric details wherever available:\n", "     - Capital expenditure (e.g., \"$1 billion investment\")\n", "     - Plant size (e.g., \"300,000 sq ft facility\")\n", "     - Projected production capacity (e.g., \"10 million units/year\")\n", "\n", "2. Employment Generation or Hiring Commitments\n", "   - Extract information about hiring plans, job creation commitments, or workforce expansion in the United States.\n", "   - Include specific figures where mentioned:\n", "     - Number of jobs (e.g., \"2,500 new roles\")\n", "     - Duration, location, or business unit if specified.\n", "\n", "3. Actual Production Output\n", "   - Extract actual production data from U.S. operations as disclosed.\n", "   - Metrics may include units produced, tons manufactured, volume output, etc.\n", "\n", "Required Output Format:\n", "\n", "Return a list of dictionaries. Each dictionary corresponds to one company and must follow this exact json structure:\n", "\n", "```json\n", "[\n", "  {\n", "  \"company\": \"<Company Name>\",\n", "  \"capacity_expansion_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"employment_generation_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"production_output\": [\n", "    {\n", "      \"output_details\": \"<Details of actual production figures per quarter>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"summary\": \"<Concise summary of all the findings across the three categories>\"\n", "}\n", "]\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 35, "id": "1dc41d85", "metadata": {}, "outputs": [], "source": ["output_format_sec = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"multi_company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"array\",\n", "            \"items\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"company\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"Name of the company under consideration.\"\n", "                    },\n", "                    \"capacity_expansion_plans\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"plan_details\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Manufacturing capacity expansion or new plant opening plan details of the company in the United States as per the 10-Q SEC filings.\"\n", "                                },\n", "                                \"source\": {\n", "                                    \"type\": \"object\",\n", "                                    \"properties\": {\n", "                                        \"publishing_date\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Quarter ending date provided for the 10-Q SEC filing by the user (YYYY-MM-DD).\"\n", "                                        },\n", "                                        \"section\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Section or heading within the 10-Q SEC filing where the excerpt is found.\"\n", "                                        },\n", "                                        \"url\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"URL of the source 10-Q SEC filing.\"\n", "                                        }\n", "                                    },\n", "                                    \"required\": [\n", "                                        \"publishing_date\",\n", "                                        \"section\",\n", "                                        \"url\"\n", "                                    ],\n", "                                    \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                                }\n", "                            },\n", "                            \"required\": [\n", "                                \"plan_details\",\n", "                                \"source\"\n", "                            ],\n", "                            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                        }\n", "                    },\n", "                    \"employment_generation_plans\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"plan_details\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Employment generation plan details of the company in the United States as per the 10-Q SEC filings.\"\n", "                                },\n", "                                \"source\": {\n", "                                    \"type\": \"object\",\n", "                                    \"properties\": {\n", "                                        \"publishing_date\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Quarter ending date provided for the 10-Q SEC filing by the user (YYYY-MM-DD).\"\n", "                                        },\n", "                                        \"section\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Section or heading within the 10-Q SEC filing where the excerpt is found.\"\n", "                                        },\n", "                                        \"url\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"URL of the source 10-Q SEC filing.\"\n", "                                        }\n", "                                    },\n", "                                    \"required\": [\n", "                                        \"publishing_date\",\n", "                                        \"section\",\n", "                                        \"url\"\n", "                                    ],\n", "                                    \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                                }\n", "                            },\n", "                            \"required\": [\n", "                                \"plan_details\",\n", "                                \"source\"\n", "                            ],\n", "                            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                        }\n", "                    },\n", "                    \"production_output\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"output_details\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Actual production output (units or volumes produced) per quarter of the company in the United States as reported in 10-Q SEC filings.\"\n", "                                },\n", "                                \"source\": {\n", "                                    \"type\": \"object\",\n", "                                    \"properties\": {\n", "                                        \"publishing_date\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Quarter ending date provided for the 10-Q SEC filing by the user (YYYY-MM-DD).\"\n", "                                        },\n", "                                        \"section\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Section or heading within the 10-Q SEC filing where the excerpt is found.\"\n", "                                        },\n", "                                        \"url\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"URL of the source 10-Q SEC filing.\"\n", "                                        }\n", "                                    },\n", "                                    \"required\": [\n", "                                        \"publishing_date\",\n", "                                        \"section\",\n", "                                        \"url\"\n", "                                    ],\n", "                                    \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                                }\n", "                            },\n", "                            \"required\": [\n", "                                \"output_details\",\n", "                                \"source\"\n", "                            ],\n", "                            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                        }\n", "                    },\n", "                    \"summary\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"Short overview of all plan details and output details.\"\n", "                    }\n", "                },\n", "                \"required\": [\n", "                    \"company\",\n", "                    \"capacity_expansion_plans\",\n", "                    \"employment_generation_plans\",\n", "                    \"production_output\",\n", "                    \"summary\"\n", "                ],\n", "                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "            }\n", "        }\n", "    }\n", "}\n"]}, {"cell_type": "code", "execution_count": 15, "id": "40b007aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{\"ENERPAC TOOL GROUP CORP\": {\"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695525000008/epac-20250228.htm\", \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\", \"2024-05-31\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000042/epac-20240531.htm\", \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000037/epac-20240229.htm\"}}, {\"WORTHINGTON ENTERPRISES, INC.\": {\"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\", \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\", \"2024-08-31\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\", \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"}}]\n", "<class 'str'>\n"]}], "source": ["quarterly_urls = json.dumps(url_list)\n", "print(quarterly_urls)\n", "print(type(quarterly_urls))"]}, {"cell_type": "code", "execution_count": 54, "id": "b466ddad", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents= quarterly_urls,\n", "    config=GenerateContentConfig(\n", "        system_instruction = system_instruction_sec,\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")\n", "\n", "# for each in response.candidates[0].content.parts:\n", "#     print(each.text)\n", "# # get URLs retrieved for context\n", "# print(response.candidates[0].url_context_metadata)\n"]}, {"cell_type": "code", "execution_count": 57, "id": "3ca4bdb2", "metadata": {}, "outputs": [], "source": ["raw_text = response.candidates[0].content.parts[0].text\n"]}, {"cell_type": "code", "execution_count": 58, "id": "90da5a90", "metadata": {}, "outputs": [], "source": ["import re\n", "cleaned_json_str = re.sub(r\"^```json\\s*|\\s*```$\", \"\", raw_text.strip())\n", "parsed_data = json.loads(cleaned_json_str)"]}, {"cell_type": "code", "execution_count": 61, "id": "a0c10474", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"company\": \"ENERPAC TOOL GROUP CORP\",\n", "        \"capacity_expansion_plans\": [\n", "            {\n", "                \"plan_details\": \"Increased capital expenditures relating to build-out costs for the Company's new headquarters location in Milwaukee, Wisconsin, with an anticipated move-in date later in fiscal 2025. This contributed to a $2.8 million increase in Land, buildings and improvements from August 31, 2024 to November 30, 2024.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-11-30\",\n", "                    \"section\": \"Item 2\\u2014Management's Discussion and Analysis of Financial Condition and Results of Operations, Cash Flows and Liquidity & Note 1. Basis of Presentation - Property Plant and Equipment\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\"\n", "                }\n", "            }\n", "        ],\n", "        \"employment_generation_plans\": [],\n", "        \"production_output\": [],\n", "        \"summary\": \"ENERPAC TOOL GROUP CORP. made capital expenditures for the build-out of a new headquarters in Milwaukee, Wisconsin, impacting their property, plant and equipment balance. No explicit employment generation plans or specific U.S. production output figures were found in the available filings.\"\n", "    },\n", "    {\n", "        \"company\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"capacity_expansion_plans\": [\n", "            {\n", "                \"plan_details\": \"Ongoing capital expenditures on property, plant and equipment in the U.S. 'Buildings and improvements' increased by $6,206 thousand, 'Machinery and equipment' by $34,842 thousand, and 'Construction in progress' by $2,826 thousand during the three months ended November 30, 2024.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-11-30\",\n", "                    \"section\": \"Item 1. Financial Statements - Consolidated Balance Sheets\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "                }\n", "            },\n", "            {\n", "                \"plan_details\": \"Ongoing capital expenditures on property, plant and equipment in the U.S. 'Buildings and improvements' increased by $5,776 thousand, 'Machinery and equipment' by $22,414 thousand, and 'Construction in progress' by $9,337 thousand during the three months ended August 31, 2024.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-08-31\",\n", "                    \"section\": \"Item 1. Financial Statements - Consolidated Balance Sheets\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "                }\n", "            }\n", "        ],\n", "        \"employment_generation_plans\": [],\n", "        \"production_output\": [\n", "            {\n", "                \"output_details\": \"Total reportable segments volume for the three months ended November 30, 2024, was 19,264,673 units, a decrease from 19,840,403 units in the prior-year period. This includes Consumer Products at 16,170,556 units (up from 16,031,583) and Building Products at 3,094,117 units (down from 3,808,820). Note: These volumes are for global segments and are not explicitly stated as U.S. only production.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-11-30\",\n", "                    \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "                }\n", "            },\n", "            {\n", "                \"output_details\": \"Total reportable segments volume for the three months ended August 31, 2024, was 19,264,673 units, a decrease from 19,946,709 units in the prior-year period. This includes Consumer Products at 16,170,556 units (up from 16,031,583) and Building Products at 3,094,117 units (down from 3,808,820). Note: These volumes are for global segments and are not explicitly stated as U.S. only production.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-08-31\",\n", "                    \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "                }\n", "            }\n", "        ],\n", "        \"summary\": \"WORTHINGTON ENTERPRISES, INC. continued to invest in property, plant, and equipment in the U.S. through capital expenditures, with notable increases in buildings, machinery, and construction in progress. The company reported global production volumes for its Consumer Products and Building Products segments, but no specific U.S.-only production data was available. No explicit employment generation plans were disclosed; rather, discussions around 'restructuring activities' implied workforce adjustments.\"\n", "    }\n", "]\n"]}], "source": ["print(json.dumps(parsed_data, indent=4))"]}, {"cell_type": "markdown", "id": "cbf882e5", "metadata": {}, "source": ["## single company data"]}, {"cell_type": "code", "execution_count": 38, "id": "fb94362e", "metadata": {}, "outputs": [], "source": ["single_company_data = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of a company.\n", "The company of interest is WORTHINGTON ENTERPRISES. And url to access its different SEC reports are as follows:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "              \n", "All filings are from the period January 1, 2024 to the present. Your task is to extract relevant United States-specific information from these filings under the following three categories and return the output strictly in the described JSON format.\n", "\n", "Categories of Interest:\n", "\n", "1. Manufacturing Capacity Expansion\n", "   - Extract any statements related to expansion of existing manufacturing facilities or plans to establish new plants/factories in the United States.\n", "   - Include numeric details wherever available:\n", "     - Capital expenditure (e.g., \"$1 billion investment\")\n", "     - Plant size (e.g., \"300,000 sq ft facility\")\n", "     - Projected production capacity (e.g., \"10 million units/year\")\n", "\n", "2. Employment Generation or Hiring Commitments\n", "   - Extract information about hiring plans, job creation commitments, or workforce expansion in the United States.\n", "   - Include specific figures where mentioned:\n", "     - Number of jobs (e.g., \"2,500 new roles\")\n", "     - Duration, location, or business unit if specified.\n", "\n", "3. Actual Production Output\n", "   - Extract actual production data from U.S. operations as disclosed.\n", "   - Metrics may include units produced, tons manufactured, volume output, etc.\n", "\n", "Required Output Format:\n", "\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"capacity_expansion_plans\"`: A list of dictionaries, each capturing one U.S.-based manufacturing capacity expansion initiative.\n", "- `\"employment_generation_plans\"`: A list of dictionaries, each capturing one hiring or job creation plan.\n", "- `\"production_output\"`: A list of dictionaries, each capturing actual production figures or details.\n", "- `\"summary\"`: A concise narrative summary of findings across all categories.\n", "\n", "JSON schema format for the output generation is given below:\n", "```\n", " {\n", "  \"company\": \"<Company Name>\",\n", "  \"capacity_expansion_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"employment_generation_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"production_output\": [\n", "    {\n", "      \"output_details\": \"<Details of actual production figures per quarter>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"summary\": \"<Concise summary of all the findings across the three categories>\"\n", "}\n", "\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "fbd976b0", "metadata": {}, "outputs": [], "source": ["result_10k = get_filing_info(file_type=\"10-K\", stock_symbol=\"WOR\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-29\"})\n", "result_10q = get_filing_info(file_type=\"10-Q\", stock_symbol=\"WOR\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-29\"})\n"]}, {"cell_type": "code", "execution_count": 35, "id": "0a167348", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-K\",\n", "        \"periodOfReport\": \"2024-05-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "    }\n", "]\n", "**************************************************\n", "[\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2025-02-28\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-11-30\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-08-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-02-29\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "    }\n", "]\n", "**************************************************\n"]}], "source": ["\n", "print(json.dumps(result_10k, indent=4))\n", "print(\"*\"*50)\n", "print(json.dumps(result_10q, indent=4))\n", "print(\"*\"*50)\n"]}, {"cell_type": "code", "execution_count": 43, "id": "cbe49a24", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=single_company_data,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 44, "id": "b0461f33", "metadata": {}, "outputs": [], "source": ["raw_text = response.candidates[0].content.parts[0].text"]}, {"cell_type": "code", "execution_count": 45, "id": "a64b4ead", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"WORTHINGTON ENTERPRISES\",\n", "    \"capacity_expansion_plans\": [\n", "        {\n", "            \"plan_details\": \"On February 1, 2024, Worthington Enterprises acquired an 80% ownership stake in Halo, an asset-light business focused on outdoor cooking solutions. This acquisition included one manufacturing facility located in Kentucky, United States. The total purchase price for this acquisition was approximately $9.6 million.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-05-31\",\n", "                \"section\": \"Item 1. Business - Other Business Developments / Note Q \\u2013 Acquisitions\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "            }\n", "        }\n", "    ],\n", "    \"employment_generation_plans\": [],\n", "    \"production_output\": [\n", "        {\n", "            \"output_details\": \"For the fiscal year ended May 31, 2024, the Consumer Products segment produced 66,632,148 units. The Building Products segment produced 14,157,050 units. The consolidated total volume from continuing operations was 81,312,367 units, primarily representing U.S. operations.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-05-31\",\n", "                \"section\": \"Item 7. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "            }\n", "        },\n", "        {\n", "            \"output_details\": \"For the three months ended August 31, 2024 (first quarter of fiscal year 2025), the Consumer Products segment produced 16,170,556 units. The Building Products segment produced 3,094,117 units. The consolidated total volume from continuing operations was 19,264,673 units, primarily representing U.S. operations.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-08-31\",\n", "                \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "            }\n", "        }\n", "    ],\n", "    \"summary\": \"WORTHINGTON ENTERPRISES undertook one significant U.S.-based manufacturing initiative by acquiring an 80% stake in Halo on February 1, 2024, for approximately $9.6 million, adding a manufacturing facility in Kentucky. [1] No specific new hiring or job creation commitments were disclosed in the reviewed filings. For actual production output, the company's Consumer Products segment produced 66.6 million units and the Building Products segment produced 14.2 million units for the fiscal year ended May 31, 2024. [1] For the first quarter of fiscal year 2025 (three months ended August 31, 2024), the Consumer Products segment produced 16.2 million units, and the Building Products segment produced 3.1 million units. [4] The majority of these operations and their reported production volumes are U.S.-based. [1]\"\n", "}\n"]}], "source": ["import re\n", "cleaned_json_str = re.sub(r\"^```json\\s*|\\s*```$\", \"\", raw_text.strip())\n", "parsed_data = json.loads(cleaned_json_str)\n", "print(json.dumps(parsed_data, indent=4))"]}, {"cell_type": "markdown", "id": "a5f4cf05", "metadata": {}, "source": ["# Separate extraction for each score"]}, {"cell_type": "markdown", "id": "def6fc86", "metadata": {}, "source": ["## capacity expansion"]}, {"cell_type": "code", "execution_count": 12, "id": "c5da06e9", "metadata": {}, "outputs": [], "source": ["sec_capacity_exp_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding manufacturing capacity expansion or new plant openings in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Capacity-Expansion Plans: \n", "     - Locate statements or paragraphs in the SEC filings describing intentions, plans, or commitments to:\n", "     - Expand existing factories in the United States\n", "     - Build or commission new manufacturing facilities in the United States.\n", "b. Quantify Every Plan:\n", "     - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the SEC filings. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"capacity_expansion_plans\"`: A list of dictionaries, each capturing one U.S.-based manufacturing capacity expansion initiative.\n", "- `\"summary\"`: A concise narrative summary of findings across all categories.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": \"<Name of the company>\",\n", "  \"capacity_expansion_plans\": [\n", "    {{\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved capacity expansion plans>\"\n", "}}\n", "```\n", "\"\"\"\n"]}, {"cell_type": "markdown", "id": "0ce64505", "metadata": {}, "source": ["WORTHINGTON ENTERPRISES, INC"]}, {"cell_type": "code", "execution_count": 13, "id": "3657e9ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is WORTHINGTON ENTERPRISES. You will be provided the URLs to access 10-K and 10-Q reports of the WORTHINGTON ENTERPRISES, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding manufacturing capacity expansion or new plant openings in the United States as per the 10-K and 10-Q reports of the WORTHINGTON ENTERPRISES and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Capacity-Expansion Plans: \n", "     - Locate statements or paragraphs in the SEC filings describing intentions, plans, or commitments to:\n", "     - Expand existing factories in the United States\n", "     - Build or commission new manufacturing facilities in the United States.\n", "b. Quantify Every Plan:\n", "     - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the SEC filings. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"capacity_expansion_plans\"`: A list of dictionaries, each capturing one U.S.-based manufacturing capacity expansion initiative.\n", "- `\"summary\"`: A concise narrative summary of findings across all categories.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{\n", "  \"company\": \"<Name of the company>\",\n", "  \"capacity_expansion_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    }\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved capacity expansion plans>\"\n", "}\n", "```\n", "\n"]}], "source": ["WOR_Prompt = sec_capacity_exp_retrieval_prompt.format(company_name = \"WORTHINGTON ENTERPRISES\")\n", "print(WOR_Prompt)"]}, {"cell_type": "code", "execution_count": 14, "id": "1ef8389d", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=WOR_Prompt,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 15, "id": "9a5c284e", "metadata": {}, "outputs": [], "source": ["raw_text = response.candidates[0].content.parts[0].text"]}, {"cell_type": "code", "execution_count": 16, "id": "c09c9ec8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"WORTHINGTON ENTERPRISES\",\n", "  \"capacity_expansion_plans\": [\n", "    {\n", "      \"plan_details\": \"On February 1, 2024, Worthington Enterprises acquired an 80% ownership stake in Halo, an affiliate of HPG, an asset-light business specializing in technology-enabled solutions in the outdoor cooking space. Halo operates one owned manufacturing facility located in Kentucky. The acquisition brings this existing U.S. manufacturing presence under Worthington Enterprises' control.\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2024-05-31\",\n", "        \"section\": \"Item 1. Business - Other Business Developments; Note Q - Acquisitions\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "      }\n", "    }\n", "  ],\n", "  \"summary\": \"WORTHINGTON ENTERPRISES, from January 1, 2024, to the present, has not explicitly reported plans for new manufacturing plant openings or significant capacity expansions of existing factories in the United States with specific quantifiable metrics in its 10-K and 10-Q filings. The company did acquire an 80% ownership stake in Halo on February 1, 2024, which includes one owned manufacturing facility in Kentucky. However, Halo is described as an 'asset-light business,' and no details regarding an expansion of its manufacturing capacity or creation of new manufacturing lines within the U.S. were provided. The filings indicate general capital expenditures and increases in 'construction in progress,' but these are not broken down by specific U.S. manufacturing capacity expansion projects or new plant builds.\"\n", "}\n", "```\n"]}], "source": ["print(raw_text)"]}, {"cell_type": "code", "execution_count": 17, "id": "05f9fd58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"WORTHINGTON ENTERPRISES\",\n", "    \"capacity_expansion_plans\": [\n", "        {\n", "            \"plan_details\": \"On February 1, 2024, Worthington Enterprises acquired an 80% ownership stake in Halo, an affiliate of HPG, an asset-light business specializing in technology-enabled solutions in the outdoor cooking space. Halo operates one owned manufacturing facility located in Kentucky. The acquisition brings this existing U.S. manufacturing presence under Worthington Enterprises' control.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-05-31\",\n", "                \"section\": \"Item 1. Business - Other Business Developments; Note Q - Acquisitions\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "            }\n", "        }\n", "    ],\n", "    \"summary\": \"WORTHINGTON ENTERPRISES, from January 1, 2024, to the present, has not explicitly reported plans for new manufacturing plant openings or significant capacity expansions of existing factories in the United States with specific quantifiable metrics in its 10-K and 10-Q filings. The company did acquire an 80% ownership stake in Halo on February 1, 2024, which includes one owned manufacturing facility in Kentucky. However, Halo is described as an 'asset-light business,' and no details regarding an expansion of its manufacturing capacity or creation of new manufacturing lines within the U.S. were provided. The filings indicate general capital expenditures and increases in 'construction in progress,' but these are not broken down by specific U.S. manufacturing capacity expansion projects or new plant builds.\"\n", "}\n"]}], "source": ["import re\n", "cleaned_json_str = re.sub(r\"^```json\\s*|\\s*```$\", \"\", raw_text.strip())\n", "parsed_data = json.loads(cleaned_json_str)\n", "print(json.dumps(parsed_data, indent=4))"]}, {"cell_type": "code", "execution_count": 31, "id": "229dd2c4", "metadata": {}, "outputs": [], "source": ["sec_employment_gen_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding employment generation or hiring commitments of the {company_name} in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Employment-Generation Plans:  \n", "     - Locate any statements in the SEC filings describing hiring targets, job-creation commitments, or intended workforce increases in the United States.\n", "b. Quantify Every Plan:\n", "     - Quantify by citing numeric metrics if available in the 10-K and 10-Q reports.(e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"employment_generation_plans\"`: A list of dictionaries, each capturing one hiring or job creation plan.\n", "- `\"summary\"`: A concise narrative summary of retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": <Name of the company>,\n", "  \"employment_generation_plans\": [\n", "    {{\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved employment generation plans.>\"\n", "}}\n", "```\n", "\"\"\""]}, {"cell_type": "markdown", "id": "cd6fe151", "metadata": {}, "source": ["## employment generation"]}, {"cell_type": "code", "execution_count": 24, "id": "949a911f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is WORTHINGTON ENTERPRISES. You will be provided the URLs to access 10-K and 10-Q reports of the WORTHINGTON ENTERPRISES, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding employment generation or hiring commitments of the WORTHINGTON ENTERPRISES in the United States as per the 10-K and 10-Q reports of the WORTHINGTON ENTERPRISES and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Employment-Generation Plans:  \n", "     - Locate any statements in the SEC filings describing hiring targets, job-creation commitments, or intended workforce increases in the United States.\n", "b. Quantify Every Plan:\n", "     - Quantify by citing numeric metrics if available in the 10-K and 10-Q reports.(e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"employment_generation_plans\"`: A list of dictionaries, each capturing one hiring or job creation plan.\n", "- `\"summary\"`: A concise narrative summary of retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{\n", "  \"company\": <Name of the company>,\n", "  \"employment_generation_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    }\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved employment generation plans.>\"\n", "}\n", "```\n", "\n"]}], "source": ["WOR_Prompt_emp = sec_employment_gen_retrieval_prompt.format(company_name = \"WORTHINGTON ENTERPRISES\")\n", "print(WOR_Prompt_emp)"]}, {"cell_type": "code", "execution_count": 32, "id": "9952f9bf", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=WOR_Prompt_emp,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 33, "id": "404b1f17", "metadata": {}, "outputs": [], "source": ["raw_text = response.candidates[0].content.parts[0].text"]}, {"cell_type": "code", "execution_count": 34, "id": "04246073", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"WORTHINGTON ENTERPRISES\",\n", "  \"employment_generation_plans\": [],\n", "  \"summary\": \"Based on the provided SEC filings (10-K for the fiscal year ended May 31, 2024, and 10-Q reports for the periods ended November 30, 2024, August 31, 2024, and February 29, 2024), WORTHINGTON ENTERPRISES does not explicitly state any quantifiable employment generation plans or hiring commitments in the United States. The reports generally discuss the importance of attracting, training, and retaining talented personnel as part of their human capital management strategy and people-first philosophy. [1] However, these discussions do not include specific numeric targets for job creation or workforce expansion. Some sections of the reports refer to 'headcount reductions' and 'employee severance' in the context of restructuring activities and cost reduction efforts. [1, 3, 4, 5] One of the provided 10-Q URLs (February 28, 2025) was inaccessible.\"\n", "}\n", "```\n"]}], "source": ["print(raw_text)"]}, {"cell_type": "code", "execution_count": 35, "id": "f9ee254c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"WORTHINGTON ENTERPRISES\",\n", "    \"employment_generation_plans\": [],\n", "    \"summary\": \"Based on the provided SEC filings (10-K for the fiscal year ended May 31, 2024, and 10-Q reports for the periods ended November 30, 2024, August 31, 2024, and February 29, 2024), WORTHINGTON ENTERPRISES does not explicitly state any quantifiable employment generation plans or hiring commitments in the United States. The reports generally discuss the importance of attracting, training, and retaining talented personnel as part of their human capital management strategy and people-first philosophy. [1] However, these discussions do not include specific numeric targets for job creation or workforce expansion. Some sections of the reports refer to 'headcount reductions' and 'employee severance' in the context of restructuring activities and cost reduction efforts. [1, 3, 4, 5] One of the provided 10-Q URLs (February 28, 2025) was inaccessible.\"\n", "}\n"]}], "source": ["import re\n", "cleaned_json_str = re.sub(r\"^```json\\s*|\\s*```$\", \"\", raw_text.strip())\n", "parsed_data = json.loads(cleaned_json_str)\n", "print(json.dumps(parsed_data, indent=4))"]}, {"cell_type": "code", "execution_count": 40, "id": "8b89fb1f", "metadata": {}, "outputs": [], "source": ["sec_prod_output_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding actual production output (units or volumes produced) per quarter of the {company_name} in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Actual Production Output:\n", "     - Locate quarterly production output metrics in the United States, disclosed in the 10-K and 10-Q reports\n", "b. Quantify Every Plan:\n", "     - Extract details such as number of units produced, volume of goods manufactured, or any other production quantity disclosed on a per-quarter basis.\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"production_output\"`: A list of dictionaries, each capturing actual production figures or details.\n", "- `\"summary\"`: A concise narrative summary of the retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": <Name of the company>,\n", "  \"production_output\": [\n", "    {{\n", "      \"output_details\": \"<Details of actual production output per quarter>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name where the information is found.>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "    ...\n", "  ],\n", "\"summary\": \"<Concise summary of the production output information retrieved.>\"\n", "}}\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 41, "id": "a4c712ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is WORTHINGTON ENTERPRISES. You will be provided the URLs to access 10-K and 10-Q reports of the WORTHINGTON ENTERPRISES, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding actual production output (units or volumes produced) per quarter of the WORTHINGTON ENTERPRISES in the United States as per the 10-K and 10-Q reports of the WORTHINGTON ENTERPRISES and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Actual Production Output:\n", "     - Locate quarterly production output metrics in the United States, disclosed in the 10-K and 10-Q reports\n", "b. Quantify Every Plan:\n", "     - Extract details such as number of units produced, volume of goods manufactured, or any other production quantity disclosed on a per-quarter basis.\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"production_output\"`: A list of dictionaries, each capturing actual production figures or details.\n", "- `\"summary\"`: A concise narrative summary of the retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{\n", "  \"company\": <Name of the company>,\n", "  \"production_output\": [\n", "    {\n", "      \"output_details\": \"<Details of actual production output per quarter>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name where the information is found.>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    }\n", "    ...\n", "  ],\n", "\"summary\": \"<Concise summary of the production output information retrieved.>\"\n", "}\n", "```\n", "\n"]}], "source": ["WOR_Prompt_prod = sec_prod_output_retrieval_prompt.format(company_name = \"WORTHINGTON ENTERPRISES\")\n", "print(WOR_Prompt_prod)"]}, {"cell_type": "code", "execution_count": 50, "id": "9559f593", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=WOR_Prompt_prod,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 51, "id": "7b5c635e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"WORTHINGTON ENTERPRISES\",\n", "  \"production_output\": [\n", "    {\n", "      \"output_details\": \"For the Consumer Products segment, the production output was 17,211,048 units. This segment's facilities are located in the United States. For the Building Products segment, the production output was 3,745,885 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only.\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2024-04-03\",\n", "        \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "      },\n", "      \"period\": \"Q3 Fiscal Year 2024 (December 1, 2023 - February 29, 2024)\"\n", "    },\n", "    {\n", "      \"output_details\": \"For the Consumer Products segment, the production output was 17,360,350 units. This segment's facilities are located in the United States. For the Building Products segment, the production output was 2,718,325 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only.\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2024-07-30\",\n", "        \"section\": \"Item 7. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume (derived from annual and 9-month figures)\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "      },\n", "      \"period\": \"Q4 Fiscal Year 2024 (March 1, 2024 - May 31, 2024)\"\n", "    },\n", "    {\n", "      \"output_details\": \"For the Consumer Products segment, the production output was 16,170,556 units. This segment's facilities are located in the United States. For the Building Products segment, the production output was 3,094,117 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only.\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2024-10-09\",\n", "        \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "      },\n", "      \"period\": \"Q1 Fiscal Year 2025 (June 1, 2024 - August 31, 2024)\"\n", "    },\n", "    {\n", "      \"output_details\": \"For the Consumer Products segment, the production output was 16,170,556 units. This segment's facilities are located in the United States. (Note: This figure is derived from the six-month cumulative data and the first quarter data, and represents the same volume as the prior quarter, as presented in the company's filings). For the Building Products segment, the production output was 3,094,117 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only. (Note: This figure is derived from the six-month cumulative data and the first quarter data, and represents the same volume as the prior quarter, as presented in the company's filings).\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2025-01-06\",\n", "        \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume (derived from six-month and three-month figures)\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "      },\n", "      \"period\": \"Q2 Fiscal Year 2025 (September 1, 2024 - November 30, 2024)\"\n", "    }\n", "  ],\n", "  \"summary\": \"WORTHINGTON ENTERPRISES reports production output in units for its Consumer Products and Building Products segments. All facilities for the Consumer Products segment are located in the United States, allowing for direct reporting of U.S. production. [1] For the Building Products segment, the reported unit volumes include production from facilities in both the United States and Europe (Norway and Portugal), and the SEC filings do not provide a geographical breakdown to isolate U.S. production specifically. [1] The 'Other' segment, which included Sustainable Energy Solutions and was deconsolidated on May 29, 2024, is primarily based in Europe and therefore its production is not considered U.S. output. [1] \\n\\nQuarterly production output for the relevant period (January 1, 2024, to the present) is as follows:\\n\\nFor Q3 Fiscal Year 2024 (December 1, 2023 - February 29, 2024), Consumer Products reported 17,211,048 units and Building Products reported 3,745,885 units. [5]\\nFor Q4 Fiscal Year 2024 (March 1, 2024 - May 31, 2024), Consumer Products reported 17,360,350 units, and Building Products reported 2,718,325 units. [1, 5]\\nFor Q1 Fiscal Year 2025 (June 1, 2024 - August 31, 2024), Consumer Products reported 16,170,556 units, and Building Products reported 3,094,117 units. [4]\\nFor Q2 Fiscal Year 2025 (September 1, 2024 - November 30, 2024), Consumer Products reported 16,170,556 units, and Building Products reported 3,094,117 units. [3, 4] It is noteworthy that the reported production volumes for Consumer Products and Building Products in Q1 and Q2 of Fiscal Year 2025 are identical based on the company's filings, which may indicate a data presentation anomaly within the reports. [3, 4]\\n\\nProduction output for Q3 Fiscal Year 2025 (December 1, 2024 - February 28, 2025) could not be retrieved as the corresponding 10-Q report URL was inaccessible. [2]\"\n", "}\n", "```\n"]}], "source": ["raw_text = response.candidates[0].content.parts[0].text\n", "\n", "print(raw_text)"]}, {"cell_type": "code", "execution_count": 52, "id": "a60fb6ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"WORTHINGTON ENTERPRISES\",\n", "    \"production_output\": [\n", "        {\n", "            \"output_details\": \"For the Consumer Products segment, the production output was 17,211,048 units. This segment's facilities are located in the United States. For the Building Products segment, the production output was 3,745,885 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-04-03\",\n", "                \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "            },\n", "            \"period\": \"Q3 Fiscal Year 2024 (December 1, 2023 - February 29, 2024)\"\n", "        },\n", "        {\n", "            \"output_details\": \"For the Consumer Products segment, the production output was 17,360,350 units. This segment's facilities are located in the United States. For the Building Products segment, the production output was 2,718,325 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-07-30\",\n", "                \"section\": \"Item 7. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume (derived from annual and 9-month figures)\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "            },\n", "            \"period\": \"Q4 Fiscal Year 2024 (March 1, 2024 - May 31, 2024)\"\n", "        },\n", "        {\n", "            \"output_details\": \"For the Consumer Products segment, the production output was 16,170,556 units. This segment's facilities are located in the United States. For the Building Products segment, the production output was 3,094,117 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-10-09\",\n", "                \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "            },\n", "            \"period\": \"Q1 Fiscal Year 2025 (June 1, 2024 - August 31, 2024)\"\n", "        },\n", "        {\n", "            \"output_details\": \"For the Consumer Products segment, the production output was 16,170,556 units. This segment's facilities are located in the United States. (Note: This figure is derived from the six-month cumulative data and the first quarter data, and represents the same volume as the prior quarter, as presented in the company's filings). For the Building Products segment, the production output was 3,094,117 units. This segment includes facilities in both the United States and Europe, and the reported units are not disaggregated by geography in the filing to isolate U.S. production only. (Note: This figure is derived from the six-month cumulative data and the first quarter data, and represents the same volume as the prior quarter, as presented in the company's filings).\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2025-01-06\",\n", "                \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations, Net Sales and Volume (derived from six-month and three-month figures)\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "            },\n", "            \"period\": \"Q2 Fiscal Year 2025 (September 1, 2024 - November 30, 2024)\"\n", "        }\n", "    ],\n", "    \"summary\": \"WORTHINGTON ENTERPRISES reports production output in units for its Consumer Products and Building Products segments. All facilities for the Consumer Products segment are located in the United States, allowing for direct reporting of U.S. production. [1] For the Building Products segment, the reported unit volumes include production from facilities in both the United States and Europe (Norway and Portugal), and the SEC filings do not provide a geographical breakdown to isolate U.S. production specifically. [1] The 'Other' segment, which included Sustainable Energy Solutions and was deconsolidated on May 29, 2024, is primarily based in Europe and therefore its production is not considered U.S. output. [1] \\n\\nQuarterly production output for the relevant period (January 1, 2024, to the present) is as follows:\\n\\nFor Q3 Fiscal Year 2024 (December 1, 2023 - February 29, 2024), Consumer Products reported 17,211,048 units and Building Products reported 3,745,885 units. [5]\\nFor Q4 Fiscal Year 2024 (March 1, 2024 - May 31, 2024), Consumer Products reported 17,360,350 units, and Building Products reported 2,718,325 units. [1, 5]\\nFor Q1 Fiscal Year 2025 (June 1, 2024 - August 31, 2024), Consumer Products reported 16,170,556 units, and Building Products reported 3,094,117 units. [4]\\nFor Q2 Fiscal Year 2025 (September 1, 2024 - November 30, 2024), Consumer Products reported 16,170,556 units, and Building Products reported 3,094,117 units. [3, 4] It is noteworthy that the reported production volumes for Consumer Products and Building Products in Q1 and Q2 of Fiscal Year 2025 are identical based on the company's filings, which may indicate a data presentation anomaly within the reports. [3, 4]\\n\\nProduction output for Q3 Fiscal Year 2025 (December 1, 2024 - February 28, 2025) could not be retrieved as the corresponding 10-Q report URL was inaccessible. [2]\"\n", "}\n"]}], "source": ["import re\n", "cleaned_json_str = re.sub(r\"^```json\\s*|\\s*```$\", \"\", raw_text.strip())\n", "parsed_data = json.loads(cleaned_json_str)\n", "print(json.dumps(parsed_data, indent=4))"]}, {"cell_type": "markdown", "id": "288037c3", "metadata": {}, "source": ["## (BLFS) BioLife Solutions, Inc"]}, {"cell_type": "code", "execution_count": 56, "id": "34bc5f28", "metadata": {}, "outputs": [], "source": ["result_10k = get_filing_info(file_type=\"10-K\", stock_symbol=\"BLFS\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-30\"})\n", "result_10q = get_filing_info(file_type=\"10-Q\", stock_symbol=\"BLFS\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-30\"})\n"]}, {"cell_type": "code", "execution_count": 84, "id": "dd53ba3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2024-12-31'"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["result_10k[0].get(SecEdgerImportantInfo.FISCAL_DATE_ENDING)"]}, {"cell_type": "code", "execution_count": 57, "id": "7c10cf1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"companyName\": \"BIOLIFE SOLUTIONS INC\",\n", "        \"formType\": \"10-K/A\",\n", "        \"periodOfReport\": \"2024-12-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828025017079/blfs-20241231.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"BIOLIFE SOLUTIONS INC\",\n", "        \"formType\": \"10-K\",\n", "        \"periodOfReport\": \"2024-12-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828025009511/blfs-20241231.htm\"\n", "    }\n", "]\n", "**************************************************\n", "[\n", "    {\n", "        \"companyName\": \"BIOLIFE SOLUTIONS INC\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2025-03-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828025023833/blfs-20250331.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"BIOLIFE SOLUTIONS INC\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-09-30\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828024047178/blfs-20240930.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"BIOLIFE SOLUTIONS INC\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-06-30\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828024036470/blfs-20240630.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"BIOLIFE SOLUTIONS INC\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-03-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828024022690/blfs-20240331.htm\"\n", "    }\n", "]\n", "**************************************************\n"]}], "source": ["print(json.dumps(result_10k, indent=4))\n", "print(\"*\"*50)\n", "print(json.dumps(result_10q, indent=4))\n", "print(\"*\"*50)"]}, {"cell_type": "markdown", "id": "a108703c", "metadata": {}, "source": ["### Capacity Expansion"]}, {"cell_type": "code", "execution_count": 60, "id": "fd1a025e", "metadata": {}, "outputs": [], "source": ["sec_blfs_capacity_exp_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding manufacturing capacity expansion or new plant openings in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Capacity-Expansion Plans: \n", "     - Locate statements or paragraphs in the SEC filings describing intentions, plans, or commitments to:\n", "     - Expand existing factories in the United States\n", "     - Build or commission new manufacturing facilities in the United States.\n", "b. Quantify Every Plan:\n", "     - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the SEC filings. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/834365/000162828025009511/blfs-20241231.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828025023833/blfs-20250331.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024047178/blfs-20240930.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024036470/blfs-20240630.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024022690/blfs-20240331.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"capacity_expansion_plans\"`: A list of dictionaries, each capturing one U.S.-based manufacturing capacity expansion initiative.\n", "- `\"summary\"`: A concise narrative summary of findings across all categories.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": \"<Name of the company>\",\n", "  \"capacity_expansion_plans\": [\n", "    {{\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved capacity expansion plans>\"\n", "}}\n", "```\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 61, "id": "1ad47215", "metadata": {}, "outputs": [], "source": ["BLFS_cap_Prompt = sec_blfs_capacity_exp_retrieval_prompt.format(company_name = \"BIOLIFE SOLUTIONS\")\n"]}, {"cell_type": "code", "execution_count": 62, "id": "20be3f39", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=BLFS_cap_Prompt,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 63, "id": "afaf088b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"BIOLIFE SOLUTIONS\",\n", "  \"capacity_expansion_plans\": [\n", "    {\n", "      \"plan_details\": \"Ongoing capital expenditures on property and equipment, categorized as 'Construction in-progress.' While not explicitly detailed as new manufacturing plants or specific square footage expansions of manufacturing facilities, these investments typically include facility upgrades or expansions to support company operations, which may encompass manufacturing sites in the United States. The company operates manufacturing facilities in Bothell, Washington; Indianapolis, Indiana; and Bruce Township, Michigan.\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2025-03-03\",\n", "        \"section\": \"ITEM 8. CONSOLIDATED FINANCIAL STATEMENTS AND SUPPLEMENTARY DATA, Note 9. Property and equipment\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828025009511/blfs-20241231.htm\"\n", "      }\n", "    },\n", "    {\n", "      \"plan_details\": \"Investment in 'Shippers and related components in production' for evo cold chain management hardware products. This indicates an increase in assets related to the production of these components, which are manufactured in the Bruce Township, Michigan facility. This reflects an investment in production capabilities for this specific product line.\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2024-11-12\",\n", "        \"section\": \"PART I. FINANCIAL INFORMATION, Item 1. Unaudited Condensed Consolidated Financial Statements, Note 8. Assets held for rent\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828024047178/blfs-20240930.htm\"\n", "      }\n", "    }\n", "  ],\n", "  \"summary\": \"BIOLIFE SOLUTIONS' SEC filings from January 1, 2024, to the present indicate general capital investments in property and equipment through 'Construction in-progress,' amounting to $2,478 thousand as of December 31, 2024. Additionally, there was an increase in 'Shippers and related components in production' from $2,651 thousand as of December 31, 2023, to $3,235 thousand as of September 30, 2024, reflecting an investment in production assets for their evo cold chain management hardware products. While these figures represent capital allocated to physical assets and production capabilities in the United States, the filings do not contain explicit, detailed plans for new plant openings or manufacturing capacity expansion with specific square footage or dedicated capital expenditure figures specifically identified for such initiatives beyond these general investment categories.\"\n", "}\n", "```\n"]}], "source": ["raw_text = response.candidates[0].content.parts[0].text\n", "\n", "print(raw_text)"]}, {"cell_type": "markdown", "id": "1551ee9d", "metadata": {}, "source": ["### Emp generation"]}, {"cell_type": "code", "execution_count": 64, "id": "c2b6b025", "metadata": {}, "outputs": [], "source": ["sec_blfs_employment_gen_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding employment generation or hiring commitments of the {company_name} in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Employment-Generation Plans:  \n", "     - Locate any statements in the SEC filings describing hiring targets, job-creation commitments, or intended workforce increases in the United States.\n", "b. Quantify Every Plan:\n", "     - Quantify by citing numeric metrics if available in the 10-K and 10-Q reports.(e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/834365/000162828025009511/blfs-20241231.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828025023833/blfs-20250331.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024047178/blfs-20240930.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024036470/blfs-20240630.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024022690/blfs-20240331.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"employment_generation_plans\"`: A list of dictionaries, each capturing one hiring or job creation plan.\n", "- `\"summary\"`: A concise narrative summary of retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": <Name of the company>,\n", "  \"employment_generation_plans\": [\n", "    {{\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved employment generation plans.>\"\n", "}}\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 66, "id": "ced8db31", "metadata": {}, "outputs": [], "source": ["BLFS_emp_Prompt = sec_blfs_employment_gen_retrieval_prompt.format(company_name = \"BIOLIFE SOLUTIONS\")\n"]}, {"cell_type": "code", "execution_count": 71, "id": "73d30c42", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=BLFS_emp_Prompt,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 72, "id": "ccfff8ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"BIOLIFE SOLUTIONS\",\n", "  \"employment_generation_plans\": [],\n", "  \"summary\": \"Based on the review of the provided 10-K report for the year ended December 31, 2024, and the 10-Q report for the quarter ended September 30, 2024, BioLife Solutions, Inc. does not explicitly detail or quantify specific employment generation plans or hiring commitments in the United States with numeric targets for increasing its workforce. The company's human capital strategy focuses on retaining existing talent and attracting key personnel due to competition in the industry, as indicated in its risk factors and human capital disclosures. For example, the company notes that its success depends on its ability to attract and retain essential engineering, scientific, sales, and management personnel, and it is continually at risk of being unable to hire additional such personnel. [1] \\n\\nConversely, the reports mention workforce reductions. Specifically, BioLife Solutions implemented a Reduction in Force (RIF) related to the divestiture of its Global Cooling business, which reduced the company's workforce by 47 employees in April 2024. [1, 3] As of December 31, 2024, the company had 159 full-time team members. [1]\"\n", "}\n", "```\n"]}], "source": ["raw_text = response.candidates[0].content.parts[0].text\n", "\n", "print(raw_text)"]}, {"cell_type": "markdown", "id": "a9a05471", "metadata": {}, "source": ["### prod ouptput"]}, {"cell_type": "code", "execution_count": 73, "id": "3e17eb00", "metadata": {}, "outputs": [], "source": ["sec__blfs_prod_output_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding actual production output (units or volumes produced) per quarter of the {company_name} in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Actual Production Output:\n", "     - Locate quarterly production output metrics in the United States, disclosed in the 10-K and 10-Q reports\n", "b. Quantify Every Plan:\n", "     - Extract details such as number of units produced, volume of goods manufactured, or any other production quantity disclosed on a per-quarter basis.\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/834365/000162828025009511/blfs-20241231.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828025023833/blfs-20250331.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024047178/blfs-20240930.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024036470/blfs-20240630.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/834365/000162828024022690/blfs-20240331.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"production_output\"`: A list of dictionaries, each capturing actual production figures or details.\n", "- `\"summary\"`: A concise narrative summary of the retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": <Name of the company>,\n", "  \"production_output\": [\n", "    {{\n", "      \"output_details\": \"<Details of actual production output per quarter>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name where the information is found.>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "    ...\n", "  ],\n", "\"summary\": \"<Concise summary of the production output information retrieved.>\"\n", "}}\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 74, "id": "6901086e", "metadata": {}, "outputs": [], "source": ["BLFS_prod_Prompt = sec__blfs_prod_output_retrieval_prompt.format(company_name = \"BIOLIFE SOLUTIONS\")\n"]}, {"cell_type": "code", "execution_count": 82, "id": "832579e1", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=BLFS_prod_Prompt,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 83, "id": "1a523b85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"BIOLIFE SOLUTIONS\",\n", "  \"production_output\": [\n", "    {\n", "      \"output_details\": \"Total revenue from customers in the United States for the full year ended December 31, 2024, was approximately $61,690.5 thousand (75% of total revenue). [1]\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2025-02-24\",\n", "        \"section\": \"ITEM 1. BUSINESS - Sales and marketing, ITEM 7. MANAGEMENT'S DISCUSSION AND ANALYSIS OF FINANCIAL CONDITION AND RESULTS OF OPERATIONS - Revenue\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828025009511/blfs-20241231.htm\"\n", "      }\n", "    },\n", "    {\n", "      \"output_details\": \"Total revenue from customers in the United States for the nine months ended September 30, 2024, was approximately $65,972.06 thousand (77% of total revenue). [3]\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2024-11-05\",\n", "        \"section\": \"PART I. FINANCIAL INFORMATION - Item 1. Unaudited Condensed Consolidated Financial Statements, Notes to Unaudited Condensed Consolidated Financial Statements - Concentrations of credit risk and business risk\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828024047178/blfs-20240930.htm\"\n", "      }\n", "    },\n", "    {\n", "      \"output_details\": \"Total revenue from customers in the United States for the three months ended September 30, 2024, was approximately $23,234.00 thousand (76% of total revenue). [3]\",\n", "      \"source\": {\n", "        \"publishing_date\": \"2024-11-05\",\n", "        \"section\": \"PART I. FINANCIAL INFORMATION - Item 1. Unaudited Condensed Consolidated Financial Statements, Notes to Unaudited Condensed Consolidated Financial Statements - Concentrations of credit risk and business risk\",\n", "        \"url\": \"https://www.sec.gov/Archives/edgar/data/834365/000162828024047178/blfs-20240930.htm\"\n", "      }\n", "    }\n", "  ],\n", "  \"summary\": \"BIOLIFE SOLUTIONS' SEC filings (10-K for the year ended December 31, 2024, and 10-Q for the quarter ended September 30, 2024) do not disclose actual production output in terms of units or volumes produced per quarter in the United States. The company provides revenue figures by geographic area, which serve as the closest available quantitative information related to their output in the specified region. For the full year ended December 31, 2024, total revenue from customers in the United States was approximately $61,690.5 thousand. [1] For the nine months ended September 30, 2024, U.S. revenue was about $65,972.06 thousand. [3] Specifically for the third quarter of 2024 (three months ended September 30, 2024), U.S. revenue amounted to approximately $23,234.00 thousand. [3] The 10-Q reports for the first and second quarters of 2024 were inaccessible, thus preventing the extraction of direct quarterly U.S. revenue data for those periods.\"\n", "}\n", "```\n"]}], "source": ["raw_text = response.candidates[0].content.parts[0].text\n", "\n", "print(raw_text)"]}, {"cell_type": "markdown", "id": "12492e9b", "metadata": {}, "source": ["# ENERPAC TOOL GROUP CORP"]}, {"cell_type": "code", "execution_count": 87, "id": "d425da10", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'companyName': 'ENERPAC TOOL GROUP CORP',\n", "  'formType': '10-K',\n", "  'periodOfReport': '2024-08-31',\n", "  'linkToFilingDetails': 'https://www.sec.gov/Archives/edgar/data/6955/000000695524000046/epac-20240831.htm'}]"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["get_filing_info(file_type=\"10-K\", stock_symbol=\"EPAC\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-29\"})\n"]}, {"cell_type": "markdown", "id": "4440df9a", "metadata": {}, "source": ["## Capacity Expansion"]}, {"cell_type": "code", "execution_count": 88, "id": "58591cf2", "metadata": {}, "outputs": [], "source": ["sec_epac_capacity_exp_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding manufacturing capacity expansion or new plant openings in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Capacity-Expansion Plans: \n", "     - Locate statements or paragraphs in the SEC filings describing intentions, plans, or commitments to:\n", "     - Expand existing factories in the United States\n", "     - Build or commission new manufacturing facilities in the United States.\n", "b. Quantify Every Plan:\n", "     - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the SEC filings. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000046/epac-20240831.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695525000008/epac-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000042/epac-20240531.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000037/epac-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"capacity_expansion_plans\"`: A list of dictionaries, each capturing one U.S.-based manufacturing capacity expansion initiative.\n", "- `\"summary\"`: A concise narrative summary of findings across all categories.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": \"<Name of the company>\",\n", "  \"capacity_expansion_plans\": [\n", "    {{\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved capacity expansion plans>\"\n", "}}\n", "```\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 89, "id": "50128132", "metadata": {}, "outputs": [], "source": ["epac_cap_Prompt = sec_epac_capacity_exp_retrieval_prompt.format(company_name = \"ENERPAC TOOL GROUP CORP\")\n"]}, {"cell_type": "code", "execution_count": 97, "id": "d20960ac", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=epac_cap_Prompt,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 98, "id": "52a43a53", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"ENERPAC TOOL GROUP CORP.\",\n", "  \"capacity_expansion_plans\": [],\n", "  \"summary\": \"ENERPAC TOOL GROUP CORP.'s SEC filings from January 1, 2024, to the present do not contain specific information regarding new manufacturing capacity expansions or new plant openings in the United States. The company's capital expenditure activities during this period primarily include the build-out costs associated with the relocation of its corporate headquarters to a new downtown Milwaukee, Wisconsin location, with an anticipated move-in date in fiscal 2025. [1, 3] Additionally, in September 2024, Enerpac Tool Group Corp. acquired DTA The Smart Move, S.A., a global leader in industrial heavy loads transportation, which is intended to complement Enerpac's Heavy Lifting Technology product line. [3]\"\n", "}\n", "```\n"]}], "source": ["raw_text = response.candidates[0].content.parts[0].text\n", "\n", "print(raw_text)"]}, {"cell_type": "markdown", "id": "384fe281", "metadata": {}, "source": ["## Emp generation"]}, {"cell_type": "code", "execution_count": 102, "id": "2732724d", "metadata": {}, "outputs": [], "source": ["sec_epac_employment_gen_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding employment generation or hiring commitments of the {company_name} in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Employment-Generation Plans:  \n", "     - Locate any statements in the SEC filings describing hiring targets, job-creation commitments, or intended workforce increases in the United States.\n", "b. Quantify Every Plan:\n", "     - Quantify by citing numeric metrics if available in the 10-K and 10-Q reports.(e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000046/epac-20240831.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695525000008/epac-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000042/epac-20240531.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000037/epac-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"employment_generation_plans\"`: A list of dictionaries, each capturing one hiring or job creation plan.\n", "- `\"summary\"`: A concise narrative summary of retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": <Name of the company>,\n", "  \"employment_generation_plans\": [\n", "    {{\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "   ...\n", "  ],\n", "\"summary\": \"<Concise summary of the retrieved employment generation plans.>\"\n", "}}\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 103, "id": "320b14e4", "metadata": {}, "outputs": [], "source": ["epac_emp_Prompt = sec_epac_employment_gen_retrieval_prompt.format(company_name = \"ENERPAC TOOL GROUP CORP\")\n"]}, {"cell_type": "code", "execution_count": 105, "id": "6c927cbb", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=epac_emp_Prompt,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 106, "id": "3482de6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"ENERPAC TOOL GROUP CORP.\",\n", "  \"employment_generation_plans\": [],\n", "  \"summary\": \"Based on the review of ENERPAC TOOL GROUP CORP.'s 10-K report for the fiscal year ended August 31, 2024, and the 10-Q report for the quarter ended November 30, 2024, no explicit forward-looking employment generation plans or specific hiring commitments with quantifiable metrics for the United States were identified. The reports discuss the company's human capital management strategy, which focuses on attracting, developing, and retaining qualified employees, and mention the company's total workforce of approximately 2,000 employees globally as of August 31, 2024. The ASCEND transformation program is noted as a key initiative aimed at improving operational excellence and driving efficiency, which has involved restructuring and severance costs, indicating workforce adjustments rather than expansion. The company's headquarters relocation to Milwaukee, Wisconsin, in fiscal 2025, is mentioned as a move of existing operations rather than a specific job creation initiative. [1, 3]\"\n", "}\n", "```\n"]}], "source": ["raw_text = response.candidates[0].content.parts[0].text\n", "\n", "print(raw_text)"]}, {"cell_type": "markdown", "id": "eb7a9c93", "metadata": {}, "source": ["## Prod output"]}, {"cell_type": "code", "execution_count": 123, "id": "d039bbb3", "metadata": {}, "outputs": [], "source": ["sec_epac_prod_output_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You will be provided the URLs to access 10-K and 10-Q reports of the {company_name}, all reports are from the period January 1, 2024 to the present. You need to extract all the information regarding actual production output (units or volumes produced) per quarter of the {company_name} in the United States as per the 10-K and 10-Q reports of the {company_name} and return the output in defined JSON format.\n", "\n", "while retrieving the information follow the below guidelines:\n", "a. Identify Actual Production Output:\n", "     - Locate quarterly production output metrics in the United States, disclosed in the 10-K and 10-Q reports\n", "b. Quantify Every Plan:\n", "     - Extract details such as number of units produced, volume of goods manufactured, or any other production quantity disclosed on a per-quarter basis.\n", "\n", "SEC report URLs:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000046/epac-20240831.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695525000008/epac-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000042/epac-20240531.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000037/epac-20240229.htm\"\n", "]\n", "\n", "Output Structure:\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"production_output\"`: A list of dictionaries, each capturing actual production figures or details.\n", "- `\"summary\"`: A concise narrative summary of the retrieved information.\n", "\n", "JSON schema format for the output generation is given below:\n", "```json\n", "{{\n", "  \"company\": <Name of the company>,\n", "  \"production_output\": [\n", "    {{\n", "      \"output_details\": \"<Details of actual production output per quarter>\",\n", "      \"source\": {{\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name where the information is found.>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }}\n", "    }}\n", "    ...\n", "  ],\n", "\"summary\": \"<Concise summary of the production output information retrieved.>\"\n", "}}\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 124, "id": "68ddd1eb", "metadata": {}, "outputs": [], "source": ["epac_prod_Prompt = sec_epac_prod_output_retrieval_prompt.format(company_name = \"ENERPAC TOOL GROUP CORP\")\n"]}, {"cell_type": "code", "execution_count": 127, "id": "7ca759dc", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=epac_prod_Prompt,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 128, "id": "3122fecb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"company\": \"ENERPAC TOOL GROUP CORP.\",\n", "  \"production_output\": [],\n", "  \"summary\": \"The SEC filings for ENERPAC TOOL GROUP CORP. (10-K report for the fiscal year ended August 31, 2024, and 10-Q report for the quarter ended November 30, 2024) do not provide specific details regarding actual production output in terms of units or volumes produced per quarter in the United States. The reports primarily focus on financial performance metrics, such as net sales by product line (Product and Service & Rental) and by geographic region, as well as operational efficiencies and strategic initiatives, but do not disclose quantitative production output data.\"\n", "}\n", "```\n"]}], "source": ["raw_text = response.candidates[0].content.parts[0].text\n", "\n", "print(raw_text)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}