{"cells": [{"cell_type": "markdown", "id": "c9914f9d", "metadata": {}, "source": ["## K means clustering"]}, {"cell_type": "markdown", "id": "a91324cc", "metadata": {}, "source": ["### load data"]}, {"cell_type": "code", "execution_count": 17, "id": "c4dff0f6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "37f65fe1", "metadata": {}, "outputs": [], "source": ["us_data_df = pd.read_excel(\"US_Data_Matched_new.xlsx\")"]}, {"cell_type": "code", "execution_count": 4, "id": "557c4278", "metadata": {}, "outputs": [{"data": {"text/plain": ["(809, 46)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["us_data_df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "b6c67118", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Fsym_ID</th>\n", "      <th>Fsym_Security_ID</th>\n", "      <th>Ticker</th>\n", "      <th>ISIN</th>\n", "      <th>SEDOL</th>\n", "      <th>Entity_ID</th>\n", "      <th>Entity_Name</th>\n", "      <th>Security_Name</th>\n", "      <th>Exchange_Code</th>\n", "      <th>Exchange_Name</th>\n", "      <th>...</th>\n", "      <th>Last_Trade_Date</th>\n", "      <th>ADTV_6M</th>\n", "      <th>ADTV_3M</th>\n", "      <th>Security_Mcap_USD</th>\n", "      <th>Business Description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Combined_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MH33D6-R</td>\n", "      <td>R85KLC-S</td>\n", "      <td>AAPL-US</td>\n", "      <td>US0378331005</td>\n", "      <td>2046251</td>\n", "      <td>000C7F-E</td>\n", "      <td>Apple, Inc.</td>\n", "      <td>Apple Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>11780.832445</td>\n", "      <td>12683.983288</td>\n", "      <td>3.207062e+06</td>\n", "      <td>Apple Inc. designs, manufactures and markets s...</td>\n", "      <td>0.5</td>\n", "      <td>0.02</td>\n", "      <td>0.55</td>\n", "      <td>0.22</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>K7TPSX-R</td>\n", "      <td>QDYJZC-S</td>\n", "      <td>NVDA-US</td>\n", "      <td>US67066G1040</td>\n", "      <td>2379504</td>\n", "      <td>00208X-E</td>\n", "      <td>NVIDIA Corp.</td>\n", "      <td>NVIDIA Corporation</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>33115.052807</td>\n", "      <td>34618.656093</td>\n", "      <td>2.968748e+06</td>\n", "      <td>NVIDIA Corporation is a full-stack computing i...</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>B81TLL-R</td>\n", "      <td>MDX5ZL-S</td>\n", "      <td>AVGO-US</td>\n", "      <td>US11135F1012</td>\n", "      <td>BDZ78H9</td>\n", "      <td>0JCLYY-E</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>5828.494482</td>\n", "      <td>7560.204036</td>\n", "      <td>9.194190e+05</td>\n", "      <td>Broadcom Inc. is a global technology firm that...</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>WWDPYB-S</td>\n", "      <td>TSLA-US</td>\n", "      <td>US88160R1014</td>\n", "      <td>B616C79</td>\n", "      <td>006XY7-E</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>29106.313789</td>\n", "      <td>32312.138650</td>\n", "      <td>8.040649e+05</td>\n", "      <td>Tesla, Inc. designs, develops, manufactures, s...</td>\n", "      <td>0.3</td>\n", "      <td>0.58</td>\n", "      <td>1.00</td>\n", "      <td>0.64</td>\n", "      <td>0.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>B19ST9-R</td>\n", "      <td>W38FV3-S</td>\n", "      <td>LLY-US</td>\n", "      <td>US5324571083</td>\n", "      <td>2516152</td>\n", "      <td>000P56-E</td>\n", "      <td>Eli Lilly &amp; Co.</td>\n", "      <td>Eli Lilly and Company</td>\n", "      <td>NYS</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>3030.989148</td>\n", "      <td>3017.401099</td>\n", "      <td>7.711702e+05</td>\n", "      <td>Eli Lilly and Company is a medicine company. T...</td>\n", "      <td>0.5</td>\n", "      <td>0.28</td>\n", "      <td>0.85</td>\n", "      <td>0.62</td>\n", "      <td>0.67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 46 columns</p>\n", "</div>"], "text/plain": ["    Fsym_ID Fsym_Security_ID   Ticker          ISIN    SEDOL Entity_ID  \\\n", "0  MH33D6-R         R85KLC-S  AAPL-US  US0378331005  2046251  000C7F-E   \n", "1  K7TPSX-R         QDYJZC-S  NVDA-US  US67066G1040  2379504  00208X-E   \n", "2  B81TLL-R         MDX5ZL-S  AVGO-US  US11135F1012  BDZ78H9  0JCLYY-E   \n", "3  Q2YN1N-R         WWDPYB-S  TSLA-US  US88160R1014  B616C79  006XY7-E   \n", "4  B19ST9-R         W38FV3-S   LLY-US  US5324571083  2516152  000P56-E   \n", "\n", "       Entity_Name          Security_Name Exchange_Code  \\\n", "0      Apple, Inc.             Apple Inc.           NAS   \n", "1     NVIDIA Corp.     NVIDIA Corporation           NAS   \n", "2    Broadcom Inc.          Broadcom Inc.           NAS   \n", "3      Tesla, Inc.            Tesla, Inc.           NAS   \n", "4  Eli Lilly & Co.  Eli Lilly and Company           NYS   \n", "\n", "             Exchange_Name  ... Last_Trade_Date       ADTV_6M       ADTV_3M  \\\n", "0                   NASDAQ  ...      2025-03-20  11780.832445  12683.983288   \n", "1                   NASDAQ  ...      2025-03-20  33115.052807  34618.656093   \n", "2                   NASDAQ  ...      2025-03-20   5828.494482   7560.204036   \n", "3                   NASDAQ  ...      2025-03-20  29106.313789  32312.138650   \n", "4  New York Stock Exchange  ...      2025-03-20   3030.989148   3017.401099   \n", "\n", "  Security_Mcap_USD                               Business Description  \\\n", "0      3.207062e+06  Apple Inc. designs, manufactures and markets s...   \n", "1      2.968748e+06  NVIDIA Corporation is a full-stack computing i...   \n", "2      9.194190e+05  Broadcom Inc. is a global technology firm that...   \n", "3      8.040649e+05  Tesla, Inc. designs, develops, manufactures, s...   \n", "4      7.711702e+05  Eli Lilly and Company is a medicine company. T...   \n", "\n", "  Fixed_Assets_Score Economic_Heft_Score  Import_Intensity_Score  \\\n", "0                0.5                0.02                    0.55   \n", "1                0.5                0.11                    0.53   \n", "2                0.5                0.11                    0.53   \n", "3                0.3                0.58                    1.00   \n", "4                0.5                0.28                    0.85   \n", "\n", "  Employment_Score Combined_Score  \n", "0             0.22           0.40  \n", "1             0.62           0.48  \n", "2             0.62           0.48  \n", "3             0.64           0.76  \n", "4             0.62           0.67  \n", "\n", "[5 rows x 46 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["us_data_df.head()"]}, {"cell_type": "markdown", "id": "2b0a0006", "metadata": {}, "source": ["### Filtering Medical specialities companies"]}, {"cell_type": "code", "execution_count": 6, "id": "bcc344d9", "metadata": {}, "outputs": [], "source": ["medical_specialties_df = us_data_df[us_data_df['Factset_Industry'] == 'Medical Specialties'][['Entity_Name', 'Company_Mcap_USD']]"]}, {"cell_type": "code", "execution_count": 7, "id": "b538dc24", "metadata": {}, "outputs": [{"data": {"text/plain": ["(83, 2)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_specialties_df.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "20328376", "metadata": {}, "outputs": [], "source": ["medical_specialties_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": 10, "id": "bd288c16", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Abbott Laboratories</td>\n", "      <td>219756.111897</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Thermo Fisher Scientific, Inc.</td>\n", "      <td>196949.204829</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Intuitive Surgical, Inc.</td>\n", "      <td>173562.531943</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Danaher Corp.</td>\n", "      <td>150617.968231</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Boston Scientific Corp.</td>\n", "      <td>143706.465119</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Entity_Name  Company_Mcap_USD\n", "0             Abbott Laboratories     219756.111897\n", "1  Thermo Fisher Scientific, Inc.     196949.204829\n", "2        Intuitive Surgical, Inc.     173562.531943\n", "3                   Danaher Corp.     150617.968231\n", "4         Boston Scientific Corp.     143706.465119"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_specialties_df.head()"]}, {"cell_type": "markdown", "id": "ff67e4ec", "metadata": {}, "source": ["### visulaizing raw market cap data"]}, {"cell_type": "code", "execution_count": 16, "id": "21f7aa1b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(medical_specialties_df['Company_Mcap_USD'], bins=20, kde=True)\n", "plt.title('Raw Market Cap Distribution')\n", "plt.xlabel('Market Cap (USD Millions)')\n", "plt.ylabel('Frequency')\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "ae1216f5", "metadata": {}, "source": ["Distribution is right skewed"]}, {"cell_type": "markdown", "id": "cd173a56", "metadata": {}, "source": ["### log transform to handle skewness"]}, {"cell_type": "code", "execution_count": 18, "id": "0e054beb", "metadata": {}, "outputs": [], "source": ["medical_specialties_df['LogMCap'] = np.log1p(medical_specialties_df['Company_Mcap_USD'])  # log1p(x) = log(1 + x)"]}, {"cell_type": "code", "execution_count": 19, "id": "6101fd97", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Abbott Laboratories</td>\n", "      <td>219756.111897</td>\n", "      <td>12.300278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Thermo Fisher Scientific, Inc.</td>\n", "      <td>196949.204829</td>\n", "      <td>12.190706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Intuitive Surgical, Inc.</td>\n", "      <td>173562.531943</td>\n", "      <td>12.064299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Danaher Corp.</td>\n", "      <td>150617.968231</td>\n", "      <td>11.922509</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Boston Scientific Corp.</td>\n", "      <td>143706.465119</td>\n", "      <td>11.875535</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Entity_Name  Company_Mcap_USD    LogMCap\n", "0             Abbott Laboratories     219756.111897  12.300278\n", "1  Thermo Fisher Scientific, Inc.     196949.204829  12.190706\n", "2        Intuitive Surgical, Inc.     173562.531943  12.064299\n", "3                   Danaher Corp.     150617.968231  11.922509\n", "4         Boston Scientific Corp.     143706.465119  11.875535"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_specialties_df.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "dbf02e02", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(medical_specialties_df['LogMCap'], bins=20, kde=True)\n", "plt.title('Log Market Cap Distribution')\n", "plt.xlabel('LogMcap')\n", "plt.ylabel('Frequency')\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "1da9aa9f", "metadata": {}, "source": ["### Apply K means clustering"]}, {"cell_type": "code", "execution_count": 22, "id": "d259403a", "metadata": {}, "outputs": [], "source": ["from sklearn.cluster import KMeans\n", "\n", "X = medical_specialties_df[['LogMCap']] # KMeans expects a 2D array\n", "\n", "kmeans = KMeans(n_clusters=3, random_state=42)\n", "medical_specialties_df['Cluster'] = kmeans.fit_predict(X)\n"]}, {"cell_type": "code", "execution_count": 33, "id": "9a54e6ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["kmeans.n_iter_\n"]}, {"cell_type": "code", "execution_count": 23, "id": "31351d74", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "      <th>Cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Abbott Laboratories</td>\n", "      <td>219756.111897</td>\n", "      <td>12.300278</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Thermo Fisher Scientific, Inc.</td>\n", "      <td>196949.204829</td>\n", "      <td>12.190706</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Intuitive Surgical, Inc.</td>\n", "      <td>173562.531943</td>\n", "      <td>12.064299</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Danaher Corp.</td>\n", "      <td>150617.968231</td>\n", "      <td>11.922509</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Boston Scientific Corp.</td>\n", "      <td>143706.465119</td>\n", "      <td>11.875535</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Entity_Name  Company_Mcap_USD    LogMCap  Cluster\n", "0             Abbott Laboratories     219756.111897  12.300278        2\n", "1  Thermo Fisher Scientific, Inc.     196949.204829  12.190706        2\n", "2        Intuitive Surgical, Inc.     173562.531943  12.064299        2\n", "3                   Danaher Corp.     150617.968231  11.922509        2\n", "4         Boston Scientific Corp.     143706.465119  11.875535        2"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_specialties_df.head()"]}, {"cell_type": "markdown", "id": "2d3a4951", "metadata": {}, "source": ["### Assigning cluster names"]}, {"cell_type": "code", "execution_count": 24, "id": "a6f702f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cluster\n", "1     1113.458981\n", "0     9575.723884\n", "2    94515.265799\n", "Name: Company_Mcap_USD, dtype: float64\n"]}], "source": ["cluster_avg = medical_specialties_df.groupby('Cluster')['Company_Mcap_USD'].mean().sort_values()\n", "print(cluster_avg)"]}, {"cell_type": "code", "execution_count": 25, "id": "20f82344", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1: 'Small', 0: 'Medium', 2: 'Large'}\n"]}], "source": ["cluster_to_size = {cluster: size for cluster, size in zip(cluster_avg.index, ['Small', 'Medium', 'Large'])}\n", "print(cluster_to_size)"]}, {"cell_type": "code", "execution_count": 26, "id": "3197c87e", "metadata": {}, "outputs": [], "source": ["medical_specialties_df['SizeCategory'] = medical_specialties_df['Cluster'].map(cluster_to_size)"]}, {"cell_type": "code", "execution_count": 27, "id": "8ffd66d4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "      <th>Cluster</th>\n", "      <th>SizeCategory</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Abbott Laboratories</td>\n", "      <td>219756.111897</td>\n", "      <td>12.300278</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Thermo Fisher Scientific, Inc.</td>\n", "      <td>196949.204829</td>\n", "      <td>12.190706</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Intuitive Surgical, Inc.</td>\n", "      <td>173562.531943</td>\n", "      <td>12.064299</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Danaher Corp.</td>\n", "      <td>150617.968231</td>\n", "      <td>11.922509</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Boston Scientific Corp.</td>\n", "      <td>143706.465119</td>\n", "      <td>11.875535</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Entity_Name  Company_Mcap_USD    LogMCap  Cluster  \\\n", "0             Abbott Laboratories     219756.111897  12.300278        2   \n", "1  Thermo Fisher Scientific, Inc.     196949.204829  12.190706        2   \n", "2        Intuitive Surgical, Inc.     173562.531943  12.064299        2   \n", "3                   Danaher Corp.     150617.968231  11.922509        2   \n", "4         Boston Scientific Corp.     143706.465119  11.875535        2   \n", "\n", "  SizeCategory  \n", "0        Large  \n", "1        Large  \n", "2        Large  \n", "3        Large  \n", "4        Large  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_specialties_df.head()"]}, {"cell_type": "markdown", "id": "0dc3c36a", "metadata": {}, "source": ["### number of industries in each cluster"]}, {"cell_type": "code", "execution_count": 28, "id": "c0e5afa8", "metadata": {}, "outputs": [{"data": {"text/plain": ["SizeCategory\n", "Small     38\n", "Medium    31\n", "Large     14\n", "Name: count, dtype: int64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_specialties_df['SizeCategory'].value_counts()\n"]}, {"cell_type": "markdown", "id": "66fd3d7a", "metadata": {}, "source": ["## Quantile based clustering"]}, {"cell_type": "code", "execution_count": 30, "id": "10a47f82", "metadata": {}, "outputs": [], "source": ["medical_specialties_df['QuantileCluster'] = pd.qcut(medical_specialties_df['LogMCap'], q=3, labels=['Small', 'Medium', 'Large'])"]}, {"cell_type": "markdown", "id": "4d46d74e", "metadata": {}, "source": ["Calculates 33 percentile and 66 percentile"]}, {"cell_type": "code", "execution_count": 31, "id": "0f54471a", "metadata": {}, "outputs": [{"data": {"text/plain": ["QuantileCluster\n", "Small     28\n", "Large     28\n", "Medium    27\n", "Name: count, dtype: int64"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["medical_specialties_df['QuantileCluster'].value_counts()"]}, {"cell_type": "code", "execution_count": 32, "id": "0606c57d", "metadata": {}, "outputs": [], "source": ["medical_specialties_df.to_excel(\"medical_specialties_df.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}