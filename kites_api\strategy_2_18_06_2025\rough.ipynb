{"cells": [{"cell_type": "code", "execution_count": 1, "id": "eea85462", "metadata": {}, "outputs": [], "source": ["d= {'name': 'ICICIBANK', 'quantity': 1, 'purchase_price': 1000, 'total_amount_paid': 1000, 'current_price': 1000, 'current_profit_loss_on_stocks': 0}"]}, {"cell_type": "code", "execution_count": 6, "id": "36e9a824", "metadata": {}, "outputs": [], "source": ["d_new = {'name_new': 'SBIN', **d}"]}, {"cell_type": "code", "execution_count": 7, "id": "335feb7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name_new': 'SBIN',\n", " 'name': 'ICICIBANK',\n", " 'quantity': 1,\n", " 'purchase_price': 1000,\n", " 'total_amount_paid': 1000,\n", " 'current_price': 1000,\n", " 'current_profit_loss_on_stocks': 0}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["d_new"]}, {"cell_type": "code", "execution_count": 9, "id": "7b5894a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'data': {'transaction_type': 'OPTION_SELL', 'name': 'stock_ticker', 'tradingsymbol': ['tradingsymbol'], 'instrument_token': ['instrument_token'], 'expiry': ['expiry'], 'strike': ['strike']}}\n"]}], "source": ["d = {'data':                                                         {\n", "                                                                #'transaction_date': datetime.now(),\n", "                                                                'transaction_type': 'OPTION_SELL',\n", "                                                                'name': 'stock_ticker',\n", "                                                                'tradingsymbol': ['tradingsymbol'],\n", "                                                                'instrument_token': ['instrument_token'],\n", "                                                                'expiry': ['expiry'],\n", "                                                                'strike': ['strike'], # Not applicable\n", "                                                                }}\n", "\n", "print(d)"]}, {"cell_type": "code", "execution_count": 10, "id": "054123c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'data': {'transaction_type': 'OPTION_SELL',\n", "  'name': 'stock_ticker',\n", "  'tradingsymbol': ['tradingsymbol'],\n", "  'instrument_token': ['instrument_token'],\n", "  'expiry': ['expiry'],\n", "  'strike': ['strike']}}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["d"]}, {"cell_type": "code", "execution_count": 12, "id": "522ab7e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ratio': 2.0}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ra = {'ratio': 4/2}\n", "ra"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}