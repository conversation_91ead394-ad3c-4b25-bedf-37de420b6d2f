2025-06-19 17:30:36 - trading_strategy_app - INFO - ================== Starting Strategy Cycle (DRY RUN: False) ==================
2025-06-19 17:30:36 - trading_strategy_app - INFO - Kite API connection initialized successfully.
2025-06-19 17:30:36 - trading_strategy_app - WARNING - active_options.xlsx not found. Creating a new empty sheet.
2025-06-19 17:30:37 - trading_strategy_app - WARNING - stock_sheet.xlsx not found. Creating a new empty sheet.
2025-06-19 17:30:37 - trading_strategy_app - INFO - --- Checking 'HDFCBANK' for new trade (State: No Position) ---
2025-06-19 17:30:37 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:30:38 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:30:38 - trading_strategy_app - INFO - Fetching margin for 1 orders
2025-06-19 17:30:38 - trading_strategy_app - ERROR - Error fetching margin: Unknown Content-Type (text/html) with response: (b'<html><body><h1>503 Service Unavailable</h1>\nNo server is available to handle this request.\n</body></html>\n')
2025-06-19 17:35:39 - trading_strategy_app - INFO - ================== Starting Strategy Cycle (DRY RUN: False) ==================
2025-06-19 17:35:39 - trading_strategy_app - INFO - Kite API connection initialized successfully.
2025-06-19 17:35:39 - trading_strategy_app - INFO - Loading existing sheet: active_options.xlsx
2025-06-19 17:35:40 - trading_strategy_app - INFO - Loading existing sheet: stock_sheet.xlsx
2025-06-19 17:35:40 - trading_strategy_app - INFO - --- Checking 'HDFCBANK' for new trade (State: No Position) ---
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching margin for 1 orders
2025-06-19 17:35:40 - trading_strategy_app - INFO - HDFCBANK: All entry conditions MET. (Stock Price: 1935.3 >= Si: 1900, Premium: 0.02% >= 0.01%)
2025-06-19 17:35:40 - trading_strategy_app - INFO - ACTION: Conditions met to SELL PUT for HDFCBANK.
2025-06-19 17:35:40 - trading_strategy_app - INFO - --- Checking 'ICICIBANK' for new trade (State: No Position) ---
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching margin for 1 orders
2025-06-19 17:35:40 - trading_strategy_app - INFO - ICICIBANK: All entry conditions MET. (Stock Price: 1411.3 >= Si: 1400, Premium: 0.03% >= 0.01%)
2025-06-19 17:35:40 - trading_strategy_app - INFO - ACTION: Conditions met to SELL PUT for ICICIBANK.
2025-06-19 17:35:40 - trading_strategy_app - INFO - --- Checking 'SBIN' for new trade (State: No Position) ---
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching quotes for 1 instruments
2025-06-19 17:35:40 - trading_strategy_app - INFO - Fetching margin for 1 orders
2025-06-19 17:35:41 - trading_strategy_app - INFO - SBIN: Entry condition NOT MET. Premium % (0.01%) is < required 0.01%.
2025-06-19 17:35:41 - trading_strategy_app - INFO - --- Processing 4 collected actions ---
2025-06-19 17:35:41 - trading_strategy_app - INFO - Executing action: ADD_OPTION
2025-06-19 17:35:41 - trading_strategy_app - INFO - Prepared to add option: HDFCBANK25JUN1900PE
2025-06-19 17:35:41 - trading_strategy_app - INFO - Executing action: LOG_TRANSACTION
2025-06-19 17:35:41 - trading_strategy_app - INFO - Logging new transaction: HDFCBANK25JUN1900PE
2025-06-19 17:35:41 - trading_strategy_app - WARNING - transaction_sheet.xlsx not found. Creating a new empty sheet.
2025-06-19 17:35:41 - trading_strategy_app - INFO - Transaction logged successfully.
2025-06-19 17:35:41 - trading_strategy_app - INFO - Executing action: ADD_OPTION
2025-06-19 17:35:41 - trading_strategy_app - INFO - Prepared to add option: ICICIBANK25JUN1400PE
2025-06-19 17:35:41 - trading_strategy_app - INFO - Executing action: LOG_TRANSACTION
2025-06-19 17:35:41 - trading_strategy_app - INFO - Logging new transaction: ICICIBANK25JUN1400PE
2025-06-19 17:35:41 - trading_strategy_app - INFO - Loading existing sheet: transaction_sheet.xlsx
2025-06-19 17:35:41 - trading_strategy_app - INFO - Transaction logged successfully.
2025-06-19 17:35:41 - trading_strategy_app - INFO - Saving updated data sheets...
2025-06-19 17:35:41 - trading_strategy_app - INFO - Saving updated data to active_options.xlsx
2025-06-19 17:35:41 - trading_strategy_app - INFO - Saving updated data to stock_sheet.xlsx
2025-06-19 17:35:41 - trading_strategy_app - INFO - ================== Strategy Execution Cycle Finished ==================
2025-06-19 17:37:59 - trading_strategy_app - INFO - --- Starting Daily Update Process ---
2025-06-19 17:37:59 - trading_strategy_app - INFO - Kite API connection initialized successfully.
2025-06-19 17:37:59 - trading_strategy_app - INFO - Loading existing sheet: active_options.xlsx
2025-06-19 17:38:00 - trading_strategy_app - INFO - Loading existing sheet: stock_sheet.xlsx
2025-06-19 17:38:00 - trading_strategy_app - INFO - Fetching quotes for 4 instruments...
2025-06-19 17:38:00 - trading_strategy_app - INFO - Fetching quotes for 4 instruments
2025-06-19 17:38:00 - trading_strategy_app - INFO - Updating active options P&L...
2025-06-19 17:38:00 - trading_strategy_app - INFO - Calculated loss to close: (4.85 - 4.85) * 550 = $0.00
2025-06-19 17:38:00 - trading_strategy_app - INFO - Calculated loss to close: (7.15 - 7.15) * 700 = $0.00
2025-06-19 17:38:00 - trading_strategy_app - INFO - Saving updated data to active_options.xlsx
2025-06-19 17:38:00 - trading_strategy_app - INFO - Daily update process completed successfully.
2025-06-19 18:59:17 - trading_strategy_app - INFO - --- Starting Daily Update Process ---
2025-06-19 18:59:17 - trading_strategy_app - INFO - Kite API connection initialized successfully.
2025-06-19 18:59:17 - trading_strategy_app - INFO - Loading existing sheet: active_options.xlsx
2025-06-19 18:59:17 - trading_strategy_app - INFO - Loading existing sheet: stock_sheet.xlsx
2025-06-19 18:59:17 - trading_strategy_app - INFO - Fetching quotes for 4 instruments...
2025-06-19 18:59:17 - trading_strategy_app - INFO - Fetching quotes for 4 instruments
2025-06-19 18:59:18 - trading_strategy_app - INFO - Updating active options P&L...
2025-06-19 18:59:18 - trading_strategy_app - INFO - Calculated loss to close: (4.85 - 4.85) * 550 = $0.00
2025-06-19 18:59:18 - trading_strategy_app - INFO - Calculated loss to close: (7.15 - 7.15) * 700 = $0.00
2025-06-19 18:59:18 - trading_strategy_app - INFO - Saving updated data to active_options.xlsx
2025-06-19 18:59:18 - trading_strategy_app - INFO - Daily update process completed successfully.
