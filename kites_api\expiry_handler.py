import pandas as pd
import kite_utils


def fetch_stock_prices(active_positions_df):
    kite = kite_utils.init_kite()
    instruments_list = active_positions_df.apply(
    lambda row: f"NSE:{row['name']}", axis=1
    ).to_list()
    print(f"Fetching current prices for: {instruments_list}")
    try:
        quote = kite.quote(instruments_list)
    except Exception as e:
        print(f"[ERROR] Failed to fetch quotes: {e}")
        return None

    stock_prices_on_exp_df =  pd.DataFrame([
        {
            'name': instrument.split(':')[1],
            'stock_price_on_exp': quote[instrument]['average_price']
        }
        for instrument in quote.keys()
    ])
    return stock_prices_on_exp_df

def determine_itm_otm(row):
    if row['instrument_type'] == 'CE':
        return 'ITM' if row['strike'] < row['stock_price_on_exp'] else 'OTM'
    elif row['instrument_type'] == 'PE':
        return 'ITM' if row['strike'] > row['stock_price_on_exp'] else 'OTM'
    else:
        return 'N/A'
    
def itm_otm_flagging(active_positions_df):
    transaction_df = active_positions_df[['instrument_token', 'exchange_token', 'tradingsymbol', 'name', 'expiry', 'strike', 'lot_size', 'instrument_type', 'premium_received', 'total_margin']]
    stock_prices_df = fetch_stock_prices(active_positions_df)
    print(f"Shape of transaction_df: {transaction_df.shape}")
    print(f"Shape of stock_prices_df: {stock_prices_df.shape}")
    transaction_df = transaction_df.merge(stock_prices_df, on='name', how='inner')
    transaction_df['itm_otm'] = transaction_df.apply(determine_itm_otm, axis=1)
    return transaction_df


def make_transaction_log(transaction_df):
    kite = kite_utils.init_kite()
    instruments_list = transaction_df.apply(
    lambda row: f"NFO:{row['tradingsymbol']}", axis=1
).to_list()
    print(f"Fetching option prices for: {instruments_list}")
    try:
        quote = kite.quote(instruments_list)
    except Exception as e:
        print(f"[ERROR] Could not fetch option prices: {e}")
        return None

    quote_df = pd.DataFrame([
        {
            'tradingsymbol': key.split(':')[1],
            'last_price': quote[key]['last_price']
        }
        for key in quote.keys()
    ])
    print(f"Shape of quote_df: {quote_df.shape}")
    transaction_df = transaction_df.merge(quote_df, on='tradingsymbol', how='inner')
    transaction_df['premium_paid'] = transaction_df['last_price'] * transaction_df['lot_size']
    transaction_df['profit_loss'] = transaction_df['premium_received'] - transaction_df['premium_paid']
    transaction_df['yield'] = transaction_df['profit_loss'] / transaction_df['total_margin']
    # transaction_df['expiry'] = pd.to_datetime(transaction_df['expiry'], errors='coerce').dt.normalize()
    print(f"Shape of transaction_df: {transaction_df.shape}")
    return transaction_df

def extract_itm_otm_mapping(transaction_df):
    """
    Returns a dictionary mapping each 'name' to its 'ITM/OTM' status from the transaction DataFrame.

    Parameters:
    - transaction_df (pd.DataFrame): DataFrame with columns 'name' and 'ITM/OTM'.

    Returns:
    - dict: Mapping like {'AXISBANK': 'OTM', 'HDFCBANK': 'OTM', ...}
    """
    itm_otm_mapping = transaction_df.set_index('name')['itm_otm'].to_dict()
    print(f"size of itm_otm_mapping: {len(itm_otm_mapping)}")
    return itm_otm_mapping

if __name__ == "__main__":
    active_positions_df = pd.read_excel('data/active_positions.xlsx')
    transaction_df = itm_otm_flagging(active_positions_df)
    transaction_log = make_transaction_log(transaction_df)
    itm_otm_mapping = extract_itm_otm_mapping(transaction_df)
    transaction_log.to_excel('transaction_test.xlsx', index=False)
    print(itm_otm_mapping)
    
    

