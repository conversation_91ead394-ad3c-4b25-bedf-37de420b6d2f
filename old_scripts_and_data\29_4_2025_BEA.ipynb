{"cells": [{"cell_type": "markdown", "id": "d00485bd", "metadata": {}, "source": ["```\n", "Strategy:\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3e8f23fe", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "826e1604", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(\"NAICS_Economic_Heft_Scores_underlying.xlsx\")"]}, {"cell_type": "code", "execution_count": 3, "id": "17c11374", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>GDP_2022</th>\n", "      <th>GDP_2023</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>521.5</td>\n", "      <td>531.8</td>\n", "      <td>2.0</td>\n", "      <td>10.3</td>\n", "      <td>1.000000</td>\n", "      <td>0.214674</td>\n", "      <td>0.685870</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>311FT</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>296.8</td>\n", "      <td>335.2</td>\n", "      <td>12.9</td>\n", "      <td>38.4</td>\n", "      <td>0.626945</td>\n", "      <td>0.510870</td>\n", "      <td>0.580515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3364OT</td>\n", "      <td>Other transportation equipment</td>\n", "      <td>160.4</td>\n", "      <td>190.3</td>\n", "      <td>18.6</td>\n", "      <td>29.9</td>\n", "      <td>0.351992</td>\n", "      <td>0.665761</td>\n", "      <td>0.477500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>118.9</td>\n", "      <td>145.9</td>\n", "      <td>22.7</td>\n", "      <td>27.0</td>\n", "      <td>0.267742</td>\n", "      <td>0.777174</td>\n", "      <td>0.471515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>298.9</td>\n", "      <td>308.1</td>\n", "      <td>3.1</td>\n", "      <td>9.2</td>\n", "      <td>0.575522</td>\n", "      <td>0.244565</td>\n", "      <td>0.443139</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry                        IndustrYDescription  GDP_2022  GDP_2023  \\\n", "0      325                          Chemical products     521.5     531.8   \n", "1    311FT     Food and beverage and tobacco products     296.8     335.2   \n", "2   3364OT             Other transportation equipment     160.4     190.3   \n", "3     3364  Aerospace product and parts manufacturing     118.9     145.9   \n", "4      334           Computer and electronic products     298.9     308.1   \n", "\n", "   Recent_Growth_%  Recent_Increase  GDP_Score  GDP_Growth_Score  \\\n", "0              2.0             10.3   1.000000          0.214674   \n", "1             12.9             38.4   0.626945          0.510870   \n", "2             18.6             29.9   0.351992          0.665761   \n", "3             22.7             27.0   0.267742          0.777174   \n", "4              3.1              9.2   0.575522          0.244565   \n", "\n", "   Economic_Heft_Score  \n", "0             0.685870  \n", "1             0.580515  \n", "2             0.477500  \n", "3             0.471515  \n", "4             0.443139  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "abf3103f", "metadata": {}, "outputs": [], "source": ["df.rename(columns={'Industry': 'BEA_code', 'IndustrYDescription': 'Industry_description'}, inplace=True)"]}, {"cell_type": "markdown", "id": "b05396a2", "metadata": {}, "source": ["## Indentify 3 digit Numerical Parent code"]}, {"cell_type": "code", "execution_count": 5, "id": "d488e167", "metadata": {}, "outputs": [], "source": ["parent_codes = df['BEA_code'].apply(str).str.match(r'^\\d{3}$')\n", "parents = df[parent_codes][['BEA_code', 'Industry_description', 'GDP_2023']].set_index('BEA_code')"]}, {"cell_type": "code", "execution_count": 6, "id": "b0575c10", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry_description</th>\n", "      <th>GDP_2023</th>\n", "    </tr>\n", "    <tr>\n", "      <th>BEA_code</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>325</th>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>334</th>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>311</th>\n", "      <td>Food manufacturing</td>\n", "      <td>226.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>332</th>\n", "      <td>Fabricated metal products</td>\n", "      <td>188.6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Industry_description  GDP_2023\n", "BEA_code                                            \n", "325                      Chemical products     531.8\n", "334       Computer and electronic products     308.1\n", "333                              Machinery     200.4\n", "311                     Food manufacturing     226.4\n", "332              Fabricated metal products     188.6"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["parents.head()"]}, {"cell_type": "markdown", "id": "1c27d5c1", "metadata": {}, "source": ["## Decomposition of frame"]}, {"cell_type": "code", "execution_count": 7, "id": "7d6a4934", "metadata": {}, "outputs": [], "source": ["decomposition = []"]}, {"cell_type": "code", "execution_count": 8, "id": "ff5c6ec4", "metadata": {}, "outputs": [], "source": ["# For each parent code, find constituents and calculate ratios\n", "\n", "for parent_code, parent_row in parents.iterrows():\n", "    parent_gdp = parent_row['GDP_2023']\n", "    parent_description = parent_row['Industry_description']\n", "    \n", "    # Find constituents: codes that start with the parent code and are longer than 3 characters\n", "    constituents = df[df['BEA_code'].apply(str).str.startswith(str(parent_code)) & \n", "                     (df['BEA_code'].str.len() > 3)][['BEA_code','Industry_description', 'GDP_2023']]\n", "    \n", "    for _, const_row in constituents.iterrows():\n", "        const_code = const_row['BEA_code']\n", "        const_gdp = const_row['GDP_2023']\n", "        ratio = (const_gdp / parent_gdp) \n", "        const_desc = const_row['Industry_description']  \n", "        \n", "        decomposition.append({\n", "            '3 level Industry code': parent_code,\n", "            '3 level Industry description': parent_description,\n", "            'Total GDP 2023': parent_gdp,\n", "            'Constituents code': const_code,\n", "            'Constituents description': const_desc,\n", "            'GDP2023 constituents': const_gdp,\n", "            'Ratio': ratio\n", "        })\n"]}, {"cell_type": "code", "execution_count": 9, "id": "9d83f083", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>3 level Industry code</th>\n", "      <th>3 level Industry description</th>\n", "      <th>Total GDP 2023</th>\n", "      <th>Constituents code</th>\n", "      <th>Constituents description</th>\n", "      <th>GDP2023 constituents</th>\n", "      <th><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>3254</td>\n", "      <td>Pharmaceutical and medicine manufacturing</td>\n", "      <td>251.1</td>\n", "      <td>0.472170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>3251</td>\n", "      <td>Basic chemical manufacturing</td>\n", "      <td>120.4</td>\n", "      <td>0.226401</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>325X</td>\n", "      <td>Other chemical manufacturing</td>\n", "      <td>120.9</td>\n", "      <td>0.227341</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>3252</td>\n", "      <td>Resin, rubber, and artificial fibers manufactu...</td>\n", "      <td>39.5</td>\n", "      <td>0.074276</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3345</td>\n", "      <td>Navigational, measuring, electromedical, and c...</td>\n", "      <td>137.5</td>\n", "      <td>0.446284</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3344</td>\n", "      <td>Semiconductor and other electronic component m...</td>\n", "      <td>107.8</td>\n", "      <td>0.349886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>334X</td>\n", "      <td>Other computer and electronic product manufact...</td>\n", "      <td>4.8</td>\n", "      <td>0.015579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3341</td>\n", "      <td>Computer and peripheral equipment manufacturing</td>\n", "      <td>32.0</td>\n", "      <td>0.103862</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3342</td>\n", "      <td>Communications equipment manufacturing</td>\n", "      <td>25.9</td>\n", "      <td>0.084064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>33312</td>\n", "      <td>Construction machinery manufacturing</td>\n", "      <td>19.9</td>\n", "      <td>0.099301</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  3 level Industry code      3 level Industry description  Total GDP 2023  \\\n", "0                   325                 Chemical products           531.8   \n", "1                   325                 Chemical products           531.8   \n", "2                   325                 Chemical products           531.8   \n", "3                   325                 Chemical products           531.8   \n", "4                   334  Computer and electronic products           308.1   \n", "5                   334  Computer and electronic products           308.1   \n", "6                   334  Computer and electronic products           308.1   \n", "7                   334  Computer and electronic products           308.1   \n", "8                   334  Computer and electronic products           308.1   \n", "9                   333                         Machinery           200.4   \n", "\n", "  Constituents code                           Constituents description  \\\n", "0              3254          Pharmaceutical and medicine manufacturing   \n", "1              3251                       Basic chemical manufacturing   \n", "2              325X                       Other chemical manufacturing   \n", "3              3252  Resin, rubber, and artificial fibers manufactu...   \n", "4              3345  Navigational, measuring, electromedical, and c...   \n", "5              3344  Semiconductor and other electronic component m...   \n", "6              334X  Other computer and electronic product manufact...   \n", "7              3341    Computer and peripheral equipment manufacturing   \n", "8              3342             Communications equipment manufacturing   \n", "9             33312               Construction machinery manufacturing   \n", "\n", "   GDP2023 constituents     Ratio  \n", "0                 251.1  0.472170  \n", "1                 120.4  0.226401  \n", "2                 120.9  0.227341  \n", "3                  39.5  0.074276  \n", "4                 137.5  0.446284  \n", "5                 107.8  0.349886  \n", "6                   4.8  0.015579  \n", "7                  32.0  0.103862  \n", "8                  25.9  0.084064  \n", "9                  19.9  0.099301  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df = pd.DataFrame(decomposition)\n", "\n", "result_df.head(10)"]}, {"cell_type": "code", "execution_count": 10, "id": "c9e8e0b0", "metadata": {}, "outputs": [{"data": {"text/plain": ["18"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["len(result_df)"]}, {"cell_type": "code", "execution_count": 11, "id": "0646ea84", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>3 level Industry code</th>\n", "      <th>3 level Industry description</th>\n", "      <th>Total GDP 2023</th>\n", "      <th>Constituents code</th>\n", "      <th>Constituents description</th>\n", "      <th>GDP2023 constituents</th>\n", "      <th><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>3254</td>\n", "      <td>Pharmaceutical and medicine manufacturing</td>\n", "      <td>251.1</td>\n", "      <td>0.472170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>3251</td>\n", "      <td>Basic chemical manufacturing</td>\n", "      <td>120.4</td>\n", "      <td>0.226401</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>325X</td>\n", "      <td>Other chemical manufacturing</td>\n", "      <td>120.9</td>\n", "      <td>0.227341</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>3252</td>\n", "      <td>Resin, rubber, and artificial fibers manufactu...</td>\n", "      <td>39.5</td>\n", "      <td>0.074276</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3345</td>\n", "      <td>Navigational, measuring, electromedical, and c...</td>\n", "      <td>137.5</td>\n", "      <td>0.446284</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3344</td>\n", "      <td>Semiconductor and other electronic component m...</td>\n", "      <td>107.8</td>\n", "      <td>0.349886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>334X</td>\n", "      <td>Other computer and electronic product manufact...</td>\n", "      <td>4.8</td>\n", "      <td>0.015579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3341</td>\n", "      <td>Computer and peripheral equipment manufacturing</td>\n", "      <td>32.0</td>\n", "      <td>0.103862</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>3342</td>\n", "      <td>Communications equipment manufacturing</td>\n", "      <td>25.9</td>\n", "      <td>0.084064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>33312</td>\n", "      <td>Construction machinery manufacturing</td>\n", "      <td>19.9</td>\n", "      <td>0.099301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>3332OM</td>\n", "      <td>Other machinery</td>\n", "      <td>155.0</td>\n", "      <td>0.773453</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>33313</td>\n", "      <td>Mining and oil and gas field machinery manufac...</td>\n", "      <td>6.8</td>\n", "      <td>0.033932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>33311</td>\n", "      <td>Agricultural implement manufacturing</td>\n", "      <td>18.7</td>\n", "      <td>0.093313</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>311</td>\n", "      <td>Food manufacturing</td>\n", "      <td>226.4</td>\n", "      <td>311FT</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>335.2</td>\n", "      <td>1.480565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>339</td>\n", "      <td>Miscellaneous manufacturing</td>\n", "      <td>116.9</td>\n", "      <td>3391</td>\n", "      <td>Medical equipment and supplies manufacturing</td>\n", "      <td>77.7</td>\n", "      <td>0.664671</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>339</td>\n", "      <td>Miscellaneous manufacturing</td>\n", "      <td>116.9</td>\n", "      <td>3399</td>\n", "      <td>Other miscellaneous manufacturing</td>\n", "      <td>39.2</td>\n", "      <td>0.335329</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>331</td>\n", "      <td>Primary metals</td>\n", "      <td>84.0</td>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>38.6</td>\n", "      <td>0.459524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>331</td>\n", "      <td>Primary metals</td>\n", "      <td>84.0</td>\n", "      <td>3311IS</td>\n", "      <td>Iron and steel mills and manufacturing from pu...</td>\n", "      <td>45.4</td>\n", "      <td>0.540476</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   3 level Industry code      3 level Industry description  Total GDP 2023  \\\n", "0                    325                 Chemical products           531.8   \n", "1                    325                 Chemical products           531.8   \n", "2                    325                 Chemical products           531.8   \n", "3                    325                 Chemical products           531.8   \n", "4                    334  Computer and electronic products           308.1   \n", "5                    334  Computer and electronic products           308.1   \n", "6                    334  Computer and electronic products           308.1   \n", "7                    334  Computer and electronic products           308.1   \n", "8                    334  Computer and electronic products           308.1   \n", "9                    333                         Machinery           200.4   \n", "10                   333                         Machinery           200.4   \n", "11                   333                         Machinery           200.4   \n", "12                   333                         Machinery           200.4   \n", "13                   311                Food manufacturing           226.4   \n", "14                   339       Miscellaneous manufacturing           116.9   \n", "15                   339       Miscellaneous manufacturing           116.9   \n", "16                   331                    Primary metals            84.0   \n", "17                   331                    Primary metals            84.0   \n", "\n", "   Constituents code                           Constituents description  \\\n", "0               3254          Pharmaceutical and medicine manufacturing   \n", "1               3251                       Basic chemical manufacturing   \n", "2               325X                       Other chemical manufacturing   \n", "3               3252  Resin, rubber, and artificial fibers manufactu...   \n", "4               3345  Navigational, measuring, electromedical, and c...   \n", "5               3344  Semiconductor and other electronic component m...   \n", "6               334X  Other computer and electronic product manufact...   \n", "7               3341    Computer and peripheral equipment manufacturing   \n", "8               3342             Communications equipment manufacturing   \n", "9              33312               Construction machinery manufacturing   \n", "10            3332OM                                    Other machinery   \n", "11             33313  Mining and oil and gas field machinery manufac...   \n", "12             33311               Agricultural implement manufacturing   \n", "13             311FT             Food and beverage and tobacco products   \n", "14              3391       Medical equipment and supplies manufacturing   \n", "15              3399                  Other miscellaneous manufacturing   \n", "16            3313NF  Nonferrous metal production and processing and...   \n", "17            3311IS  Iron and steel mills and manufacturing from pu...   \n", "\n", "    GDP2023 constituents     Ratio  \n", "0                  251.1  0.472170  \n", "1                  120.4  0.226401  \n", "2                  120.9  0.227341  \n", "3                   39.5  0.074276  \n", "4                  137.5  0.446284  \n", "5                  107.8  0.349886  \n", "6                    4.8  0.015579  \n", "7                   32.0  0.103862  \n", "8                   25.9  0.084064  \n", "9                   19.9  0.099301  \n", "10                 155.0  0.773453  \n", "11                   6.8  0.033932  \n", "12                  18.7  0.093313  \n", "13                 335.2  1.480565  \n", "14                  77.7  0.664671  \n", "15                  39.2  0.335329  \n", "16                  38.6  0.459524  \n", "17                  45.4  0.540476  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df.head(18)"]}, {"cell_type": "markdown", "id": "6acaee0a", "metadata": {}, "source": ["## Industry codes who did not get decomposed"]}, {"cell_type": "code", "execution_count": 12, "id": "1b877aa5", "metadata": {}, "outputs": [], "source": ["df['BEA_code'] = df['BEA_code'].astype(str)\n", "result_df['Constituents code'] = result_df['Constituents code'].astype(str)\n", "result_df['3 level Industry code'] = result_df['3 level Industry code'].astype(str)"]}, {"cell_type": "code", "execution_count": 13, "id": "b28f1d4d", "metadata": {}, "outputs": [], "source": ["# Get the unique constituent codes from result_df\n", "constituent_codes = result_df['Constituents code'].unique()\n", "industry_codes = result_df['3 level Industry code'].unique()"]}, {"cell_type": "code", "execution_count": 14, "id": "737c5057", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24\n"]}], "source": ["unique_codes = set(constituent_codes).union(set(industry_codes))\n", "print(len(unique_codes))"]}, {"cell_type": "code", "execution_count": 15, "id": "c6c68cd1", "metadata": {}, "outputs": [], "source": ["# Create a separate dataframe with rows not in constituent_codes\n", "separate_df = df[~df['BEA_code'].isin(unique_codes)]"]}, {"cell_type": "code", "execution_count": 16, "id": "14015b32", "metadata": {}, "outputs": [{"data": {"text/plain": ["21"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["len(separate_df)"]}, {"cell_type": "code", "execution_count": 17, "id": "ad55ec2a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>Industry_description</th>\n", "      <th>GDP_2022</th>\n", "      <th>GDP_2023</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3364OT</td>\n", "      <td>Other transportation equipment</td>\n", "      <td>160.4</td>\n", "      <td>190.3</td>\n", "      <td>18.6</td>\n", "      <td>29.9</td>\n", "      <td>0.351992</td>\n", "      <td>0.665761</td>\n", "      <td>0.477500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>118.9</td>\n", "      <td>145.9</td>\n", "      <td>22.7</td>\n", "      <td>27.0</td>\n", "      <td>0.267742</td>\n", "      <td>0.777174</td>\n", "      <td>0.471515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3122</td>\n", "      <td>Tobacco product manufacturing</td>\n", "      <td>44.6</td>\n", "      <td>57.3</td>\n", "      <td>28.5</td>\n", "      <td>12.7</td>\n", "      <td>0.099620</td>\n", "      <td>0.934783</td>\n", "      <td>0.433685</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>3361MV</td>\n", "      <td>Motor vehicles, bodies and trailers, and parts</td>\n", "      <td>160.2</td>\n", "      <td>176.4</td>\n", "      <td>10.1</td>\n", "      <td>16.2</td>\n", "      <td>0.325617</td>\n", "      <td>0.434783</td>\n", "      <td>0.369283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>332</td>\n", "      <td>Fabricated metal products</td>\n", "      <td>175.5</td>\n", "      <td>188.6</td>\n", "      <td>7.5</td>\n", "      <td>13.1</td>\n", "      <td>0.348767</td>\n", "      <td>0.364130</td>\n", "      <td>0.354912</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>336112</td>\n", "      <td>Light truck and utility vehicle manufacturing</td>\n", "      <td>57.2</td>\n", "      <td>64.3</td>\n", "      <td>12.4</td>\n", "      <td>7.1</td>\n", "      <td>0.112903</td>\n", "      <td>0.497283</td>\n", "      <td>0.266655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>3362BP</td>\n", "      <td>Motor vehicle body, trailer, and parts manufac...</td>\n", "      <td>84.0</td>\n", "      <td>91.2</td>\n", "      <td>8.6</td>\n", "      <td>7.2</td>\n", "      <td>0.163947</td>\n", "      <td>0.394022</td>\n", "      <td>0.255977</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>335</td>\n", "      <td>Electrical equipment, appliances, and components</td>\n", "      <td>69.9</td>\n", "      <td>76.1</td>\n", "      <td>8.9</td>\n", "      <td>6.2</td>\n", "      <td>0.135294</td>\n", "      <td>0.402174</td>\n", "      <td>0.242046</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>324</td>\n", "      <td>Petroleum and coal products</td>\n", "      <td>230.3</td>\n", "      <td>216.8</td>\n", "      <td>-5.9</td>\n", "      <td>-13.5</td>\n", "      <td>0.402277</td>\n", "      <td>0.000000</td>\n", "      <td>0.241366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>327</td>\n", "      <td>Nonmetallic mineral products</td>\n", "      <td>72.0</td>\n", "      <td>76.7</td>\n", "      <td>6.5</td>\n", "      <td>4.7</td>\n", "      <td>0.136433</td>\n", "      <td>0.336957</td>\n", "      <td>0.216642</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>3121</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>47.3</td>\n", "      <td>51.6</td>\n", "      <td>9.1</td>\n", "      <td>4.3</td>\n", "      <td>0.088805</td>\n", "      <td>0.407609</td>\n", "      <td>0.216326</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>322</td>\n", "      <td>Paper products</td>\n", "      <td>70.3</td>\n", "      <td>74.7</td>\n", "      <td>6.3</td>\n", "      <td>4.4</td>\n", "      <td>0.132638</td>\n", "      <td>0.331522</td>\n", "      <td>0.212191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>326</td>\n", "      <td>Plastics and rubber products</td>\n", "      <td>94.7</td>\n", "      <td>97.7</td>\n", "      <td>3.2</td>\n", "      <td>3.0</td>\n", "      <td>0.176281</td>\n", "      <td>0.247283</td>\n", "      <td>0.204682</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>33612</td>\n", "      <td>Heavy duty truck manufacturing</td>\n", "      <td>5.5</td>\n", "      <td>6.1</td>\n", "      <td>10.9</td>\n", "      <td>0.6</td>\n", "      <td>0.002467</td>\n", "      <td>0.456522</td>\n", "      <td>0.184089</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>3365AO</td>\n", "      <td>All other transportation equipment manufacturing</td>\n", "      <td>41.6</td>\n", "      <td>44.4</td>\n", "      <td>6.7</td>\n", "      <td>2.8</td>\n", "      <td>0.075142</td>\n", "      <td>0.342391</td>\n", "      <td>0.182042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>336111</td>\n", "      <td>Automobile manufacturing</td>\n", "      <td>13.5</td>\n", "      <td>14.8</td>\n", "      <td>9.6</td>\n", "      <td>1.3</td>\n", "      <td>0.018975</td>\n", "      <td>0.421196</td>\n", "      <td>0.179863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>323</td>\n", "      <td>Printing and related support activities</td>\n", "      <td>41.6</td>\n", "      <td>42.9</td>\n", "      <td>3.1</td>\n", "      <td>1.3</td>\n", "      <td>0.072296</td>\n", "      <td>0.244565</td>\n", "      <td>0.141204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>321</td>\n", "      <td>Wood products</td>\n", "      <td>64.1</td>\n", "      <td>62.1</td>\n", "      <td>-3.1</td>\n", "      <td>-2.0</td>\n", "      <td>0.108729</td>\n", "      <td>0.076087</td>\n", "      <td>0.095672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>32.3</td>\n", "      <td>32.3</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.052182</td>\n", "      <td>0.160326</td>\n", "      <td>0.095440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>315AL</td>\n", "      <td>Apparel and leather and allied products</td>\n", "      <td>12.3</td>\n", "      <td>12.4</td>\n", "      <td>0.8</td>\n", "      <td>0.1</td>\n", "      <td>0.014421</td>\n", "      <td>0.182065</td>\n", "      <td>0.081479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>313TT</td>\n", "      <td>Textile mills and textile product mills</td>\n", "      <td>17.4</td>\n", "      <td>17.0</td>\n", "      <td>-2.3</td>\n", "      <td>-0.4</td>\n", "      <td>0.023150</td>\n", "      <td>0.097826</td>\n", "      <td>0.053020</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   BEA_code                               Industry_description  GDP_2022  \\\n", "2    3364OT                     Other transportation equipment     160.4   \n", "3      3364          Aerospace product and parts manufacturing     118.9   \n", "6      3122                      Tobacco product manufacturing      44.6   \n", "11   3361MV     Motor vehicles, bodies and trailers, and parts     160.2   \n", "12      332                          Fabricated metal products     175.5   \n", "15   336112      Light truck and utility vehicle manufacturing      57.2   \n", "17   3362BP  Motor vehicle body, trailer, and parts manufac...      84.0   \n", "19      335   Electrical equipment, appliances, and components      69.9   \n", "20      324                        Petroleum and coal products     230.3   \n", "22      327                       Nonmetallic mineral products      72.0   \n", "23     3121                             Beverage manufacturing      47.3   \n", "24      322                                     Paper products      70.3   \n", "25      326                       Plastics and rubber products      94.7   \n", "29    33612                     Heavy duty truck manufacturing       5.5   \n", "30   3365AO   All other transportation equipment manufacturing      41.6   \n", "32   336111                           Automobile manufacturing      13.5   \n", "34      323            Printing and related support activities      41.6   \n", "38      321                                      Wood products      64.1   \n", "39      337                     Furniture and related products      32.3   \n", "41    315AL            Apparel and leather and allied products      12.3   \n", "44    313TT            Textile mills and textile product mills      17.4   \n", "\n", "    GDP_2023  Recent_Growth_%  Recent_Increase  GDP_Score  GDP_Growth_Score  \\\n", "2      190.3             18.6             29.9   0.351992          0.665761   \n", "3      145.9             22.7             27.0   0.267742          0.777174   \n", "6       57.3             28.5             12.7   0.099620          0.934783   \n", "11     176.4             10.1             16.2   0.325617          0.434783   \n", "12     188.6              7.5             13.1   0.348767          0.364130   \n", "15      64.3             12.4              7.1   0.112903          0.497283   \n", "17      91.2              8.6              7.2   0.163947          0.394022   \n", "19      76.1              8.9              6.2   0.135294          0.402174   \n", "20     216.8             -5.9            -13.5   0.402277          0.000000   \n", "22      76.7              6.5              4.7   0.136433          0.336957   \n", "23      51.6              9.1              4.3   0.088805          0.407609   \n", "24      74.7              6.3              4.4   0.132638          0.331522   \n", "25      97.7              3.2              3.0   0.176281          0.247283   \n", "29       6.1             10.9              0.6   0.002467          0.456522   \n", "30      44.4              6.7              2.8   0.075142          0.342391   \n", "32      14.8              9.6              1.3   0.018975          0.421196   \n", "34      42.9              3.1              1.3   0.072296          0.244565   \n", "38      62.1             -3.1             -2.0   0.108729          0.076087   \n", "39      32.3              0.0              0.0   0.052182          0.160326   \n", "41      12.4              0.8              0.1   0.014421          0.182065   \n", "44      17.0             -2.3             -0.4   0.023150          0.097826   \n", "\n", "    Economic_Heft_Score  \n", "2              0.477500  \n", "3              0.471515  \n", "6              0.433685  \n", "11             0.369283  \n", "12             0.354912  \n", "15             0.266655  \n", "17             0.255977  \n", "19             0.242046  \n", "20             0.241366  \n", "22             0.216642  \n", "23             0.216326  \n", "24             0.212191  \n", "25             0.204682  \n", "29             0.184089  \n", "30             0.182042  \n", "32             0.179863  \n", "34             0.141204  \n", "38             0.095672  \n", "39             0.095440  \n", "41             0.081479  \n", "44             0.053020  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["separate_df.head(30)"]}, {"cell_type": "code", "execution_count": 18, "id": "870590a0", "metadata": {}, "outputs": [], "source": ["result_df.to_excel('split_bea.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "773350d0", "metadata": {}, "source": ["## Changing NAICS heft sheet"]}, {"cell_type": "code", "execution_count": 19, "id": "4df0848a", "metadata": {}, "outputs": [], "source": ["naics_heft = pd.read_excel('NAICS_Economic_Heft_Scores_new.xlsx')"]}, {"cell_type": "code", "execution_count": 20, "id": "a1765fbe", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>GDP_2023</th>\n", "      <th>GDP_2024</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Two_Year_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>575.2</td>\n", "      <td>8.2</td>\n", "      <td>10.3</td>\n", "      <td>43.4</td>\n", "      <td>1.000000</td>\n", "      <td>0.984043</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>311FT</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>335.2</td>\n", "      <td>350.1</td>\n", "      <td>4.4</td>\n", "      <td>18.0</td>\n", "      <td>14.9</td>\n", "      <td>0.600036</td>\n", "      <td>0.781915</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3361MV</td>\n", "      <td>Motor vehicles, bodies and trailers, and parts</td>\n", "      <td>176.4</td>\n", "      <td>190.5</td>\n", "      <td>8.0</td>\n", "      <td>18.9</td>\n", "      <td>14.1</td>\n", "      <td>0.316453</td>\n", "      <td>0.973404</td>\n", "      <td>0.579234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>211.8</td>\n", "      <td>5.7</td>\n", "      <td>20.6</td>\n", "      <td>11.4</td>\n", "      <td>0.354300</td>\n", "      <td>0.851064</td>\n", "      <td>0.553005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>308.3</td>\n", "      <td>0.1</td>\n", "      <td>3.1</td>\n", "      <td>0.2</td>\n", "      <td>0.525764</td>\n", "      <td>0.553191</td>\n", "      <td>0.536735</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry                             IndustrYDescription  GDP_2023  \\\n", "0      325                               Chemical products     531.8   \n", "1    311FT          Food and beverage and tobacco products     335.2   \n", "2   3361MV  Motor vehicles, bodies and trailers, and parts     176.4   \n", "3      333                                       Machinery     200.4   \n", "4      334                Computer and electronic products     308.1   \n", "\n", "   GDP_2024  Recent_Growth_%  Two_Year_Growth_%  Recent_Increase  GDP_Score  \\\n", "0     575.2              8.2               10.3             43.4   1.000000   \n", "1     350.1              4.4               18.0             14.9   0.600036   \n", "2     190.5              8.0               18.9             14.1   0.316453   \n", "3     211.8              5.7               20.6             11.4   0.354300   \n", "4     308.3              0.1                3.1              0.2   0.525764   \n", "\n", "   GDP_Growth_Score  Economic_Heft_Score  \n", "0          0.984043             0.993617  \n", "1          0.781915             0.672787  \n", "2          0.973404             0.579234  \n", "3          0.851064             0.553005  \n", "4          0.553191             0.536735  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["naics_heft.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "72812fda", "metadata": {}, "outputs": [], "source": ["# Convert '3 level Industry code' to string in result_df\n", "result_df['3 level Industry code'] = result_df['3 level Industry code'].astype(str)"]}, {"cell_type": "code", "execution_count": 22, "id": "9790fdf6", "metadata": {}, "outputs": [], "source": ["# Ensure 'Industry' in naics_heft is string\n", "naics_heft['Industry'] = naics_heft['Industry'].astype(str)"]}, {"cell_type": "code", "execution_count": 23, "id": "7cedd6a6", "metadata": {}, "outputs": [], "source": ["# Group split_bea by '3 level Industry code'\n", "constituents_grouped = result_df.groupby('3 level Industry code')\n", "\n", "# Find common codes between split_bea and naics_heft\n", "common_codes = set(constituents_grouped.groups.keys()) & set(naics_heft['Industry'])"]}, {"cell_type": "code", "execution_count": 24, "id": "fa83972d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'325', '331', '333', '334', '339'}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["common_codes"]}, {"cell_type": "code", "execution_count": 39, "id": "4e5afacd", "metadata": {}, "outputs": [], "source": ["df_list = []"]}, {"cell_type": "code", "execution_count": 31, "id": "7f337cbd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Industry</th>\n", "      <td>325</td>\n", "      <td>311FT</td>\n", "      <td>3361MV</td>\n", "      <td>333</td>\n", "      <td>334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IndustrYDescription</th>\n", "      <td>Chemical products</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>Motor vehicles, bodies and trailers, and parts</td>\n", "      <td>Machinery</td>\n", "      <td>Computer and electronic products</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GDP_2023</th>\n", "      <td>531.8</td>\n", "      <td>335.2</td>\n", "      <td>176.4</td>\n", "      <td>200.4</td>\n", "      <td>308.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GDP_2024</th>\n", "      <td>575.2</td>\n", "      <td>350.1</td>\n", "      <td>190.5</td>\n", "      <td>211.8</td>\n", "      <td>308.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Recent_Growth_%</th>\n", "      <td>8.2</td>\n", "      <td>4.4</td>\n", "      <td>8.0</td>\n", "      <td>5.7</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Two_Year_Growth_%</th>\n", "      <td>10.3</td>\n", "      <td>18.0</td>\n", "      <td>18.9</td>\n", "      <td>20.6</td>\n", "      <td>3.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Recent_Increase</th>\n", "      <td>43.4</td>\n", "      <td>14.9</td>\n", "      <td>14.1</td>\n", "      <td>11.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GDP_Score</th>\n", "      <td>1.0</td>\n", "      <td>0.600036</td>\n", "      <td>0.316453</td>\n", "      <td>0.3543</td>\n", "      <td>0.525764</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GDP_Growth_Score</th>\n", "      <td>0.984043</td>\n", "      <td>0.781915</td>\n", "      <td>0.973404</td>\n", "      <td>0.851064</td>\n", "      <td>0.553191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Economic_Heft_Score</th>\n", "      <td>0.993617</td>\n", "      <td>0.672787</td>\n", "      <td>0.579234</td>\n", "      <td>0.553005</td>\n", "      <td>0.536735</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     0  \\\n", "Industry                           325   \n", "IndustrYDescription  Chemical products   \n", "GDP_2023                         531.8   \n", "GDP_2024                         575.2   \n", "Recent_Growth_%                    8.2   \n", "Two_Year_Growth_%                 10.3   \n", "Recent_Increase                   43.4   \n", "GDP_Score                          1.0   \n", "GDP_Growth_Score              0.984043   \n", "Economic_Heft_Score           0.993617   \n", "\n", "                                                          1  \\\n", "Industry                                              311FT   \n", "IndustrYDescription  Food and beverage and tobacco products   \n", "GDP_2023                                              335.2   \n", "GDP_2024                                              350.1   \n", "Recent_Growth_%                                         4.4   \n", "Two_Year_Growth_%                                      18.0   \n", "Recent_Increase                                        14.9   \n", "GDP_Score                                          0.600036   \n", "GDP_Growth_Score                                   0.781915   \n", "Economic_Heft_Score                                0.672787   \n", "\n", "                                                                  2  \\\n", "Industry                                                     3361MV   \n", "IndustrYDescription  Motor vehicles, bodies and trailers, and parts   \n", "GDP_2023                                                      176.4   \n", "GDP_2024                                                      190.5   \n", "Recent_Growth_%                                                 8.0   \n", "Two_Year_Growth_%                                              18.9   \n", "Recent_Increase                                                14.1   \n", "GDP_Score                                                  0.316453   \n", "GDP_Growth_Score                                           0.973404   \n", "Economic_Heft_Score                                        0.579234   \n", "\n", "                             3                                 4  \n", "Industry                   333                               334  \n", "IndustrYDescription  Machinery  Computer and electronic products  \n", "GDP_2023                 200.4                             308.1  \n", "GDP_2024                 211.8                             308.3  \n", "Recent_Growth_%            5.7                               0.1  \n", "Two_Year_Growth_%         20.6                               3.1  \n", "Recent_Increase           11.4                               0.2  \n", "GDP_Score               0.3543                          0.525764  \n", "GDP_Growth_Score      0.851064                          0.553191  \n", "Economic_Heft_Score   0.553005                          0.536735  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["naics_heft.head().T"]}, {"cell_type": "code", "execution_count": 28, "id": "b0147e9a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>GDP_2023</th>\n", "      <th>GDP_2024</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Two_Year_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>575.2</td>\n", "      <td>8.2</td>\n", "      <td>10.3</td>\n", "      <td>43.4</td>\n", "      <td>1.000000</td>\n", "      <td>0.984043</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>311FT</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>335.2</td>\n", "      <td>350.1</td>\n", "      <td>4.4</td>\n", "      <td>18.0</td>\n", "      <td>14.9</td>\n", "      <td>0.600036</td>\n", "      <td>0.781915</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3361MV</td>\n", "      <td>Motor vehicles, bodies and trailers, and parts</td>\n", "      <td>176.4</td>\n", "      <td>190.5</td>\n", "      <td>8.0</td>\n", "      <td>18.9</td>\n", "      <td>14.1</td>\n", "      <td>0.316453</td>\n", "      <td>0.973404</td>\n", "      <td>0.579234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>211.8</td>\n", "      <td>5.7</td>\n", "      <td>20.6</td>\n", "      <td>11.4</td>\n", "      <td>0.354300</td>\n", "      <td>0.851064</td>\n", "      <td>0.553005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>308.3</td>\n", "      <td>0.1</td>\n", "      <td>3.1</td>\n", "      <td>0.2</td>\n", "      <td>0.525764</td>\n", "      <td>0.553191</td>\n", "      <td>0.536735</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry                             IndustrYDescription  GDP_2023  \\\n", "0      325                               Chemical products     531.8   \n", "1    311FT          Food and beverage and tobacco products     335.2   \n", "2   3361MV  Motor vehicles, bodies and trailers, and parts     176.4   \n", "3      333                                       Machinery     200.4   \n", "4      334                Computer and electronic products     308.1   \n", "\n", "   GDP_2024  Recent_Growth_%  Two_Year_Growth_%  Recent_Increase  GDP_Score  \\\n", "0     575.2              8.2               10.3             43.4   1.000000   \n", "1     350.1              4.4               18.0             14.9   0.600036   \n", "2     190.5              8.0               18.9             14.1   0.316453   \n", "3     211.8              5.7               20.6             11.4   0.354300   \n", "4     308.3              0.1                3.1              0.2   0.525764   \n", "\n", "   GDP_Growth_Score  Economic_Heft_Score  \n", "0          0.984043             0.993617  \n", "1          0.781915             0.672787  \n", "2          0.973404             0.579234  \n", "3          0.851064             0.553005  \n", "4          0.553191             0.536735  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["naics_heft.head()"]}, {"cell_type": "code", "execution_count": 40, "id": "acd8729f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["df_list"]}, {"cell_type": "code", "execution_count": 41, "id": "52437ab8", "metadata": {}, "outputs": [], "source": ["# Iterate through each row in naics_heft\n", "for idx, row in naics_heft.iterrows():\n", "    # Append the current row\n", "    df_list.append(row.to_frame().T)\n", "    \n", "    # Check if the industry code is in common_codes\n", "    industry_code = row['Industry']\n", "    if industry_code in common_codes:\n", "        # Get the constituents for this industry code\n", "        constituents = constituents_grouped.get_group(industry_code)\n", "        gdp_2024 = row['GDP_2024']\n", "        \n", "        # For each constituent, create a new row with specified columns filled\n", "        for _, const_row in constituents.iterrows():\n", "            new_row = pd.Series(index=naics_heft.columns, dtype='object')\n", "            new_row['Industry'] = str(const_row['Constituents code'])\n", "            new_row['IndustrYDescription'] = const_row['Constituents description']\n", "            new_row['GDP_2023'] = const_row['GDP2023 constituents']\n", "            new_row['GDP_2024'] = gdp_2024 * const_row['Ratio']\n", "            #new_row['Ratio'] = const_row['Ratio']  # Add <PERSON><PERSON> from split_bea\n", "            # Other columns remain NaN\n", "            df_list.append(new_row.to_frame().T)\n", "\n"]}, {"cell_type": "code", "execution_count": 43, "id": "7415de05", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>GDP_2023</th>\n", "      <th>GDP_2024</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Two_Year_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>575.2</td>\n", "      <td>8.2</td>\n", "      <td>10.3</td>\n", "      <td>43.4</td>\n", "      <td>1.0</td>\n", "      <td>0.984043</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry IndustrYDescription GDP_2023 GDP_2024 Recent_Growth_%  \\\n", "0      325   Chemical products    531.8    575.2             8.2   \n", "\n", "  Two_Year_Growth_% Recent_Increase GDP_Score GDP_Growth_Score  \\\n", "0              10.3            43.4       1.0         0.984043   \n", "\n", "  Economic_Heft_Score  \n", "0            0.993617  "]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df_list[0]"]}, {"cell_type": "code", "execution_count": 42, "id": "721891fb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>GDP_2023</th>\n", "      <th>GDP_2024</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Two_Year_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3254</td>\n", "      <td>Pharmaceutical and medicine manufacturing</td>\n", "      <td>251.1</td>\n", "      <td>271.592178</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry                        IndustrYDescription GDP_2023    GDP_2024  \\\n", "0     3254  Pharmaceutical and medicine manufacturing    251.1  271.592178   \n", "\n", "  Recent_Growth_% Two_Year_Growth_% Recent_Increase GDP_Score  \\\n", "0             NaN               NaN             NaN       NaN   \n", "\n", "  GDP_Growth_Score Economic_Heft_Score  \n", "0              NaN                 NaN  "]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["df_list[1]"]}, {"cell_type": "code", "execution_count": 44, "id": "708ed855", "metadata": {}, "outputs": [], "source": ["# Concatenate all dataframes in df_list to form the updated dataframe\n", "new_naics_heft = pd.concat(df_list, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 53, "id": "3432e79e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>GDP_2023</th>\n", "      <th>GDP_2024</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>575.2</td>\n", "      <td>8.2</td>\n", "      <td>43.4</td>\n", "      <td>1.0</td>\n", "      <td>0.984043</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3254</td>\n", "      <td>Pharmaceutical and medicine manufacturing</td>\n", "      <td>251.1</td>\n", "      <td>271.592178</td>\n", "      <td>8.160963</td>\n", "      <td>20.492178</td>\n", "      <td>0.467725</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3251</td>\n", "      <td>Basic chemical manufacturing</td>\n", "      <td>120.4</td>\n", "      <td>130.225799</td>\n", "      <td>8.160963</td>\n", "      <td>9.825799</td>\n", "      <td>0.219887</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>325X</td>\n", "      <td>Other chemical manufacturing</td>\n", "      <td>120.9</td>\n", "      <td>130.766604</td>\n", "      <td>8.160963</td>\n", "      <td>9.866604</td>\n", "      <td>0.220835</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3252</td>\n", "      <td>Resin, rubber, and artificial fibers manufactu...</td>\n", "      <td>39.5</td>\n", "      <td>42.72358</td>\n", "      <td>8.160963</td>\n", "      <td>3.22358</td>\n", "      <td>0.066481</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>311FT</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>335.2</td>\n", "      <td>350.1</td>\n", "      <td>4.4</td>\n", "      <td>14.9</td>\n", "      <td>0.600036</td>\n", "      <td>0.781915</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3361MV</td>\n", "      <td>Motor vehicles, bodies and trailers, and parts</td>\n", "      <td>176.4</td>\n", "      <td>190.5</td>\n", "      <td>8.0</td>\n", "      <td>14.1</td>\n", "      <td>0.316453</td>\n", "      <td>0.973404</td>\n", "      <td>0.579234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>211.8</td>\n", "      <td>5.7</td>\n", "      <td>11.4</td>\n", "      <td>0.3543</td>\n", "      <td>0.851064</td>\n", "      <td>0.553005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>33312</td>\n", "      <td>Construction machinery manufacturing</td>\n", "      <td>19.9</td>\n", "      <td>21.032036</td>\n", "      <td>5.688623</td>\n", "      <td>1.132036</td>\n", "      <td>0.028452</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3332OM</td>\n", "      <td>Other machinery</td>\n", "      <td>155.0</td>\n", "      <td>163.817365</td>\n", "      <td>5.688623</td>\n", "      <td>8.817365</td>\n", "      <td>0.278778</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry                                IndustrYDescription GDP_2023  \\\n", "0      325                                  Chemical products    531.8   \n", "1     3254          Pharmaceutical and medicine manufacturing    251.1   \n", "2     3251                       Basic chemical manufacturing    120.4   \n", "3     325X                       Other chemical manufacturing    120.9   \n", "4     3252  Resin, rubber, and artificial fibers manufactu...     39.5   \n", "5    311FT             Food and beverage and tobacco products    335.2   \n", "6   3361MV     Motor vehicles, bodies and trailers, and parts    176.4   \n", "7      333                                          Machinery    200.4   \n", "8    33312               Construction machinery manufacturing     19.9   \n", "9   3332OM                                    Other machinery    155.0   \n", "\n", "     GDP_2024 Recent_Growth_% Recent_Increase GDP_Score GDP_Growth_Score  \\\n", "0       575.2             8.2            43.4       1.0         0.984043   \n", "1  271.592178        8.160963       20.492178  0.467725              NaN   \n", "2  130.225799        8.160963        9.825799  0.219887              NaN   \n", "3  130.766604        8.160963        9.866604  0.220835              NaN   \n", "4    42.72358        8.160963         3.22358  0.066481              NaN   \n", "5       350.1             4.4            14.9  0.600036         0.781915   \n", "6       190.5             8.0            14.1  0.316453         0.973404   \n", "7       211.8             5.7            11.4    0.3543         0.851064   \n", "8   21.032036        5.688623        1.132036  0.028452              NaN   \n", "9  163.817365        5.688623        8.817365  0.278778              NaN   \n", "\n", "  Economic_Heft_Score  \n", "0            0.993617  \n", "1                 NaN  \n", "2                 NaN  \n", "3                 NaN  \n", "4                 NaN  \n", "5            0.672787  \n", "6            0.579234  \n", "7            0.553005  \n", "8                 NaN  \n", "9                 NaN  "]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["new_naics_heft.head(10)"]}, {"cell_type": "code", "execution_count": 46, "id": "cacfb9c5", "metadata": {}, "outputs": [], "source": ["# Drop the Two_Year_Growth_% column\n", "new_naics_heft = new_naics_heft.drop('Two_Year_Growth_%', axis=1)"]}, {"cell_type": "markdown", "id": "440fbd08", "metadata": {}, "source": ["Calculating Recent grwoth percentage"]}, {"cell_type": "code", "execution_count": null, "id": "ab13b52f", "metadata": {}, "outputs": [], "source": ["# Create a mask to identify rows where Recent_Growth_% is NaN\n", "mask = new_naics_heft['Recent_Growth_%'].isna()\n", "\n", "# Calculate Recent_Growth_% for those rows using the formula\n", "new_naics_heft.loc[mask, 'Recent_Growth_%'] = (\n", "    (new_naics_heft.loc[mask, 'GDP_2024'] - new_naics_heft.loc[mask, 'GDP_2023']) /\n", "    new_naics_heft.loc[mask, 'GDP_2023']\n", ") * 100"]}, {"cell_type": "markdown", "id": "e663d237", "metadata": {}, "source": ["Calculate Recent increase"]}, {"cell_type": "code", "execution_count": null, "id": "1aba55e6", "metadata": {}, "outputs": [], "source": ["mask = new_naics_heft['Recent_Increase'].isna()\n", "\n", "new_naics_heft.loc[mask, 'Recent_Increase'] = new_naics_heft.loc[mask, 'GDP_2024'] - new_naics_heft.loc[mask, 'GDP_2023']"]}, {"cell_type": "markdown", "id": "319e9729", "metadata": {}, "source": ["Calculating gdp score"]}, {"cell_type": "code", "execution_count": 52, "id": "420ec8d9", "metadata": {}, "outputs": [], "source": ["# Calculate GDP_Score for rows where it's NaN\n", "mask_gdp =new_naics_heft['GDP_Score'].isna()\n", "min_gdp = new_naics_heft['GDP_2024'].min()\n", "max_gdp = new_naics_heft['GDP_2024'].max()\n", "new_naics_heft.loc[mask_gdp, 'GDP_Score'] = (new_naics_heft.loc[mask_gdp, 'GDP_2024'] - min_gdp) / (max_gdp - min_gdp)"]}, {"cell_type": "markdown", "id": "38c2f1d8", "metadata": {}, "source": ["Calculating GDP growth score"]}, {"cell_type": "code", "execution_count": 54, "id": "c8252e98", "metadata": {}, "outputs": [], "source": ["mask_growth =new_naics_heft['GDP_Growth_Score'].isna()\n", "min_growth = new_naics_heft['Recent_Growth_%'].min()\n", "max_growth = new_naics_heft['Recent_Growth_%'].max()\n", "new_naics_heft.loc[mask_gdp, 'GDP_Growth_Score'] = (new_naics_heft.loc[mask_gdp, 'Recent_Growth_%'] - min_gdp) / (max_gdp - min_gdp)"]}, {"cell_type": "code", "execution_count": 60, "id": "fbf2719d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>GDP_2023</th>\n", "      <th>GDP_2024</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Recent_Increase</th>\n", "      <th>GDP_Score</th>\n", "      <th>GDP_Growth_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>531.8</td>\n", "      <td>575.2</td>\n", "      <td>8.2</td>\n", "      <td>43.4</td>\n", "      <td>1.0</td>\n", "      <td>0.984043</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3254</td>\n", "      <td>Pharmaceutical and medicine manufacturing</td>\n", "      <td>251.1</td>\n", "      <td>271.592178</td>\n", "      <td>8.160963</td>\n", "      <td>20.492178</td>\n", "      <td>0.467725</td>\n", "      <td>0.005887</td>\n", "      <td>0.28299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3251</td>\n", "      <td>Basic chemical manufacturing</td>\n", "      <td>120.4</td>\n", "      <td>130.225799</td>\n", "      <td>8.160963</td>\n", "      <td>9.825799</td>\n", "      <td>0.219887</td>\n", "      <td>0.005887</td>\n", "      <td>0.134287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>325X</td>\n", "      <td>Other chemical manufacturing</td>\n", "      <td>120.9</td>\n", "      <td>130.766604</td>\n", "      <td>8.160963</td>\n", "      <td>9.866604</td>\n", "      <td>0.220835</td>\n", "      <td>0.005887</td>\n", "      <td>0.134856</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3252</td>\n", "      <td>Resin, rubber, and artificial fibers manufactu...</td>\n", "      <td>39.5</td>\n", "      <td>42.72358</td>\n", "      <td>8.160963</td>\n", "      <td>3.22358</td>\n", "      <td>0.066481</td>\n", "      <td>0.005887</td>\n", "      <td>0.042243</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>311FT</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>335.2</td>\n", "      <td>350.1</td>\n", "      <td>4.4</td>\n", "      <td>14.9</td>\n", "      <td>0.600036</td>\n", "      <td>0.781915</td>\n", "      <td>0.672787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3361MV</td>\n", "      <td>Motor vehicles, bodies and trailers, and parts</td>\n", "      <td>176.4</td>\n", "      <td>190.5</td>\n", "      <td>8.0</td>\n", "      <td>14.1</td>\n", "      <td>0.316453</td>\n", "      <td>0.973404</td>\n", "      <td>0.579234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>333</td>\n", "      <td>Machinery</td>\n", "      <td>200.4</td>\n", "      <td>211.8</td>\n", "      <td>5.7</td>\n", "      <td>11.4</td>\n", "      <td>0.3543</td>\n", "      <td>0.851064</td>\n", "      <td>0.553005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>33312</td>\n", "      <td>Construction machinery manufacturing</td>\n", "      <td>19.9</td>\n", "      <td>21.032036</td>\n", "      <td>5.688623</td>\n", "      <td>1.132036</td>\n", "      <td>0.028452</td>\n", "      <td>0.001552</td>\n", "      <td>0.017692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3332OM</td>\n", "      <td>Other machinery</td>\n", "      <td>155.0</td>\n", "      <td>163.817365</td>\n", "      <td>5.688623</td>\n", "      <td>8.817365</td>\n", "      <td>0.278778</td>\n", "      <td>0.001552</td>\n", "      <td>0.167888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>33313</td>\n", "      <td>Mining and oil and gas field machinery manufac...</td>\n", "      <td>6.8</td>\n", "      <td>7.186826</td>\n", "      <td>5.688623</td>\n", "      <td>0.386826</td>\n", "      <td>0.004179</td>\n", "      <td>0.001552</td>\n", "      <td>0.003128</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>33311</td>\n", "      <td>Agricultural implement manufacturing</td>\n", "      <td>18.7</td>\n", "      <td>19.763772</td>\n", "      <td>5.688623</td>\n", "      <td>1.063772</td>\n", "      <td>0.026229</td>\n", "      <td>0.001552</td>\n", "      <td>0.016358</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>308.3</td>\n", "      <td>0.1</td>\n", "      <td>0.2</td>\n", "      <td>0.525764</td>\n", "      <td>0.553191</td>\n", "      <td>0.536735</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>3345</td>\n", "      <td>Navigational, measuring, electromedical, and c...</td>\n", "      <td>137.5</td>\n", "      <td>137.589257</td>\n", "      <td>0.064914</td>\n", "      <td>0.089257</td>\n", "      <td>0.232796</td>\n", "      <td>-0.008307</td>\n", "      <td>0.136355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>3344</td>\n", "      <td>Semiconductor and other electronic component m...</td>\n", "      <td>107.8</td>\n", "      <td>107.869977</td>\n", "      <td>0.064914</td>\n", "      <td>0.069977</td>\n", "      <td>0.180693</td>\n", "      <td>-0.008307</td>\n", "      <td>0.105093</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>334X</td>\n", "      <td>Other computer and electronic product manufact...</td>\n", "      <td>4.8</td>\n", "      <td>4.803116</td>\n", "      <td>0.064914</td>\n", "      <td>0.003116</td>\n", "      <td>0.0</td>\n", "      <td>-0.008307</td>\n", "      <td>-0.003323</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>3341</td>\n", "      <td>Computer and peripheral equipment manufacturing</td>\n", "      <td>32.0</td>\n", "      <td>32.020772</td>\n", "      <td>0.064914</td>\n", "      <td>0.020772</td>\n", "      <td>0.047717</td>\n", "      <td>-0.008307</td>\n", "      <td>0.025307</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>3342</td>\n", "      <td>Communications equipment manufacturing</td>\n", "      <td>25.9</td>\n", "      <td>25.916813</td>\n", "      <td>0.064914</td>\n", "      <td>0.016813</td>\n", "      <td>0.037016</td>\n", "      <td>-0.008307</td>\n", "      <td>0.018887</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>339</td>\n", "      <td>Miscellaneous manufacturing</td>\n", "      <td>116.9</td>\n", "      <td>126.8</td>\n", "      <td>8.5</td>\n", "      <td>9.9</td>\n", "      <td>0.203269</td>\n", "      <td>1.0</td>\n", "      <td>0.521962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>3391</td>\n", "      <td>Medical equipment and supplies manufacturing</td>\n", "      <td>77.7</td>\n", "      <td>84.28024</td>\n", "      <td>8.468777</td>\n", "      <td>6.58024</td>\n", "      <td>0.139337</td>\n", "      <td>0.006427</td>\n", "      <td>0.086173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3399</td>\n", "      <td>Other miscellaneous manufacturing</td>\n", "      <td>39.2</td>\n", "      <td>42.51976</td>\n", "      <td>8.468777</td>\n", "      <td>3.31976</td>\n", "      <td>0.066124</td>\n", "      <td>0.006427</td>\n", "      <td>0.042245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>3364OT</td>\n", "      <td>Other transportation equipment</td>\n", "      <td>190.3</td>\n", "      <td>193.3</td>\n", "      <td>1.6</td>\n", "      <td>3.0</td>\n", "      <td>0.321429</td>\n", "      <td>0.632979</td>\n", "      <td>0.446049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>332</td>\n", "      <td>Fabricated metal products</td>\n", "      <td>188.6</td>\n", "      <td>191.5</td>\n", "      <td>1.5</td>\n", "      <td>2.9</td>\n", "      <td>0.31823</td>\n", "      <td>0.62766</td>\n", "      <td>0.442002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>335</td>\n", "      <td>Electrical equipment, appliances, and components</td>\n", "      <td>76.1</td>\n", "      <td>78.4</td>\n", "      <td>3.0</td>\n", "      <td>2.3</td>\n", "      <td>0.117271</td>\n", "      <td>0.707447</td>\n", "      <td>0.353341</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>326</td>\n", "      <td>Plastics and rubber products</td>\n", "      <td>97.7</td>\n", "      <td>99.2</td>\n", "      <td>1.5</td>\n", "      <td>1.5</td>\n", "      <td>0.154229</td>\n", "      <td>0.62766</td>\n", "      <td>0.343601</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>327</td>\n", "      <td>Nonmetallic mineral products</td>\n", "      <td>76.7</td>\n", "      <td>78.5</td>\n", "      <td>2.3</td>\n", "      <td>1.8</td>\n", "      <td>0.117448</td>\n", "      <td>0.670213</td>\n", "      <td>0.338554</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>32.3</td>\n", "      <td>33.0</td>\n", "      <td>2.2</td>\n", "      <td>0.7</td>\n", "      <td>0.036603</td>\n", "      <td>0.664894</td>\n", "      <td>0.287919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>323</td>\n", "      <td>Printing and related support activities</td>\n", "      <td>42.9</td>\n", "      <td>43.2</td>\n", "      <td>0.7</td>\n", "      <td>0.3</td>\n", "      <td>0.054726</td>\n", "      <td>0.585106</td>\n", "      <td>0.266878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>313TT</td>\n", "      <td>Textile mills and textile product mills</td>\n", "      <td>17.0</td>\n", "      <td>17.3</td>\n", "      <td>1.8</td>\n", "      <td>0.3</td>\n", "      <td>0.008706</td>\n", "      <td>0.643617</td>\n", "      <td>0.262671</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>315AL</td>\n", "      <td>Apparel and leather and allied products</td>\n", "      <td>12.4</td>\n", "      <td>12.4</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.547872</td>\n", "      <td>0.219149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>322</td>\n", "      <td>Paper products</td>\n", "      <td>74.7</td>\n", "      <td>71.6</td>\n", "      <td>-4.1</td>\n", "      <td>-3.1</td>\n", "      <td>0.105188</td>\n", "      <td>0.329787</td>\n", "      <td>0.195028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>324</td>\n", "      <td>Petroleum and coal products</td>\n", "      <td>216.8</td>\n", "      <td>194.4</td>\n", "      <td>-10.3</td>\n", "      <td>-22.4</td>\n", "      <td>0.323383</td>\n", "      <td>0.0</td>\n", "      <td>0.19403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>321</td>\n", "      <td>Wood products</td>\n", "      <td>62.1</td>\n", "      <td>59.2</td>\n", "      <td>-4.7</td>\n", "      <td>-2.9</td>\n", "      <td>0.083156</td>\n", "      <td>0.297872</td>\n", "      <td>0.169042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>331</td>\n", "      <td>Primary metals</td>\n", "      <td>84.0</td>\n", "      <td>78.4</td>\n", "      <td>-6.7</td>\n", "      <td>-5.6</td>\n", "      <td>0.117271</td>\n", "      <td>0.191489</td>\n", "      <td>0.146958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>3313NF</td>\n", "      <td>Nonferrous metal production and processing and...</td>\n", "      <td>38.6</td>\n", "      <td>36.026667</td>\n", "      <td>-6.666667</td>\n", "      <td>-2.573333</td>\n", "      <td>0.05474</td>\n", "      <td>-0.020108</td>\n", "      <td>0.024801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>3311IS</td>\n", "      <td>Iron and steel mills and manufacturing from pu...</td>\n", "      <td>45.4</td>\n", "      <td>42.373333</td>\n", "      <td>-6.666667</td>\n", "      <td>-3.026667</td>\n", "      <td>0.065867</td>\n", "      <td>-0.020108</td>\n", "      <td>0.031477</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Industry                                IndustrYDescription GDP_2023  \\\n", "0       325                                  Chemical products    531.8   \n", "1      3254          Pharmaceutical and medicine manufacturing    251.1   \n", "2      3251                       Basic chemical manufacturing    120.4   \n", "3      325X                       Other chemical manufacturing    120.9   \n", "4      3252  Resin, rubber, and artificial fibers manufactu...     39.5   \n", "5     311FT             Food and beverage and tobacco products    335.2   \n", "6    3361MV     Motor vehicles, bodies and trailers, and parts    176.4   \n", "7       333                                          Machinery    200.4   \n", "8     33312               Construction machinery manufacturing     19.9   \n", "9    3332OM                                    Other machinery    155.0   \n", "10    33313  Mining and oil and gas field machinery manufac...      6.8   \n", "11    33311               Agricultural implement manufacturing     18.7   \n", "12      334                   Computer and electronic products    308.1   \n", "13     3345  Navigational, measuring, electromedical, and c...    137.5   \n", "14     3344  Semiconductor and other electronic component m...    107.8   \n", "15     334X  Other computer and electronic product manufact...      4.8   \n", "16     3341    Computer and peripheral equipment manufacturing     32.0   \n", "17     3342             Communications equipment manufacturing     25.9   \n", "18      339                        Miscellaneous manufacturing    116.9   \n", "19     3391       Medical equipment and supplies manufacturing     77.7   \n", "20     3399                  Other miscellaneous manufacturing     39.2   \n", "21   3364OT                     Other transportation equipment    190.3   \n", "22      332                          Fabricated metal products    188.6   \n", "23      335   Electrical equipment, appliances, and components     76.1   \n", "24      326                       Plastics and rubber products     97.7   \n", "25      327                       Nonmetallic mineral products     76.7   \n", "26      337                     Furniture and related products     32.3   \n", "27      323            Printing and related support activities     42.9   \n", "28    313TT            Textile mills and textile product mills     17.0   \n", "29    315AL            Apparel and leather and allied products     12.4   \n", "30      322                                     Paper products     74.7   \n", "31      324                        Petroleum and coal products    216.8   \n", "32      321                                      Wood products     62.1   \n", "33      331                                     Primary metals     84.0   \n", "34   3313NF  Nonferrous metal production and processing and...     38.6   \n", "35   3311IS  Iron and steel mills and manufacturing from pu...     45.4   \n", "\n", "      GDP_2024 Recent_Growth_% Recent_Increase GDP_Score GDP_Growth_Score  \\\n", "0        575.2             8.2            43.4       1.0         0.984043   \n", "1   271.592178        8.160963       20.492178  0.467725         0.005887   \n", "2   130.225799        8.160963        9.825799  0.219887         0.005887   \n", "3   130.766604        8.160963        9.866604  0.220835         0.005887   \n", "4     42.72358        8.160963         3.22358  0.066481         0.005887   \n", "5        350.1             4.4            14.9  0.600036         0.781915   \n", "6        190.5             8.0            14.1  0.316453         0.973404   \n", "7        211.8             5.7            11.4    0.3543         0.851064   \n", "8    21.032036        5.688623        1.132036  0.028452         0.001552   \n", "9   163.817365        5.688623        8.817365  0.278778         0.001552   \n", "10    7.186826        5.688623        0.386826  0.004179         0.001552   \n", "11   19.763772        5.688623        1.063772  0.026229         0.001552   \n", "12       308.3             0.1             0.2  0.525764         0.553191   \n", "13  137.589257        0.064914        0.089257  0.232796        -0.008307   \n", "14  107.869977        0.064914        0.069977  0.180693        -0.008307   \n", "15    4.803116        0.064914        0.003116       0.0        -0.008307   \n", "16   32.020772        0.064914        0.020772  0.047717        -0.008307   \n", "17   25.916813        0.064914        0.016813  0.037016        -0.008307   \n", "18       126.8             8.5             9.9  0.203269              1.0   \n", "19    84.28024        8.468777         6.58024  0.139337         0.006427   \n", "20    42.51976        8.468777         3.31976  0.066124         0.006427   \n", "21       193.3             1.6             3.0  0.321429         0.632979   \n", "22       191.5             1.5             2.9   0.31823          0.62766   \n", "23        78.4             3.0             2.3  0.117271         0.707447   \n", "24        99.2             1.5             1.5  0.154229          0.62766   \n", "25        78.5             2.3             1.8  0.117448         0.670213   \n", "26        33.0             2.2             0.7  0.036603         0.664894   \n", "27        43.2             0.7             0.3  0.054726         0.585106   \n", "28        17.3             1.8             0.3  0.008706         0.643617   \n", "29        12.4             0.0             0.0       0.0         0.547872   \n", "30        71.6            -4.1            -3.1  0.105188         0.329787   \n", "31       194.4           -10.3           -22.4  0.323383              0.0   \n", "32        59.2            -4.7            -2.9  0.083156         0.297872   \n", "33        78.4            -6.7            -5.6  0.117271         0.191489   \n", "34   36.026667       -6.666667       -2.573333   0.05474        -0.020108   \n", "35   42.373333       -6.666667       -3.026667  0.065867        -0.020108   \n", "\n", "   Economic_Heft_Score  \n", "0             0.993617  \n", "1              0.28299  \n", "2             0.134287  \n", "3             0.134856  \n", "4             0.042243  \n", "5             0.672787  \n", "6             0.579234  \n", "7             0.553005  \n", "8             0.017692  \n", "9             0.167888  \n", "10            0.003128  \n", "11            0.016358  \n", "12            0.536735  \n", "13            0.136355  \n", "14            0.105093  \n", "15           -0.003323  \n", "16            0.025307  \n", "17            0.018887  \n", "18            0.521962  \n", "19            0.086173  \n", "20            0.042245  \n", "21            0.446049  \n", "22            0.442002  \n", "23            0.353341  \n", "24            0.343601  \n", "25            0.338554  \n", "26            0.287919  \n", "27            0.266878  \n", "28            0.262671  \n", "29            0.219149  \n", "30            0.195028  \n", "31             0.19403  \n", "32            0.169042  \n", "33            0.146958  \n", "34            0.024801  \n", "35            0.031477  "]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["new_naics_heft.head(37)"]}, {"cell_type": "markdown", "id": "243c22bd", "metadata": {}, "source": ["Calculating economic heft score (alpha = 0.6)"]}, {"cell_type": "code", "execution_count": 57, "id": "62f452d5", "metadata": {}, "outputs": [], "source": ["mask_growth =new_naics_heft['Economic_Heft_Score'].isna()\n", "new_naics_heft.loc[mask_gdp, 'Economic_Heft_Score'] = (new_naics_heft.loc[mask_gdp, 'GDP_Score'] *0.6) + (new_naics_heft.loc[mask_gdp, 'GDP_Growth_Score'] *0.4)"]}, {"cell_type": "code", "execution_count": 59, "id": "37dc3b1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["36"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_naics_heft)\n", "\n"]}, {"cell_type": "code", "execution_count": 61, "id": "f2f8c5aa", "metadata": {}, "outputs": [], "source": ["new_naics_heft.to_excel('NAICS_Economic_Heft_Scores.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": 1, "id": "b7784ca6", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "6f2459d6", "metadata": {}, "outputs": [], "source": ["comb_scores = pd.read_excel('Combined_NAICS_BEA_Scores_factset_new.xlsx')"]}, {"cell_type": "code", "execution_count": 4, "id": "b7f0ee5c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>2410</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  Factset_code  \\\n", "0                          Animal food manufacturing          2225   \n", "1                          Grain and oilseed milling          2225   \n", "2      Sugar and confectionery product manufacturing          2410   \n", "3  Fruit and vegetable preserving and specialty f...          2410   \n", "4                        Dairy product manufacturing          2415   \n", "\n", "                      Factset_title  Fixed_Assets_Score  Economic_Heft_Score  \\\n", "0  Agricultural Commodities/Milling            0.498296                  0.5   \n", "1  Agricultural Commodities/Milling            0.498296                  0.5   \n", "2  Agricultural Commodities/Milling            0.498296                  0.5   \n", "3             Food: Specialty/Candy            0.498296                  0.5   \n", "4             Food: Meat/Fish/Dairy            0.498296                  0.5   \n", "\n", "   Import_Intensity_Score  Employment_Score  \n", "0                0.115238          0.385857  \n", "1                0.159188          0.438410  \n", "2                0.153765          0.354900  \n", "3                0.167181          0.458670  \n", "4                0.117253          0.458316  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["comb_scores.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "a5e78f92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Final_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>0.374848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>0.398973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>2410</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>0.376740</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>0.406037</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>0.393466</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  Factset_code  \\\n", "0                          Animal food manufacturing          2225   \n", "1                          Grain and oilseed milling          2225   \n", "2      Sugar and confectionery product manufacturing          2410   \n", "3  Fruit and vegetable preserving and specialty f...          2410   \n", "4                        Dairy product manufacturing          2415   \n", "\n", "                      Factset_title  Fixed_Assets_Score  Economic_Heft_Score  \\\n", "0  Agricultural Commodities/Milling            0.498296                  0.5   \n", "1  Agricultural Commodities/Milling            0.498296                  0.5   \n", "2  Agricultural Commodities/Milling            0.498296                  0.5   \n", "3             Food: Specialty/Candy            0.498296                  0.5   \n", "4             Food: Meat/Fish/Dairy            0.498296                  0.5   \n", "\n", "   Import_Intensity_Score  Employment_Score  Final_Score  \n", "0                0.115238          0.385857     0.374848  \n", "1                0.159188          0.438410     0.398973  \n", "2                0.153765          0.354900     0.376740  \n", "3                0.167181          0.458670     0.406037  \n", "4                0.117253          0.458316     0.393466  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["comb_scores['Final_Score'] = comb_scores[['Fixed_Assets_Score', 'Economic_Heft_Score', 'Import_Intensity_Score', 'Employment_Score']].mean(axis=1)\n", "\n", "comb_scores.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "00aa2036", "metadata": {}, "outputs": [], "source": ["comb_scores.to_excel('Combined_scores_finalppt.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "9e7ee11a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}