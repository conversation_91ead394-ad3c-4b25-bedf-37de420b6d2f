{"cells": [{"cell_type": "code", "execution_count": 1, "id": "65de3752", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Load the spreadsheet\n", "df = pd.read_excel(\"Combined_NAICS_BEA_Scores_new.xlsx\")\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "46d981d5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M)  Fixed_Assets_Score  Economic_Heft_Score  \\\n", "0                        CAPG311S            0.498296                  0.5   \n", "1                        CAPG311S            0.498296                  0.5   \n", "2                        CAPG311S            0.498296                  0.5   \n", "3                        CAPG311S            0.498296                  0.5   \n", "4                        CAPG311S            0.498296                  0.5   \n", "\n", "   Import_Intensity_Score  Employment_Score  \n", "0                0.115238          0.385857  \n", "1                0.159188          0.438410  \n", "2                0.153765          0.354900  \n", "3                0.167181          0.458670  \n", "4                0.117253          0.458316  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "2a13b2fb", "metadata": {}, "outputs": [], "source": ["# Add new columns by copying values and replacing the last 'S' with 'A'\n", "df[\"SeriesID Industry production (A)\"] = df[\"Series ID Industry Production (M)\"].str[:-1] + 'A'\n", "df[\"SeriesID capacity utilization(A)\"] = df[\"Series ID Capacity Utilization(M)\"].str[:-1] + 'A'\n", "df[\"SeriesID Industrial capacity(A)\"] = df[\"Industrial Cpacity series ID(M)\"].str[:-1] + 'A'"]}, {"cell_type": "code", "execution_count": 4, "id": "29778b6d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M)  Fixed_Assets_Score  Economic_Heft_Score  \\\n", "0                        CAPG311S            0.498296                  0.5   \n", "1                        CAPG311S            0.498296                  0.5   \n", "2                        CAPG311S            0.498296                  0.5   \n", "3                        CAPG311S            0.498296                  0.5   \n", "4                        CAPG311S            0.498296                  0.5   \n", "\n", "   Import_Intensity_Score  Employment_Score SeriesID Industry production (A)  \\\n", "0                0.115238          0.385857                         IPG3111A   \n", "1                0.159188          0.438410                         IPG3112A   \n", "2                0.153765          0.354900                         IPG3113A   \n", "3                0.167181          0.458670                         IPG3114A   \n", "4                0.117253          0.458316                         IPG3115A   \n", "\n", "  SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \n", "0                      CAPUTLG311A                        CAPG311A  \n", "1                      CAPUTLG311A                        CAPG311A  \n", "2                      CAPUTLG311A                        CAPG311A  \n", "3                      CAPUTLG311A                        CAPG311A  \n", "4                      CAPUTLG311A                        CAPG311A  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "fa17b4bd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>SeriesID Industrial Capacity(M)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>3371</td>\n", "      <td>Household and institutional furniture and kitc...</td>\n", "      <td>IPN3371S</td>\n", "      <td>CAPUTLG337S</td>\n", "      <td>CAPG337S</td>\n", "      <td>0.370733</td>\n", "      <td>0.287919</td>\n", "      <td>0.232060</td>\n", "      <td>0.338024</td>\n", "      <td>IPN3371A</td>\n", "      <td>CAPUTLG337A</td>\n", "      <td>CAPG337A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>3372</td>\n", "      <td>Office furniture (including fixtures) and othe...</td>\n", "      <td>IPG337S</td>\n", "      <td>CAPUTLG337S</td>\n", "      <td>CAPG337S</td>\n", "      <td>0.370733</td>\n", "      <td>0.287919</td>\n", "      <td>0.137959</td>\n", "      <td>0.223960</td>\n", "      <td>IPG337A</td>\n", "      <td>CAPUTLG337A</td>\n", "      <td>CAPG337A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>3379</td>\n", "      <td>FURNITURE RELATED PRODUCTS, NESOI</td>\n", "      <td>IPG337S</td>\n", "      <td>CAPUTLG337S</td>\n", "      <td>CAPG337S</td>\n", "      <td>0.370733</td>\n", "      <td>0.287919</td>\n", "      <td>0.121508</td>\n", "      <td>0.500000</td>\n", "      <td>IPG337A</td>\n", "      <td>CAPUTLG337A</td>\n", "      <td>CAPG337A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>3391</td>\n", "      <td>Medical equipment and supplies manufacturing</td>\n", "      <td>3391</td>\n", "      <td>Medical equipment and supplies manufacturing</td>\n", "      <td>IPN3391S</td>\n", "      <td>CAPUTLG339S</td>\n", "      <td>CAPG339S</td>\n", "      <td>0.500000</td>\n", "      <td>0.086173</td>\n", "      <td>0.262585</td>\n", "      <td>0.540576</td>\n", "      <td>IPN3391A</td>\n", "      <td>CAPUTLG339A</td>\n", "      <td>CAPG339A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>3399</td>\n", "      <td>Other miscellaneous manufacturing</td>\n", "      <td>3399</td>\n", "      <td>Other miscellaneous manufacturing</td>\n", "      <td>IPG339S</td>\n", "      <td>CAPUTLG339S</td>\n", "      <td>CAPG339S</td>\n", "      <td>0.500000</td>\n", "      <td>0.042245</td>\n", "      <td>0.442334</td>\n", "      <td>0.463972</td>\n", "      <td>IPG339A</td>\n", "      <td>CAPUTLG339A</td>\n", "      <td>CAPG339A</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>86 rows × 14 columns</p>\n", "</div>"], "text/plain": ["   BEA_code                      BEA_industry_description  NAICS_code  \\\n", "0      3110                            Food manufacturing        3111   \n", "1      3110                            Food manufacturing        3112   \n", "2      3110                            Food manufacturing        3113   \n", "3      3110                            Food manufacturing        3114   \n", "4      3110                            Food manufacturing        3115   \n", "..      ...                                           ...         ...   \n", "81      337                Furniture and related products        3371   \n", "82      337                Furniture and related products        3372   \n", "83      337                Furniture and related products        3379   \n", "84     3391  Medical equipment and supplies manufacturing        3391   \n", "85     3399             Other miscellaneous manufacturing        3399   \n", "\n", "                           NAICS_industry_description  \\\n", "0                           Animal food manufacturing   \n", "1                           Grain and oilseed milling   \n", "2       Sugar and confectionery product manufacturing   \n", "3   Fruit and vegetable preserving and specialty f...   \n", "4                         Dairy product manufacturing   \n", "..                                                ...   \n", "81  Household and institutional furniture and kitc...   \n", "82  Office furniture (including fixtures) and othe...   \n", "83                  FURNITURE RELATED PRODUCTS, NESOI   \n", "84       Medical equipment and supplies manufacturing   \n", "85                  Other miscellaneous manufacturing   \n", "\n", "   Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                           IPG3111S                       CAPUTLG311S   \n", "1                           IPG3112S                       CAPUTLG311S   \n", "2                           IPG3113S                       CAPUTLG311S   \n", "3                           IPG3114S                       CAPUTLG311S   \n", "4                           IPG3115S                       CAPUTLG311S   \n", "..                               ...                               ...   \n", "81                          IPN3371S                       CAPUTLG337S   \n", "82                           IPG337S                       CAPUTLG337S   \n", "83                           IPG337S                       CAPUTLG337S   \n", "84                          IPN3391S                       CAPUTLG339S   \n", "85                           IPG339S                       CAPUTLG339S   \n", "\n", "   SeriesID Industrial Capacity(M)  Fixed_Assets_Score  Economic_Heft_Score  \\\n", "0                         CAPG311S            0.498296             0.500000   \n", "1                         CAPG311S            0.498296             0.500000   \n", "2                         CAPG311S            0.498296             0.500000   \n", "3                         CAPG311S            0.498296             0.500000   \n", "4                         CAPG311S            0.498296             0.500000   \n", "..                             ...                 ...                  ...   \n", "81                        CAPG337S            0.370733             0.287919   \n", "82                        CAPG337S            0.370733             0.287919   \n", "83                        CAPG337S            0.370733             0.287919   \n", "84                        CAPG339S            0.500000             0.086173   \n", "85                        CAPG339S            0.500000             0.042245   \n", "\n", "    Import_Intensity_Score  Employment_Score SeriesID Industry production (A)  \\\n", "0                 0.115238          0.385857                         IPG3111A   \n", "1                 0.159188          0.438410                         IPG3112A   \n", "2                 0.153765          0.354900                         IPG3113A   \n", "3                 0.167181          0.458670                         IPG3114A   \n", "4                 0.117253          0.458316                         IPG3115A   \n", "..                     ...               ...                              ...   \n", "81                0.232060          0.338024                         IPN3371A   \n", "82                0.137959          0.223960                          IPG337A   \n", "83                0.121508          0.500000                          IPG337A   \n", "84                0.262585          0.540576                         IPN3391A   \n", "85                0.442334          0.463972                          IPG339A   \n", "\n", "   SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \n", "0                       CAPUTLG311A                        CAPG311A  \n", "1                       CAPUTLG311A                        CAPG311A  \n", "2                       CAPUTLG311A                        CAPG311A  \n", "3                       CAPUTLG311A                        CAPG311A  \n", "4                       CAPUTLG311A                        CAPG311A  \n", "..                              ...                             ...  \n", "81                      CAPUTLG337A                        CAPG337A  \n", "82                      CAPUTLG337A                        CAPG337A  \n", "83                      CAPUTLG337A                        CAPG337A  \n", "84                      CAPUTLG339A                        CAPG339A  \n", "85                      CAPUTLG339A                        CAPG339A  \n", "\n", "[86 rows x 14 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.rename(columns={'Industrial Cpacity series ID(M)': 'SeriesID Industrial Capacity(M)'})"]}, {"cell_type": "code", "execution_count": 7, "id": "45b471a0", "metadata": {}, "outputs": [], "source": ["df.to_excel(\"Combined_Scores_NAICS_based_with_fred_ids.xlsx\", index=False)"]}, {"cell_type": "markdown", "id": "057ca434", "metadata": {}, "source": ["## Adding data of series into the sheet"]}, {"cell_type": "code", "execution_count": 8, "id": "0466157f", "metadata": {}, "outputs": [], "source": ["fred_api_key = \"51c0e7ec1da836c20ce6d40a9e50f5d1\""]}, {"cell_type": "code", "execution_count": 9, "id": "d6e595e1", "metadata": {}, "outputs": [], "source": ["from fredapi import Fred\n", "fred = Fred(api_key=fred_api_key)"]}, {"cell_type": "code", "execution_count": 12, "id": "93b88876", "metadata": {}, "outputs": [], "source": ["series = fred.get_series_latest_release('IPG3111A')\n", "df = pd.DataFrame(series, columns=['value'])\n", "df.index.name = 'date'"]}, {"cell_type": "code", "execution_count": 13, "id": "7be9f084", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>value</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1972-01-01</th>\n", "      <td>34.2669</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1973-01-01</th>\n", "      <td>33.3376</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1974-01-01</th>\n", "      <td>34.4881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1975-01-01</th>\n", "      <td>34.9208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1976-01-01</th>\n", "      <td>37.0799</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              value\n", "date               \n", "1972-01-01  34.2669\n", "1973-01-01  33.3376\n", "1974-01-01  34.4881\n", "1975-01-01  34.9208\n", "1976-01-01  37.0799"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 48, "id": "62607bda", "metadata": {}, "outputs": [], "source": ["data = fred.get_series('CAPG311A',\n", "                               observation_start='2024-01-01')"]}, {"cell_type": "code", "execution_count": 49, "id": "6f9f8a92", "metadata": {}, "outputs": [{"data": {"text/plain": ["2024-01-01    120.1574\n", "dtype: float64"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 44, "id": "cd838c82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["from pandas import DatetimeIndex\n", "\n", "is_datetime_index = isinstance(data.index, DatetimeIndex)\n", "print(is_datetime_index)  # True means it's already a datetime index"]}, {"cell_type": "code", "execution_count": 22, "id": "aa1d8aa6", "metadata": {}, "outputs": [], "source": ["val_2023 = data[data.index.year == 2023].squeeze()"]}, {"cell_type": "code", "execution_count": 23, "id": "2b2912c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(101.8116)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["val_2023"]}, {"cell_type": "code", "execution_count": 24, "id": "0ee1656c", "metadata": {}, "outputs": [], "source": ["if hasattr(val_2023, 'iloc'):\n", "            val_2023 = val_2023.iloc[-1]"]}, {"cell_type": "code", "execution_count": 25, "id": "38bd4179", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(101.8116)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["val_2023"]}, {"cell_type": "code", "execution_count": 18, "id": "ae67e9c6", "metadata": {}, "outputs": [], "source": ["scoresheet_df = pd.read_excel(\"Combined_Scores_NAICS_based_with_fred_ids.xlsx\")"]}, {"cell_type": "code", "execution_count": 19, "id": "970f903d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M) SeriesID Industry production (A)  \\\n", "0                        CAPG311S                         IPG3111A   \n", "1                        CAPG311S                         IPG3112A   \n", "2                        CAPG311S                         IPG3113A   \n", "3                        CAPG311S                         IPG3114A   \n", "4                        CAPG311S                         IPG3115A   \n", "\n", "  SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \\\n", "0                      CAPUTLG311A                        CAPG311A   \n", "1                      CAPUTLG311A                        CAPG311A   \n", "2                      CAPUTLG311A                        CAPG311A   \n", "3                      CAPUTLG311A                        CAPG311A   \n", "4                      CAPUTLG311A                        CAPG311A   \n", "\n", "   Fixed_Assets_Score  Economic_Heft_Score  Import_Intensity_Score  \\\n", "0            0.498296                  0.5                0.115238   \n", "1            0.498296                  0.5                0.159188   \n", "2            0.498296                  0.5                0.153765   \n", "3            0.498296                  0.5                0.167181   \n", "4            0.498296                  0.5                0.117253   \n", "\n", "   Employment_Score  \n", "0          0.385857  \n", "1          0.438410  \n", "2          0.354900  \n", "3          0.458670  \n", "4          0.458316  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["scoresheet_df.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "8cf4f7a6", "metadata": {}, "outputs": [], "source": ["scoresheet_df['Industry_production2023'] = pd.NA\n", "scoresheet_df['Industry_production2024'] = pd.NA"]}, {"cell_type": "code", "execution_count": 32, "id": "cef8140a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IPG3111A\n", "101.8116\n", "98.1873\n", "IPG3112A\n", "103.3364\n", "106.236\n", "IPG3113A\n", "101.9761\n", "103.3032\n", "IPG3114A\n", "95.8364\n", "95.615\n", "IPG3115A\n", "105.8241\n", "103.824\n", "IPG3116A\n", "103.7052\n", "103.9325\n", "IPG311A\n", "103.1211\n", "102.0379\n", "IPN3118A\n", "103.079\n", "104.9696\n", "IPG3119A\n", "109.9063\n", "104.0773\n", "IPG3121A\n", "112.0181\n", "109.8861\n", "IPG3122A\n", "72.5727\n", "66.8097\n", "IPG3131A\n", "63.0469\n", "66.4386\n", "IPG3132A\n", "78.8533\n", "77.8981\n", "IPG3133A\n", "82.0557\n", "82.6749\n", "IPG3141A\n", "74.5748\n", "69.6771\n", "IPG3149A\n", "100.3781\n", "93.8436\n", "IPG315A\n", "80.3132\n", "67.7072\n", "IPG316A\n", "110.1151\n", "108.3637\n", "IPN3211A\n", "97.3048\n", "95.1149\n", "IPG3212A\n", "76.9922\n", "76.3095\n", "IPG3219A\n", "101.779\n", "104.0593\n", "IPG3221A\n", "78.2779\n", "80.2799\n", "IPG3222A\n", "93.0419\n", "93.4836\n", "IPG323A\n", "83.3475\n", "84.0911\n", "IPG324A\n", "90.6986\n", "93.3581\n", "IPG3251A\n", "93.2759\n", "91.7198\n", "IPG3252A\n", "87.2515\n", "89.231\n", "IPG3253A\n", "112.4511\n", "112.9923\n", "IPG3254A\n", "Failed to fetch IPG3254A: Bad Request.  The series does not exist.\n", "IPG3255A\n", "100.4414\n", "97.3093\n", "IPG3256A\n", "100.962\n", "106.5706\n", "IPG325A\n", "103.7034\n", "105.705\n", "IPG3261A\n", "103.2969\n", "101.9648\n", "IPG3262A\n", "100.0543\n", "96.9574\n", "IPG3271A\n", "98.5424\n", "93.3914\n", "IPG3272A\n", "85.2083\n", "83.1704\n", "IPG3273A\n", "107.6063\n", "98.2003\n", "IPG3274A\n", "103.6794\n", "102.017\n", "IPG3279A\n", "125.3208\n", "126.8503\n", "IPG331A\n", "94.8438\n", "93.2749\n", "IPG3313A\n", "91.1978\n", "93.2895\n", "IPG3314A\n", "108.448\n", "109.1593\n", "IPG3315A\n", "80.9844\n", "80.2882\n", "IPN3321A\n", "82.493\n", "81.83\n", "IPN3322A\n", "99.4147\n", "98.6119\n", "IPN3323A\n", "98.9524\n", "103.3699\n", "IPG332A\n", "99.5775\n", "98.6683\n", "IPG3325A\n", "93.7375\n", "90.2177\n", "IPN3326A\n", "91.3946\n", "88.6092\n", "IPG3327A\n", "100.7227\n", "95.4563\n", "IPN3328A\n", "108.8276\n", "103.1247\n", "IPG3329A\n", "108.6582\n", "106.3726\n", "IPG3331A\n", "119.4244\n", "111.8609\n", "IPG3332A\n", "100.4106\n", "93.5817\n", "IPG333A\n", "100.9098\n", "98.6336\n", "IPG3334A\n", "76.1175\n", "82.4784\n", "IPG3335A\n", "94.6552\n", "91.1392\n", "IPG3336A\n", "89.6931\n", "87.323\n", "IPG3341A\n", "156.3919\n", "163.7614\n", "IPG3342A\n", "174.0604\n", "196.4269\n", "IPG3343A\n", "131.5938\n", "143.1164\n", "IPG3344A\n", "131.0959\n", "140.2432\n", "IPG3345A\n", "95.7063\n", "96.6327\n", "IPG334A\n", "114.3372\n", "119.0827\n", "IPG3351A\n", "91.7272\n", "76.817\n", "IPG3352A\n", "Failed to fetch IPG3352A: Bad Request.  The series does not exist.\n", "IPG3353A\n", "114.7592\n", "120.8719\n", "IPG3359A\n", "101.5469\n", "104.7251\n", "IPG3361A\n", "116.8666\n", "115.5088\n", "IPG3362A\n", "92.3377\n", "85.1828\n", "IPG3363A\n", "99.7503\n", "100.1575\n", "IPG3364A\n", "84.174\n", "82.2367\n", "IPN3365A\n", "72.2988\n", "76.6528\n", "IPG3366A\n", "114.9767\n", "108.4085\n", "IPN3369A\n", "98.4493\n", "105.9574\n", "IPN3371A\n", "83.4931\n", "79.8555\n", "IPG337A\n", "81.1798\n", "76.5918\n", "IPN3391A\n", "112.2631\n", "109.3774\n", "IPG339A\n", "107.6954\n", "105.4818\n"]}], "source": ["for series_id in scoresheet_df['SeriesID Industry production (A)'].dropna().unique():\n", "    print(series_id)\n", "    try:\n", "        # Fetch annual data for 2023 and 2024\n", "        data = fred.get_series(series_id,\n", "                               observation_start='2023-01-01',\n", "                               observation_end='2024-12-31')\n", "        \n", "        val_2023 = data[data.index.year == 2023].squeeze()\n", "        print(val_2023)\n", "        val_2024 = data[data.index.year == 2024].squeeze()\n", "        print(val_2024)\n", "        \n", "        mask = scoresheet_df['SeriesID Industry production (A)'] == series_id\n", "        scoresheet_df.loc[mask, 'Industry_production2023'] = val_2023\n", "        scoresheet_df.loc[mask, 'Industry_production2024'] = val_2024\n", "    except Exception as e:\n", "        print(f\"Failed to fetch {series_id}: {e}\")\n", "        "]}, {"cell_type": "markdown", "id": "5b73956b", "metadata": {}, "source": ["Adding missing values for two columns"]}, {"cell_type": "code", "execution_count": 36, "id": "4401c326", "metadata": {}, "outputs": [], "source": ["scoresheet_df.loc[\n", "    scoresheet_df['SeriesID Industry production (A)'] == 'IPG3254A', \n", "    ['Industry_production2023', 'Industry_production2024']\n", "] = [103.7034, 105.7050]\n", "\n", "scoresheet_df.loc[\n", "    scoresheet_df['SeriesID Industry production (A)'] == 'IPG3352A', \n", "    ['Industry_production2023', 'Industry_production2024']\n", "] = [104.5038, 105.4233]\n"]}, {"cell_type": "code", "execution_count": 39, "id": "59e5c8a6", "metadata": {}, "outputs": [{"data": {"text/plain": ["BEA_code                             0\n", "BEA_industry_description             0\n", "NAICS_code                           0\n", "NAICS_industry_description           0\n", "Series ID Industry Production (M)    0\n", "Series ID Capacity Utilization(M)    0\n", "Industrial Cpacity series ID(M)      0\n", "SeriesID Industry production (A)     0\n", "SeriesID capacity utilization(A)     0\n", "SeriesID Industrial capacity(A)      0\n", "Fixed_Assets_Score                   0\n", "Economic_Heft_Score                  0\n", "Import_Intensity_Score               0\n", "Employment_Score                     0\n", "Industry_production2023              0\n", "Industry_production2024              0\n", "dtype: int64"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["scoresheet_df.isna().sum()\n", "\n"]}, {"cell_type": "markdown", "id": "adc49092", "metadata": {}, "source": ["```\n", "for checking date time index\n", "from pandas import DatetimeIndex\n", "\n", "is_datetime_index = isinstance(data.index, DatetimeIndex)\n", "print(is_datetime_index)  # True means it's already a datetime index\n"]}, {"cell_type": "markdown", "id": "59848d91", "metadata": {}, "source": ["## Adding data for industrial capacity"]}, {"cell_type": "code", "execution_count": 40, "id": "8b32cd6a", "metadata": {}, "outputs": [], "source": ["scoresheet_df['Industry_capacity2023'] = pd.NA\n", "scoresheet_df['Industry_capacity2024'] = pd.NA"]}, {"cell_type": "code", "execution_count": 41, "id": "e05e4a45", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Industry_production2023</th>\n", "      <th>Industry_production2024</th>\n", "      <th>Industry_capacity2023</th>\n", "      <th>Industry_capacity2024</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>101.8116</td>\n", "      <td>98.1873</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>103.3364</td>\n", "      <td>106.236</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>101.9761</td>\n", "      <td>103.3032</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>95.8364</td>\n", "      <td>95.615</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>105.8241</td>\n", "      <td>103.824</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M) SeriesID Industry production (A)  \\\n", "0                        CAPG311S                         IPG3111A   \n", "1                        CAPG311S                         IPG3112A   \n", "2                        CAPG311S                         IPG3113A   \n", "3                        CAPG311S                         IPG3114A   \n", "4                        CAPG311S                         IPG3115A   \n", "\n", "  SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \\\n", "0                      CAPUTLG311A                        CAPG311A   \n", "1                      CAPUTLG311A                        CAPG311A   \n", "2                      CAPUTLG311A                        CAPG311A   \n", "3                      CAPUTLG311A                        CAPG311A   \n", "4                      CAPUTLG311A                        CAPG311A   \n", "\n", "   Fixed_Assets_Score  Economic_Heft_Score  Import_Intensity_Score  \\\n", "0            0.498296                  0.5                0.115238   \n", "1            0.498296                  0.5                0.159188   \n", "2            0.498296                  0.5                0.153765   \n", "3            0.498296                  0.5                0.167181   \n", "4            0.498296                  0.5                0.117253   \n", "\n", "   Employment_Score Industry_production2023 Industry_production2024  \\\n", "0          0.385857                101.8116                 98.1873   \n", "1          0.438410                103.3364                 106.236   \n", "2          0.354900                101.9761                103.3032   \n", "3          0.458670                 95.8364                  95.615   \n", "4          0.458316                105.8241                 103.824   \n", "\n", "  Industry_capacity2023 Industry_capacity2024  \n", "0                  <NA>                  <NA>  \n", "1                  <NA>                  <NA>  \n", "2                  <NA>                  <NA>  \n", "3                  <NA>                  <NA>  \n", "4                  <NA>                  <NA>  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["scoresheet_df.head()"]}, {"cell_type": "code", "execution_count": 45, "id": "2d00dce3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAPG311A\n", "120.8364\n", "120.1574\n", "CAPG312A\n", "136.7861\n", "137.6166\n", "CAPG313A\n", "115.6206\n", "110.1729\n", "CAPG314A\n", "118.6152\n", "116.4666\n", "CAPG315A\n", "121.5322\n", "118.7791\n", "CAPG316A\n", "143.024\n", "148.6457\n", "CAPG321A\n", "122.9365\n", "123.8929\n", "CAPG322A\n", "106.141\n", "105.4153\n", "CAPG323A\n", "111.6762\n", "112.413\n", "CAPG324A\n", "101.563\n", "102.7498\n", "CAPG325A\n", "134.4934\n", "138.7414\n", "CAPG326A\n", "131.7669\n", "136.7652\n", "CAPG327A\n", "128.7383\n", "128.5897\n", "CAPG331A\n", "133.6263\n", "134.4232\n", "CAPG332A\n", "129.4609\n", "129.4389\n", "CAPG333A\n", "121.4478\n", "121.7151\n", "CAPG3341A\n", "204.7627\n", "209.9347\n", "CAPG3342A\n", "241.2686\n", "270.246\n", "CAPG334A\n", "149.916\n", "158.3492\n", "CAPG3344A\n", "153.9813\n", "174.8267\n", "CAPG335A\n", "129.318\n", "130.4587\n", "CAPG3361T3A\n", "145.1184\n", "148.1806\n", "CAPG336A\n", "136.3497\n", "138.1077\n", "CAPG337A\n", "113.0489\n", "110.2361\n", "CAPG339A\n", "130.019\n", "137.4206\n"]}], "source": ["for series_id in scoresheet_df['SeriesID Industrial capacity(A)'].dropna().unique():\n", "    print(series_id)\n", "    try:\n", "        # Fetch annual data for 2023 and 2024\n", "        data = fred.get_series(series_id,\n", "                               observation_start='2023-01-01',\n", "                               observation_end='2024-12-31')\n", "        \n", "        val_2023 = data[data.index.year == 2023].squeeze()\n", "        print(val_2023)\n", "        val_2024 = data[data.index.year == 2024].squeeze()\n", "        print(val_2024)\n", "        \n", "        mask = scoresheet_df['SeriesID Industrial capacity(A)'] == series_id\n", "        scoresheet_df.loc[mask, 'Industry_capacity2023'] = val_2023\n", "        scoresheet_df.loc[mask, 'Industry_capacity2024'] = val_2024\n", "    except Exception as e:\n", "        print(f\"Failed to fetch {series_id}: {e}\")\n", "        "]}, {"cell_type": "markdown", "id": "6c7d4e35", "metadata": {}, "source": ["Changing the codes of manually added data"]}, {"cell_type": "code", "execution_count": 50, "id": "7e5838ff", "metadata": {}, "outputs": [], "source": ["scoresheet_df['SeriesID Industry production (A)'] = scoresheet_df['SeriesID Industry production (A)'].replace({\n", "    'IPG3254A': 'IPG325A',\n", "    'IPG3352A': 'IPG335A'\n", "})\n"]}, {"cell_type": "markdown", "id": "5fa3a53e", "metadata": {}, "source": ["## Adding capacity utlization percentage"]}, {"cell_type": "code", "execution_count": 53, "id": "8cc280e8", "metadata": {}, "outputs": [], "source": ["scoresheet_df['Capacity_utilization2024'] = pd.NA\n"]}, {"cell_type": "code", "execution_count": 54, "id": "353a8585", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Industry_production2023</th>\n", "      <th>Industry_production2024</th>\n", "      <th>Industry_capacity2023</th>\n", "      <th>Industry_capacity2024</th>\n", "      <th>Capacity_utilization2024</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>101.8116</td>\n", "      <td>98.1873</td>\n", "      <td>120.8364</td>\n", "      <td>120.1574</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>103.3364</td>\n", "      <td>106.236</td>\n", "      <td>120.8364</td>\n", "      <td>120.1574</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>101.9761</td>\n", "      <td>103.3032</td>\n", "      <td>120.8364</td>\n", "      <td>120.1574</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>95.8364</td>\n", "      <td>95.615</td>\n", "      <td>120.8364</td>\n", "      <td>120.1574</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>105.8241</td>\n", "      <td>103.824</td>\n", "      <td>120.8364</td>\n", "      <td>120.1574</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M) SeriesID Industry production (A)  \\\n", "0                        CAPG311S                         IPG3111A   \n", "1                        CAPG311S                         IPG3112A   \n", "2                        CAPG311S                         IPG3113A   \n", "3                        CAPG311S                         IPG3114A   \n", "4                        CAPG311S                         IPG3115A   \n", "\n", "  SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \\\n", "0                      CAPUTLG311A                        CAPG311A   \n", "1                      CAPUTLG311A                        CAPG311A   \n", "2                      CAPUTLG311A                        CAPG311A   \n", "3                      CAPUTLG311A                        CAPG311A   \n", "4                      CAPUTLG311A                        CAPG311A   \n", "\n", "   Fixed_Assets_Score  Economic_Heft_Score  Import_Intensity_Score  \\\n", "0            0.498296                  0.5                0.115238   \n", "1            0.498296                  0.5                0.159188   \n", "2            0.498296                  0.5                0.153765   \n", "3            0.498296                  0.5                0.167181   \n", "4            0.498296                  0.5                0.117253   \n", "\n", "   Employment_Score Industry_production2023 Industry_production2024  \\\n", "0          0.385857                101.8116                 98.1873   \n", "1          0.438410                103.3364                 106.236   \n", "2          0.354900                101.9761                103.3032   \n", "3          0.458670                 95.8364                  95.615   \n", "4          0.458316                105.8241                 103.824   \n", "\n", "  Industry_capacity2023 Industry_capacity2024 Capacity_utilization2024  \n", "0              120.8364              120.1574                     <NA>  \n", "1              120.8364              120.1574                     <NA>  \n", "2              120.8364              120.1574                     <NA>  \n", "3              120.8364              120.1574                     <NA>  \n", "4              120.8364              120.1574                     <NA>  "]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["scoresheet_df.head()"]}, {"cell_type": "code", "execution_count": 55, "id": "e45c0ce5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAPUTLG311A\n", "2024-01-01    84.9162\n", "dtype: float64\n", "84.9162\n", "CAPUTLG312A\n", "2024-01-01    65.3897\n", "dtype: float64\n", "65.3897\n", "CAPUTLG313A\n", "2024-01-01    69.8004\n", "dtype: float64\n", "69.8004\n", "CAPUTLG314A\n", "2024-01-01    69.4107\n", "dtype: float64\n", "69.4107\n", "CAPUTLG315A\n", "2024-01-01    57.1\n", "dtype: float64\n", "57.1\n", "CAPUTLG316A\n", "2024-01-01    73.1231\n", "dtype: float64\n", "73.1231\n", "CAPUTLG321A\n", "2024-01-01    76.1746\n", "dtype: float64\n", "76.1746\n", "CAPUTLG322A\n", "2024-01-01    82.4668\n", "dtype: float64\n", "82.4668\n", "CAPUTLG323A\n", "2024-01-01    74.7026\n", "dtype: float64\n", "74.7026\n", "CAPUTLG324A\n", "2024-01-01    90.8853\n", "dtype: float64\n", "90.8853\n", "CAPUTLG325A\n", "2024-01-01    76.2143\n", "dtype: float64\n", "76.2143\n", "CAPUTLG326A\n", "2024-01-01    73.9231\n", "dtype: float64\n", "73.9231\n", "CAPUTLG327A\n", "2024-01-01    78.2776\n", "dtype: float64\n", "78.2776\n", "CAPUTLG3311A2A\n", "2024-01-01    70.4132\n", "dtype: float64\n", "70.4132\n", "CAPUTLG331A\n", "2024-01-01    69.4134\n", "dtype: float64\n", "69.4134\n", "CAPUTLG332A\n", "2024-01-01    76.2314\n", "dtype: float64\n", "76.2314\n", "CAPUTLG333A\n", "2024-01-01    81.0329\n", "dtype: float64\n", "81.0329\n", "CAPUTLG3341A\n", "2024-01-01    77.8028\n", "dtype: float64\n", "77.8028\n", "CAPUTLG3342A\n", "2024-01-01    72.8843\n", "dtype: float64\n", "72.8843\n", "CAPUTLG334A\n", "2024-01-01    75.2326\n", "dtype: float64\n", "75.2326\n", "CAPUTLG3344A\n", "2024-01-01    80.3372\n", "dtype: float64\n", "80.3372\n", "CAPUTLG335A\n", "2024-01-01    80.8094\n", "dtype: float64\n", "80.8094\n", "CAPUTLG3361T3A\n", "2024-01-01    70.6868\n", "dtype: float64\n", "70.6868\n", "CAPUTLG3364T9A\n", "2024-01-01    68.3927\n", "dtype: float64\n", "68.3927\n", "CAPUTLG337A\n", "2024-01-01    69.4244\n", "dtype: float64\n", "69.4244\n", "CAPUTLG339A\n", "2024-01-01    76.8227\n", "dtype: float64\n", "76.8227\n"]}], "source": ["for series_id in scoresheet_df['SeriesID capacity utilization(A)'].dropna().unique():\n", "    print(series_id)\n", "    try:\n", "        # Fetch annual data for 2023 and 2024\n", "        data = fred.get_series(series_id,\n", "                               observation_start='2024-01-01')\n", "        print(data)\n", "        val_2024 = data[data.index.year == 2024].squeeze()\n", "        print(val_2024)\n", "        \n", "        mask = scoresheet_df['SeriesID capacity utilization(A)'] == series_id\n", "        scoresheet_df.loc[mask, 'Capacity_utilization2024'] = val_2024\n", "    except Exception as e:\n", "        print(f\"Failed to fetch {series_id}: {e}\")\n", "        "]}, {"cell_type": "code", "execution_count": 56, "id": "4133979f", "metadata": {}, "outputs": [{"data": {"text/plain": ["BEA_code                             0\n", "BEA_industry_description             0\n", "NAICS_code                           0\n", "NAICS_industry_description           0\n", "Series ID Industry Production (M)    0\n", "Series ID Capacity Utilization(M)    0\n", "Industrial Cpacity series ID(M)      0\n", "SeriesID Industry production (A)     0\n", "SeriesID capacity utilization(A)     0\n", "SeriesID Industrial capacity(A)      0\n", "Fixed_Assets_Score                   0\n", "Economic_Heft_Score                  0\n", "Import_Intensity_Score               0\n", "Employment_Score                     0\n", "Industry_production2023              0\n", "Industry_production2024              0\n", "Industry_capacity2023                0\n", "Industry_capacity2024                0\n", "Capacity_utilization2024             0\n", "dtype: int64"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["scoresheet_df.isna().sum()\n", "\n"]}, {"cell_type": "code", "execution_count": 58, "id": "8bf07505", "metadata": {}, "outputs": [], "source": ["scoresheet_df.to_excel(\"Combined_Scores_NAICS_based_with_fred_indexes.xlsx\", index=False, float_format='%.3f')"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}