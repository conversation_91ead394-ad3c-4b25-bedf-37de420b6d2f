{"cells": [{"cell_type": "markdown", "id": "e2d4f67f", "metadata": {}, "source": ["# DataWEB"]}, {"cell_type": "markdown", "id": "94e05306", "metadata": {}, "source": ["Imports for consumption<br>\n", "Exports for domestic <br>\n", "getting excel sheets via api"]}, {"cell_type": "markdown", "id": "75764ec7", "metadata": {}, "source": ["## Exports domestic"]}, {"cell_type": "markdown", "id": "23fdeaac", "metadata": {}, "source": ["Query Payload need to be created by inspecting the payload on the user interface"]}, {"cell_type": "code", "execution_count": 3, "id": "82c30344", "metadata": {}, "outputs": [], "source": ["saved_query = \"\"\"{\"savedQueryType\":\"\",\"savedQueryName\":\"\",\"savedQueryDesc\":\"\",\"isOwner\":true,\"runMonthly\":false,\"unitConversion\":\"0\",\"manualConversions\":[],\"reportOptions\":{\"tradeType\":\"Export\",\"classificationSystem\":\"NAIC\"},\"searchOptions\":{\"MiscGroup\":{\"districts\":{\"aggregation\":\"Aggregate District\",\"districtGroups\":{\"userGroups\":[]},\"districts\":[],\"districtsExpanded\":[{\"name\":\"All Districts\",\"value\":\"all\"}],\"districtsSelectType\":\"all\"},\"importPrograms\":{\"aggregation\":null,\"importPrograms\":[],\"programsSelectType\":\"all\"},\"extImportPrograms\":{\"aggregation\":\"Aggregate CSC\",\"extImportPrograms\":[],\"extImportProgramsExpanded\":[],\"programsSelectType\":\"all\"},\"provisionCodes\":{\"aggregation\":\"Aggregate RPCODE\",\"provisionCodesSelectType\":\"all\",\"rateProvisionCodes\":[],\"rateProvisionCodesExpanded\":[],\"rateProvisionGroups\":{\"systemGroups\":[]}}},\"commodities\":{\"aggregation\":\"Break Out Commodities\",\"codeDisplayFormat\":\"YES\",\"commodities\":[],\"commoditiesExpanded\":[],\"commoditiesManual\":\"\",\"commodityGroups\":{\"systemGroups\":[],\"userGroups\":[]},\"commoditySelectType\":\"all\",\"granularity\":\"4\",\"groupGranularity\":null,\"searchGranularity\":null,\"showHTSValidDetails\":\"\"},\"componentSettings\":{\"dataToReport\":[\"FAS_VALUE\"],\"scale\":\"1\",\"timeframeSelectType\":\"fullYears\",\"years\":[\"2025\",\"2024\",\"2023\"],\"startDate\":null,\"endDate\":null,\"startMonth\":null,\"endMonth\":null,\"yearsTimeline\":\"Annual\"},\"countries\":{\"aggregation\":\"Aggregate Countries\",\"countries\":[],\"countriesExpanded\":[{\"name\":\"All Countries\",\"value\":\"all\"}],\"countriesSelectType\":\"all\",\"countryGroups\":{\"systemGroups\":[],\"userGroups\":[]}}},\"sortingAndDataFormat\":{\"DataSort\":{\"columnOrder\":[\"NAICS4 & DESCRIPTION\"],\"fullColumnOrder\":[{\"hasChildren\":false,\"name\":\"NAICS4 & DESCRIPTION\",\"value\":\"NAICS4 & DESCRIPTION\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"}],\"sortOrder\":[{\"sortData\":\"NAICS4 & DESCRIPTION\",\"orderBy\":\"asc\",\"year\":\"\"}]},\"reportCustomizations\":{\"exportCombineTables\":false,\"totalRecords\":\"20000\",\"exportRawData\":false}},\"deletedCountryUserGroups\":[],\"deletedCommodityUserGroups\":[],\"deletedDistrictUserGroups\":[]}\"\"\""]}, {"cell_type": "code", "execution_count": 4, "id": "823b385e", "metadata": {}, "outputs": [], "source": ["import json\n", "basicQuery = json.loads(saved_query)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b9e6c108", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'savedQueryType': '',\n", " 'savedQueryName': '',\n", " 'savedQueryDesc': '',\n", " 'isOwner': True,\n", " 'runMonthly': <PERSON><PERSON><PERSON>,\n", " 'unitConversion': '0',\n", " 'manualConversions': [],\n", " 'reportOptions': {'tradeType': 'Export', 'classificationSystem': 'NAIC'},\n", " 'searchOptions': {'MiscGroup': {'districts': {'aggregation': 'Aggregate District',\n", "    'districtGroups': {'userGroups': []},\n", "    'districts': [],\n", "    'districtsExpanded': [{'name': 'All Districts', 'value': 'all'}],\n", "    'districtsSelectType': 'all'},\n", "   'importPrograms': {'aggregation': None,\n", "    'importPrograms': [],\n", "    'programsSelectType': 'all'},\n", "   'extImportPrograms': {'aggregation': 'Aggregate CSC',\n", "    'extImportPrograms': [],\n", "    'extImportProgramsExpanded': [],\n", "    'programsSelectType': 'all'},\n", "   'provisionCodes': {'aggregation': 'Aggregate RPCODE',\n", "    'provisionCodesSelectType': 'all',\n", "    'rateProvisionCodes': [],\n", "    'rateProvisionCodesExpanded': [],\n", "    'rateProvisionGroups': {'systemGroups': []}}},\n", "  'commodities': {'aggregation': 'Break Out Commodities',\n", "   'codeDisplayFormat': 'YES',\n", "   'commodities': [],\n", "   'commoditiesExpanded': [],\n", "   'commoditiesManual': '',\n", "   'commodityGroups': {'systemGroups': [], 'userGroups': []},\n", "   'commoditySelectType': 'all',\n", "   'granularity': '4',\n", "   'groupGranularity': None,\n", "   'searchGranularity': None,\n", "   'showHTSValidDetails': ''},\n", "  'componentSettings': {'dataToReport': ['FAS_VALUE'],\n", "   'scale': '1',\n", "   'timeframeSelectType': 'fullYears',\n", "   'years': ['2025', '2024', '2023'],\n", "   'startDate': None,\n", "   'endDate': None,\n", "   'startMonth': None,\n", "   'endMonth': None,\n", "   'yearsTimeline': 'Annual'},\n", "  'countries': {'aggregation': 'Aggregate Countries',\n", "   'countries': [],\n", "   'countriesExpanded': [{'name': 'All Countries', 'value': 'all'}],\n", "   'countriesSelectType': 'all',\n", "   'countryGroups': {'systemGroups': [], 'userGroups': []}}},\n", " 'sortingAndDataFormat': {'DataSort': {'columnOrder': ['NAICS4 & DESCRIPTION'],\n", "   'fullColumnOrder': [{'hasChildren': False,\n", "     'name': 'NAICS4 & DESCRIPTION',\n", "     'value': 'NAICS4 & DESCRIPTION',\n", "     'classificationSystem': '',\n", "     'groupUUID': '',\n", "     'items': [],\n", "     'tradeType': ''}],\n", "   'sortOrder': [{'sortData': 'NAICS4 & DESCRIPTION',\n", "     'orderBy': 'asc',\n", "     'year': ''}]},\n", "  'reportCustomizations': {'exportCombineTables': False,\n", "   'totalRecords': '20000',\n", "   'exportRawData': False}},\n", " 'deletedCountryUserGroups': [],\n", " 'deletedCommodityUserGroups': [],\n", " 'deletedDistrictUserGroups': []}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["basicQuery"]}, {"cell_type": "code", "execution_count": 9, "id": "fbb14069", "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas\n", "token = 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDAwOTg5IiwianRpIjoiYjM2ZDlkZTAtOTRmMS00MjljLTk1YTItN2VmMDlmZmUxYWZmIiwiaXNzIjoiZGF0YXdlYiIsImlhdCI6MTc0OTc5ODMyOSwiZXhwIjoxNzUxMDA3OTI5fQ.jzG-bZScQFdpR0TEQoVT6kdQ_uSNzVnA66DRBNGe6aJbq9GhbfqwsoG-kvnSHcTrTQKEyhhyAMwUCFJym_Basg'\n", "baseUrl = 'https://datawebws.usitc.gov/dataweb'\n", "headers = {\n", "    \"Content-Type\": \"application/json; charset=utf-8\", \n", "    \"Authorization\": \"Bearer \" + token\n", "}\n", "\n", "requests.packages.urllib3.disable_warnings() "]}, {"cell_type": "code", "execution_count": 16, "id": "a633273c", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 10, "id": "d1a6f9ba", "metadata": {}, "outputs": [], "source": ["response = requests.post(baseUrl+'/api/v2/report2/runReport', \n", "                         headers=headers, json=basicQuery, verify=False)"]}, {"cell_type": "code", "execution_count": 7, "id": "462135e4", "metadata": {}, "outputs": [], "source": ["def getData(dataGroups):\n", "    data = []\n", "    for row in dataGroups:\n", "        rowData = []\n", "        for field in row['rowEntries']:\n", "            rowData.append(field['value'])\n", "        data.append(rowData)\n", "    return data"]}, {"cell_type": "code", "execution_count": 8, "id": "92ef9ba1", "metadata": {}, "outputs": [], "source": ["def getColumns(columnGroups, prevCols = None):\n", "    if prevCols is None:\n", "        columns = []\n", "    else:\n", "        columns = prevCols\n", "    for group in columnGroups:\n", "        if isinstance(group, dict) and 'columns' in group.keys():\n", "            getColumns(group['columns'], columns)\n", "        elif isinstance(group, dict) and 'label' in group.keys():\n", "            columns.append(group['label'])\n", "        elif isinstance(group, list):\n", "            getColumns(group, columns)\n", "    return columns"]}, {"cell_type": "code", "execution_count": 15, "id": "f7042bf7", "metadata": {}, "outputs": [], "source": ["columns = getColumns(response.json()['dto']['tables'][0]['column_groups'])\n", "data = getData(response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew'])"]}, {"cell_type": "code", "execution_count": 17, "id": "c843dadf", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(data, columns = columns)"]}, {"cell_type": "code", "execution_count": 18, "id": "d1712fc7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>2023</th>\n", "      <th>2024</th>\n", "      <th>2025</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>50,914,772,421</td>\n", "      <td>48,934,445,070</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1112</td>\n", "      <td>VEGETABLES &amp; MELONS</td>\n", "      <td>3,296,884,168</td>\n", "      <td>3,552,139,595</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1113</td>\n", "      <td>FRUITS &amp; TREE NUTS</td>\n", "      <td>12,247,991,620</td>\n", "      <td>13,666,486,369</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1114</td>\n", "      <td>MUSHROOMS, NURSERY &amp; RELATED PRODUCTS</td>\n", "      <td>566,576,865</td>\n", "      <td>554,673,934</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1119</td>\n", "      <td>OTHER AGRICULTURAL PRODUCTS</td>\n", "      <td>9,770,686,771</td>\n", "      <td>8,589,324,159</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number                            Description            2023  \\\n", "0        1111                      OILSEEDS & GRAINS  50,914,772,421   \n", "1        1112                    VEGETABLES & MELONS   3,296,884,168   \n", "2        1113                     FRUITS & TREE NUTS  12,247,991,620   \n", "3        1114  MUSHROOMS, NURSERY & RELATED PRODUCTS     566,576,865   \n", "4        1119            OTHER AGRICULTURAL PRODUCTS   9,770,686,771   \n", "\n", "             2024 2025  \n", "0  48,934,445,070    0  \n", "1   3,552,139,595    0  \n", "2  13,666,486,369    0  \n", "3     554,673,934    0  \n", "4   8,589,324,159    0  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "353ae661", "metadata": {}, "outputs": [], "source": ["### combined function\n", "\"\"\"\n", "def printQueryResults(headers, requestData):\n", "    response = requests.post(baseUrl+\"/api/v2/report2/runReport\", \n", "                            headers=headers, json=requestData, verify=False)\n", "\n", "    columns = getColumns(response.json()['dto']['tables'][0]['column_groups'])\n", "\n", "    data = getData(response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew'])\n", "\n", "    df = pd.DataFrame(data, columns = columns)\n", "\n", "    return df\n", "\"\"\""]}, {"cell_type": "markdown", "id": "64fed153", "metadata": {}, "source": ["we are able to get the same excel sheet"]}, {"cell_type": "markdown", "id": "9eccc664", "metadata": {}, "source": ["## Imports for consumption"]}, {"cell_type": "code", "execution_count": 20, "id": "1f125787", "metadata": {}, "outputs": [], "source": ["saved_query = \"\"\"{\"savedQueryType\":\"\",\"savedQueryName\":\"\",\"savedQueryDesc\":\"\",\"isOwner\":true,\"runMonthly\":false,\"unitConversion\":\"0\",\"manualConversions\":[],\"reportOptions\":{\"tradeType\":\"Import\",\"classificationSystem\":\"NAIC\"},\"searchOptions\":{\"MiscGroup\":{\"districts\":{\"aggregation\":\"Aggregate District\",\"districtGroups\":{\"userGroups\":[]},\"districts\":[],\"districtsExpanded\":[{\"name\":\"All Districts\",\"value\":\"all\"}],\"districtsSelectType\":\"all\"},\"importPrograms\":{\"aggregation\":null,\"importPrograms\":[],\"programsSelectType\":\"all\"},\"extImportPrograms\":{\"aggregation\":\"Aggregate CSC\",\"extImportPrograms\":[],\"extImportProgramsExpanded\":[],\"programsSelectType\":\"all\"},\"provisionCodes\":{\"aggregation\":\"Aggregate RPCODE\",\"provisionCodesSelectType\":\"all\",\"rateProvisionCodes\":[],\"rateProvisionCodesExpanded\":[],\"rateProvisionGroups\":{\"systemGroups\":[]}}},\"commodities\":{\"aggregation\":\"Break Out Commodities\",\"codeDisplayFormat\":\"YES\",\"commodities\":[],\"commoditiesExpanded\":[],\"commoditiesManual\":\"\",\"commodityGroups\":{\"systemGroups\":[],\"userGroups\":[]},\"commoditySelectType\":\"all\",\"granularity\":\"4\",\"groupGranularity\":null,\"searchGranularity\":null,\"showHTSValidDetails\":\"\"},\"componentSettings\":{\"dataToReport\":[\"CONS_CUSTOMS_VALUE\"],\"scale\":\"1\",\"timeframeSelectType\":\"fullYears\",\"years\":[\"2025\",\"2024\",\"2023\"],\"startDate\":null,\"endDate\":null,\"startMonth\":null,\"endMonth\":null,\"yearsTimeline\":\"Year-to-Date\"},\"countries\":{\"aggregation\":\"Aggregate Countries\",\"countries\":[],\"countriesExpanded\":[{\"name\":\"All Countries\",\"value\":\"all\"}],\"countriesSelectType\":\"all\",\"countryGroups\":{\"systemGroups\":[],\"userGroups\":[]}}},\"sortingAndDataFormat\":{\"DataSort\":{\"columnOrder\":[\"NAICS4 & DESCRIPTION\"],\"fullColumnOrder\":[{\"hasChildren\":false,\"name\":\"NAICS4 & DESCRIPTION\",\"value\":\"NAICS4 & DESCRIPTION\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"}],\"sortOrder\":[{\"sortData\":\"NAICS4 & DESCRIPTION\",\"orderBy\":\"asc\",\"year\":\"\"}]},\"reportCustomizations\":{\"exportCombineTables\":false,\"totalRecords\":\"20000\",\"exportRawData\":true}},\"deletedCountryUserGroups\":[],\"deletedCommodityUserGroups\":[],\"deletedDistrictUserGroups\":[]}\"\"\""]}, {"cell_type": "code", "execution_count": 21, "id": "b58677b2", "metadata": {}, "outputs": [], "source": ["user_query = json.loads(saved_query)"]}, {"cell_type": "code", "execution_count": 22, "id": "f6255ebb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'savedQueryType': '',\n", " 'savedQueryName': '',\n", " 'savedQueryDesc': '',\n", " 'isOwner': True,\n", " 'runMonthly': <PERSON><PERSON><PERSON>,\n", " 'unitConversion': '0',\n", " 'manualConversions': [],\n", " 'reportOptions': {'tradeType': 'Import', 'classificationSystem': 'NAIC'},\n", " 'searchOptions': {'MiscGroup': {'districts': {'aggregation': 'Aggregate District',\n", "    'districtGroups': {'userGroups': []},\n", "    'districts': [],\n", "    'districtsExpanded': [{'name': 'All Districts', 'value': 'all'}],\n", "    'districtsSelectType': 'all'},\n", "   'importPrograms': {'aggregation': None,\n", "    'importPrograms': [],\n", "    'programsSelectType': 'all'},\n", "   'extImportPrograms': {'aggregation': 'Aggregate CSC',\n", "    'extImportPrograms': [],\n", "    'extImportProgramsExpanded': [],\n", "    'programsSelectType': 'all'},\n", "   'provisionCodes': {'aggregation': 'Aggregate RPCODE',\n", "    'provisionCodesSelectType': 'all',\n", "    'rateProvisionCodes': [],\n", "    'rateProvisionCodesExpanded': [],\n", "    'rateProvisionGroups': {'systemGroups': []}}},\n", "  'commodities': {'aggregation': 'Break Out Commodities',\n", "   'codeDisplayFormat': 'YES',\n", "   'commodities': [],\n", "   'commoditiesExpanded': [],\n", "   'commoditiesManual': '',\n", "   'commodityGroups': {'systemGroups': [], 'userGroups': []},\n", "   'commoditySelectType': 'all',\n", "   'granularity': '4',\n", "   'groupGranularity': None,\n", "   'searchGranularity': None,\n", "   'showHTSValidDetails': ''},\n", "  'componentSettings': {'dataToReport': ['CONS_CUSTOMS_VALUE'],\n", "   'scale': '1',\n", "   'timeframeSelectType': 'fullYears',\n", "   'years': ['2025', '2024', '2023'],\n", "   'startDate': None,\n", "   'endDate': None,\n", "   'startMonth': None,\n", "   'endMonth': None,\n", "   'yearsTimeline': 'Year-to-Date'},\n", "  'countries': {'aggregation': 'Aggregate Countries',\n", "   'countries': [],\n", "   'countriesExpanded': [{'name': 'All Countries', 'value': 'all'}],\n", "   'countriesSelectType': 'all',\n", "   'countryGroups': {'systemGroups': [], 'userGroups': []}}},\n", " 'sortingAndDataFormat': {'DataSort': {'columnOrder': ['NAICS4 & DESCRIPTION'],\n", "   'fullColumnOrder': [{'hasChildren': False,\n", "     'name': 'NAICS4 & DESCRIPTION',\n", "     'value': 'NAICS4 & DESCRIPTION',\n", "     'classificationSystem': '',\n", "     'groupUUID': '',\n", "     'items': [],\n", "     'tradeType': ''}],\n", "   'sortOrder': [{'sortData': 'NAICS4 & DESCRIPTION',\n", "     'orderBy': 'asc',\n", "     'year': ''}]},\n", "  'reportCustomizations': {'exportCombineTables': False,\n", "   'totalRecords': '20000',\n", "   'exportRawData': True}},\n", " 'deletedCountryUserGroups': [],\n", " 'deletedCommodityUserGroups': [],\n", " 'deletedDistrictUserGroups': []}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["user_query"]}, {"cell_type": "code", "execution_count": 23, "id": "6843e205", "metadata": {}, "outputs": [], "source": ["response = requests.post(baseUrl+'/api/v2/report2/runReport', \n", "                         headers=headers, json=user_query, verify=False)"]}, {"cell_type": "code", "execution_count": 25, "id": "5696dbd4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>2023</th>\n", "      <th>2024</th>\n", "      <th>2025</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>4,207,635,847</td>\n", "      <td>3,409,279,572</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1112</td>\n", "      <td>VEGETABLES &amp; MELONS</td>\n", "      <td>9,373,166,879</td>\n", "      <td>9,955,760,415</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1113</td>\n", "      <td>FRUITS &amp; TREE NUTS</td>\n", "      <td>25,979,611,337</td>\n", "      <td>28,571,831,736</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1114</td>\n", "      <td>MUSHROOMS, NURSERY &amp; RELATED PRODUCTS</td>\n", "      <td>7,468,657,756</td>\n", "      <td>8,168,430,958</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1119</td>\n", "      <td>OTHER AGRICULTURAL PRODUCTS</td>\n", "      <td>1,986,723,590</td>\n", "      <td>2,187,025,273</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number                            Description            2023  \\\n", "0        1111                      OILSEEDS & GRAINS   4,207,635,847   \n", "1        1112                    VEGETABLES & MELONS   9,373,166,879   \n", "2        1113                     FRUITS & TREE NUTS  25,979,611,337   \n", "3        1114  MUSHROOMS, NURSERY & RELATED PRODUCTS   7,468,657,756   \n", "4        1119            OTHER AGRICULTURAL PRODUCTS   1,986,723,590   \n", "\n", "             2024 2025  \n", "0   3,409,279,572    0  \n", "1   9,955,760,415    0  \n", "2  28,571,831,736    0  \n", "3   8,168,430,958    0  \n", "4   2,187,025,273    0  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["columns = getColumns(response.json()['dto']['tables'][0]['column_groups'])\n", "data = getData(response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew'])\n", "\n", "df = pd.DataFrame(data, columns = columns)\n", "\n", "df.head()"]}, {"cell_type": "markdown", "id": "a676869c", "metadata": {}, "source": ["This is the first excel sheet"]}, {"cell_type": "code", "execution_count": 26, "id": "7d223961", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NAIC Number', 'Description', '2023_Year_to_date', '2024_Year_to_date', '2025_Year_to_date']\n"]}], "source": ["columns = getColumns(response.json()['dto']['tables'][1]['column_groups'])\n", "print(columns)"]}, {"cell_type": "code", "execution_count": 27, "id": "4616c06d", "metadata": {}, "outputs": [], "source": ["data = getData(response.json()['dto']['tables'][1]['row_groups'][0]['rowsNew'])"]}, {"cell_type": "code", "execution_count": 29, "id": "51831142", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>2023_Year_to_date</th>\n", "      <th>2024_Year_to_date</th>\n", "      <th>2025_Year_to_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>1,477,929,900</td>\n", "      <td>1,260,186,806</td>\n", "      <td>1,176,324,836</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1112</td>\n", "      <td>VEGETABLES &amp; MELONS</td>\n", "      <td>4,028,732,928</td>\n", "      <td>4,349,069,760</td>\n", "      <td>3,661,586,357</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1113</td>\n", "      <td>FRUITS &amp; TREE NUTS</td>\n", "      <td>10,009,989,362</td>\n", "      <td>10,504,468,666</td>\n", "      <td>13,555,135,542</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1114</td>\n", "      <td>MUSHROOMS, NURSERY &amp; RELATED PRODUCTS</td>\n", "      <td>2,614,690,363</td>\n", "      <td>2,908,435,588</td>\n", "      <td>2,798,269,908</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1119</td>\n", "      <td>OTHER AGRICULTURAL PRODUCTS</td>\n", "      <td>671,003,214</td>\n", "      <td>656,602,157</td>\n", "      <td>799,416,260</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number                            Description 2023_Year_to_date  \\\n", "0        1111                      OILSEEDS & GRAINS     1,477,929,900   \n", "1        1112                    VEGETABLES & MELONS     4,028,732,928   \n", "2        1113                     FRUITS & TREE NUTS    10,009,989,362   \n", "3        1114  MUSHROOMS, NURSERY & RELATED PRODUCTS     2,614,690,363   \n", "4        1119            OTHER AGRICULTURAL PRODUCTS       671,003,214   \n", "\n", "  2024_Year_to_date 2025_Year_to_date  \n", "0     1,260,186,806     1,176,324,836  \n", "1     4,349,069,760     3,661,586,357  \n", "2    10,504,468,666    13,555,135,542  \n", "3     2,908,435,588     2,798,269,908  \n", "4       656,602,157       799,416,260  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 = pd.DataFrame(data, columns = columns)\n", "\n", "df2.head()"]}, {"cell_type": "markdown", "id": "f3052cd1", "metadata": {}, "source": ["This is second excel file"]}, {"cell_type": "markdown", "id": "51f0219f", "metadata": {}, "source": ["# BEA DATA"]}, {"cell_type": "markdown", "id": "9d0775ee", "metadata": {}, "source": ["## GDP by Industry (Quarterly)"]}, {"cell_type": "code", "execution_count": 1, "id": "5bb8e4e1", "metadata": {}, "outputs": [], "source": ["import beaapi"]}, {"cell_type": "code", "execution_count": 3, "id": "7f95ac86", "metadata": {}, "outputs": [], "source": ["beakey = '0CB6FB71-8009-4FF4-B57F-184021F1A352'"]}, {"cell_type": "code", "execution_count": 32, "id": "4a91c9ca", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ParameterName</th>\n", "      <th>ParameterDataType</th>\n", "      <th>ParameterDescription</th>\n", "      <th>ParameterIsRequiredFlag</th>\n", "      <th>ParameterDefaultValue</th>\n", "      <th>MultipleAcceptedFlag</th>\n", "      <th>AllValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Frequency</td>\n", "      <td>string</td>\n", "      <td>A - Annual, Q-Quarterly</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Industry</td>\n", "      <td>string</td>\n", "      <td>List of industries to retrieve (ALL for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TableID</td>\n", "      <td>integer</td>\n", "      <td>The unique GDP by Industry table identifier (A...</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Year</td>\n", "      <td>integer</td>\n", "      <td>List of year(s) of data to retrieve (ALL for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ParameterName ParameterDataType  \\\n", "0     Frequency            string   \n", "1      Industry            string   \n", "2       TableID           integer   \n", "3          Year           integer   \n", "\n", "                                ParameterDescription  ParameterIsRequiredFlag  \\\n", "0                            A - Annual, Q-Quarterly                        1   \n", "1       List of industries to retrieve (ALL for All)                        1   \n", "2  The unique GDP by Industry table identifier (A...                        1   \n", "3  List of year(s) of data to retrieve (ALL for All)                        1   \n", "\n", "  ParameterDefaultValue  MultipleAcceptedFlag AllValue  \n", "0                                           1      ALL  \n", "1                                           1      ALL  \n", "2                                           1      ALL  \n", "3                                           1      ALL  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_params = beaapi.get_parameter_list(beakey, 'GDPbyIndustry')\n", "display(list_of_params)"]}, {"cell_type": "code", "execution_count": 35, "id": "f50347b6", "metadata": {}, "outputs": [], "source": ["list_of_params.to_csv('bea_params.csv', index=False)"]}, {"cell_type": "code", "execution_count": 66, "id": "c279fd9a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Key</th>\n", "      <th>Desc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Value Added by Industry (A) (Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>Value added by Industry as a Percentage of Gro...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>Components of Value Added by Industry (A)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7</td>\n", "      <td>Components of Value Added by Industry as a Per...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8</td>\n", "      <td>Chain-Type Quantity Indexes for Value Added by...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>9</td>\n", "      <td>Percent Changes in Chain-Type Quantity Indexes...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>10</td>\n", "      <td>Real Value Added by Industry (A) (Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>11</td>\n", "      <td>Chain-Type Price Indexes for Value Added by In...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>12</td>\n", "      <td>Percent Changes in Chain-Type Price Indexes fo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>13</td>\n", "      <td>Contributions to Percent Change in Real Gross ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>14</td>\n", "      <td>Contributions to Percent Change in the Chain-T...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>15</td>\n", "      <td>Gross Output by Industry (A) (Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>16</td>\n", "      <td>Chain-Type Quantity Indexes for Gross Output b...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>17</td>\n", "      <td>Percent Changes in Chain-Type Quantity Indexes...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>18</td>\n", "      <td>Chain-Type Price Indexes for Gross Output by I...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>19</td>\n", "      <td>Percent Changes in Chain-Type Price Indexes fo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>20</td>\n", "      <td>Intermediate Inputs by Industry (A) (Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>21</td>\n", "      <td>Chain-Type Quantity Indexes for Intermediate I...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>22</td>\n", "      <td>Percent Changes in Chain-Type Quantity Indexes...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>23</td>\n", "      <td>Chain-Type Price Indexes for Intermediate Inpu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>24</td>\n", "      <td>Percent Changes in Chain-Type Price Indexes fo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>25</td>\n", "      <td>Composition of Gross Output by Industry (A)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>26</td>\n", "      <td>Shares of Gross Output by Industry (A)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>29</td>\n", "      <td>Contributions to Percent Changes in Chain-Type...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>30</td>\n", "      <td>Contributions to Percent Changes in Chain-Type...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>31</td>\n", "      <td>Chain-Type Quantity Indexes for Energy Inputs ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>32</td>\n", "      <td>Contributions to Percent Change by Industry in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>33</td>\n", "      <td>Chain-Type Price Indexes for Energy Inputs by ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>34</td>\n", "      <td>Contributions to Percent Change by Industry in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>35</td>\n", "      <td>Chain-Type Quantity Indexes for Materials Inpu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>36</td>\n", "      <td>Contributions to Percent Change by Industry in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>37</td>\n", "      <td>Chain-Type Price Indexes for Materials Inputs ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>38</td>\n", "      <td>Contributions to Percent Change by Industry in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>39</td>\n", "      <td>Chain-Type Quantity Indexes for Purchased Serv...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>40</td>\n", "      <td>Contributions to Percent Change by Industry in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>41</td>\n", "      <td>Chain-Type Price Indexes for Purchased Service...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>42</td>\n", "      <td>Contributions to Percent Change by Industry in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>208</td>\n", "      <td>Real Gross Output by Industry (A) (Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>209</td>\n", "      <td>Real Intermediate Inputs by Industry (A) (Q)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Key                                               Desc\n", "0     1                    Value Added by Industry (A) (Q)\n", "1     5  Value added by Industry as a Percentage of Gro...\n", "2     6          Components of Value Added by Industry (A)\n", "3     7  Components of Value Added by Industry as a Per...\n", "4     8  Chain-Type Quantity Indexes for Value Added by...\n", "5     9  Percent Changes in Chain-Type Quantity Indexes...\n", "6    10               Real Value Added by Industry (A) (Q)\n", "7    11  Chain-Type Price Indexes for Value Added by In...\n", "8    12  Percent Changes in Chain-Type Price Indexes fo...\n", "9    13  Contributions to Percent Change in Real Gross ...\n", "10   14  Contributions to Percent Change in the Chain-T...\n", "11   15                   Gross Output by Industry (A) (Q)\n", "12   16  Chain-Type Quantity Indexes for Gross Output b...\n", "13   17  Percent Changes in Chain-Type Quantity Indexes...\n", "14   18  Chain-Type Price Indexes for Gross Output by I...\n", "15   19  Percent Changes in Chain-Type Price Indexes fo...\n", "16   20            Intermediate Inputs by Industry (A) (Q)\n", "17   21  Chain-Type Quantity Indexes for Intermediate I...\n", "18   22  Percent Changes in Chain-Type Quantity Indexes...\n", "19   23  Chain-Type Price Indexes for Intermediate Inpu...\n", "20   24  Percent Changes in Chain-Type Price Indexes fo...\n", "21   25        Composition of Gross Output by Industry (A)\n", "22   26             Shares of Gross Output by Industry (A)\n", "23   29  Contributions to Percent Changes in Chain-Type...\n", "24   30  Contributions to Percent Changes in Chain-Type...\n", "25   31  Chain-Type Quantity Indexes for Energy Inputs ...\n", "26   32  Contributions to Percent Change by Industry in...\n", "27   33  Chain-Type Price Indexes for Energy Inputs by ...\n", "28   34  Contributions to Percent Change by Industry in...\n", "29   35  Chain-Type Quantity Indexes for Materials Inpu...\n", "30   36  Contributions to Percent Change by Industry in...\n", "31   37  Chain-Type Price Indexes for Materials Inputs ...\n", "32   38  Contributions to Percent Change by Industry in...\n", "33   39  Chain-Type Quantity Indexes for Purchased Serv...\n", "34   40  Contributions to Percent Change by Industry in...\n", "35   41  Chain-Type Price Indexes for Purchased Service...\n", "36   42  Contributions to Percent Change by Industry in...\n", "37  208              Real Gross Output by Industry (A) (Q)\n", "38  209       Real Intermediate Inputs by Industry (A) (Q)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tableid_param_vals = beaapi.get_parameter_values(beakey, 'GDPbyIndustry', 'TableID')\n", "display(tableid_param_vals)"]}, {"cell_type": "code", "execution_count": 4, "id": "aa9d401d", "metadata": {}, "outputs": [], "source": ["bea_tbl_gdp = beaapi.get_data(beakey, datasetname ='GDPbyIndustry', Frequency ='Q', Industry = 'ALL', TableID = 10, Year = '2023,2024,2025' )\n"]}, {"cell_type": "code", "execution_count": 6, "id": "ae459a8c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableID</th>\n", "      <th>Frequency</th>\n", "      <th>Year</th>\n", "      <th>Quarter</th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>790</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>GSL</td>\n", "      <td>State and local</td>\n", "      <td>1833.4</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>791</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>GSLE</td>\n", "      <td>Government enterprises</td>\n", "      <td>133.5</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>792</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>GSLG</td>\n", "      <td>General government</td>\n", "      <td>1699.4</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>793</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>HS</td>\n", "      <td>Housing</td>\n", "      <td>2113.4</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>794</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>NABI</td>\n", "      <td>Not allocated by industry&lt;sup&gt;1&lt;/sup&gt;</td>\n", "      <td>-259.7</td>\n", "      <td>10;10.1.Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>795</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>ORE</td>\n", "      <td>Other real estate</td>\n", "      <td>768.5</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>796</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>PGOOD</td>\n", "      <td>Private goods-producing industries&lt;sup&gt;2&lt;/sup&gt;</td>\n", "      <td>3842.5</td>\n", "      <td>10;10.2.Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>797</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>PROF</td>\n", "      <td>Professional and business services</td>\n", "      <td>3511.7</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>798</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>PSERV</td>\n", "      <td>Private services-producing industries&lt;sup&gt;3&lt;/sup&gt;</td>\n", "      <td>17050.5</td>\n", "      <td>10;10.3.Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799</th>\n", "      <td>10</td>\n", "      <td>Q</td>\n", "      <td>2024</td>\n", "      <td>IV</td>\n", "      <td>PVT</td>\n", "      <td>Private industries</td>\n", "      <td>20892.1</td>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     TableID Frequency  Year Quarter Industry  \\\n", "790       10         Q  2024      IV      GSL   \n", "791       10         Q  2024      IV     GSLE   \n", "792       10         Q  2024      IV     GSLG   \n", "793       10         Q  2024      IV       HS   \n", "794       10         Q  2024      IV     NABI   \n", "795       10         Q  2024      IV      ORE   \n", "796       10         Q  2024      IV    PGOOD   \n", "797       10         Q  2024      IV     PROF   \n", "798       10         Q  2024      IV    PSERV   \n", "799       10         Q  2024      IV      PVT   \n", "\n", "                                   IndustrYDescription  DataValue    NoteRef  \n", "790                                    State and local     1833.4         10  \n", "791                             Government enterprises      133.5         10  \n", "792                                 General government     1699.4         10  \n", "793                                            Housing     2113.4         10  \n", "794              Not allocated by industry<sup>1</sup>     -259.7  10;10.1.Q  \n", "795                                  Other real estate      768.5         10  \n", "796     Private goods-producing industries<sup>2</sup>     3842.5  10;10.2.Q  \n", "797                 Professional and business services     3511.7         10  \n", "798  Private services-producing industries<sup>3</sup>    17050.5  10;10.3.Q  \n", "799                                 Private industries    20892.1         10  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["bea_tbl_gdp.tail(10)"]}, {"cell_type": "markdown", "id": "a0757f9f", "metadata": {}, "source": ["Data is available till 2024, values are in billions of dollars"]}, {"cell_type": "markdown", "id": "e6af8c61", "metadata": {}, "source": ["## Fixed assets"]}, {"cell_type": "markdown", "id": "bfbfec9c", "metadata": {}, "source": ["FA Inv (available by year only)<br>\n", "FAAt307ESI Table 3.7ESI. Investment in Private Fixed Assets by Industry (A)"]}, {"cell_type": "code", "execution_count": 43, "id": "4612b103", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ParameterName</th>\n", "      <th>ParameterDataType</th>\n", "      <th>ParameterDescription</th>\n", "      <th>ParameterIsRequiredFlag</th>\n", "      <th>ParameterDefaultValue</th>\n", "      <th>MultipleAcceptedFlag</th>\n", "      <th>AllValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TableName</td>\n", "      <td>string</td>\n", "      <td>The new Fixed Assets identifier</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Year</td>\n", "      <td>integer</td>\n", "      <td>List of year(s) of data to retrieve (X for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>X</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ParameterName ParameterDataType  \\\n", "0     TableName            string   \n", "1          Year           integer   \n", "\n", "                              ParameterDescription  ParameterIsRequiredFlag  \\\n", "0                  The new Fixed Assets identifier                        1   \n", "1  List of year(s) of data to retrieve (X for All)                        1   \n", "\n", "  ParameterDefaultValue  MultipleAcceptedFlag AllValue  \n", "0                                           0           \n", "1                                           1        X  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_params = beaapi.get_parameter_list(beakey, 'FixedAssets')\n", "display(list_of_params)"]}, {"cell_type": "code", "execution_count": 44, "id": "b974e9b4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>FirstAnnualYear</th>\n", "      <th>LastAnnualYear</th>\n", "      <th>FirstQuarterlyYear</th>\n", "      <th>LastQuarterlyYear</th>\n", "      <th>FirstMonthlyYear</th>\n", "      <th>LastMonthlyYear</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAAt101</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAAt102</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAAt103</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAAt104</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAAt105</td>\n", "      <td>1901</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>FAAt809</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>FAAt810</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>FAAt901</td>\n", "      <td>2007</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>FAAt902</td>\n", "      <td>2007</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>FAAt903</td>\n", "      <td>2007</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>109 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    TableName FirstAnnualYear LastAnnualYear FirstQuarterlyYear  \\\n", "0     FAAt101            1925           2023                  0   \n", "1     FAAt102            1925           2023                  0   \n", "2     FAAt103            1925           2023                  0   \n", "3     FAAt104            1925           2023                  0   \n", "4     FAAt105            1901           2023                  0   \n", "..        ...             ...            ...                ...   \n", "104   FAAt809            1925           2023                  0   \n", "105   FAAt810            1925           2023                  0   \n", "106   FAAt901            2007           2023                  0   \n", "107   FAAt902            2007           2023                  0   \n", "108   FAAt903            2007           2023                  0   \n", "\n", "    LastQuarterlyYear FirstMonthlyYear LastMonthlyYear  \n", "0                   0                0               0  \n", "1                   0                0               0  \n", "2                   0                0               0  \n", "3                   0                0               0  \n", "4                   0                0               0  \n", "..                ...              ...             ...  \n", "104                 0                0               0  \n", "105                 0                0               0  \n", "106                 0                0               0  \n", "107                 0                0               0  \n", "108                 0                0               0  \n", "\n", "[109 rows x 7 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_param_vals = beaapi.get_parameter_values(beakey, 'FixedAssets', 'Year')\n", "display(list_of_param_vals)"]}, {"cell_type": "code", "execution_count": 45, "id": "62c0e41a", "metadata": {}, "outputs": [], "source": ["list_of_param_vals.to_csv('bea_fixed_assets_tables.csv', index=False)"]}, {"cell_type": "code", "execution_count": 56, "id": "c4227305", "metadata": {}, "outputs": [], "source": ["bea_tbl_FA = beaapi.get_data(beakey, datasetname ='FixedAssets', TableName = 'FAAt307ESI', Year = '2023,2024,2025' )\n"]}, {"cell_type": "code", "execution_count": 54, "id": "7399fd92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>SeriesCode</th>\n", "      <th>LineNumber</th>\n", "      <th>LineDescription</th>\n", "      <th>TimePeriod</th>\n", "      <th>METRIC_NAME</th>\n", "      <th>CL_UNIT</th>\n", "      <th>UNIT_MULT</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n622h1es00</td>\n", "      <td>87</td>\n", "      <td>Hospitals</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>115099</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n62301es00</td>\n", "      <td>88</td>\n", "      <td>Nursing and residential care facilities</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>11380</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n62401es00</td>\n", "      <td>89</td>\n", "      <td>Social assistance</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>7518</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n71001es00</td>\n", "      <td>90</td>\n", "      <td>Arts, entertainment, and recreation</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>59316</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n711a1es00</td>\n", "      <td>91</td>\n", "      <td>Performing arts, spectator sports, museums, an...</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>31082</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n71301es00</td>\n", "      <td>92</td>\n", "      <td>Amusements, gambling, and recreation industries</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>28234</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n72001es00</td>\n", "      <td>93</td>\n", "      <td>Accommodation and food services</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>53308</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n72101es00</td>\n", "      <td>94</td>\n", "      <td>Accommodation</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>16913</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n72201es00</td>\n", "      <td>95</td>\n", "      <td>Food services and drinking places</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>36395</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>FAAt307ESI</td>\n", "      <td>i3n81001es00</td>\n", "      <td>96</td>\n", "      <td>Other services, except government</td>\n", "      <td>2023</td>\n", "      <td>Historical Cost</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>55776</td>\n", "      <td>FAAt307ESI</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     TableName    SeriesCode  LineNumber  \\\n", "86  FAAt307ESI  i3n622h1es00          87   \n", "87  FAAt307ESI  i3n62301es00          88   \n", "88  FAAt307ESI  i3n62401es00          89   \n", "89  FAAt307ESI  i3n71001es00          90   \n", "90  FAAt307ESI  i3n711a1es00          91   \n", "91  FAAt307ESI  i3n71301es00          92   \n", "92  FAAt307ESI  i3n72001es00          93   \n", "93  FAAt307ESI  i3n72101es00          94   \n", "94  FAAt307ESI  i3n72201es00          95   \n", "95  FAAt307ESI  i3n81001es00          96   \n", "\n", "                                      LineDescription TimePeriod  \\\n", "86                                          Hospitals       2023   \n", "87            Nursing and residential care facilities       2023   \n", "88                                  Social assistance       2023   \n", "89                Arts, entertainment, and recreation       2023   \n", "90  Performing arts, spectator sports, museums, an...       2023   \n", "91    Amusements, gambling, and recreation industries       2023   \n", "92                    Accommodation and food services       2023   \n", "93                                      Accommodation       2023   \n", "94                  Food services and drinking places       2023   \n", "95                  Other services, except government       2023   \n", "\n", "        METRIC_NAME CL_UNIT  UNIT_MULT  DataValue     NoteRef  \n", "86  Historical Cost   Level          6     115099  FAAt307ESI  \n", "87  Historical Cost   Level          6      11380  FAAt307ESI  \n", "88  Historical Cost   Level          6       7518  FAAt307ESI  \n", "89  Historical Cost   Level          6      59316  FAAt307ESI  \n", "90  Historical Cost   Level          6      31082  FAAt307ESI  \n", "91  Historical Cost   Level          6      28234  FAAt307ESI  \n", "92  Historical Cost   Level          6      53308  FAAt307ESI  \n", "93  Historical Cost   Level          6      16913  FAAt307ESI  \n", "94  Historical Cost   Level          6      36395  FAAt307ESI  \n", "95  Historical Cost   Level          6      55776  FAAt307ESI  "]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["bea_tbl_FA.tail(10)"]}, {"cell_type": "markdown", "id": "d01c9f4b", "metadata": {}, "source": ["## Underlying GDP"]}, {"cell_type": "code", "execution_count": 58, "id": "a7fa92ef", "metadata": {}, "outputs": [], "source": ["bea_tbl_gdp = beaapi.get_data(beakey, datasetname ='UnderlyingGDPbyIndustry', Frequency = 'A', Industry = 'ALL', TableID = 210, Year = '2023,2024,2025' )"]}, {"cell_type": "code", "execution_count": 53, "id": "7b37d34e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableID</th>\n", "      <th>Frequency</th>\n", "      <th>Year</th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>GSLGO</td>\n", "      <td>State and local government other services</td>\n", "      <td>747.2</td>\n", "      <td>210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>HS</td>\n", "      <td>Housing</td>\n", "      <td>2640.9</td>\n", "      <td>210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>HSO</td>\n", "      <td>Owner-occupied housing</td>\n", "      <td>1972.4</td>\n", "      <td>210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>HST</td>\n", "      <td>Tenant-occupied housing</td>\n", "      <td>668.5</td>\n", "      <td>210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>ICT</td>\n", "      <td>Information-communications-technology-producin...</td>\n", "      <td>1950.7</td>\n", "      <td>210;210.3.A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>ORE</td>\n", "      <td>Other real estate</td>\n", "      <td>806.3</td>\n", "      <td>210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>PGOOD</td>\n", "      <td>Private goods-producing industries &lt;sup&gt;1&lt;/sup&gt;</td>\n", "      <td>4746.9</td>\n", "      <td>210;210.1.A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>PROF</td>\n", "      <td>Professional and business services</td>\n", "      <td>3611.7</td>\n", "      <td>210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>PSERV</td>\n", "      <td>Private services-producing industries &lt;sup&gt;2&lt;/...</td>\n", "      <td>19868.7</td>\n", "      <td>210;210.2.A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>210</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>PVT</td>\n", "      <td>Private industries</td>\n", "      <td>24615.6</td>\n", "      <td>210</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     TableID Frequency  Year Industry  \\\n", "181      210         A  2023    GSLGO   \n", "182      210         A  2023       HS   \n", "183      210         A  2023      HSO   \n", "184      210         A  2023      HST   \n", "185      210         A  2023      ICT   \n", "186      210         A  2023      ORE   \n", "187      210         A  2023    PGOOD   \n", "188      210         A  2023     PROF   \n", "189      210         A  2023    PSERV   \n", "190      210         A  2023      PVT   \n", "\n", "                                   IndustrYDescription  DataValue      NoteRef  \n", "181          State and local government other services      747.2          210  \n", "182                                            Housing     2640.9          210  \n", "183                             Owner-occupied housing     1972.4          210  \n", "184                            Tenant-occupied housing      668.5          210  \n", "185  Information-communications-technology-producin...     1950.7  210;210.3.A  \n", "186                                  Other real estate      806.3          210  \n", "187    Private goods-producing industries <sup>1</sup>     4746.9  210;210.1.A  \n", "188                 Professional and business services     3611.7          210  \n", "189  Private services-producing industries <sup>2</...    19868.7  210;210.2.A  \n", "190                                 Private industries    24615.6          210  "]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["bea_tbl_gdp.tail(10)"]}, {"cell_type": "markdown", "id": "e74e5c5e", "metadata": {}, "source": ["Fixed Asset and Underlying gdp is available till 2023"]}, {"cell_type": "markdown", "id": "8680a675", "metadata": {}, "source": ["# BLS"]}, {"cell_type": "markdown", "id": "78616b4d", "metadata": {}, "source": ["## NAICS based employment data (Monthly)"]}, {"cell_type": "markdown", "id": "b9a26d31", "metadata": {}, "source": ["``` Separate Notebook to make series id and retreiving employment data  bls_employment.ipynb```"]}, {"cell_type": "markdown", "id": "0d2cd5eb", "metadata": {}, "source": ["# FRED"]}, {"cell_type": "markdown", "id": "dd6c9b33", "metadata": {}, "source": ["```\n", "Notebook to retrieve data and creating scores - 09_05_fred_series_ids.ipynb\n", "fred series ids corresponding to 31, 32 and 33 (manufacturing sector) in excel sheet.\n", "```"]}, {"cell_type": "markdown", "id": "d1c13f9d", "metadata": {}, "source": ["```\n", "Next steps: \n", "1. Calulation of scores fixed assets, economic heft, import intensity, employment.\n", "2. <PERSON><PERSON><PERSON> all the scores.\n", "```\n"]}, {"cell_type": "markdown", "id": "803ef154", "metadata": {}, "source": ["# Creating series ids file"]}, {"cell_type": "code", "execution_count": 7, "id": "420b692f", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 8, "id": "834585da", "metadata": {}, "outputs": [], "source": ["bls_series_df = pd.read_csv('manufacturing_employment_rankings.csv')"]}, {"cell_type": "code", "execution_count": 9, "id": "d39ef33e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>seriesID</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>CEU3231120001</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>CEU3133640001</td>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>CEU3231180001</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>CEU3231170001</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>CEU3133530001</td>\n", "      <td>3353</td>\n", "      <td>Electrical equipment manufacturing</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank       seriesID  naics_code                              industry_name  \\\n", "0     1  CEU3231120001        3112                  Grain and oilseed milling   \n", "1     2  CEU3133640001        3364  Aerospace product and parts manufacturing   \n", "2     3  CEU3231180001        3118        Bakeries and tortilla manufacturing   \n", "3     4  CEU3231170001        3117  Seafood product preparation and packaging   \n", "4     5  CEU3133530001        3353         Electrical equipment manufacturing   \n", "\n", "     avg_2023    avg_2024     ratio  \n", "0   64.525000   67.391667  1.044427  \n", "1  536.658333  559.066667  1.041755  \n", "2  337.133333  350.400000  1.039351  \n", "3   30.583333   31.475000  1.029155  \n", "4  148.708333  153.033333  1.029084  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["bls_series_df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "91a13c48", "metadata": {}, "outputs": [], "source": ["bls_series_df['seas_adj_series_id'] = bls_series_df['seriesID'].str.replace('CEU', 'CES', regex=False)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "48041976", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>seriesID</th>\n", "      <th>naics_code</th>\n", "      <th>industry_name</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "      <th>seas_adj_series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>CEU3231120001</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "      <td>CES3231120001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>CEU3133640001</td>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "      <td>CES3133640001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>CEU3231180001</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "      <td>CES3231180001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>CEU3231170001</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "      <td>CES3231170001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>CEU3133530001</td>\n", "      <td>3353</td>\n", "      <td>Electrical equipment manufacturing</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "      <td>CES3133530001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank       seriesID  naics_code                              industry_name  \\\n", "0     1  CEU3231120001        3112                  Grain and oilseed milling   \n", "1     2  CEU3133640001        3364  Aerospace product and parts manufacturing   \n", "2     3  CEU3231180001        3118        Bakeries and tortilla manufacturing   \n", "3     4  CEU3231170001        3117  Seafood product preparation and packaging   \n", "4     5  CEU3133530001        3353         Electrical equipment manufacturing   \n", "\n", "     avg_2023    avg_2024     ratio seas_adj_series_id  \n", "0   64.525000   67.391667  1.044427      CES3231120001  \n", "1  536.658333  559.066667  1.041755      CES3133640001  \n", "2  337.133333  350.400000  1.039351      CES3231180001  \n", "3   30.583333   31.475000  1.029155      CES3231170001  \n", "4  148.708333  153.033333  1.029084      CES3133530001  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["bls_series_df.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "5d50b153", "metadata": {}, "outputs": [], "source": ["all_other_ids = pd.read_excel('Combined_Scores_NAICS_based_with_fred_ids.xlsx')"]}, {"cell_type": "code", "execution_count": 13, "id": "7c996b72", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M) SeriesID Industry production (A)  \\\n", "0                        CAPG311S                         IPG3111A   \n", "1                        CAPG311S                         IPG3112A   \n", "2                        CAPG311S                         IPG3113A   \n", "3                        CAPG311S                         IPG3114A   \n", "4                        CAPG311S                         IPG3115A   \n", "\n", "  SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \\\n", "0                      CAPUTLG311A                        CAPG311A   \n", "1                      CAPUTLG311A                        CAPG311A   \n", "2                      CAPUTLG311A                        CAPG311A   \n", "3                      CAPUTLG311A                        CAPG311A   \n", "4                      CAPUTLG311A                        CAPG311A   \n", "\n", "   Fixed_Assets_Score  Economic_Heft_Score  Import_Intensity_Score  \\\n", "0            0.498296                  0.5                0.115238   \n", "1            0.498296                  0.5                0.159188   \n", "2            0.498296                  0.5                0.153765   \n", "3            0.498296                  0.5                0.167181   \n", "4            0.498296                  0.5                0.117253   \n", "\n", "   Employment_Score  \n", "0          0.385857  \n", "1          0.438410  \n", "2          0.354900  \n", "3          0.458670  \n", "4          0.458316  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["all_other_ids.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "5f056f62", "metadata": {}, "outputs": [], "source": ["bls_series_df.rename(columns={'naics_code': 'NAICS_code', 'seriesID' : 'seas_unadj_series_id'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 19, "id": "9cfef7fe", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>seas_unadj_series_id</th>\n", "      <th>NAICS_code</th>\n", "      <th>industry_name</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "      <th>seas_adj_series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>CEU3231120001</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>64.525000</td>\n", "      <td>67.391667</td>\n", "      <td>1.044427</td>\n", "      <td>CES3231120001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>CEU3133640001</td>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "      <td>CES3133640001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>CEU3231180001</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "      <td>CES3231180001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>CEU3231170001</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>30.583333</td>\n", "      <td>31.475000</td>\n", "      <td>1.029155</td>\n", "      <td>CES3231170001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>CEU3133530001</td>\n", "      <td>3353</td>\n", "      <td>Electrical equipment manufacturing</td>\n", "      <td>148.708333</td>\n", "      <td>153.033333</td>\n", "      <td>1.029084</td>\n", "      <td>CES3133530001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank seas_unadj_series_id  NAICS_code  \\\n", "0     1        CEU3231120001        3112   \n", "1     2        CEU3133640001        3364   \n", "2     3        CEU3231180001        3118   \n", "3     4        CEU3231170001        3117   \n", "4     5        CEU3133530001        3353   \n", "\n", "                               industry_name    avg_2023    avg_2024  \\\n", "0                  Grain and oilseed milling   64.525000   67.391667   \n", "1  Aerospace product and parts manufacturing  536.658333  559.066667   \n", "2        Bakeries and tortilla manufacturing  337.133333  350.400000   \n", "3  Seafood product preparation and packaging   30.583333   31.475000   \n", "4         Electrical equipment manufacturing  148.708333  153.033333   \n", "\n", "      ratio seas_adj_series_id  \n", "0  1.044427      CES3231120001  \n", "1  1.041755      CES3133640001  \n", "2  1.039351      CES3231180001  \n", "3  1.029155      CES3231170001  \n", "4  1.029084      CES3133530001  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["bls_series_df.head()"]}, {"cell_type": "code", "execution_count": 16, "id": "d1a14957", "metadata": {}, "outputs": [{"data": {"text/plain": ["(63, 8)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["bls_series_df.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "bae38e1f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(86, 14)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["all_other_ids.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "4a4f8b0a", "metadata": {}, "outputs": [], "source": ["merged_df = all_other_ids.merge(\n", "    bls_series_df[['NAICS_code', 'seas_unadj_series_id', 'seas_adj_series_id']],\n", "    on='NAICS_code',\n", "    how='left'  \n", ")"]}, {"cell_type": "code", "execution_count": 21, "id": "29564e8a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>seas_unadj_series_id</th>\n", "      <th>seas_adj_series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>CEU3231110001</td>\n", "      <td>CES3231110001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>CEU3231120001</td>\n", "      <td>CES3231120001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>CEU3231130001</td>\n", "      <td>CES3231130001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>CEU3231140001</td>\n", "      <td>CES3231140001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>CEU3231150001</td>\n", "      <td>CES3231150001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M) SeriesID Industry production (A)  \\\n", "0                        CAPG311S                         IPG3111A   \n", "1                        CAPG311S                         IPG3112A   \n", "2                        CAPG311S                         IPG3113A   \n", "3                        CAPG311S                         IPG3114A   \n", "4                        CAPG311S                         IPG3115A   \n", "\n", "  SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \\\n", "0                      CAPUTLG311A                        CAPG311A   \n", "1                      CAPUTLG311A                        CAPG311A   \n", "2                      CAPUTLG311A                        CAPG311A   \n", "3                      CAPUTLG311A                        CAPG311A   \n", "4                      CAPUTLG311A                        CAPG311A   \n", "\n", "   Fixed_Assets_Score  Economic_Heft_Score  Import_Intensity_Score  \\\n", "0            0.498296                  0.5                0.115238   \n", "1            0.498296                  0.5                0.159188   \n", "2            0.498296                  0.5                0.153765   \n", "3            0.498296                  0.5                0.167181   \n", "4            0.498296                  0.5                0.117253   \n", "\n", "   Employment_Score seas_unadj_series_id seas_adj_series_id  \n", "0          0.385857        CEU3231110001      CES3231110001  \n", "1          0.438410        CEU3231120001      CES3231120001  \n", "2          0.354900        CEU3231130001      CES3231130001  \n", "3          0.458670        CEU3231140001      CES3231140001  \n", "4          0.458316        CEU3231150001      CES3231150001  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "793738dd", "metadata": {}, "outputs": [], "source": ["final_ids = merged_df.drop(columns = ['Fixed_Assets_Score','Economic_Heft_Score', 'Import_Intensity_Score', 'Employment_Score'])"]}, {"cell_type": "code", "execution_count": 23, "id": "5666b8c2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Series ID Industry Production (M)</th>\n", "      <th>Series ID Capacity Utilization(M)</th>\n", "      <th>Industrial Cpacity series ID(M)</th>\n", "      <th>SeriesID Industry production (A)</th>\n", "      <th>SeriesID capacity utilization(A)</th>\n", "      <th>SeriesID Industrial capacity(A)</th>\n", "      <th>seas_unadj_series_id</th>\n", "      <th>seas_adj_series_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>IPG3111S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3111A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>CEU3231110001</td>\n", "      <td>CES3231110001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>IPG3112S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3112A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>CEU3231120001</td>\n", "      <td>CES3231120001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>IPG3113S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3113A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>CEU3231130001</td>\n", "      <td>CES3231130001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>IPG3114S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3114A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>CEU3231140001</td>\n", "      <td>CES3231140001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>IPG3115S</td>\n", "      <td>CAPUTLG311S</td>\n", "      <td>CAPG311S</td>\n", "      <td>IPG3115A</td>\n", "      <td>CAPUTLG311A</td>\n", "      <td>CAPG311A</td>\n", "      <td>CEU3231150001</td>\n", "      <td>CES3231150001</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  \\\n", "0                          Animal food manufacturing   \n", "1                          Grain and oilseed milling   \n", "2      Sugar and confectionery product manufacturing   \n", "3  Fruit and vegetable preserving and specialty f...   \n", "4                        Dairy product manufacturing   \n", "\n", "  Series ID Industry Production (M) Series ID Capacity Utilization(M)  \\\n", "0                          IPG3111S                       CAPUTLG311S   \n", "1                          IPG3112S                       CAPUTLG311S   \n", "2                          IPG3113S                       CAPUTLG311S   \n", "3                          IPG3114S                       CAPUTLG311S   \n", "4                          IPG3115S                       CAPUTLG311S   \n", "\n", "  Industrial Cpacity series ID(M) SeriesID Industry production (A)  \\\n", "0                        CAPG311S                         IPG3111A   \n", "1                        CAPG311S                         IPG3112A   \n", "2                        CAPG311S                         IPG3113A   \n", "3                        CAPG311S                         IPG3114A   \n", "4                        CAPG311S                         IPG3115A   \n", "\n", "  SeriesID capacity utilization(A) SeriesID Industrial capacity(A)  \\\n", "0                      CAPUTLG311A                        CAPG311A   \n", "1                      CAPUTLG311A                        CAPG311A   \n", "2                      CAPUTLG311A                        CAPG311A   \n", "3                      CAPUTLG311A                        CAPG311A   \n", "4                      CAPUTLG311A                        CAPG311A   \n", "\n", "  seas_unadj_series_id seas_adj_series_id  \n", "0        CEU3231110001      CES3231110001  \n", "1        CEU3231120001      CES3231120001  \n", "2        CEU3231130001      CES3231130001  \n", "3        CEU3231140001      CES3231140001  \n", "4        CEU3231150001      CES3231150001  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["final_ids.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "a3ea4f25", "metadata": {}, "outputs": [], "source": ["final_ids.to_csv('all_series_ids.csv', index=False)"]}, {"cell_type": "markdown", "id": "92ce00bc", "metadata": {}, "source": ["# Monthly data checking"]}, {"cell_type": "markdown", "id": "42faaf42", "metadata": {}, "source": ["dataweb, <PERSON><PERSON> and Fred"]}, {"cell_type": "markdown", "id": "91687815", "metadata": {}, "source": ["# Dataweb"]}, {"cell_type": "markdown", "id": "5052a616", "metadata": {}, "source": ["## Exports domestic"]}, {"cell_type": "code", "execution_count": 1, "id": "c5b77fe5", "metadata": {}, "outputs": [], "source": ["saved_query = \"\"\"{\"savedQueryType\":\"\",\"savedQueryName\":\"\",\"savedQueryDesc\":\"\",\"isOwner\":true,\"runMonthly\":false,\"unitConversion\":\"0\",\"manualConversions\":[],\"reportOptions\":{\"tradeType\":\"Export\",\"classificationSystem\":\"NAIC\"},\"searchOptions\":{\"MiscGroup\":{\"districts\":{\"aggregation\":\"Aggregate District\",\"districtGroups\":{\"userGroups\":[]},\"districts\":[],\"districtsExpanded\":[{\"name\":\"All Districts\",\"value\":\"all\"}],\"districtsSelectType\":\"all\"},\"importPrograms\":{\"aggregation\":null,\"importPrograms\":[],\"programsSelectType\":\"all\"},\"extImportPrograms\":{\"aggregation\":\"Aggregate CSC\",\"extImportPrograms\":[],\"extImportProgramsExpanded\":[],\"programsSelectType\":\"all\"},\"provisionCodes\":{\"aggregation\":\"Aggregate RPCODE\",\"provisionCodesSelectType\":\"all\",\"rateProvisionCodes\":[],\"rateProvisionCodesExpanded\":[],\"rateProvisionGroups\":{\"systemGroups\":[]}}},\"commodities\":{\"aggregation\":\"Break Out Commodities\",\"codeDisplayFormat\":\"YES\",\"commodities\":[],\"commoditiesExpanded\":[],\"commoditiesManual\":\"\",\"commodityGroups\":{\"systemGroups\":[],\"userGroups\":[]},\"commoditySelectType\":\"all\",\"granularity\":\"4\",\"groupGranularity\":null,\"searchGranularity\":null,\"showHTSValidDetails\":\"\"},\"componentSettings\":{\"dataToReport\":[\"FAS_VALUE\"],\"scale\":\"1\",\"timeframeSelectType\":\"fullYears\",\"years\":[\"2025\",\"2024\",\"2023\"],\"startDate\":null,\"endDate\":null,\"startMonth\":null,\"endMonth\":null,\"yearsTimeline\":\"Monthly\"},\"countries\":{\"aggregation\":\"Aggregate Countries\",\"countries\":[],\"countriesExpanded\":[{\"name\":\"All Countries\",\"value\":\"all\"}],\"countriesSelectType\":\"all\",\"countryGroups\":{\"systemGroups\":[],\"userGroups\":[]}}},\"sortingAndDataFormat\":{\"DataSort\":{\"columnOrder\":[\"NAICS4 & DESCRIPTION\",\"YEAR\"],\"fullColumnOrder\":[{\"hasChildren\":false,\"name\":\"NAICS4 & DESCRIPTION\",\"value\":\"NAICS4 & DESCRIPTION\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"},{\"hasChildren\":false,\"name\":\"Year\",\"value\":\"YEAR\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"}],\"sortOrder\":[{\"sortData\":\"NAICS4 & DESCRIPTION\",\"orderBy\":\"asc\",\"year\":\"\"},{\"sortData\":\"Year\",\"orderBy\":\"asc\",\"year\":\"\"}]},\"reportCustomizations\":{\"exportCombineTables\":false,\"totalRecords\":\"20000\",\"exportRawData\":false}},\"deletedCountryUserGroups\":[],\"deletedCommodityUserGroups\":[],\"deletedDistrictUserGroups\":[]}\"\"\""]}, {"cell_type": "code", "execution_count": 2, "id": "bc41ee9c", "metadata": {}, "outputs": [], "source": ["import json\n", "basicQuery = json.loads(saved_query)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "40eb6dcf", "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas\n", "token = 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDAwOTg5IiwianRpIjoiYjM2ZDlkZTAtOTRmMS00MjljLTk1YTItN2VmMDlmZmUxYWZmIiwiaXNzIjoiZGF0YXdlYiIsImlhdCI6MTc0OTc5ODMyOSwiZXhwIjoxNzUxMDA3OTI5fQ.jzG-bZScQFdpR0TEQoVT6kdQ_uSNzVnA66DRBNGe6aJbq9GhbfqwsoG-kvnSHcTrTQKEyhhyAMwUCFJym_Basg'\n", "baseUrl = 'https://datawebws.usitc.gov/dataweb'\n", "headers = {\n", "    \"Content-Type\": \"application/json; charset=utf-8\", \n", "    \"Authorization\": \"Bearer \" + token\n", "}\n", "\n", "requests.packages.urllib3.disable_warnings() "]}, {"cell_type": "code", "execution_count": 6, "id": "21066708", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "response = requests.post(baseUrl+'/api/v2/report2/runReport', \n", "                         headers=headers, json=basicQuery, verify=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "34d1358f", "metadata": {}, "outputs": [], "source": ["columns = getColumns(response.json()['dto']['tables'][0]['column_groups'])\n", "data = getData(response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew'])"]}, {"cell_type": "code", "execution_count": 10, "id": "aa0d1df7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>Year</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>2023</td>\n", "      <td>7,057,433,045</td>\n", "      <td>5,553,355,116</td>\n", "      <td>4,340,492,871</td>\n", "      <td>3,853,534,724</td>\n", "      <td>3,302,986,765</td>\n", "      <td>2,186,741,304</td>\n", "      <td>2,229,128,096</td>\n", "      <td>2,442,056,479</td>\n", "      <td>3,081,167,277</td>\n", "      <td>6,393,247,955</td>\n", "      <td>5,717,314,809</td>\n", "      <td>4,757,313,980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>2024</td>\n", "      <td>5,081,226,107</td>\n", "      <td>5,108,894,594</td>\n", "      <td>3,897,552,227</td>\n", "      <td>3,251,416,236</td>\n", "      <td>2,838,504,545</td>\n", "      <td>2,517,788,434</td>\n", "      <td>2,683,078,346</td>\n", "      <td>2,860,064,156</td>\n", "      <td>3,174,332,415</td>\n", "      <td>5,779,352,399</td>\n", "      <td>6,228,311,204</td>\n", "      <td>5,513,924,407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>2025</td>\n", "      <td>4,197,612,028</td>\n", "      <td>3,437,172,718</td>\n", "      <td>3,988,169,082</td>\n", "      <td>3,464,583,722</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1112</td>\n", "      <td>VEGETABLES &amp; MELONS</td>\n", "      <td>2023</td>\n", "      <td>261,153,905</td>\n", "      <td>245,934,378</td>\n", "      <td>276,786,093</td>\n", "      <td>268,400,664</td>\n", "      <td>317,544,751</td>\n", "      <td>326,243,044</td>\n", "      <td>294,775,047</td>\n", "      <td>254,658,331</td>\n", "      <td>238,841,145</td>\n", "      <td>253,665,372</td>\n", "      <td>269,684,104</td>\n", "      <td>289,197,334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1112</td>\n", "      <td>VEGETABLES &amp; MELONS</td>\n", "      <td>2024</td>\n", "      <td>274,211,352</td>\n", "      <td>279,686,370</td>\n", "      <td>331,060,686</td>\n", "      <td>307,455,003</td>\n", "      <td>358,531,899</td>\n", "      <td>332,843,505</td>\n", "      <td>301,578,132</td>\n", "      <td>251,433,329</td>\n", "      <td>249,929,094</td>\n", "      <td>269,065,003</td>\n", "      <td>302,781,103</td>\n", "      <td>293,564,119</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number          Description  Year        January       February  \\\n", "0        1111    OILSEEDS & GRAINS  2023  7,057,433,045  5,553,355,116   \n", "1        1111    OILSEEDS & GRAINS  2024  5,081,226,107  5,108,894,594   \n", "2        1111    OILSEEDS & GRAINS  2025  4,197,612,028  3,437,172,718   \n", "3        1112  VEGETABLES & MELONS  2023    261,153,905    245,934,378   \n", "4        1112  VEGETABLES & MELONS  2024    274,211,352    279,686,370   \n", "\n", "           March          April            May           June           July  \\\n", "0  4,340,492,871  3,853,534,724  3,302,986,765  2,186,741,304  2,229,128,096   \n", "1  3,897,552,227  3,251,416,236  2,838,504,545  2,517,788,434  2,683,078,346   \n", "2  3,988,169,082  3,464,583,722              0              0              0   \n", "3    276,786,093    268,400,664    317,544,751    326,243,044    294,775,047   \n", "4    331,060,686    307,455,003    358,531,899    332,843,505    301,578,132   \n", "\n", "          August      September        October       November       December  \n", "0  2,442,056,479  3,081,167,277  6,393,247,955  5,717,314,809  4,757,313,980  \n", "1  2,860,064,156  3,174,332,415  5,779,352,399  6,228,311,204  5,513,924,407  \n", "2              0              0              0              0              0  \n", "3    254,658,331    238,841,145    253,665,372    269,684,104    289,197,334  \n", "4    251,433,329    249,929,094    269,065,003    302,781,103    293,564,119  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(data, columns = columns)\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "05bb332a", "metadata": {}, "outputs": [], "source": ["df.to_excel('dataweb_exports.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "7373ff07", "metadata": {}, "source": ["## Import for consumption"]}, {"cell_type": "code", "execution_count": 12, "id": "3bff57bb", "metadata": {}, "outputs": [], "source": ["saved_query = \"\"\"{\"savedQueryType\":\"\",\"savedQueryName\":\"\",\"savedQueryDesc\":\"\",\"isOwner\":true,\"runMonthly\":false,\"unitConversion\":\"0\",\"manualConversions\":[],\"reportOptions\":{\"tradeType\":\"Import\",\"classificationSystem\":\"NAIC\"},\"searchOptions\":{\"MiscGroup\":{\"districts\":{\"aggregation\":\"Aggregate District\",\"districtGroups\":{\"userGroups\":[]},\"districts\":[],\"districtsExpanded\":[{\"name\":\"All Districts\",\"value\":\"all\"}],\"districtsSelectType\":\"all\"},\"importPrograms\":{\"aggregation\":null,\"importPrograms\":[],\"programsSelectType\":\"all\"},\"extImportPrograms\":{\"aggregation\":\"Aggregate CSC\",\"extImportPrograms\":[],\"extImportProgramsExpanded\":[],\"programsSelectType\":\"all\"},\"provisionCodes\":{\"aggregation\":\"Aggregate RPCODE\",\"provisionCodesSelectType\":\"all\",\"rateProvisionCodes\":[],\"rateProvisionCodesExpanded\":[],\"rateProvisionGroups\":{\"systemGroups\":[]}}},\"commodities\":{\"aggregation\":\"Break Out Commodities\",\"codeDisplayFormat\":\"YES\",\"commodities\":[],\"commoditiesExpanded\":[],\"commoditiesManual\":\"\",\"commodityGroups\":{\"systemGroups\":[],\"userGroups\":[]},\"commoditySelectType\":\"all\",\"granularity\":\"4\",\"groupGranularity\":null,\"searchGranularity\":null,\"showHTSValidDetails\":\"\"},\"componentSettings\":{\"dataToReport\":[\"CONS_CUSTOMS_VALUE\"],\"scale\":\"1\",\"timeframeSelectType\":\"fullYears\",\"years\":[\"2025\",\"2024\",\"2023\"],\"startDate\":null,\"endDate\":null,\"startMonth\":null,\"endMonth\":null,\"yearsTimeline\":\"Monthly\"},\"countries\":{\"aggregation\":\"Aggregate Countries\",\"countries\":[],\"countriesExpanded\":[{\"name\":\"All Countries\",\"value\":\"all\"}],\"countriesSelectType\":\"all\",\"countryGroups\":{\"systemGroups\":[],\"userGroups\":[]}}},\"sortingAndDataFormat\":{\"DataSort\":{\"columnOrder\":[\"NAICS4 & DESCRIPTION\",\"YEAR\"],\"fullColumnOrder\":[{\"hasChildren\":false,\"name\":\"NAICS4 & DESCRIPTION\",\"value\":\"NAICS4 & DESCRIPTION\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"},{\"hasChildren\":false,\"name\":\"Year\",\"value\":\"YEAR\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"}],\"sortOrder\":[{\"sortData\":\"NAICS4 & DESCRIPTION\",\"orderBy\":\"asc\",\"year\":\"\"},{\"sortData\":\"Year\",\"orderBy\":\"asc\",\"year\":\"\"}]},\"reportCustomizations\":{\"exportCombineTables\":false,\"totalRecords\":\"20000\",\"exportRawData\":false}},\"deletedCountryUserGroups\":[],\"deletedCommodityUserGroups\":[],\"deletedDistrictUserGroups\":[]}\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "194cb369", "metadata": {}, "outputs": [], "source": ["import json\n", "basicQuery = json.loads(saved_query)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "8a69cc18", "metadata": {}, "outputs": [], "source": ["response = requests.post(baseUrl+'/api/v2/report2/runReport', \n", "                         headers=headers, json=basicQuery, verify=False)"]}, {"cell_type": "code", "execution_count": 16, "id": "2b876125", "metadata": {}, "outputs": [], "source": ["columns = getColumns(response.json()['dto']['tables'][0]['column_groups'])\n", "data = getData(response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew'])"]}, {"cell_type": "code", "execution_count": 17, "id": "e389821e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>Year</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>2023</td>\n", "      <td>339,137,430</td>\n", "      <td>354,770,393</td>\n", "      <td>423,432,901</td>\n", "      <td>360,589,176</td>\n", "      <td>407,225,701</td>\n", "      <td>431,289,219</td>\n", "      <td>391,894,560</td>\n", "      <td>319,834,952</td>\n", "      <td>323,407,270</td>\n", "      <td>289,556,288</td>\n", "      <td>304,850,162</td>\n", "      <td>261,647,795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>2024</td>\n", "      <td>279,998,574</td>\n", "      <td>282,321,722</td>\n", "      <td>361,018,008</td>\n", "      <td>336,848,502</td>\n", "      <td>295,927,169</td>\n", "      <td>237,677,118</td>\n", "      <td>250,965,873</td>\n", "      <td>251,310,239</td>\n", "      <td>287,151,135</td>\n", "      <td>265,936,318</td>\n", "      <td>259,480,573</td>\n", "      <td>300,644,341</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1111</td>\n", "      <td>OILSEEDS &amp; GRAINS</td>\n", "      <td>2025</td>\n", "      <td>338,412,206</td>\n", "      <td>272,530,857</td>\n", "      <td>306,122,803</td>\n", "      <td>259,258,970</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1112</td>\n", "      <td>VEGETABLES &amp; MELONS</td>\n", "      <td>2023</td>\n", "      <td>1,039,998,733</td>\n", "      <td>963,361,311</td>\n", "      <td>1,091,004,503</td>\n", "      <td>934,368,381</td>\n", "      <td>846,754,965</td>\n", "      <td>629,875,904</td>\n", "      <td>528,140,057</td>\n", "      <td>539,205,473</td>\n", "      <td>541,488,704</td>\n", "      <td>665,352,779</td>\n", "      <td>731,539,737</td>\n", "      <td>862,076,332</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1112</td>\n", "      <td>VEGETABLES &amp; MELONS</td>\n", "      <td>2024</td>\n", "      <td>1,105,872,083</td>\n", "      <td>1,127,755,214</td>\n", "      <td>1,104,977,559</td>\n", "      <td>1,010,464,904</td>\n", "      <td>837,522,922</td>\n", "      <td>581,220,227</td>\n", "      <td>571,358,691</td>\n", "      <td>559,749,043</td>\n", "      <td>544,151,722</td>\n", "      <td>695,396,535</td>\n", "      <td>801,839,395</td>\n", "      <td>1,015,452,120</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number          Description  Year        January       February  \\\n", "0        1111    OILSEEDS & GRAINS  2023    339,137,430    354,770,393   \n", "1        1111    OILSEEDS & GRAINS  2024    279,998,574    282,321,722   \n", "2        1111    OILSEEDS & GRAINS  2025    338,412,206    272,530,857   \n", "3        1112  VEGETABLES & MELONS  2023  1,039,998,733    963,361,311   \n", "4        1112  VEGETABLES & MELONS  2024  1,105,872,083  1,127,755,214   \n", "\n", "           March          April          May         June         July  \\\n", "0    423,432,901    360,589,176  407,225,701  431,289,219  391,894,560   \n", "1    361,018,008    336,848,502  295,927,169  237,677,118  250,965,873   \n", "2    306,122,803    259,258,970            0            0            0   \n", "3  1,091,004,503    934,368,381  846,754,965  629,875,904  528,140,057   \n", "4  1,104,977,559  1,010,464,904  837,522,922  581,220,227  571,358,691   \n", "\n", "        August    September      October     November       December  \n", "0  319,834,952  323,407,270  289,556,288  304,850,162    261,647,795  \n", "1  251,310,239  287,151,135  265,936,318  259,480,573    300,644,341  \n", "2            0            0            0            0              0  \n", "3  539,205,473  541,488,704  665,352,779  731,539,737    862,076,332  \n", "4  559,749,043  544,151,722  695,396,535  801,839,395  1,015,452,120  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(data, columns = columns)\n", "\n", "df.head()"]}, {"cell_type": "markdown", "id": "49f7931f", "metadata": {}, "source": ["# BLS"]}, {"cell_type": "code", "execution_count": 1, "id": "7b3d73ba", "metadata": {}, "outputs": [], "source": ["ids =['CES3231120001',\n", "'CES3133640001',\n", "'CES3231180001',\n", "'CES3231170001',\n", "'CES3133530001',\n", "'CES3133610001']"]}, {"cell_type": "code", "execution_count": 2, "id": "292e693b", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "headers = {'Content-type': 'application/json'}\n", "data = json.dumps({\n", "            \"seriesid\": ids,\n", "            \"startyear\": \"2024\",\n", "            \"endyear\": \"2025\",\n", "            \"registrationkey\": \"77631563995c4764bcfe327957cb19cf\" # Best to use a key if you have one\n", "        })"]}, {"cell_type": "code", "execution_count": 3, "id": "720dee60", "metadata": {}, "outputs": [], "source": ["p = requests.post('https://api.bls.gov/publicAPI/v2/timeseries/data/', data=data, headers=headers)\n", "p.raise_for_status()  # This will raise an HTTPError if the status is 4xx or 5xx\n", "json_data = p.json()"]}, {"cell_type": "code", "execution_count": 4, "id": "7ce6b5ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"status\": \"REQUEST_SUCCEEDED\",\n", "    \"responseTime\": 106,\n", "    \"message\": [],\n", "    \"Results\": {\n", "        \"series\": [\n", "            {\n", "                \"seriesID\": \"CES3231120001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"68.2\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"68.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"68.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"68.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"68.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"68.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"68.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"68.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"67.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"67.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"66.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"67.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"66.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"66.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"66.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"66.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133640001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"552.8\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"552.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"552.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"554.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"558.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"558.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"524.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"563.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"562.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"564.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"563.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"564.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"565.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"562.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"561.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"559.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3231180001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"353.0\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"352.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"352.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"351.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"352.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"353.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"355.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"355.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"353.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"351.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"350.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"349.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"347.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"346.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"345.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"344.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3231170001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"27.5\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"27.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"28.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"28.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"29.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"31.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"32.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"32.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"32.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"32.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"31.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"31.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"31.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"31.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133530001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"150.8\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"151.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"150.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"150.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"150.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"151.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"152.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"152.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"152.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"153.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"153.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"154.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"152.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133610001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"297.5\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"297.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"299.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"294.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"302.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"305.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"305.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"305.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"306.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"310.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"302.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"300.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"296.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"295.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"295.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"296.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    }\n", "}\n"]}], "source": ["print(json.dumps(json_data, indent=4))"]}, {"cell_type": "code", "execution_count": 19, "id": "6cd62ac4", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "headers = {'Content-type': 'application/json'}\n", "data_1 = json.dumps({\"seriesid\": ids,\"startyear\":\"2023\", \"endyear\":\"2025\"})\n", "#print(data_1)\n", "p = requests.post('https://api.bls.gov/publicAPI/v1/timeseries/data/', data=data_1, headers=headers)\n", "json_data = json.loads(p.text)"]}, {"cell_type": "code", "execution_count": 20, "id": "1fbf8da7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"status\": \"REQUEST_SUCCEEDED\",\n", "    \"responseTime\": 106,\n", "    \"message\": [],\n", "    \"Results\": {\n", "        \"series\": [\n", "            {\n", "                \"seriesID\": \"CES3231120001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"68.2\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"68.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"68.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"68.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"68.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"68.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"68.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"68.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"67.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"67.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"66.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"67.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"66.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"66.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"66.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"66.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"65.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"65.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"65.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"64.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"64.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"64.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"64.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"64.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"64.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"64.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"64.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"63.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133640001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"552.8\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"552.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"552.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"554.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"558.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"558.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"524.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"563.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"562.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"564.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"563.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"564.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"565.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"562.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"561.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"559.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"556.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"553.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"549.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"543.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"542.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"539.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"536.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"529.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"524.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"524.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"522.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"517.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3231180001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"353.0\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"352.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"352.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"351.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"352.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"353.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"355.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"355.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"353.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"351.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"350.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"349.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"347.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"346.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"345.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"344.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"341.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"340.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"338.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"336.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"338.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"337.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"337.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"336.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"334.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"335.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"334.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"334.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3231170001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"27.5\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"27.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"28.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"28.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"29.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"31.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"32.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"32.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"32.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"32.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"31.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"31.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"31.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"31.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"31.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"30.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"29.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"29.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"28.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"29.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"28.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"30.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"31.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"33.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"32.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"31.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133530001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"150.8\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"151.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"150.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"150.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"150.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"151.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"152.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"152.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"152.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"153.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"153.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"154.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"154.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"152.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"151.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"151.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"150.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"150.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"149.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"146.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"148.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"148.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"146.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"147.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"147.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"147.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"seriesID\": \"CES3133610001\",\n", "                \"data\": [\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"latest\": \"true\",\n", "                        \"value\": \"297.5\",\n", "                        \"footnotes\": [\n", "                            {\n", "                                \"code\": \"P\",\n", "                                \"text\": \"preliminary\"\n", "                            }\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"297.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"299.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2025\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"294.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"302.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"305.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"305.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"305.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"306.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"310.3\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"302.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"300.0\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"296.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"295.1\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"295.8\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2024\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"296.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M12\",\n", "                        \"periodName\": \"December\",\n", "                        \"value\": \"297.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M11\",\n", "                        \"periodName\": \"November\",\n", "                        \"value\": \"296.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M10\",\n", "                        \"periodName\": \"October\",\n", "                        \"value\": \"275.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M09\",\n", "                        \"periodName\": \"September\",\n", "                        \"value\": \"297.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M08\",\n", "                        \"periodName\": \"August\",\n", "                        \"value\": \"294.5\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M07\",\n", "                        \"periodName\": \"July\",\n", "                        \"value\": \"296.4\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M06\",\n", "                        \"periodName\": \"June\",\n", "                        \"value\": \"294.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M05\",\n", "                        \"periodName\": \"May\",\n", "                        \"value\": \"294.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M04\",\n", "                        \"periodName\": \"April\",\n", "                        \"value\": \"296.7\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M03\",\n", "                        \"periodName\": \"March\",\n", "                        \"value\": \"288.9\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M02\",\n", "                        \"periodName\": \"February\",\n", "                        \"value\": \"294.6\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    },\n", "                    {\n", "                        \"year\": \"2023\",\n", "                        \"period\": \"M01\",\n", "                        \"periodName\": \"January\",\n", "                        \"value\": \"294.2\",\n", "                        \"footnotes\": [\n", "                            {}\n", "                        ]\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    }\n", "}\n"]}], "source": ["import json\n", "print(json.dumps(json_data, indent=4))"]}, {"cell_type": "markdown", "id": "624dcb26", "metadata": {}, "source": ["# FRED"]}, {"cell_type": "code", "execution_count": 21, "id": "0e904b76", "metadata": {}, "outputs": [], "source": ["fred_api_key = \"51c0e7ec1da836c20ce6d40a9e50f5d1\""]}, {"cell_type": "code", "execution_count": 22, "id": "e17d7141", "metadata": {}, "outputs": [], "source": ["from fredapi import Fred\n", "fred = Fred(api_key=fred_api_key)"]}, {"cell_type": "code", "execution_count": 27, "id": "9c3956e7", "metadata": {}, "outputs": [], "source": ["data = fred.get_series('CAPG311S',\n", "                               observation_start='2023-01-01',\n", "                               observation_end='2025-12-31')"]}, {"cell_type": "code", "execution_count": 28, "id": "c2c619b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["2023-01-01    121.2157\n", "2023-02-01    121.1278\n", "2023-03-01    121.0475\n", "2023-04-01    120.9746\n", "2023-05-01    120.9074\n", "2023-06-01    120.8451\n", "2023-07-01    120.7871\n", "2023-08-01    120.7320\n", "2023-09-01    120.6786\n", "2023-10-01    120.6260\n", "2023-11-01    120.5738\n", "2023-12-01    120.5215\n", "2024-01-01    120.4686\n", "2024-02-01    120.4148\n", "2024-03-01    120.3598\n", "2024-04-01    120.3039\n", "2024-05-01    120.2469\n", "2024-06-01    120.1890\n", "2024-07-01    120.1307\n", "2024-08-01    120.0721\n", "2024-09-01    120.0135\n", "2024-10-01    119.9548\n", "2024-11-01    119.8963\n", "2024-12-01    119.8383\n", "2025-01-01    119.9752\n", "2025-02-01    120.0312\n", "2025-03-01    120.1112\n", "2025-04-01    120.2132\n", "dtype: float64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "77dc6a0a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}