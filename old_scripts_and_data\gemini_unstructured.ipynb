{"cells": [{"cell_type": "code", "execution_count": 1, "id": "54ceb9d3", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "fe1df40c", "metadata": {}, "outputs": [], "source": ["api_key = \"********************************************************************************************************************************************************************\""]}, {"cell_type": "code", "execution_count": 3, "id": "37019f65", "metadata": {}, "outputs": [], "source": ["client = OpenAI(api_key=api_key)"]}, {"cell_type": "code", "execution_count": 10, "id": "cd267441", "metadata": {}, "outputs": [], "source": ["user_prompt_2 = \"\"\"\\\n", "I want to know {company_name}’s plans to increase its manufacturing capacity or open a new plant in United States from January 1, 2024, through today.\n", "\n", "When answering, please adhere strictly to these rules:\n", "\n", "1. **Web search**\n", "\n", "   * Search only the Official SEC filings (10-K/10-Q/8-K) of the target company to gather authoritative sources.\n", "   * Do not search any other source apart from the official SEC filings.\n", "\n", "2. **Identify Capacity-Expansion Plans**\n", "\n", "   * Locate any statements in the found sources where intentions, plans, or commitments are described to:\n", "\n", "     * Expand existing factories\n", "     * Build or commission new manufacturing facilities.\n", "\n", "3. **Quantify Every Plan**\n", "\n", "   * Quantify the manufacturing capacity expansion or new plant opening plans by citing any available numerical metrics, such as the allocated capital expenditure for increasing the existing plant capacity or establishing the new plant, planned plant capacity, or other relevant figures.\n", "\n", "4. **Time-<PERSON>ame <PERSON>**\n", "\n", "   * Include only those sources dated on or after January 1, 2024, up to the current date.\n", "   * Exclude any sources from earlier periods.\n", "\n", "5. **Output Structure**\n", "\n", "   - Present your findings in JSON format with following fields:\n", "\n", "     - `company`: name of the target company.\n", "     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, title, section, url).  \n", "     - `summary`: a concise overview of `plan_details`from `capacity_expansion_plans`.\n", "   - Order `plan_details` chronologically (earliest → latest).\n", "\n", "6. After the table provide a short summary encompassing the overall information regarding manufacturing capacity expansion and new plant openings.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 21, "id": "070401be", "metadata": {}, "outputs": [], "source": ["\n", "user_prompt_capacity_increase = \"\"\"\\\n", "You are a research assistant with access to web-search. Tell me about Tesla's plans to increase its manufacturing capacity\\\n", "or opening a new plant in the United States from 2024-01-01, through today.\n", " \n", "When responding, adhere strictly to these rules:\n", "\n", "1. Web search: \n", "   - Search the Official SEC filings (10-K/10-Q/8-K), relevant websites, news articles, press releases etc. to gather authoritative sources.\n", "   - Web Search Priority:\n", "      Conduct a thorough search in the following strict order of priority:\n", "      - First, check EDGAR for **official SEC filings** (10-K, 10-Q, 8-K) for relevant details.\n", "      - Next, look at **company press releases**.\n", "      - Then, search **reputable news sources or industry publications**.\n", "      - Finally, refer to **any other relevant web content**.\n", "   - SEC filings are considered as the most reliable source, hence you must prioritize them over other sources.\n", "\n", "2. Plan Identification:\n", "   - Identify Capacity-Expansion Plans: \n", "      - Locate statements describing intentions, plans, or commitments to:\n", "         - Expand existing factories  \n", "         - Build or commission new manufacturing facilities.\n", "      - Quantify Every Plan:\n", "         - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "   \n", "4. Time-<PERSON><PERSON>: \n", "   - Include only sources dated on or after 2024-01-01, up to today's date.  \n", "   - Exclude any sources published before 2024-01-01.\n", "\n", "5. Restrict to Company's Own Data: \n", "   - Only include target company's own plans to increase its manufacturing capacity or opening a new plant\\\n", "     Do not include any supplier, vendor, or third‑party manufacturing partner plans or information.\n", "\n", "6. Output Structure:\n", "   - Return results in JSON with these fields:\n", "     - `company`: name of the target company.\n", "     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, agency, title, section, url), and `confidence_score`.  \n", "     - `summary`: a concise overview of `plan_details`from `capacity_expansion_plans`.\n", "   - Order `plan_details` chronologically (earliest → latest).\n", "\n", "7. Confidence Score Calculation: \n", "   - A score between 0 and 1 suggesting confidence in the information given in plan details and source, calculated as follows:  \n", "      - Source Reliability (0.0–0.5):  \n", "         0.5=Official SEC filing (10-K/10-Q/8-K) ; 0.4=Company's official press release; 0.3=Tier-1 industry press; 0.2=Blog/aggregator; 0.0=Unverified forum/social media  \n", "      - Specificity Score(0.0–0.5):  \n", "         0.5=Direct management quote with numeric metric; 0.4=Exact excerpt paraphrased; 0.3=Summary statement without numbers; 0.2=Analyst commentary; 0.0= pure Speculation  \n", "      - `confidence_score = Source Reliability + Specificity Score`\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 22, "id": "2f35ecb8", "metadata": {}, "outputs": [], "source": ["output_format = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"company\":{\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Name of the company about which the information is being asked by the user.\"\n", "                },\n", "                \"capacity_expansion_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Capacity expansion or new plant opening plan details of the company under consideration as per the found sources.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the source was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the document or article\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"confidence_score\": {\n", "                                \"type\": \"number\",\n", "                                \"description\": \"A score between 0 and 1 calculated as Source Reliability (0.0–0.5) + Specificity Score(0.0–0.5).\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\",\n", "                            \"confidence_score\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"summary\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Short overview of all plan details.\"\n", "                }\n", "            },\n", "            \"required\": [\"company\",\"capacity_expansion_plans\",\"summary\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 7, "id": "49e8eed6", "metadata": {}, "outputs": [], "source": ["output_format_2 = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"company\":{\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Name of the company about which the information is being asked by the user.\"\n", "                },\n", "                \"capacity_expansion_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Capacity expansion or new plant opening plan details of the company under consideration as per the SEC filings.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the SEC filing was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the SEC filing (10-K/10-Q/8-K)\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"summary\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Short overview of all plan details.\"\n", "                }\n", "            },\n", "            \"required\": [\"company\",\"capacity_expansion_plans\",\"summary\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 23, "id": "46ee42b0", "metadata": {}, "outputs": [], "source": ["response = client.responses.create(\n", "    model=\"gpt-4.1\",\n", "    tools=[{\n", "        \"type\": \"web_search_preview\",\n", "        \"search_context_size\": \"high\",\n", "    }],\n", "    input=user_prompt_capacity_increase,\n", "    text=output_format\n", ")"]}, {"cell_type": "code", "execution_count": 24, "id": "76a9b839", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"Tesla\",\n", "    \"capacity_expansion_plans\": [\n", "        {\n", "            \"plan_details\": \"Tesla is expanding Gigafactory Nevada to include a production line for the Tesla Semi and additional battery manufacturing capacity. The facility will produce 100 GWh of batteries annually, sufficient for 1.5 million light-duty vehicles. Tesla is investing $3.6 billion over 10 years in this expansion.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2023-03-02\",\n", "                \"publishing_agency\": \"Reno Gazette Journal\",\n", "                \"title\": \"Nevada approves $330 million in tax incentives for Tesla electric semi facility. What we know.\",\n", "                \"section\": \"Expansion for Tesla Semi\",\n", "                \"url\": \"https://www.rgj.com/story/news/2023/03/02/nevada-approves-330-million-in-tax-incentives-for-tesla-electric-semi-facility-what-we-know/69912300007/\"\n", "            },\n", "            \"confidence_score\": 0.7\n", "        },\n", "        {\n", "            \"plan_details\": \"Tesla is constructing a new Megafactory near Houston, Texas, to operate a battery storage facility. The 1-million-square-foot building will manage and produce Tesla's Megapack energy products. The project is expected to create around 1,500 jobs.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2025-03-05\",\n", "                \"publishing_agency\": \"Reuters\",\n", "                \"title\": \"Tesla to build new megafactory in Texas, Electrek reports\",\n", "                \"section\": \"None\",\n", "                \"url\": \"https://www.reuters.com/business/autos-transportation/tesla-build-new-megafactory-texas-electrek-reports-2025-03-05/\"\n", "            },\n", "            \"confidence_score\": 0.7\n", "        },\n", "        {\n", "            \"plan_details\": \"Tesla plans to start trial production of the Cybercab and Semi models in October 2025, with mass production in 2026. The Cybercab will be produced in Texas and the Semi in Nevada.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2025-05-14\",\n", "                \"publishing_agency\": \"Reuters\",\n", "                \"title\": \"Tesla to resume shipping Chinese parts for Cybercab, Semi production in the US, source says\",\n", "                \"section\": \"None\",\n", "                \"url\": \"https://www.reuters.com/business/autos-transportation/tesla-resume-shipping-chinese-parts-cybercab-semi-production-us-source-says-2025-05-14/\"\n", "            },\n", "            \"confidence_score\": 0.7\n", "        }\n", "    ],\n", "    \"summary\": \"Tesla is expanding its manufacturing capacity in the United States with significant investments in Nevada and Texas. The company is enhancing Gigafactory Nevada to produce the Tesla Semi and additional batteries, building a new Megafactory in Texas for Megapack production, and planning to commence production of the Cybercab and Semi models in 2025.\"\n", "}\n"]}], "source": ["data = json.loads(response.output_text)\n", "print(json.dumps(data, indent=4))"]}, {"cell_type": "code", "execution_count": 8, "id": "26bf6615", "metadata": {}, "outputs": [], "source": ["response = client.responses.create(\n", "    model=\"gpt-4.1\",\n", "    tools=[{\n", "        \"type\": \"web_search_preview\",\n", "        \"search_context_size\": \"high\",\n", "    }],\n", "    input=user_prompt_2,\n", "    text=output_format_2\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "b5371408", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"Tesla, Inc.\",\n", "    \"capacity_expansion_plans\": [\n", "        {\n", "            \"plan_details\": \"Tesla is focused on profitable growth by leveraging existing factories and production lines to introduce new and more affordable products, including the Cybertruck and Tesla Semi, as well as future vehicles utilizing its next-generation platform.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-10-24\",\n", "                \"title\": \"Form 10-Q for the Quarter Ended September 30, 2024\",\n", "                \"section\": \"Management Opportunities, Challenges and Uncertainties and 2024 Outlook\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/1318605/000162828024043486/tsla-20240930.htm\"\n", "            }\n", "        },\n", "        {\n", "            \"plan_details\": \"Tesla plans to invest over $11 billion in capital expenditures in 2024, with investments ranging between $8 billion to $10 billion in each of the following two fiscal years. These investments are primarily focused on developing and ramping new products, building or expanding manufacturing facilities, piloting new battery cell technologies, and expanding the Supercharger network and autonomy capabilities.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-10-24\",\n", "                \"title\": \"Form 10-Q for the Quarter Ended September 30, 2024\",\n", "                \"section\": \"Material Cash Requirements\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/1318605/000162828024043486/tsla-20240930.htm\"\n", "            }\n", "        }\n", "    ],\n", "    \"summary\": \"Tesla is investing over $11 billion in 2024 and $8-10 billion in subsequent years to develop new products, expand manufacturing facilities, and enhance technologies, including the Cybertruck, Tesla Semi, and next-generation vehicles.\"\n", "}\n"]}], "source": ["data = json.loads(response.output_text)\n", "print(json.dumps(data, indent=4))"]}, {"cell_type": "code", "execution_count": 15, "id": "00e6afe3", "metadata": {}, "outputs": [], "source": ["response = client.responses.create(\n", "    model=\"gpt-4.1\",\n", "    tools=[{\n", "        \"type\": \"web_search_preview\",\n", "        \"search_context_size\": \"high\",\n", "    }],\n", "    input=user_prompt_2.format(company_name = \"Ford Motor Company\"),\n", "    text=output_format_2\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "13c48132", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"Ford Motor Company\",\n", "    \"capacity_expansion_plans\": [\n", "        {\n", "            \"plan_details\": \"Ford Motor Company, through its joint venture BlueOval SK, LLC, entered into a Sponsor Support, Share Retention and Subordination Agreement with the U.S. Department of Energy on December 13, 2024. This agreement pertains to the construction and operation of battery manufacturing facilities in the United States, with the final maturity date of the associated loan expected to be in July 2040.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-12-16\",\n", "                \"title\": \"Form 8-K\",\n", "                \"section\": \"Item 9.01. Financial Statements and Exhibits.\",\n", "                \"url\": \"https://www.streetinsider.com/SEC%2BFilings/Form%2B%2B8-K%2B%2B%2B%2B%2B%2B%2B%2BFORD%2BMOTOR%2BCO%2B%2B%2B%2B%2B%2B%2B%2B%2B%2B%2B%2B%2BFor%3A%2BDec%2B13/24110835.html\"\n", "            }\n", "        }\n", "    ],\n", "    \"summary\": \"Ford Motor Company, through its joint venture BlueOval SK, LLC, has entered into an agreement with the U.S. Department of Energy to construct and operate battery manufacturing facilities in the United States, with the associated loan maturing in July 2040.\"\n", "}\n"]}], "source": ["data = json.loads(response.output_text)\n", "print(json.dumps(data, indent=4))"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}