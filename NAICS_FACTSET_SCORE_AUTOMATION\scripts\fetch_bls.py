import requests
import json
import pandas as pd
import sys
import time
from pathlib import Path

project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

import config

# BLS API allows a max of 50 series per request with a v2 key, but let's stick to 25 for safety/older keys.
MAX_SERIES_PER_REQUEST = 25
BLS_API_ENDPOINT = 'https://api.bls.gov/publicAPI/v2/timeseries/data/' # Using v2 is generally better

def fetch_and_save_bls_data(series_ids: list, start_year: str, end_year: str):
    """
    Fetches data from the BLS API, handles deduplication with existing data,
    and saves it to a single CSV file.

    Args:
        series_ids (list): A list of BLS series ID strings.
        start_year (str): The starting year for the data pull.
        end_year (str): The ending year for the data pull.
    """
    print("Starting BLS data fetch...")
    
    all_series_data = []
    
    # 1. Chunk the series_ids into lists of appropriate size for the API
    id_chunks = [series_ids[i:i + MAX_SERIES_PER_REQUEST] for i in range(0, len(series_ids), MAX_SERIES_PER_REQUEST)]
    
    print(f"Total series to fetch: {len(series_ids)}. This will require {len(id_chunks)} API calls.")

    # 2. Loop through the chunks, making an API call for each
    for i, chunk in enumerate(id_chunks):
        print(f"Fetching chunk {i+1}/{len(id_chunks)}...")
        headers = {'Content-type': 'application/json'}
        data = json.dumps({
            "seriesid": chunk,
            "startyear": start_year,
            "endyear": end_year,
            "registrationkey": config.BLS_API_KEY # Best to use a key if you have one
        })

        try:
            p = requests.post(BLS_API_ENDPOINT, data=data, headers=headers)
            p.raise_for_status()  # This will raise an HTTPError if the status is 4xx or 5xx
            json_data = p.json()

            if json_data['status'] != 'REQUEST_SUCCEEDED':
                print(f"  - API call failed for chunk {i+1}. Message: {json_data.get('message')}")
                continue

            # 3. Collect all the results into a single list
            for series in json_data['Results']['series']:
                series_id = series['seriesID']
                for item in series['data']:
                    all_series_data.append({
                        'seriesID': series_id,
                        'year': item['year'],
                        'period': item['period'],
                        'periodName': item['periodName'],
                        'value': item['value']
                    })
            
            # The BLS API has rate limits, a small delay can prevent issues.
            time.sleep(1) 

        except requests.exceptions.RequestException as e:
            print(f"  - An error occurred during API request for chunk {i+1}: {e}")
        except json.JSONDecodeError:
            print(f"  - Failed to decode JSON response for chunk {i+1}. Response text: {p.text}")
            
    if not all_series_data:
        print("No new data was fetched from the API. Exiting.")
        return

    # 4. Convert the list of new data to a pandas DataFrame
    new_df = pd.DataFrame(all_series_data)
    print(f"Successfully fetched {len(new_df)} data points from the API.")

    # --- THIS IS THE NEW DEDUPLICATION LOGIC ---
    output_path = config.RAW_DATA_DIR / "bls_employment_data_raw.csv"

    final_df = new_df

    if output_path.exists():
        print(f"Found existing data at {output_path}. Merging and deduplicating.")
        old_df = pd.read_csv(output_path)
        combined_df = pd.concat([old_df, new_df], ignore_index=True)
        
        # Drop duplicates based on the unique keys, keeping the *last* entry (the new one)
        final_df = combined_df.drop_duplicates(subset=['seriesID', 'year', 'period'], keep='last')
        print(f"Combined data size: {len(final_df)} rows.")
    
    # Sort the data for consistency before saving
    final_df = final_df.sort_values(by=['seriesID', 'year', 'period'], ascending=[True, False, False])

    # 5. Save the final, clean DataFrame to the raw_data directory
    final_df.to_csv(output_path, index=False)
    print(f"BLS data fetch complete. Data saved to {output_path}")


if __name__ == '__main__':
    # This is a good place to load IDs from your master_sheet.csv
    # For now, we use a test list.
    try:
        master_sheet_df = pd.read_csv(config.MASTER_SHEET_PATH)
        # Assuming your master sheet has a column named 'BLS_SERIES_ID'
        # and we need to drop any missing values
        series_to_fetch = master_sheet_df['BLS_SERIES_ID'].dropna().unique().tolist()
    except FileNotFoundError:
        print("Master sheet not found. Using a default list of test IDs.")
        series_to_fetch = [
            'CES0000000001', # A common total nonfarm series for testing
            'CES3231120001', 'CES3133640001', 'CES3231180001', 'CES3231170001'
        ]
    except KeyError:
        print("'BLS_SERIES_ID' column not in master sheet. Using test IDs.")
        series_to_fetch = [
            'CES0000000001', 
            'CES3231120001', 'CES3133640001', 'CES3231180001', 'CES3231170001'
        ]

    if not series_to_fetch:
        print("No BLS Series IDs found to fetch. Exiting.")
    else:
        # We can make these dates dynamic later with time_helpers.py
        fetch_and_save_bls_data(series_ids=series_to_fetch, start_year="2021", end_year="2024")