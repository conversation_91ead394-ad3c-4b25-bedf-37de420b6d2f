{"cells": [{"cell_type": "code", "execution_count": 1, "id": "91b274ba", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "cdfcba9d", "metadata": {}, "outputs": [], "source": ["region_df = pd.read_excel(\"Motor Vehicles_region.xlsx\")"]}, {"cell_type": "markdown", "id": "dc3b5ba3", "metadata": {}, "source": ["## creating a dictionary of unique labels for each company"]}, {"cell_type": "code", "execution_count": 3, "id": "27fc24e1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>entity_proper_name</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-10-31</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>United States and Canada</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-10-31</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>Europe/Africa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-10-31</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>United States</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-10-31</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>Rest of World</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-10-31</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>United States and Canada</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date entity_proper_name                     label\n", "0 2020-10-31    REV Group, Inc.  United States and Canada\n", "1 2020-10-31    REV Group, Inc.             Europe/Africa\n", "2 2020-10-31    REV Group, Inc.             United States\n", "3 2020-10-31    REV Group, Inc.             Rest of World\n", "4 2021-10-31    REV Group, Inc.  United States and Canada"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["region_df[['date','entity_proper_name', 'label']].head()"]}, {"cell_type": "markdown", "id": "f53f9e00", "metadata": {}, "source": ["### removing those rows which have missing sales value"]}, {"cell_type": "code", "execution_count": 4, "id": "0bb3dd4b", "metadata": {}, "outputs": [], "source": ["rows_with_missing = region_df[region_df['sales'].isna()]"]}, {"cell_type": "code", "execution_count": 5, "id": "63dec4cb", "metadata": {}, "outputs": [{"data": {"text/plain": ["52"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(rows_with_missing)"]}, {"cell_type": "code", "execution_count": 6, "id": "a70e4471", "metadata": {}, "outputs": [], "source": ["region_df_filtered = region_df.dropna(subset=['sales'])"]}, {"cell_type": "code", "execution_count": 7, "id": "319dfbaa", "metadata": {}, "outputs": [{"data": {"text/plain": ["(154, 154)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["region_df_filtered.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "aa925b4a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(206, 154)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["region_df.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "6d5aedb0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['fsym_id', 'date', 'ff_segment_type', 'ff_segment_num', 'adjdate_x', 'currency_x', 'label', 'sales', 'opinc', 'assets', 'capex', 'dep', 'footnote_opinc', 'footnote_assets', 'footnote_dep', 'entity_proper_name', 'factset_industry_code', 'factset_industry_desc', 'factset_sector_code', 'factset_sector_desc', 'adjdate_y', 'currency_y', 'ff_fpnc', 'ff_upd_type', 'ff_sales', 'ff_int_inc', 'ff_non_int_inc', 'ff_gross_inc', 'ff_oper_exp_oth', 'ff_oper_exp_tot', 'ff_oper_inc', 'ff_int_exp_tot', 'ff_loan_loss_prov', 'ff_non_int_exp', 'ff_ptx_xord_chrg', 'ff_ptx_xord_cr', 'ff_ptx_inc', 'ff_inc_tax', 'ff_min_int_exp', 'ff_eq_aff_inc', 'ff_net_income', 'ff_div_pfd', 'ff_cash_st', 'ff_cash_only', 'ff_cash_due_fr_bk', 'ff_bk_invest_tot', 'ff_prem_receiv', 'ff_receiv_tot', 'ff_inven', 'ff_assets_curr', 'ff_assets_oth_intang', 'ff_intang', 'ff_assets', 'ff_deps', 'ff_liabs_curr', 'ff_debt_st', 'ff_pay_acct', 'ff_accr_exp_xpayr', 'ff_ins_liabs_pol', 'ff_debt_lt', 'ff_prov_risk', 'ff_dfd_tax_itc', 'ff_liabs_xmin_int_accum', 'ff_pfd_stk', 'ff_com_eq', 'ff_debt', 'ff_debt_lt_conv', 'ff_shldrs_eq', 'ff_tier1_cap', 'ff_tier2_cap', 'ff_int_pay', 'ff_secs_custody', 'ff_secs_invest', 'ff_net_inc_cf', 'ff_dep_exp_cf', 'ff_dfd_tax_cf', 'ff_non_cash', 'ff_funds_oper_gross', 'ff_xord_cf', 'ff_wkcap_chg', 'ff_acq_bus_cf', 'ff_loan_incr_cf', 'ff_loan_decr_cf', 'ff_sale_assets_bus_cf', 'ff_invest_activ_cf', 'ff_invest_sources_cf', 'ff_div_cf', 'ff_deps_decr_cf', 'ff_deps_incr_cf', 'ff_for_exch_cf', 'ff_chg_cash_cf', 'ff_dps', 'ff_ebit_oper_ps', 'ff_eps_reported', 'ff_bps', 'ff_com_shs_out_eps_basic', 'ff_com_shs_out_eps_dil', 'ff_com_shs_out', 'ff_price_close_fp', 'ff_invest_yld_5yavg', 'ff_actg_standard', 'ff_fy_length_days', 'ff_consol_net_inc', 'ff_eq_tot', 'ff_incr_fed_home_adv_cf', 'ff_eps_basic', 'ff_com_shs_out_eps', 'ff_exp_tot', 'ff_notes_receiv_lt', 'ff_pay_tax', 'ff_dfd_tax', 'ff_min_int_accum', 'ff_liabs_shldrs_eq', 'ff_loan_net', 'ff_invest_aff', 'ff_cust_accept', 'ff_invest_re', 'ff_ppe_net', 'ff_rsrv_noneq', 'ff_deps_cust', 'ff_receiv_st', 'ff_adj_net_oth', 'ff_dps_gross', 'ff_div_pay_out_ps', 'ff_rsrv_chg', 'ff_dfd_tax_db', 'ff_dfd_tax_cr', 'ff_receiv_int', 'ff_restate_ind', 'ff_eps_rpt_date', 'ff_source_is_date', 'ff_source_bs_date', 'ff_source_cf_date', 'ff_cash_restr', 'ff_com_eq_retain_earn', 'ff_source_doc', 'ff_report_freq_code', 'ff_fiscal_date', 'ff_fyr', 'ff_dps_exdate', 'ff_inven_lifo', 'ff_rent_inc', 'ff_loan_chg_cf', 'ff_invest_activ_oth', 'ff_dep_chg_cf', 'ff_assets_nonperf', 'ff_dps_all', 'ff_bk_com_eq_tier1_tot', 'ff_misc_net_oth', 'ff_cap_lease_curr', 'ff_curr_ins_ben', 'ff_intang_devt', 'ff_net_inc_aft_pfd', 'ff_oper_lease_repay']\n"]}], "source": ["print(list(region_df_filtered.columns))"]}, {"cell_type": "code", "execution_count": 10, "id": "8816ae19", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>entity_proper_name</th>\n", "      <th>ff_sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>2224.7</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>1.6</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>51.3</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>2332.0</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2380.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>6.4</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2380.8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    fsym_id       date                     label   sales entity_proper_name  \\\n", "0  C2083V-R 2020-10-31  United States and Canada  2224.7    REV Group, Inc.   \n", "1  C2083V-R 2020-10-31             Europe/Africa     1.6    REV Group, Inc.   \n", "3  C2083V-R 2020-10-31             Rest of World    51.3    REV Group, Inc.   \n", "4  C2083V-R 2021-10-31  United States and Canada  2332.0    REV Group, Inc.   \n", "5  C2083V-R 2021-10-31             Europe/Africa     6.4    REV Group, Inc.   \n", "\n", "   ff_sales  \n", "0    2277.6  \n", "1    2277.6  \n", "3    2277.6  \n", "4    2380.8  \n", "5    2380.8  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_columns = ['fsym_id', 'date', 'label', 'sales', 'entity_proper_name', 'ff_sales']\n", "new_df = region_df_filtered[selected_columns].copy()\n", "\n", "new_df.head()\n"]}, {"cell_type": "code", "execution_count": 11, "id": "38cef1d8", "metadata": {}, "outputs": [], "source": ["# Function to lowercase and strip punctuation\n", "import string\n", "def normalize_label(text):\n", "    return text.translate(str.maketrans('', '', string.punctuation)).lower().strip()"]}, {"cell_type": "code", "execution_count": 12, "id": "6cd6489f", "metadata": {}, "outputs": [], "source": ["new_df['normalized_label'] = new_df['label'].apply(normalize_label)"]}, {"cell_type": "code", "execution_count": 13, "id": "5dc8db16", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>entity_proper_name</th>\n", "      <th>ff_sales</th>\n", "      <th>normalized_label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>2224.7</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "      <td>united states and canada</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>1.6</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "      <td>europeafrica</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>51.3</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>2332.0</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2380.8</td>\n", "      <td>united states and canada</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>6.4</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2380.8</td>\n", "      <td>europeafrica</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    fsym_id       date                     label   sales entity_proper_name  \\\n", "0  C2083V-R 2020-10-31  United States and Canada  2224.7    REV Group, Inc.   \n", "1  C2083V-R 2020-10-31             Europe/Africa     1.6    REV Group, Inc.   \n", "3  C2083V-R 2020-10-31             Rest of World    51.3    REV Group, Inc.   \n", "4  C2083V-R 2021-10-31  United States and Canada  2332.0    REV Group, Inc.   \n", "5  C2083V-R 2021-10-31             Europe/Africa     6.4    REV Group, Inc.   \n", "\n", "   ff_sales          normalized_label  \n", "0    2277.6  united states and canada  \n", "1    2277.6              europeafrica  \n", "3    2277.6             rest of world  \n", "4    2380.8  united states and canada  \n", "5    2380.8              europeafrica  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "a69a97db", "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "# Create a defaultdict to group normalized labels by company\n", "company_label_dict = defaultdict(set)\n", "\n", "# Populate the dictionary\n", "for _, row in new_df.iterrows():\n", "    company = row['entity_proper_name']\n", "    label = row['normalized_label']\n", "    if pd.notna(company) and pd.notna(label):\n", "        company_label_dict[company].add(label)\n", "\n", "# Optional: convert sets to sorted lists for cleaner output\n", "company_label_dict = {\n", "    company: sorted(list(labels)) for company, labels in company_label_dict.items()\n", "}\n"]}, {"cell_type": "code", "execution_count": 15, "id": "13845091", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"REV Group, Inc.\": [\n", "        \"europeafrica\",\n", "        \"north america\",\n", "        \"rest of world\",\n", "        \"united states and canada\"\n", "    ],\n", "    \"Rivian Automotive, Inc.\": [\n", "        \"united states\"\n", "    ],\n", "    \"Oshkosh Corp.\": [\n", "        \"europe africa and middle east\",\n", "        \"other north america\",\n", "        \"rest of the world\",\n", "        \"united states\"\n", "    ],\n", "    \"Harley-Davidson, Inc.\": [\n", "        \"australia and new zealand\",\n", "        \"canada\",\n", "        \"emea\",\n", "        \"japan\",\n", "        \"other countries\",\n", "        \"united states\"\n", "    ],\n", "    \"Tesla, Inc.\": [\n", "        \"china\",\n", "        \"other international\",\n", "        \"united states\"\n", "    ],\n", "    \"Lucid Group, Inc.\": [\n", "        \"middle east\",\n", "        \"north america\",\n", "        \"other international\",\n", "        \"saudi arabia\",\n", "        \"united states\"\n", "    ],\n", "    \"General Motors Co.\": [\n", "        \"nonunited states\",\n", "        \"united states\"\n", "    ],\n", "    \"Miller Industries, Inc. (Tennessee)\": [\n", "        \"foreign\",\n", "        \"north america\"\n", "    ],\n", "    \"Blue Bird Corp.\": [\n", "        \"canada\",\n", "        \"rest of world\",\n", "        \"united states\"\n", "    ],\n", "    \"Ford Motor Co.\": [\n", "        \"all other\",\n", "        \"canada\",\n", "        \"germany\",\n", "        \"mexico\",\n", "        \"united kingdom\",\n", "        \"united states\"\n", "    ]\n", "}\n"]}], "source": ["import json\n", "print(json.dumps(company_label_dict, indent=4))\n"]}, {"cell_type": "markdown", "id": "c9a3ed7d", "metadata": {}, "source": ["## Reducing dictionary using LLM"]}, {"cell_type": "code", "execution_count": 16, "id": "f57b2e74", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "api_key = \"********************************************************************************************************************************************************************\"\n", "\n", "client = OpenAI(api_key=api_key)"]}, {"cell_type": "code", "execution_count": 17, "id": "240528be", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"\\\n", "You are a geography-aware assistant that helps identify which regional labels refer to the United States for each company. Each company has a list of region labels. If a label clearly refers to the U.S. (e.g. 'united states', 'us', 'u.s.a.', etc.), return it.\n", "If no label refers directly to the U.S., return that label which is closest to the U.S. such as 'north america' or 'canada' etc. Return your output as a JSON object mapping company names to the best matching label.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 18, "id": "bd3c0667", "metadata": {}, "outputs": [], "source": ["output = {\n", "  \"format\": {\n", "    \"type\": \"json_schema\",\n", "    \"name\": \"company_region_labels\",\n", "    \"schema\": {\n", "      \"type\": \"object\",\n", "      \"properties\": {\n", "        \"companies\": {\n", "          \"type\": \"array\",\n", "          \"description\": \"An array of company-to-region mappings.\",\n", "          \"items\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "              \"company_name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The exact name of the company.\"\n", "              },\n", "              \"region_label\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The label that best identifies the United States (e.g. “us”, “u.s.a.”, “united states”) or, if none is present, the geographically closest alternative region (e.g. “north america”, “canada”).\"\n", "              }\n", "            },\n", "            \"required\": [\"company_name\", \"region_label\"],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "          },\n", "        }\n", "      },\n", "      \"required\": [\"companies\"],\n", "      \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "  }\n", "}\n"]}, {"cell_type": "code", "execution_count": 20, "id": "aaaddb0d", "metadata": {}, "outputs": [], "source": ["response = client.responses.parse(\n", "    model=\"gpt-4.1\",\n", "    input=[\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": json.dumps(company_label_dict, indent=2),\n", "        },\n", "    ],\n", "    temperature=0,\n", "    text=output\n", ")\n"]}, {"cell_type": "code", "execution_count": 21, "id": "530b850c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"companies\": [\n", "        {\n", "            \"company_name\": \"REV Group, Inc.\",\n", "            \"region_label\": \"united states and canada\"\n", "        },\n", "        {\n", "            \"company_name\": \"Rivian Automotive, Inc.\",\n", "            \"region_label\": \"united states\"\n", "        },\n", "        {\n", "            \"company_name\": \"Oshkosh Corp.\",\n", "            \"region_label\": \"united states\"\n", "        },\n", "        {\n", "            \"company_name\": \"Harley-Davidson, Inc.\",\n", "            \"region_label\": \"united states\"\n", "        },\n", "        {\n", "            \"company_name\": \"Tesla, Inc.\",\n", "            \"region_label\": \"united states\"\n", "        },\n", "        {\n", "            \"company_name\": \"Lucid Group, Inc.\",\n", "            \"region_label\": \"united states\"\n", "        },\n", "        {\n", "            \"company_name\": \"General Motors Co.\",\n", "            \"region_label\": \"united states\"\n", "        },\n", "        {\n", "            \"company_name\": \"Miller Industries, Inc. (Tennessee)\",\n", "            \"region_label\": \"north america\"\n", "        },\n", "        {\n", "            \"company_name\": \"Blue Bird Corp.\",\n", "            \"region_label\": \"united states\"\n", "        },\n", "        {\n", "            \"company_name\": \"Ford Motor Co.\",\n", "            \"region_label\": \"united states\"\n", "        }\n", "    ]\n", "}\n"]}], "source": ["data = json.loads(response.output_text)\n", "print(json.dumps(data, indent=4))"]}, {"cell_type": "code", "execution_count": 22, "id": "90d52aa2", "metadata": {}, "outputs": [], "source": ["company_to_chosen = {\n", "    item[\"company_name\"]: item[\"region_label\"]\n", "    for item in data[\"companies\"]\n", "}\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "id": "821340d7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'REV Group, Inc.': 'united states and canada',\n", " 'Rivian Automotive, Inc.': 'united states',\n", " 'Oshkosh Corp.': 'united states',\n", " 'Harley-Davidson, Inc.': 'united states',\n", " 'Tesla, Inc.': 'united states',\n", " 'Lucid Group, Inc.': 'united states',\n", " 'General Motors Co.': 'united states',\n", " 'Miller Industries, Inc. (Tennessee)': 'north america',\n", " 'Blue Bird Corp.': 'united states',\n", " 'Ford Motor Co.': 'united states'}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["company_to_chosen"]}, {"cell_type": "code", "execution_count": 24, "id": "de72cf43", "metadata": {}, "outputs": [], "source": ["def mark_us_region(row):\n", "    chosen = company_to_chosen.get(row[\"entity_proper_name\"])\n", "    # if this row’s normalized_label matches the LLM’s chosen region_label → US_region\n", "    if chosen and row[\"normalized_label\"] == chosen:\n", "        return \"Us_region\"\n", "    else:\n", "        return \"others\""]}, {"cell_type": "code", "execution_count": 25, "id": "e62aff0b", "metadata": {}, "outputs": [], "source": ["new_df[\"US_region\"] = new_df.apply(mark_us_region, axis=1)"]}, {"cell_type": "code", "execution_count": 26, "id": "b1955b5f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(154, 8)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df.shape"]}, {"cell_type": "code", "execution_count": 38, "id": "3c7cb9c1", "metadata": {}, "outputs": [], "source": ["new_df.to_excel(\"region_sales_US.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": 33, "id": "df92016c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>entity_proper_name</th>\n", "      <th>ff_sales</th>\n", "      <th>US_region</th>\n", "      <th>sales_percent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>2224.7</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "      <td>Us_region</td>\n", "      <td>0.9768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>1.6</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "      <td>others</td>\n", "      <td>0.0007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>51.3</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2277.6</td>\n", "      <td>others</td>\n", "      <td>0.0225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>2332.0</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2380.8</td>\n", "      <td>Us_region</td>\n", "      <td>0.9795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>6.4</td>\n", "      <td>REV Group, Inc.</td>\n", "      <td>2380.8</td>\n", "      <td>others</td>\n", "      <td>0.0027</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    fsym_id       date                     label   sales entity_proper_name  \\\n", "0  C2083V-R 2020-10-31  United States and Canada  2224.7    REV Group, Inc.   \n", "1  C2083V-R 2020-10-31             Europe/Africa     1.6    REV Group, Inc.   \n", "3  C2083V-R 2020-10-31             Rest of World    51.3    REV Group, Inc.   \n", "4  C2083V-R 2021-10-31  United States and Canada  2332.0    REV Group, Inc.   \n", "5  C2083V-R 2021-10-31             Europe/Africa     6.4    REV Group, Inc.   \n", "\n", "   ff_sales  US_region  sales_percent  \n", "0    2277.6  Us_region         0.9768  \n", "1    2277.6     others         0.0007  \n", "3    2277.6     others         0.0225  \n", "4    2380.8  Us_region         0.9795  \n", "5    2380.8     others         0.0027  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df.head()"]}, {"cell_type": "markdown", "id": "bb909a77", "metadata": {}, "source": ["## Calculating sales percentage"]}, {"cell_type": "code", "execution_count": 32, "id": "4423c6a1", "metadata": {}, "outputs": [], "source": ["new_df['sales_percent'] = (new_df['sales'] / new_df['ff_sales']).round(4)"]}, {"cell_type": "code", "execution_count": 34, "id": "343fb76f", "metadata": {}, "outputs": [], "source": ["new_df.to_excel(\"region_sales_US_final.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}