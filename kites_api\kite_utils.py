def init_kite():
    from kiteconnect import KiteConnect
    import requests
    from config import API_KEY, ACCESS_TOKEN
    kite = KiteConnect(api_key=API_KEY)
    kite.set_access_token(ACCESS_TOKEN)
    return kite

# Update instruments
def update_instruments():
    kite = init_kite()
    from config import INSTRUMENTS_FILE
    instruments = kite.instruments()
    import pandas as pd
    instruments_df = pd.DataFrame(instruments)
    instruments_df.to_csv(INSTRUMENTS_FILE, index=False)
    return instruments_df

# get quotes
def get_quotes(instruments):
    kite = init_kite()
    quotes = kite.quote(instruments)
    return quotes

# get margin
def get_margin(orders):
    kite = init_kite()
    margins = kite.order_margins(params=orders)
    return margins