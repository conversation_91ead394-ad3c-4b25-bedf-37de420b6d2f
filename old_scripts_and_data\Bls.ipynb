{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import prettytable\n", "headers = {'Content-type': 'application/json'}\n", "data = json.dumps({\"seriesid\": ['CUUR0000SA0','SUUR0000SA0'],\"startyear\":\"2011\", \"endyear\":\"2014\"})\n", "p = requests.post('https://api.bls.gov/publicAPI/v1/timeseries/data/', data=data, headers=headers)\n", "json_data = json.loads(p.text)\n", "for series in json_data['Results']['series']:\n", "    x=prettytable.PrettyTable([\"series id\",\"year\",\"period\",\"value\",\"footnotes\"])\n", "    seriesId = series['seriesID']\n", "    for item in series['data']:\n", "        year = item['year']\n", "        period = item['period']\n", "        value = item['value']\n", "        footnotes=\"\"\n", "        for footnote in item['footnotes']:\n", "            if footnote:\n", "                footnotes = footnotes + footnote['text'] + ','\n", "    \n", "        if 'M01' <= period <= 'M12':\n", "            x.add_row([seriesId,year,period,value,footnotes[0:-1]])\n", "    output = open(seriesId + '.txt','w')\n", "    output.write (x.get_string())\n", "    output.close()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["<_io.TextIOWrapper name='SUUR0000SA0.txt' mode='w' encoding='utf-8'>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Civilian Employment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["https://data.bls.gov/toppicks?survey=bls"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import prettytable\n", "headers = {'Content-type': 'application/json'}\n", "data = json.dumps({\"seriesid\": ['CES3000000001'],\"startyear\":\"2021\", \"endyear\":\"2024\"})\n", "p = requests.post('https://api.bls.gov/publicAPI/v1/timeseries/data/', data=data, headers=headers)\n", "json_data = json.loads(p.text)\n", "for series in json_data['Results']['series']:\n", "    x=prettytable.PrettyTable([\"series id\",\"year\",\"period\",\"value\",\"footnotes\"])\n", "    seriesId = series['seriesID']\n", "    for item in series['data']:\n", "        year = item['year']\n", "        period = item['period']\n", "        value = item['value']\n", "        footnotes=\"\"\n", "        for footnote in item['footnotes']:\n", "            if footnote:\n", "                footnotes = footnotes + footnote['text'] + ','\n", "    \n", "        if 'M01' <= period <= 'M12':\n", "            x.add_row([seriesId,year,period,value,footnotes[0:-1]])\n", "    output = open(seriesId + '.txt','w')\n", "    output.write (x.get_string())\n", "    output.close()"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}