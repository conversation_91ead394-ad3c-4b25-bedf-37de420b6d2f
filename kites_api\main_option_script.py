import pandas as pd
import os
from datetime import datetime, timedelta

from strategy_handeler import (
    get_target_equities,
    get_current_stock_prices,
    get_all_tradable_options_for_targets,
    filter_option_by_strike,
    select_options_by_item_otm,
    get_premium_and_margin_details,
)
from expiry_handler import itm_otm_flagging, make_transaction_log, extract_itm_otm_mapping
from portfolio_manager import update_active_positions, remove_expired_positions, append_to_transaction_log
from config import ACTIVE_POSITIONS_FILE, TRANSACTION_LOG_FILE
import kite_utils


def initialize_tracking_files():
    """
    Creates initial tracking files if they don't exist.
    """
    if not os.path.exists(ACTIVE_POSITIONS_FILE):
        pd.DataFrame(columns=[
            'instrument_token', 'exchange_token', 'tradingsymbol', 'name', 'expiry', 'strike', 'lot_size', 'instrument_type', 'exchange', 'average_price','target_strike','last_price', 'premium_received', 'total_margin'
        ]).to_excel(ACTIVE_POSITIONS_FILE, index=False)

    if not os.path.exists(TRANSACTION_LOG_FILE):
        pd.DataFrame(columns=[
            'instrument_token', 'exchange_token', 'tradingsymbol', 'name', 'expiry', 'strike', 'lot_size', 'instrument_type', 'premium_received', 'total_margin','stock_price_on_exp', 'itm_otm','last_price', 'premium_paid', 'profit_loss', 'yield'
        ]).to_excel(TRANSACTION_LOG_FILE, index=False)


def should_trigger_expiry_check(active_df: pd.DataFrame, reference_date: datetime, days_before_expiry: int) -> bool:
    """
    Checks if any options expire in exactly `days_before_expiry` days from the reference date.
    """
    if active_df.empty:
        return False

    active_df['expiry'] = pd.to_datetime(active_df['expiry'], errors='coerce').dt.normalize()
    target_date = (reference_date + timedelta(days=days_before_expiry)).date()

    matching_rows = active_df[active_df['expiry'] == pd.Timestamp(target_date)]
    print(f"[DEBUG] Checking for expiry == {target_date}, found: {len(matching_rows)} rows.")

    return not matching_rows.empty



def run_expiry_handler(reference_date: datetime, days_before_expiry: int = 0):
    """
    Handles expiry logic only if the reference_date matches expiry condition.
    """
    print("[STEP] Running expiry handler...")
    active_df = pd.read_excel(ACTIVE_POSITIONS_FILE)

    if not should_trigger_expiry_check(active_df, reference_date, days_before_expiry):
        print(f"[INFO] No positions expiring in {days_before_expiry} days. Skipping expiry logic.")
        return {}

    transaction_df = itm_otm_flagging(active_df)
    transaction_log_df = make_transaction_log(transaction_df)
    remove_expired_positions(transaction_log_df)
    append_to_transaction_log(transaction_log_df)
    itm_otm_mapping = extract_itm_otm_mapping(transaction_log_df)
    return itm_otm_mapping



def run_strategy_generator(itm_otm_mapping=None):
    """
    Generates new trades using strategy handler pipeline.
    Adds only new trades to active_positions.
    """
    print("[STEP] Running strategy generator...")
    instruments_df = kite_utils.update_instruments()
    target_equities_df = get_target_equities(instruments_df)
    stock_prices_df = get_current_stock_prices(target_equities_df)

    target_underlying_symbols = stock_prices_df['name'].tolist()
    nearest_expiry_options_df = get_all_tradable_options_for_targets(instruments_df, target_underlying_symbols)
    filtered_options_df = filter_option_by_strike(nearest_expiry_options_df, stock_prices_df)
    selected_options_df = select_options_by_item_otm(filtered_options_df, itm_otm_mapping)
    enriched_options_df = get_premium_and_margin_details(selected_options_df)
    update_active_positions(enriched_options_df)


if __name__ == "__main__":
    print("[START] main_script.py")
    initialize_tracking_files()

    # 1. Handle expiry and update logs
    today = datetime.today()
    itm_otm_map = run_expiry_handler(reference_date=today, days_before_expiry=2)
    print(itm_otm_map) # save this
    print("Squared off the options.")

    # 2. Generate new strategy based on ITM/OTM results
    #run_strategy_generator(itm_otm_map)

    #print("[DONE] Monthly strategy and expiry processing complete.")
