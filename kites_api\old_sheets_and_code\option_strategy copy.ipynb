{"cells": [{"cell_type": "code", "execution_count": 1, "id": "aa04be29", "metadata": {}, "outputs": [], "source": ["from kiteconnect import KiteConnect\n", "import requests"]}, {"cell_type": "code", "execution_count": 2, "id": "2ff21cb5", "metadata": {}, "outputs": [], "source": ["api_key = \"3q0r6l1fb689slhk\"\n", "access_token = \"UqOtB0wBTSsZmCJ1dqip9wjyHtGL8vg4\""]}, {"cell_type": "code", "execution_count": 3, "id": "620d03a0", "metadata": {}, "outputs": [], "source": ["kite = KiteConnect(api_key=api_key)\n", "kite.set_access_token(access_token)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "f81a39bd", "metadata": {}, "outputs": [], "source": ["# Function to fetch instruments\n", "def get_instruments():\n", "    try:\n", "        # API endpoint for instruments\n", "        url = \"https://api.kite.trade/instruments\"\n", "        headers = {\n", "            \"X-Kite-Version\": \"3\",\n", "            \"Authorization\": f\"token {api_key}:{access_token}\"\n", "        }\n", "        \n", "        # Send request\n", "        response = requests.get(url, headers=headers)\n", "        \n", "        # Check if request was successful\n", "        if response.status_code == 200:\n", "            # Print first 100 bytes to check response\n", "            print(\"First 100 bytes of response:\", response.content[:100])\n", "            \n", "            # Save response to a file\n", "            with open(\"instruments.csv\", \"wb\") as file:\n", "                file.write(response.content)\n", "            print(\"Instrument list saved to instruments.csv\")\n", "            \n", "            # Check if response is gzipped\n", "            if response.content.startswith(b'\\x1f\\x8b'):\n", "                print(\"Response is gzipped\")\n", "            else:\n", "                print(\"Response is not gzipped\")\n", "                \n", "            return True\n", "        else:\n", "            print(\"Error: Status code\", response.status_code)\n", "            print(\"Response:\", response.text)\n", "            return False\n", "            \n", "    except Exception as e:\n", "        print(\"Error fetching instruments:\", str(e))\n", "        return False"]}, {"cell_type": "code", "execution_count": 5, "id": "db7cd1e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 100 bytes of response: b'instrument_token,exchange_token,tradingsymbol,name,last_price,expiry,strike,tick_size,lot_size,instr'\n", "Instrument list saved to instruments.csv\n", "Response is not gzipped\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["get_instruments()"]}, {"cell_type": "code", "execution_count": 5, "id": "b3cf1cc2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>*********</td>\n", "      <td>874879</td>\n", "      <td>BANKEX25MAYFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>*********</td>\n", "      <td>1100996</td>\n", "      <td>BANKEX25JUNFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>*********</td>\n", "      <td>1141118</td>\n", "      <td>BANKEX25JULFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-07-29</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>*********</td>\n", "      <td>874759</td>\n", "      <td>SENSEX25MAYFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>*********</td>\n", "      <td>1124583</td>\n", "      <td>SENSEX25603FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-03</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token   tradingsymbol    name  last_price  \\\n", "0         *********          874879  BANKEX25MAYFUT  BANKEX           0   \n", "1         *********         1100996  BANKEX25JUNFUT  BANKEX           0   \n", "2         *********         1141118  BANKEX25JULFUT  BANKEX           0   \n", "3         *********          874759  SENSEX25MAYFUT  SENSEX           0   \n", "4         *********         1124583  SENSEX25603FUT  SENSEX           0   \n", "\n", "       expiry  strike  tick_size  lot_size instrument_type  segment exchange  \n", "0  2025-05-27     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "1  2025-06-24     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "2  2025-07-29     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "3  2025-05-27     0.0       0.05        20             FUT  BFO-FUT      BFO  \n", "4  2025-06-03     0.0       0.05        20             FUT  BFO-FUT      BFO  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "instruments_df = pd.read_csv(\"instruments.csv\")\n", "\n", "instruments_df.head()"]}, {"cell_type": "markdown", "id": "2109cc46", "metadata": {}, "source": ["## Current stock price for all the companies"]}, {"cell_type": "code", "execution_count": 6, "id": "818d6060", "metadata": {}, "outputs": [], "source": ["target_banks = [\n", "    'ICICI BANK',\n", "    'HDFC BANK',\n", "    'STATE BANK OF INDIA',\n", "    'AXIS BANK',\n", "    'KOTAK MAHINDRA BANK',\n", "    'MUTHOOT FINANCE',\n", "    'MANAPPURAM FINANCE'\n", "]\n", "\n", "bank_equities = instruments_df[\n", "    (instruments_df['instrument_type'] == 'EQ') & (instruments_df['segment'] == 'NSE') &\n", "    (instruments_df['name'].isin(target_banks))].copy()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "21b020d5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7, 12)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["bank_equities.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "a07de133", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>87782</th>\n", "      <td>341249</td>\n", "      <td>1333</td>\n", "      <td>HDFCBANK</td>\n", "      <td>HDFC BANK</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>1</td>\n", "      <td>EQ</td>\n", "      <td>NSE</td>\n", "      <td>NSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87946</th>\n", "      <td>492033</td>\n", "      <td>1922</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>KOTAK MAHINDRA BANK</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>1</td>\n", "      <td>EQ</td>\n", "      <td>NSE</td>\n", "      <td>NSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88232</th>\n", "      <td>779521</td>\n", "      <td>3045</td>\n", "      <td>SBIN</td>\n", "      <td>STATE BANK OF INDIA</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>1</td>\n", "      <td>EQ</td>\n", "      <td>NSE</td>\n", "      <td>NSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88612</th>\n", "      <td>1270529</td>\n", "      <td>4963</td>\n", "      <td>ICICIBANK</td>\n", "      <td>ICICI BANK</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>1</td>\n", "      <td>EQ</td>\n", "      <td>NSE</td>\n", "      <td>NSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88832</th>\n", "      <td>1510401</td>\n", "      <td>5900</td>\n", "      <td>AXISBANK</td>\n", "      <td>AXIS BANK</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>1</td>\n", "      <td>EQ</td>\n", "      <td>NSE</td>\n", "      <td>NSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91577</th>\n", "      <td>4879617</td>\n", "      <td>19061</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>MANAPPURAM FINANCE</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.01</td>\n", "      <td>1</td>\n", "      <td>EQ</td>\n", "      <td>NSE</td>\n", "      <td>NSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93193</th>\n", "      <td>6054401</td>\n", "      <td>23650</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>MUTHOOT FINANCE</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.10</td>\n", "      <td>1</td>\n", "      <td>EQ</td>\n", "      <td>NSE</td>\n", "      <td>NSE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument_token  exchange_token tradingsymbol                 name  \\\n", "87782            341249            1333      HDFCBANK            HDFC BANK   \n", "87946            492033            1922     KOTAKBANK  KOTAK MAHINDRA BANK   \n", "88232            779521            3045          SBIN  STATE BANK OF INDIA   \n", "88612           1270529            4963     ICICIBANK           ICICI BANK   \n", "88832           1510401            5900      AXISBANK            AXIS BANK   \n", "91577           4879617           19061    MANAPPURAM   MANAPPURAM FINANCE   \n", "93193           6054401           23650    MUTHOOTFIN      MUTHOOT FINANCE   \n", "\n", "       last_price expiry  strike  tick_size  lot_size instrument_type segment  \\\n", "87782           0    NaN     0.0       0.10         1              EQ     NSE   \n", "87946           0    NaN     0.0       0.10         1              EQ     NSE   \n", "88232           0    NaN     0.0       0.05         1              EQ     NSE   \n", "88612           0    NaN     0.0       0.10         1              EQ     NSE   \n", "88832           0    NaN     0.0       0.10         1              EQ     NSE   \n", "91577           0    NaN     0.0       0.01         1              EQ     NSE   \n", "93193           0    NaN     0.0       0.10         1              EQ     NSE   \n", "\n", "      exchange  \n", "87782      NSE  \n", "87946      NSE  \n", "88232      NSE  \n", "88612      NSE  \n", "88832      NSE  \n", "91577      NSE  \n", "93193      NSE  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["bank_equities.head(8)"]}, {"cell_type": "code", "execution_count": 9, "id": "7f4ec860", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NSE:HDFCBANK', 'NSE:KOTAKBANK', 'NSE:SBIN', 'NSE:ICICIBANK', 'NSE:AXISBANK', 'NSE:MANAPPURAM', 'NSE:MUTHOOTFIN']\n"]}], "source": ["# Create a list of instruments in the format \"exchange:tradingsymbol\"\n", "instruments_list = bank_equities.apply(\n", "    lambda row: f\"{row['exchange']}:{row['tradingsymbol']}\", axis=1\n", ").to_list()\n", "print(instruments_list)"]}, {"cell_type": "code", "execution_count": null, "id": "e58e17e3", "metadata": {}, "outputs": [], "source": ["try:\n", "    quote = kite.quote(instruments_list) \n", "except Exception as e:\n", "    print(f\"Error fetching quote: {e}\")"]}, {"cell_type": "code", "execution_count": 11, "id": "78c7f012", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(quote)"]}, {"cell_type": "code", "execution_count": 12, "id": "e87e7011", "metadata": {}, "outputs": [], "source": ["stk_price_df = pd.DataFrame([\n", "    {\n", "      'instrument': instrument,\n", "      'symbol': instrument.split(':')[1],\n", "      'average_price': quote[instrument]['average_price'] \n", "    }\n", "    for instrument in quote.keys()\n", "])\n"]}, {"cell_type": "code", "execution_count": 13, "id": "56723580", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument</th>\n", "      <th>symbol</th>\n", "      <th>average_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NSE:AXISBANK</td>\n", "      <td>AXISBANK</td>\n", "      <td>1202.64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NSE:HDFCBANK</td>\n", "      <td>HDFCBANK</td>\n", "      <td>1927.84</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NSE:ICICIBANK</td>\n", "      <td>ICICIBANK</td>\n", "      <td>1445.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NSE:KOTAKBANK</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2086.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NSE:MANAPPURAM</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>230.88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>NSE:MUTHOOTFIN</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2092.87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NSE:SBIN</td>\n", "      <td>SBIN</td>\n", "      <td>790.21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument      symbol  average_price\n", "0    NSE:AXISBANK    AXISBANK        1202.64\n", "1    NSE:HDFCBANK    HDFCBANK        1927.84\n", "2   NSE:ICICIBANK   ICICIBANK        1445.26\n", "3   NSE:KOTAKBANK   KOTAKBANK        2086.16\n", "4  NSE:MANAPPURAM  MANAPPURAM         230.88\n", "5  NSE:MUTHOOTFIN  MUTHOOTFIN        2092.87\n", "6        NSE:SBIN        SBIN         790.21"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_price_df.head(8)"]}, {"cell_type": "markdown", "id": "c97e5372", "metadata": {}, "source": ["## Picking up nearest expirying  PUT options with strike price 0-5% below current stock price"]}, {"cell_type": "code", "execution_count": 17, "id": "5629ea45", "metadata": {}, "outputs": [], "source": ["bank_symbols = stk_price_df['symbol'].tolist()  "]}, {"cell_type": "code", "execution_count": 18, "id": "a268d8d6", "metadata": {}, "outputs": [], "source": ["# Filter for PUT options of target companies\n", "put_options = instruments_df[\n", "    (instruments_df['instrument_type'] == 'PE') & \n", "    (instruments_df['name'].isin(bank_symbols))\n", "].copy()"]}, {"cell_type": "code", "execution_count": 19, "id": "029b07a3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>55530</th>\n", "      <td>********</td>\n", "      <td>70939</td>\n", "      <td>AXISBANK25MAY1190PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1190.0</td>\n", "      <td>0.05</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55532</th>\n", "      <td>********</td>\n", "      <td>69295</td>\n", "      <td>AXISBANK25MAY1200PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1200.0</td>\n", "      <td>0.05</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55534</th>\n", "      <td>********</td>\n", "      <td>69293</td>\n", "      <td>AXISBANK25MAY1180PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1180.0</td>\n", "      <td>0.05</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55536</th>\n", "      <td>********</td>\n", "      <td>70941</td>\n", "      <td>AXISBANK25MAY1210PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1210.0</td>\n", "      <td>0.05</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55538</th>\n", "      <td>********</td>\n", "      <td>70937</td>\n", "      <td>AXISBANK25MAY1170PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1170.0</td>\n", "      <td>0.05</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument_token  exchange_token        tradingsymbol      name  \\\n", "55530          ********           70939  AXISBANK25MAY1190PE  AXISBANK   \n", "55532          ********           69295  AXISBANK25MAY1200PE  AXISBANK   \n", "55534          ********           69293  AXISBANK25MAY1180PE  AXISBANK   \n", "55536          ********           70941  AXISBANK25MAY1210PE  AXISBANK   \n", "55538          ********           70937  AXISBANK25MAY1170PE  AXISBANK   \n", "\n", "       last_price      expiry  strike  tick_size  lot_size instrument_type  \\\n", "55530           0  2025-05-29  1190.0       0.05       625              PE   \n", "55532           0  2025-05-29  1200.0       0.05       625              PE   \n", "55534           0  2025-05-29  1180.0       0.05       625              PE   \n", "55536           0  2025-05-29  1210.0       0.05       625              PE   \n", "55538           0  2025-05-29  1170.0       0.05       625              PE   \n", "\n", "       segment exchange  \n", "55530  NFO-OPT      NFO  \n", "55532  NFO-OPT      NFO  \n", "55534  NFO-OPT      NFO  \n", "55536  NFO-OPT      NFO  \n", "55538  NFO-OPT      NFO  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "5d9d8479", "metadata": {}, "outputs": [{"data": {"text/plain": ["(594, 12)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options.shape"]}, {"cell_type": "code", "execution_count": 21, "id": "ddb66845", "metadata": {}, "outputs": [], "source": ["# Find the nearest expiry date for each company\n", "from datetime import datetime\n", "put_options['expiry'] = pd.to_datetime(put_options['expiry'], errors='coerce')\n", "current_date = pd.to_datetime(datetime.today())\n", "valid_expiries = put_options[put_options['expiry'] > current_date]\n", "nearest_expiry = valid_expiries.groupby('name')['expiry'].min().reset_index()\n"]}, {"cell_type": "code", "execution_count": 23, "id": "5225d84a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         name     expiry\n", "0    AXISBANK 2025-05-29\n", "1    HDFCBANK 2025-05-29\n", "2   ICICIBANK 2025-05-29\n", "3   KOTAKBANK 2025-05-29\n", "4  MANAPPURAM 2025-05-29\n", "5  MUTHOOTFIN 2025-05-29\n", "6        SBIN 2025-05-29"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["nearest_expiry.head(8)"]}, {"cell_type": "code", "execution_count": 24, "id": "ca5d6556", "metadata": {}, "outputs": [], "source": ["# Filter for nearest expiry date\n", "nearest_expiry_options = put_options.merge(nearest_expiry, on=['name', 'expiry'], how='inner')"]}, {"cell_type": "code", "execution_count": 25, "id": "4a2931fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(257, 12)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["nearest_expiry_options.shape"]}, {"cell_type": "code", "execution_count": 26, "id": "331d4237", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>247</th>\n", "      <td>31230978</td>\n", "      <td>121996</td>\n", "      <td>SBIN25MAY900PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>900.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>34885122</td>\n", "      <td>136270</td>\n", "      <td>SBIN25MAY890PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>890.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>31220482</td>\n", "      <td>121955</td>\n", "      <td>SBIN25MAY880PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>880.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>34851330</td>\n", "      <td>136138</td>\n", "      <td>SBIN25MAY710PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>710.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>31200770</td>\n", "      <td>121878</td>\n", "      <td>SBIN25MAY700PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>700.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>34841090</td>\n", "      <td>136098</td>\n", "      <td>SBIN25MAY690PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>690.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>31200258</td>\n", "      <td>121876</td>\n", "      <td>SBIN25MAY680PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>680.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>254</th>\n", "      <td>34840578</td>\n", "      <td>136096</td>\n", "      <td>SBIN25MAY670PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>670.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>255</th>\n", "      <td>31199746</td>\n", "      <td>121874</td>\n", "      <td>SBIN25MAY660PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>660.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>256</th>\n", "      <td>31199234</td>\n", "      <td>121872</td>\n", "      <td>SBIN25MAY640PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>640.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     instrument_token  exchange_token   tradingsymbol  name  last_price  \\\n", "247          31230978          121996  SBIN25MAY900PE  SBIN           0   \n", "248          34885122          136270  SBIN25MAY890PE  SBIN           0   \n", "249          31220482          121955  SBIN25MAY880PE  SBIN           0   \n", "250          34851330          136138  SBIN25MAY710PE  SBIN           0   \n", "251          31200770          121878  SBIN25MAY700PE  SBIN           0   \n", "252          34841090          136098  SBIN25MAY690PE  SBIN           0   \n", "253          31200258          121876  SBIN25MAY680PE  SBIN           0   \n", "254          34840578          136096  SBIN25MAY670PE  SBIN           0   \n", "255          31199746          121874  SBIN25MAY660PE  SBIN           0   \n", "256          31199234          121872  SBIN25MAY640PE  SBIN           0   \n", "\n", "        expiry  strike  tick_size  lot_size instrument_type  segment exchange  \n", "247 2025-05-29   900.0       0.05       750              PE  NFO-OPT      NFO  \n", "248 2025-05-29   890.0       0.05       750              PE  NFO-OPT      NFO  \n", "249 2025-05-29   880.0       0.05       750              PE  NFO-OPT      NFO  \n", "250 2025-05-29   710.0       0.05       750              PE  NFO-OPT      NFO  \n", "251 2025-05-29   700.0       0.05       750              PE  NFO-OPT      NFO  \n", "252 2025-05-29   690.0       0.05       750              PE  NFO-OPT      NFO  \n", "253 2025-05-29   680.0       0.05       750              PE  NFO-OPT      NFO  \n", "254 2025-05-29   670.0       0.05       750              PE  NFO-OPT      NFO  \n", "255 2025-05-29   660.0       0.05       750              PE  NFO-OPT      NFO  \n", "256 2025-05-29   640.0       0.05       750              PE  NFO-OPT      NFO  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["nearest_expiry_options.tail(10)"]}, {"cell_type": "code", "execution_count": 27, "id": "94bde790", "metadata": {}, "outputs": [], "source": ["stk_price_df.rename(columns={'symbol': 'name'}, inplace=True)\n"]}, {"cell_type": "code", "execution_count": 28, "id": "83e80012", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument</th>\n", "      <th>name</th>\n", "      <th>average_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NSE:AXISBANK</td>\n", "      <td>AXISBANK</td>\n", "      <td>1202.64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NSE:HDFCBANK</td>\n", "      <td>HDFCBANK</td>\n", "      <td>1927.84</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NSE:ICICIBANK</td>\n", "      <td>ICICIBANK</td>\n", "      <td>1445.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NSE:KOTAKBANK</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2086.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NSE:MANAPPURAM</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>230.88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>NSE:MUTHOOTFIN</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2092.87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NSE:SBIN</td>\n", "      <td>SBIN</td>\n", "      <td>790.21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument        name  average_price\n", "0    NSE:AXISBANK    AXISBANK        1202.64\n", "1    NSE:HDFCBANK    HDFCBANK        1927.84\n", "2   NSE:ICICIBANK   ICICIBANK        1445.26\n", "3   NSE:KOTAKBANK   KOTAKBANK        2086.16\n", "4  NSE:MANAPPURAM  MANAPPURAM         230.88\n", "5  NSE:MUTHOOTFIN  MUTHOOTFIN        2092.87\n", "6        NSE:SBIN        SBIN         790.21"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_price_df.head(8)"]}, {"cell_type": "code", "execution_count": 29, "id": "acdd023e", "metadata": {}, "outputs": [], "source": ["# Merge with average stock prices\n", "nearest_expiry_options = nearest_expiry_options.merge(stk_price_df, on='name', how='inner')"]}, {"cell_type": "code", "execution_count": 30, "id": "d2d70e34", "metadata": {}, "outputs": [{"data": {"text/plain": ["(257, 14)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["nearest_expiry_options.shape"]}, {"cell_type": "code", "execution_count": 33, "id": "ce083969", "metadata": {}, "outputs": [], "source": ["# Calculate target strike price (5% below average price)\n", "nearest_expiry_options['target_strike'] = nearest_expiry_options['average_price'] * 0.95"]}, {"cell_type": "code", "execution_count": 35, "id": "e63748de", "metadata": {}, "outputs": [], "source": ["# Find the option with strike price 5% below average price\n", "def find_closest_strike(group):\n", "    # Filter strikes within 0-5% below average price\n", "    valid_strikes = group[\n", "        (group['strike'] <= group['average_price']) &\n", "        (group['strike'] >= group['target_strike'])\n", "    ]\n", "    if not valid_strikes.empty:\n", "        # Find the minimum strike price in the valid range\n", "        return valid_strikes.loc[valid_strikes['strike'].idxmin()]\n", "    return pd.Series()\n"]}, {"cell_type": "code", "execution_count": 48, "id": "f17f84c3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11836\\3802633350.py:1: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  put_options_final = nearest_expiry_options.groupby('name', group_keys=False).apply(find_closest_strike, include_groups=True).reset_index(drop=True)\n"]}], "source": ["put_options_final = nearest_expiry_options.groupby('name', group_keys=False).apply(find_closest_strike, include_groups=True).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 49, "id": "8afadf94", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["len(put_options_final)"]}, {"cell_type": "code", "execution_count": 50, "id": "422f0b7c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "      <th>instrument</th>\n", "      <th>average_price</th>\n", "      <th>target_strike</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150.0</td>\n", "      <td>0.05</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NSE:AXISBANK</td>\n", "      <td>1202.64</td>\n", "      <td>1142.5080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840.0</td>\n", "      <td>0.05</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NSE:HDFCBANK</td>\n", "      <td>1927.84</td>\n", "      <td>1831.4480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>0.05</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NSE:ICICIBANK</td>\n", "      <td>1445.26</td>\n", "      <td>1372.9970</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NSE:KOTAKBANK</td>\n", "      <td>2086.16</td>\n", "      <td>1981.8520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>220.0</td>\n", "      <td>0.05</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NSE:MANAPPURAM</td>\n", "      <td>230.88</td>\n", "      <td>219.3360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000.0</td>\n", "      <td>0.05</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NSE:MUTHOOTFIN</td>\n", "      <td>2092.87</td>\n", "      <td>1988.2265</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>760.0</td>\n", "      <td>0.05</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "      <td>NSE:SBIN</td>\n", "      <td>790.21</td>\n", "      <td>750.6995</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "   last_price     expiry  strike  tick_size  lot_size instrument_type  \\\n", "0           0 2025-05-29  1150.0       0.05       625              PE   \n", "1           0 2025-05-29  1840.0       0.05       550              PE   \n", "2           0 2025-05-29  1380.0       0.05       700              PE   \n", "3           0 2025-05-29  2000.0       0.05       400              PE   \n", "4           0 2025-05-29   220.0       0.05      3000              PE   \n", "5           0 2025-05-29  2000.0       0.05       275              PE   \n", "6           0 2025-05-29   760.0       0.05       750              PE   \n", "\n", "   segment exchange      instrument  average_price  target_strike  \n", "0  NFO-OPT      NFO    NSE:AXISBANK        1202.64      1142.5080  \n", "1  NFO-OPT      NFO    NSE:HDFCBANK        1927.84      1831.4480  \n", "2  NFO-OPT      NFO   NSE:ICICIBANK        1445.26      1372.9970  \n", "3  NFO-OPT      NFO   NSE:KOTAKBANK        2086.16      1981.8520  \n", "4  NFO-OPT      NFO  NSE:MANAPPURAM         230.88       219.3360  \n", "5  NFO-OPT      NFO  NSE:MUTHOOTFIN        2092.87      1988.2265  \n", "6  NFO-OPT      NFO        NSE:SBIN         790.21       750.6995  "]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options_final.head(8)"]}, {"cell_type": "code", "execution_count": 51, "id": "a3edf97c", "metadata": {}, "outputs": [], "source": ["# dropping the unnecessary columns\n", "put_options_final.drop(columns=['last_price', 'tick_size', 'segment', 'instrument'], inplace=True)"]}, {"cell_type": "code", "execution_count": 52, "id": "f6d9c3f9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>average_price</th>\n", "      <th>target_strike</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150.0</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1202.64</td>\n", "      <td>1142.5080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1927.84</td>\n", "      <td>1831.4480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1445.26</td>\n", "      <td>1372.9970</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000.0</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>2086.16</td>\n", "      <td>1981.8520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "      <td>220.0</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>230.88</td>\n", "      <td>219.3360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000.0</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>2092.87</td>\n", "      <td>1988.2265</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>760.0</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>790.21</td>\n", "      <td>750.6995</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  average_price  \\\n", "0 2025-05-29  1150.0       625              PE      NFO        1202.64   \n", "1 2025-05-29  1840.0       550              PE      NFO        1927.84   \n", "2 2025-05-29  1380.0       700              PE      NFO        1445.26   \n", "3 2025-05-29  2000.0       400              PE      NFO        2086.16   \n", "4 2025-05-29   220.0      3000              PE      NFO         230.88   \n", "5 2025-05-29  2000.0       275              PE      NFO        2092.87   \n", "6 2025-05-29   760.0       750              PE      NFO         790.21   \n", "\n", "   target_strike  \n", "0      1142.5080  \n", "1      1831.4480  \n", "2      1372.9970  \n", "3      1981.8520  \n", "4       219.3360  \n", "5      1988.2265  \n", "6       750.6995  "]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options_final.head(8)"]}, {"cell_type": "markdown", "id": "36652d00", "metadata": {}, "source": ["## Selling the Put options"]}, {"cell_type": "markdown", "id": "73673e98", "metadata": {}, "source": ["Adding premium and margin"]}, {"cell_type": "code", "execution_count": 53, "id": "3c98bbb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['NFO:AXISBANK25MAY1150PE', 'NFO:HDFCBANK25MAY1840PE', 'NFO:ICICIBANK25MAY1380PE', 'NFO:KOTAKBANK25MAY2000PE', 'NFO:MANAPPURAM25MAY220PE', 'NFO:MUTHOOTFIN25MAY2000PE', 'NFO:SBIN25MAY760PE']\n"]}], "source": ["# premium calculation\n", "put_options_final['instrument_key'] = put_options_final.apply(\n", "    lambda row: f\"{row['exchange']}:{row['tradingsymbol']}\", axis=1\n", ")\n", "\n", "put_instrument_list = put_options_final['instrument_key'].tolist()\n", "print(put_instrument_list)"]}, {"cell_type": "code", "execution_count": 58, "id": "703cc0cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["quote_data = kite.quote(put_instrument_list)\n", "\n", "len(quote_data)"]}, {"cell_type": "code", "execution_count": 62, "id": "a95e36d2", "metadata": {}, "outputs": [], "source": ["quote_df = pd.DataFrame([\n", "    {\n", "        'instrument_key': instrument_key,\n", "        'last_price': quote_data[instrument_key]['last_price']\n", "    }\n", "    for instrument_key in quote_data.keys()\n", "])"]}, {"cell_type": "code", "execution_count": 63, "id": "c642ab89", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_key</th>\n", "      <th>last_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NFO:AXISBANK25MAY1150PE</td>\n", "      <td>0.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NFO:HDFCBANK25MAY1840PE</td>\n", "      <td>1.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NFO:ICICIBANK25MAY1380PE</td>\n", "      <td>0.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NFO:KOTAKBANK25MAY2000PE</td>\n", "      <td>1.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NFO:MANAPPURAM25MAY220PE</td>\n", "      <td>0.15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             instrument_key  last_price\n", "0   NFO:AXISBANK25MAY1150PE        0.95\n", "1   NFO:HDFCBANK25MAY1840PE        1.10\n", "2  NFO:ICICIBANK25MAY1380PE        0.85\n", "3  NFO:KOTAKBANK25MAY2000PE        1.30\n", "4  NFO:MANAPPURAM25MAY220PE        0.15"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["quote_df.head()"]}, {"cell_type": "code", "execution_count": 64, "id": "df2f912f", "metadata": {}, "outputs": [], "source": ["# merge with put_options_final\n", "put_options_final = put_options_final.merge(quote_df, on='instrument_key', how='inner')"]}, {"cell_type": "code", "execution_count": 66, "id": "********", "metadata": {}, "outputs": [], "source": ["# premium calculation\n", "put_options_final['premium'] = put_options_final['last_price'] * put_options_final['lot_size']"]}, {"cell_type": "markdown", "id": "739e45b8", "metadata": {}, "source": ["Expiration date will vary each month, but it will be same for all the companies."]}, {"cell_type": "code", "execution_count": 68, "id": "5bd79a1f", "metadata": {}, "outputs": [], "source": ["# margin calculation\n", "base_order = {\n", "    \"exchange\":       \"NFO\",\n", "    \"transaction_type\": \"SELL\",\n", "    \"variety\":        \"regular\",\n", "    \"product\":        \"NRML\",\n", "    \"order_type\":     \"MARKET\",\n", "    \"price\":          0,\n", "    \"trigger_price\":  0\n", "}\n", "\n", "# Build orders list using tradingsymbol and quantity from DataFrame\n", "orders = [\n", "    {\n", "        **base_order,\n", "        \"tradingsymbol\": row[\"tradingsymbol\"],\n", "        \"quantity\": row[\"lot_size\"]  # Use 'lot_size' from each row\n", "    }\n", "    for _, row in put_options_final.iterrows()\n", "]"]}, {"cell_type": "code", "execution_count": 71, "id": "66e44941", "metadata": {}, "outputs": [], "source": ["margins_response = kite.order_margins(params=orders)"]}, {"cell_type": "code", "execution_count": 72, "id": "50b5e597", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'type': 'equity',\n", "  'tradingsymbol': 'AXISBANK25MAY1150PE',\n", "  'exchange': 'NFO',\n", "  'span': 79243.75,\n", "  'exposure': 26480.78125,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.53125,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.*********,\n", "   'sebi_turnover_charge': 0.0005312499999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.****************,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.****************},\n", "   'total': 24.***************},\n", "  'total': 105724.53125},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'HDFCBANK25MAY1840PE',\n", "  'exchange': 'NFO',\n", "  'span': 104505.5,\n", "  'exposure': 37261.************,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.3575,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.********,\n", "   'sebi_turnover_charge': 0.00035749999999999996,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.****************,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.****************},\n", "   'total': 24.*********},\n", "  'total': 141766.7625},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'ICICIBANK25MAY1380PE',\n", "  'exchange': 'NFO',\n", "  'span': 98749,\n", "  'exposure': 35560.525,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.525,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.1865325,\n", "   'sebi_turnover_charge': 0.000525,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.****************,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.****************},\n", "   'total': 24.***************},\n", "  'total': 134309.525},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'KOTAKBANK25MAY2000PE',\n", "  'exchange': 'NFO',\n", "  'span': 88192,\n", "  'exposure': 29418.************,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.*****************,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.*****************,\n", "   'sebi_turnover_charge': 0.0004599999999999999,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.****************,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.****************},\n", "   'total': 24.********},\n", "  'total': 117610.9},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'MANAPPURAM25MAY220PE',\n", "  'exchange': 'NFO',\n", "  'span': 117840,\n", "  'exposure': 60490.5,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.3,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.10659,\n", "   'sebi_turnover_charge': 0.0003,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6192401999999997,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.6192401999999997},\n", "   'total': 24.0261302},\n", "  'total': 178330.5},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'MUTHOOTFIN25MAY2000PE',\n", "  'exchange': 'NFO',\n", "  'span': 68153.25,\n", "  'exposure': 40314.3125,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 1.19625,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.*********,\n", "   'sebi_turnover_charge': 0.00119625,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.6767202975, 'cgst': 0, 'sgst': 0, 'total': 3.6767202975},\n", "   'total': 25.2991941725},\n", "  'total': 108467.5625},\n", " {'type': 'equity',\n", "  'tradingsymbol': 'SBIN25MAY760PE',\n", "  'exchange': 'NFO',\n", "  'span': 62302.5,\n", "  'exposure': 20769,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 1,\n", "  'charges': {'transaction_tax': 0.525,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 0.1865325,\n", "   'sebi_turnover_charge': 0.000525,\n", "   'brokerage': 20,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 3.****************,\n", "    'cgst': 0,\n", "    'sgst': 0,\n", "    'total': 3.****************},\n", "   'total': 24.***************},\n", "  'total': 83071.5}]"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response"]}, {"cell_type": "code", "execution_count": 73, "id": "34265b22", "metadata": {}, "outputs": [], "source": ["margin_df = pd.DataFrame([\n", "    {\n", "        'tradingsymbol': item['tradingsymbol'],\n", "        'span': item['span'],\n", "        'exposure': item['exposure'],\n", "        'total_margin': item['total']\n", "    }for item in margins_response\n", "])"]}, {"cell_type": "code", "execution_count": 74, "id": "aabe790c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradingsymbol</th>\n", "      <th>span</th>\n", "      <th>exposure</th>\n", "      <th>total_margin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>79243.75</td>\n", "      <td>26480.78125</td>\n", "      <td>105724.53125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>104505.50</td>\n", "      <td>37261.26250</td>\n", "      <td>141766.76250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>98749.00</td>\n", "      <td>35560.52500</td>\n", "      <td>134309.52500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>88192.00</td>\n", "      <td>29418.90000</td>\n", "      <td>117610.90000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>117840.00</td>\n", "      <td>60490.50000</td>\n", "      <td>178330.50000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>68153.25</td>\n", "      <td>40314.31250</td>\n", "      <td>108467.56250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>62302.50</td>\n", "      <td>20769.00000</td>\n", "      <td>83071.50000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           tradingsymbol       span     exposure  total_margin\n", "0    AXISBANK25MAY1150PE   79243.75  26480.78125  105724.53125\n", "1    HDFCBANK25MAY1840PE  104505.50  37261.26250  141766.76250\n", "2   ICICIBANK25MAY1380PE   98749.00  35560.52500  134309.52500\n", "3   KOTAKBANK25MAY2000PE   88192.00  29418.90000  117610.90000\n", "4   MANAPPURAM25MAY220PE  117840.00  60490.50000  178330.50000\n", "5  MUTHOOTFIN25MAY2000PE   68153.25  40314.31250  108467.56250\n", "6         SBIN25MAY760PE   62302.50  20769.00000   83071.50000"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["margin_df.head(8)"]}, {"cell_type": "code", "execution_count": 75, "id": "70ddc203", "metadata": {}, "outputs": [], "source": ["put_options_final = put_options_final.merge(margin_df[['tradingsymbol', 'total_margin']], on='tradingsymbol', how='inner')\n"]}, {"cell_type": "code", "execution_count": 77, "id": "bed87440", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>exchange</th>\n", "      <th>average_price</th>\n", "      <th>target_strike</th>\n", "      <th>last_price</th>\n", "      <th>premium</th>\n", "      <th>total_margin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>70933</td>\n", "      <td>AXISBANK25MAY1150PE</td>\n", "      <td>AXISBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1150.0</td>\n", "      <td>625</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1202.64</td>\n", "      <td>1142.5080</td>\n", "      <td>0.95</td>\n", "      <td>593.75</td>\n", "      <td>105724.53125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>87322</td>\n", "      <td>HDFCBANK25MAY1840PE</td>\n", "      <td>HDFCBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1840.0</td>\n", "      <td>550</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1927.84</td>\n", "      <td>1831.4480</td>\n", "      <td>1.10</td>\n", "      <td>605.00</td>\n", "      <td>141766.76250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>90469</td>\n", "      <td>ICICIBANK25MAY1380PE</td>\n", "      <td>ICICIBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>1380.0</td>\n", "      <td>700</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>1445.26</td>\n", "      <td>1372.9970</td>\n", "      <td>0.85</td>\n", "      <td>595.00</td>\n", "      <td>134309.52500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>99963</td>\n", "      <td>KOTAKBANK25MAY2000PE</td>\n", "      <td>KOTAKBANK</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000.0</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>2086.16</td>\n", "      <td>1981.8520</td>\n", "      <td>1.30</td>\n", "      <td>520.00</td>\n", "      <td>117610.90000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>103868</td>\n", "      <td>MANAPPURAM25MAY220PE</td>\n", "      <td>MANAPPURAM</td>\n", "      <td>2025-05-29</td>\n", "      <td>220.0</td>\n", "      <td>3000</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>230.88</td>\n", "      <td>219.3360</td>\n", "      <td>0.15</td>\n", "      <td>450.00</td>\n", "      <td>178330.50000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>********</td>\n", "      <td>107181</td>\n", "      <td>MUTHOOTFIN25MAY2000PE</td>\n", "      <td>MUTHOOTFIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>2000.0</td>\n", "      <td>275</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>2092.87</td>\n", "      <td>1988.2265</td>\n", "      <td>4.25</td>\n", "      <td>1168.75</td>\n", "      <td>108467.56250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>********</td>\n", "      <td>121884</td>\n", "      <td>SBIN25MAY760PE</td>\n", "      <td>SBIN</td>\n", "      <td>2025-05-29</td>\n", "      <td>760.0</td>\n", "      <td>750</td>\n", "      <td>PE</td>\n", "      <td>NFO</td>\n", "      <td>790.21</td>\n", "      <td>750.6995</td>\n", "      <td>0.75</td>\n", "      <td>562.50</td>\n", "      <td>83071.50000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token          tradingsymbol        name  \\\n", "0          ********           70933    AXISBANK25MAY1150PE    AXISBANK   \n", "1          ********           87322    HDFCBANK25MAY1840PE    HDFCBANK   \n", "2          ********           90469   ICICIBANK25MAY1380PE   ICICIBANK   \n", "3          ********           99963   KOTAKBANK25MAY2000PE   KOTAKBANK   \n", "4          ********          103868   MANAPPURAM25MAY220PE  MANAPPURAM   \n", "5          ********          107181  MUTHOOTFIN25MAY2000PE  MUTHOOTFIN   \n", "6          ********          121884         SBIN25MAY760PE        SBIN   \n", "\n", "      expiry  strike  lot_size instrument_type exchange  average_price  \\\n", "0 2025-05-29  1150.0       625              PE      NFO        1202.64   \n", "1 2025-05-29  1840.0       550              PE      NFO        1927.84   \n", "2 2025-05-29  1380.0       700              PE      NFO        1445.26   \n", "3 2025-05-29  2000.0       400              PE      NFO        2086.16   \n", "4 2025-05-29   220.0      3000              PE      NFO         230.88   \n", "5 2025-05-29  2000.0       275              PE      NFO        2092.87   \n", "6 2025-05-29   760.0       750              PE      NFO         790.21   \n", "\n", "   target_strike  last_price  premium  total_margin  \n", "0      1142.5080        0.95   593.75  105724.53125  \n", "1      1831.4480        1.10   605.00  141766.76250  \n", "2      1372.9970        0.85   595.00  134309.52500  \n", "3      1981.8520        1.30   520.00  117610.90000  \n", "4       219.3360        0.15   450.00  178330.50000  \n", "5      1988.2265        4.25  1168.75  108467.56250  \n", "6       750.6995        0.75   562.50   83071.50000  "]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["put_options_final.drop(columns=['instrument_key'], inplace=True)\n", "\n", "put_options_final.head(8)"]}, {"cell_type": "code", "execution_count": null, "id": "1d550190", "metadata": {}, "outputs": [], "source": ["# csv download\n", "put_options_final.to_csv('put_options_final.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}