import pandas as pd
from datetime import datetime
from logger import log
import config
import data_manager
import kite_utils
import strategy

# --- Configuration for this run ---
# Set to False to actually modify files. Set to True to only print actions.
DRY_RUN = False

def find_put_to_sell(stock_ticker: str, instruments_df: pd.DataFrame):
    """
    Finds the best put option to sell based on the strategy rules.
    Rule: Strike price closest to but below the Intrinsic Value (Si).
    """
    si_value = config.INTRINSIC_VALUES.get(stock_ticker)
    if not si_value:
        return None
    
    candidate_options = instruments_df[
        (instruments_df['name'] == stock_ticker) &
        (instruments_df['instrument_type'] == 'PE') &
        (instruments_df['strike'] <= si_value)
    ].copy()
    
    if candidate_options.empty:
        log.info(f"No puts found for {stock_ticker} with strike <= Si {si_value}.")
        return None
    
    candidate_options['expiry'] = pd.to_datetime(candidate_options['expiry']).dt.date
    future_expiries = candidate_options[candidate_options['expiry'] > datetime.today().date()]
    
    if future_expiries.empty:
        log.info(f"No puts with future expiry found for {stock_ticker}.")
        return None
        
    nearest_expiry = future_expiries['expiry'].min()
    final_candidates = future_expiries[future_expiries['expiry'] == nearest_expiry]
    
    # Return the one with the highest strike price (closest to Si)
    return final_candidates.loc[final_candidates['strike'].idxmax()]

def find_call_to_sell(stock_ticker: str, strike_price: float, instruments_df: pd.DataFrame):
    """
    Finds a call option for a specific stock and strike price for the next available expiry.
    Applicable when put option ends in the money and we have to actually buy the stocks which results in selling covered call next month at same strike.
    """
    candidate_options = instruments_df[
        (instruments_df['name'] == stock_ticker) &
        (instruments_df['instrument_type'] == 'CE') &
        (instruments_df['strike'] == strike_price)
    ].copy()
    
    if candidate_options.empty:
        log.warning(f"No calls found for {stock_ticker} at strike {strike_price}.")
        return None

    candidate_options['expiry'] = pd.to_datetime(candidate_options['expiry']).dt.date
    future_expiries = candidate_options[candidate_options['expiry'] > datetime.today().date()]
    
    if future_expiries.empty:
        log.warning(f"No calls with future expiry found for {stock_ticker} at strike {strike_price}.")
        return None
        
    nearest_expiry = future_expiries['expiry'].min()
    
    return future_expiries[future_expiries['expiry'] == nearest_expiry].iloc[0]

def find_next_month_option(current_option, instruments_df):
    """
    Finds the option for the same stock and strike, for the next month's expiry (for rollovers).
    In roll over we have to find same type of option for next month at the same strike price.
    """
    all_matching_options = instruments_df[
        (instruments_df['name'] == current_option['name']) &
        (instruments_df['strike'] == current_option['strike']) &
        (instruments_df['instrument_type'] == current_option['instrument_type'])
    ].copy()
    all_matching_options['expiry'] = pd.to_datetime(all_matching_options['expiry']).dt.date
    
    current_expiry_date = pd.to_datetime(current_option['expiry']).date()
    future_expiries = all_matching_options[all_matching_options['expiry'] > current_expiry_date]
    
    if future_expiries.empty:
        return None
        
    next_expiry_date = future_expiries['expiry'].min()
    next_month_option = future_expiries[future_expiries['expiry'] == next_expiry_date]
    return next_month_option.iloc[0] if not next_month_option.empty else None


def handle_no_position(stock_ticker, instruments_df, kite):
    """
    Handles logic for a stock with no current position.
    applicable when we are in sell put state but there is no active position for a particular stock.
    """
    actions = []
    log.info(f"--- Checking '{stock_ticker}' for new trade (State: No Position) ---")
    
    stock_quote_key = f"NSE:{stock_ticker}"
    quotes = kite_utils.get_quotes(kite, [stock_quote_key])
    if not quotes or stock_quote_key not in quotes:
        log.error(f"Could not fetch price for {stock_ticker}."); return actions
    stock_price = quotes[stock_quote_key]['last_price']
    
    put_to_sell = find_put_to_sell(stock_ticker, instruments_df)
    if put_to_sell is None:
        return actions
    
    option_quote_key = f"{put_to_sell['exchange']}:{put_to_sell['tradingsymbol']}"
    option_quote = kite_utils.get_quotes(kite, [option_quote_key])
    option_price = option_quote[option_quote_key]['last_price']
    premium_received = option_price * put_to_sell['lot_size']
    
    margin_order = [{"exchange": put_to_sell['exchange'], "tradingsymbol": put_to_sell['tradingsymbol'], "quantity": int(put_to_sell['lot_size']), "transaction_type": "SELL", "product": "NRML", "order_type": "MARKET"}]
    margin_data = kite_utils.get_margin(kite, margin_order)
    margin_required = margin_data[0]['total']

    if strategy.check_entry_conditions(stock_ticker, stock_price, premium_received, margin_required):
        log.info(f"ACTION: Conditions met to SELL PUT for {stock_ticker}.")
        new_position = {'instrument_token': put_to_sell['instrument_token'], 'tradingsymbol': put_to_sell['tradingsymbol'], 'name': stock_ticker, 'expiry': put_to_sell['expiry'], 'strike': put_to_sell['strike'], 'lot_size': put_to_sell['lot_size'], 'instrument_type': 'PE', 'exchange': put_to_sell['exchange'], 'trading_price_of_stock_at_option_sell': stock_price, 'Intrinsic_value': config.INTRINSIC_VALUES.get(stock_ticker), 'price_of_option_at_option_sell': option_price, 'premium_received': premium_received, 'total_margin': margin_required, 'premium_to_margin_percentage': (premium_received / margin_required)}
        actions.append({'action': 'ADD_OPTION', 'data': new_position})
        actions.append({'action': 'LOG_TRANSACTION', 'data': {
    'transaction_date': datetime.now(),
    'transaction_type': 'OPTION_SELL',
    'name': stock_ticker,
    'tradingsymbol': put_to_sell['tradingsymbol'],
    'instrument_token': put_to_sell['instrument_token'],
    'expiry': put_to_sell['expiry'],
    'strike': put_to_sell['strike'],
    'instrument_type': 'PE',
    'lot_size': int(put_to_sell['lot_size']),
    'option_price_at_sell': option_price,
    'premium_received': premium_received,
    'total_margin': margin_required,
    'option_price_at_exp': 0, # Not applicable yet
    'premium_paid': 0,        # Not applicable yet
    'amount_paid_buy_stock': 0,
    'amount_received_sell_stock': 0
}})
        
    return actions

def handle_expiry_day(position, instruments_df, kite):
    """Handles logic for an active option on its expiry day."""
    actions = []
    position_expiry_date = pd.to_datetime(position['expiry']).date()
    if position_expiry_date != datetime.today().date():
        return actions

    log.info(f"--- Handling Expiry for: {position['tradingsymbol']} ---")
    stock_key, option_key = f"NSE:{position['name']}", f"{position['exchange']}:{position['tradingsymbol']}"
    quotes = kite_utils.get_quotes(kite, [stock_key, option_key])
    stock_price, option_price = quotes[stock_key]['last_price'], quotes[option_key]['last_price']

    is_itm = (position['instrument_type'] == 'PE' and stock_price < position['strike']) or \
             (position['instrument_type'] == 'CE' and stock_price > position['strike'])
    
    if not is_itm:
        log.info(f"{position['tradingsymbol']} expired OTM.")
        actions.append({'action': 'CLOSE_OPTION', 'token': position['instrument_token']})
        actions.append({'action': 'LOG_TRANSACTION', 'data': {
    'transaction_date': datetime.now(),
    'transaction_type': 'OPTION_EXPIRED_OTM',
    'name': position['name'],
    'tradingsymbol': position['tradingsymbol'],
    'instrument_token': position['instrument_token'],
    'expiry': position['expiry'].date(),
    'strike': position['strike'],
    'instrument_type': position['instrument_type'],
    'lot_size': position['lot_size'],
    'option_price_at_sell': position['price_of_option_at_option_sell'], # Original sell price
    'premium_received': position['premium_received'], # Original premium is the profit
    'total_margin': 0,
    'option_price_at_exp': 0, # Expired worthless
    'premium_paid': 0,
    'amount_paid_buy_stock': 0,
    'amount_received_sell_stock': 0
}})
        return actions

    log.info(f"{position['tradingsymbol']} is ITM. Checking for rollover...")
    pnl = strategy.calculate_rollover_profit_or_loss(option_price, position['price_of_option_at_option_sell'], position['lot_size'])
    loss_to_close = abs(pnl) if pnl < 0 else 0
    
    next_option, new_premium = find_next_month_option(position, instruments_df), 0
    if next_option is not None:
        next_option_key = f"{next_option['exchange']}:{next_option['tradingsymbol']}"
        next_option_quote = kite_utils.get_quotes(kite, [next_option_key])
        new_premium = next_option_quote[next_option_key]['last_price'] * next_option['lot_size']
        new_margin_order = [{"exchange": next_option['exchange'], "tradingsymbol": next_option['tradingsymbol'], "quantity": int(next_option['lot_size']), "transaction_type": "SELL", "product": "NRML", "order_type": "MARKET"}]
        new_margin_data = kite_utils.get_margin(kite, new_margin_order)
        new_margin_required = new_margin_data[0]['total']

    if strategy.decide_rollover(loss_to_close, new_premium) == "ROLLOVER":
        log.info(f"Decision: ROLLOVER {position['tradingsymbol']}.")
        new_pos_data = {
            'instrument_token': next_option['instrument_token'],
            'tradingsymbol': next_option['tradingsymbol'],
            'name': next_option['name'],
            'expiry': next_option['expiry'],
            'strike': next_option['strike'],
            'lot_size': int(next_option['lot_size']),
            'instrument_type': next_option['instrument_type'],
            'exchange': next_option['exchange'],
            'trading_price_of_stock_at_option_sell': stock_price, # we need new stock price
            'Intrinsic_value': position['Intrinsic_value'],
            'price_of_option_at_option_sell': new_premium / next_option['lot_size'],
            'premium_received': new_premium,
            'total_margin': new_margin_required,
            'premium_to_margin_percentage': (new_premium / new_margin_required),
            # Ensure other 'current' columns are cleared
            'current_stock_price': 0,
            'current_option_price': 0,
            'premium_paid_if_today_squareoff': 0,
            'current_profit_or_loss_on_option': 0
        }
        actions.append({'action': 'UPDATE_OPTION', 'token': position['instrument_token'], 'new_data': new_pos_data})
        actions.append({'action': 'LOG_TRANSACTION', 'data': {
    'transaction_date': datetime.now(),
    'transaction_type': 'OPTION_BUY_CLOSE',
    'name': position['name'],
    'tradingsymbol': position['tradingsymbol'],
    'instrument_token': position['instrument_token'],
    'expiry': position['expiry'].date(),
    'strike': position['strike'],
    'instrument_type': position['instrument_type'],
    'lot_size': position['lot_size'],
    'option_price_at_sell': 0, # Not applicable
    'premium_received': 0,   # Not applicable
    'total_margin': 0,
    'option_price_at_exp': option_price,
    'premium_paid': option_price * position['lot_size'],
    'amount_paid_buy_stock': 0,
    'amount_received_sell_stock': 0
}})
        actions.append({'action': 'LOG_TRANSACTION', 'data': {
    'transaction_date': datetime.now(),
    'transaction_type': 'OPTION_SELL_OPEN',
    'name': next_option['name'],
    'tradingsymbol': next_option['tradingsymbol'],
    'instrument_token': next_option['instrument_token'],
    'expiry': next_option['expiry'],
    'strike': next_option['strike'],
    'instrument_type': next_option['instrument_type'],
    'lot_size': int(next_option['lot_size']),
    'option_price_at_sell': new_premium / next_option['lot_size'],
    'premium_received': new_premium,
    'total_margin': new_margin_required, # Placeholder, as we don't fetch new margin yet
    'option_price_at_exp': 0,
    'premium_paid': 0,
    'amount_paid_buy_stock': 0,
    'amount_received_sell_stock': 0
}})
    else: # FULFILL_OBLIGATION
        log.info(f"Decision: FULFILL OBLIGATION for {position['tradingsymbol']}.")
        actions.append({'action': 'CLOSE_OPTION', 'token': position['instrument_token']})
        
        if position['instrument_type'] == 'PE':
            stock_data = {'name': position['name'], 'quantity': position['lot_size'], 'purchase_price': position['strike'], 'intrinsic_value': position['Intrinsic_value'], 'total_amount_paid': position['strike'] * position['lot_size']}
            actions.append({'action': 'STOCK_BUY', 'data': stock_data})
            actions.append({'action': 'LOG_TRANSACTION', 'data': {
    'transaction_date': datetime.now(),
    'transaction_type': 'STOCK_BUY',
    'name': position['name'],
    'tradingsymbol': None, 'instrument_token': None, 'expiry': None, 'strike': None, 'instrument_type': 'EQ',
    'lot_size': position['lot_size'],
    'option_price_at_sell': 0, 'premium_received': 0, 'total_margin': 0, 'option_price_at_exp': 0, 'premium_paid': 0,
    'amount_paid_buy_stock': position['strike'] * position['lot_size'],
    'amount_received_sell_stock': 0
}})
            
            log.info(f"Put exercised. Attempting to sell call at same strike: {position['strike']}")
            call_to_sell = find_call_to_sell(position['name'], position['strike'], instruments_df)
            if call_to_sell is not None:
                call_quote = kite_utils.get_quotes(kite, [f"{call_to_sell['exchange']}:{call_to_sell['tradingsymbol']}"])
                call_price = call_quote[f"{call_to_sell['exchange']}:{call_to_sell['tradingsymbol']}"]['last_price']
                premium = call_price * call_to_sell['lot_size']
                call_margin_order = [{"exchange": call_to_sell['exchange'], "tradingsymbol": call_to_sell['tradingsymbol'], "quantity": int(call_to_sell['lot_size']), "transaction_type": "SELL", "product": "NRML", "order_type": "MARKET"}]
                call_margin_data = kite_utils.get_margin(kite, call_margin_order)
                call_margin_required = call_margin_data[0]['total']
                new_call_pos = {
            'instrument_token': call_to_sell['instrument_token'],
            'tradingsymbol': call_to_sell['tradingsymbol'],
            'name': call_to_sell['name'],
            'expiry': call_to_sell['expiry'],
            'strike': call_to_sell['strike'],
            'lot_size': int(call_to_sell['lot_size']),
            'instrument_type': call_to_sell['instrument_type'],
            'exchange': call_to_sell['exchange'],
            'trading_price_of_stock_at_option_sell': stock_price, 
            'Intrinsic_value': position['Intrinsic_value'],
            'price_of_option_at_option_sell': call_price,
            'premium_received': premium,
            'total_margin': call_margin_required,
            'premium_to_margin_percentage': (premium / call_margin_required),
            # Ensure other 'current' columns are cleared
            'current_stock_price': 0,
            'current_option_price': 0,
            'premium_paid_if_today_squareoff': 0,
            'current_profit_or_loss_on_option': 0
        }
                actions.append({'action': 'ADD_OPTION', 'data': new_call_pos})
                actions.append({'action': 'LOG_TRANSACTION', 'data': {
        'transaction_date': datetime.now(),
        'transaction_type': 'OPTION_SELL',
        'name': call_to_sell['name'],
        'tradingsymbol': call_to_sell['tradingsymbol'],
        'instrument_token': call_to_sell['instrument_token'],
        'expiry': call_to_sell['expiry'],
        'strike': call_to_sell['strike'],
        'instrument_type': 'CE',
        'lot_size': int(call_to_sell['lot_size']),
        'option_price_at_sell': call_price,
        'premium_received': premium,
        'total_margin': call_margin_required, 
        'option_price_at_exp': 0, 'premium_paid': 0, 'amount_paid_buy_stock': 0, 'amount_received_sell_stock': 0
    }})
            else:
                log.error(f"Immediate call sale FAILED. System will attempt recovery on next run.")
        
        elif position['instrument_type'] == 'CE':
            stock_data = {'name': position['name'], 'quantity': position['lot_size'], 'price': position['strike']}
            actions.append({'action': 'STOCK_SELL', 'data': stock_data})
            actions.append({'action': 'LOG_TRANSACTION', 'data': {
    'transaction_date': datetime.now(),
    'transaction_type': 'STOCK_SELL',
    'name': position['name'],
    'tradingsymbol': None, 'instrument_token': None, 'expiry': None, 'strike': None, 'instrument_type': 'EQ',
    'lot_size': position['lot_size'],
    'option_price_at_sell': 0, 'premium_received': 0, 'total_margin': 0, 'option_price_at_exp': 0, 'premium_paid': 0,
    'amount_paid_buy_stock': 0,
    'amount_received_sell_stock': position['strike'] * position['lot_size']
}})
    return actions


def run_strategy():
    """The main orchestrator function for the entire strategy."""
    log.info(f"================== Starting Strategy Cycle (DRY RUN: {DRY_RUN}) ==================")
    try:
        kite = kite_utils.initialize_kite()
        instruments_df = pd.read_csv(f"{config.DATA_DIR}/instruments.csv")
        active_options_df = data_manager.get_active_options()
        if not active_options_df.empty:
            active_options_df['expiry'] = pd.to_datetime(active_options_df['expiry'])
        stock_holdings_df = data_manager.get_stock_holdings()
    except Exception as e:
        log.error(f"FATAL: Could not initialize or load data. {e}", exc_info=True); return

    all_actions = []
    # --- 1. Main Loop: Collect all actions based on current state ---
    for stock_ticker in config.TARGET_STOCKS:
        active_option = active_options_df[active_options_df['name'] == stock_ticker]
        
        if not active_option.empty:
            all_actions.extend(handle_expiry_day(active_option.iloc[0], instruments_df, kite))
        else:
            all_actions.extend(handle_no_position(stock_ticker, instruments_df, kite))

    if not all_actions:
        log.info("No actions to perform in this cycle.")
        log.info("================== Strategy Execution Cycle Finished ==================")
        return

    # --- 2. Action Processing Loop: Execute collected actions ---
    log.info(f"--- Processing {len(all_actions)} collected actions ---")
    options_to_update = active_options_df.copy()
    stocks_to_update = stock_holdings_df.copy()

    for item in all_actions:
        action = item['action']
        log.info(f"Executing action: {action}")
        
        if action == 'LOG_TRANSACTION':
            if not DRY_RUN: data_manager.log_transaction(item['data'])
            else: log.warning(f"[DRY RUN] Would log transaction: {item['data']}")
        
        elif action == 'ADD_OPTION':
            new_row = pd.DataFrame([item['data']])
            options_to_update = pd.concat([options_to_update, new_row], ignore_index=True)
            log.info(f"Prepared to add option: {item['data']['tradingsymbol']}")
        
        elif action == 'CLOSE_OPTION':
            options_to_update.drop(options_to_update[options_to_update['instrument_token'] == item['token']].index, inplace=True)
            log.info(f"Prepared to close option token: {item['token']}")

        elif action == 'UPDATE_OPTION':
            idx = options_to_update[options_to_update['instrument_token'] == item['token']].index
            for col, value in item['new_data'].items():
                if col in options_to_update.columns: options_to_update.loc[idx, col] = value
            log.info(f"Prepared to update option token: {item['token']}")
            
        elif action == 'STOCK_BUY':
            data = item['data']
            if not stocks_to_update[stocks_to_update['name'] == data['name']].empty:
                log.warning(f"Stock {data['name']} already exists. Averaging logic not implemented; replacing.")
                stocks_to_update.drop(stocks_to_update[stocks_to_update['name'] == data['name']].index, inplace=True)
            new_stock = {'name': data['name'], 'quantity': data['quantity'], 'purchase_price': data['price'], 'total_amount_paid': data['quantity'] * data['price']}
            stocks_to_update = pd.concat([stocks_to_update, pd.DataFrame([new_stock])], ignore_index=True)
            log.info(f"Prepared to BUY {data['quantity']} shares of {data['name']}.")
            
        elif action == 'STOCK_SELL':
            stocks_to_update.drop(stocks_to_update[stocks_to_update['name'] == item['data']['name']].index, inplace=True)
            log.info(f"Prepared to SELL shares of {item['data']['name']}.")

    # --- 3. Save Final State ---
    if not DRY_RUN:
        log.info("Saving updated data sheets...")
        data_manager.save_active_options(options_to_update)
        data_manager.save_stock_holdings(stocks_to_update)
    else:
        log.warning("[DRY RUN] No files were saved.")
        print("\n--- Final State (Dry Run) ---")
        print("\nActive Options:")
        print(options_to_update.to_markdown(index=False))
        print("\nStock Holdings:")
        print(stocks_to_update.to_markdown(index=False))

    log.info("================== Strategy Execution Cycle Finished ==================")

if __name__ == "__main__":
    run_strategy()