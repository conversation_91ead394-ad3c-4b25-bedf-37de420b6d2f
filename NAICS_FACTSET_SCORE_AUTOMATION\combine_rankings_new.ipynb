{"cells": [{"cell_type": "markdown", "id": "4e8bfef7", "metadata": {}, "source": ["```\n", "Strategy:\n", "1. Combine BEA data\n", "2. Combine NAICS data\n", "3. Extract first three digit from NAICS code and BEA code.\n", "4. Assign correct BEA code to NAICS code based on three digit mapping. One BEA code will map to multiple NAICS codes.\n", "If we find more than one BEA code for one NAICS code then do the match the industry description and decide which BEA industry code is correct.\n", "```\n"]}, {"cell_type": "code", "execution_count": 1, "id": "dbd08f94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index(['rank', 'seriesID', 'Industry', 'industry_name', 'avg_2023', 'avg_2024',\n", "       'ratio', 'Recent_Growth_%', 'Growth', 'Emp_Score', 'Emp_Growth_Score',\n", "       'Employment_Score'],\n", "      dtype='object')\n", "Index(['Data Type', 'NAICS', 'Year', 'Description', 'Exports1', 'Imports1',\n", "       'Deficit', 'Exports ', 'Imports', 'Trade Balance', 'Imports_Score',\n", "       'Deficit_Score', 'Import_Intensity_Score'],\n", "      dtype='object')\n", "Index(['SeriesCode', 'Industry', 'LineDescription', 'FA_2022', 'FA_2023',\n", "       'Recent_Growth_%', 'Increase in Fixed Assets', 'FA_Score',\n", "       'FA_Growth_Score', 'Fixed_Assets_Score'],\n", "      dtype='object')\n", "Index(['Industry', 'IndustrYDescription', 'GDP_2023', 'GDP_2024',\n", "       'Recent_Growth_%', 'Recent_Increase', 'GDP_Score', 'GDP_Growth_Score',\n", "       'Economic_Heft_Score'],\n", "      dtype='object')\n"]}], "source": ["import pandas as pd\n", "#from fuzzywuzzy import fuzz, process\n", "\n", "# Load dataframes\n", "emp_df = pd.read_excel('NAICS_Employment_Scores.xlsx')\n", "import_df = pd.read_excel('NAICS_Import_Intensity_Score.xlsx')\n", "fa_df = pd.read_excel('NAICS_Fixed_Assets_Scores.xlsx')\n", "eh_df = pd.read_excel('NAICS_Economic_Heft_Scores.xlsx')\n", "\n", "# Check column names\n", "print(emp_df.columns)\n", "print(import_df.columns)\n", "print(fa_df.columns)\n", "print(eh_df.columns)"]}, {"cell_type": "markdown", "id": "c2092ad6", "metadata": {}, "source": ["## Renaming the column of economic heft and fixed assets dataframes"]}, {"cell_type": "code", "execution_count": 2, "id": "46ea144c", "metadata": {}, "outputs": [], "source": ["fa_df.rename(columns={'LineDescription': 'BEA_description'}, inplace=True)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "cedea576", "metadata": {}, "outputs": [], "source": ["eh_df.rename(columns={'IndustrYDescription': 'BEA_description'}, inplace=True)"]}, {"cell_type": "markdown", "id": "050da5a7", "metadata": {}, "source": ["## Outer join of fa and eh and filling missing values to 0"]}, {"cell_type": "code", "execution_count": 4, "id": "65803049", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["36\n", "23\n"]}], "source": ["print(len(eh_df))\n", "print(len(fa_df))"]}, {"cell_type": "code", "execution_count": 5, "id": "5a4ab0b7", "metadata": {}, "outputs": [], "source": ["# merge, keeping both Industry columns\n", "bea_df = pd.merge(\n", "    fa_df[['Industry', 'BEA_description', 'Fixed_Assets_Score']],\n", "    eh_df[['Industry', 'BEA_description', 'Economic_Heft_Score']],\n", "    on='BEA_description',\n", "    how='outer',\n", "    suffixes=('_fa', '_eh')\n", ")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "db59657b", "metadata": {}, "outputs": [], "source": ["# build the final Industry column:\n", "#  – if eh’s Industry exists use that, else fall back to fa’s Industry\n", "bea_df['Industry'] = bea_df['Industry_eh'].combine_first(bea_df['Industry_fa'])"]}, {"cell_type": "code", "execution_count": 7, "id": "440cd9fd", "metadata": {}, "outputs": [], "source": ["# now drop the old split columns\n", "bea_df = bea_df.drop(columns=['Industry_fa', 'Industry_eh'])"]}, {"cell_type": "code", "execution_count": 8, "id": "08fda1c1", "metadata": {}, "outputs": [], "source": ["# if you still want to fill missing scores with 0.5:\n", "bea_df[['Fixed_Assets_Score', 'Economic_Heft_Score']] = (\n", "    bea_df[['Fixed_Assets_Score', 'Economic_Heft_Score']]\n", "    .fillna(0.5)\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "1cf122fb", "metadata": {}, "outputs": [], "source": ["bea_df = bea_df[['Industry', 'BEA_description', 'Fixed_Assets_Score', 'Economic_Heft_Score']]"]}, {"cell_type": "code", "execution_count": 10, "id": "f0d1ddd9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Industry</th>\n", "      <th>BEA_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33311</td>\n", "      <td>Agricultural implement manufacturing</td>\n", "      <td>0.500000</td>\n", "      <td>0.016358</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>315AL</td>\n", "      <td>Apparel and leather and allied products</td>\n", "      <td>0.022472</td>\n", "      <td>0.219149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3251</td>\n", "      <td>Basic chemical manufacturing</td>\n", "      <td>0.500000</td>\n", "      <td>0.134287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>325</td>\n", "      <td>Chemical products</td>\n", "      <td>0.734831</td>\n", "      <td>0.993617</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Industry                             BEA_description  Fixed_Assets_Score  \\\n", "0    33311        Agricultural implement manufacturing            0.500000   \n", "1    315AL     Apparel and leather and allied products            0.022472   \n", "2     3251                Basic chemical manufacturing            0.500000   \n", "3     3120  Beverage and tobacco product manufacturing            0.228889   \n", "4      325                           Chemical products            0.734831   \n", "\n", "   Economic_Heft_Score  \n", "0             0.016358  \n", "1             0.219149  \n", "2             0.134287  \n", "3             0.500000  \n", "4             0.993617  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["bea_df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "56a80cb1", "metadata": {}, "outputs": [], "source": ["bea_df.rename(columns={'Industry': 'BEA_code'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 12, "id": "5266ff9e", "metadata": {}, "outputs": [{"data": {"text/plain": ["40"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["len(bea_df)"]}, {"cell_type": "markdown", "id": "86564213", "metadata": {}, "source": ["## Combine NAICS data"]}, {"cell_type": "code", "execution_count": 13, "id": "904dcd8a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>rank</th>\n", "      <th>seriesID</th>\n", "      <th>Industry</th>\n", "      <th>industry_name</th>\n", "      <th>avg_2023</th>\n", "      <th>avg_2024</th>\n", "      <th>ratio</th>\n", "      <th>Recent_Growth_%</th>\n", "      <th>Growth</th>\n", "      <th>Emp_Score</th>\n", "      <th>Emp_Growth_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>CEU3133640001</td>\n", "      <td>3364</td>\n", "      <td>Aerospace product and parts manufacturing</td>\n", "      <td>536.658333</td>\n", "      <td>559.066667</td>\n", "      <td>1.041755</td>\n", "      <td>0.041755</td>\n", "      <td>22.408333</td>\n", "      <td>0.940365</td>\n", "      <td>0.979195</td>\n", "      <td>0.955897</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>16</td>\n", "      <td>CEU3231160001</td>\n", "      <td>3116</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>548.800000</td>\n", "      <td>556.400000</td>\n", "      <td>1.013848</td>\n", "      <td>0.013848</td>\n", "      <td>7.600000</td>\n", "      <td>0.935612</td>\n", "      <td>0.761891</td>\n", "      <td>0.866124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>41</td>\n", "      <td>CEU3232610001</td>\n", "      <td>3261</td>\n", "      <td>Plastics product manufacturing</td>\n", "      <td>602.933333</td>\n", "      <td>592.525000</td>\n", "      <td>0.982737</td>\n", "      <td>-0.017263</td>\n", "      <td>-10.408333</td>\n", "      <td>1.000000</td>\n", "      <td>0.519637</td>\n", "      <td>0.807855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>44</td>\n", "      <td>CEU3133630001</td>\n", "      <td>3363</td>\n", "      <td>Motor vehicle parts manufacturing</td>\n", "      <td>563.783333</td>\n", "      <td>553.316667</td>\n", "      <td>0.981435</td>\n", "      <td>-0.018565</td>\n", "      <td>-10.466667</td>\n", "      <td>0.930116</td>\n", "      <td>0.509496</td>\n", "      <td>0.761868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3</td>\n", "      <td>CEU3231180001</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>337.133333</td>\n", "      <td>350.400000</td>\n", "      <td>1.039351</td>\n", "      <td>0.039351</td>\n", "      <td>13.266667</td>\n", "      <td>0.568443</td>\n", "      <td>0.960476</td>\n", "      <td>0.725256</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   rank       seriesID  Industry                              industry_name  \\\n", "0     2  CEU3133640001      3364  Aerospace product and parts manufacturing   \n", "1    16  CEU3231160001      3116         Animal slaughtering and processing   \n", "2    41  CEU3232610001      3261             Plastics product manufacturing   \n", "3    44  CEU3133630001      3363          Motor vehicle parts manufacturing   \n", "4     3  CEU3231180001      3118        Bakeries and tortilla manufacturing   \n", "\n", "     avg_2023    avg_2024     ratio  Recent_Growth_%     Growth  Emp_Score  \\\n", "0  536.658333  559.066667  1.041755         0.041755  22.408333   0.940365   \n", "1  548.800000  556.400000  1.013848         0.013848   7.600000   0.935612   \n", "2  602.933333  592.525000  0.982737        -0.017263 -10.408333   1.000000   \n", "3  563.783333  553.316667  0.981435        -0.018565 -10.466667   0.930116   \n", "4  337.133333  350.400000  1.039351         0.039351  13.266667   0.568443   \n", "\n", "   Emp_Growth_Score  Employment_Score  \n", "0          0.979195          0.955897  \n", "1          0.761891          0.866124  \n", "2          0.519637          0.807855  \n", "3          0.509496          0.761868  \n", "4          0.960476          0.725256  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["emp_df.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "cdcf0441", "metadata": {}, "outputs": [], "source": ["emp_df.rename(columns={'Industry': 'NAICS'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 15, "id": "1ca24a55", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Data Type</th>\n", "      <th>NAICS</th>\n", "      <th>Year</th>\n", "      <th>Description</th>\n", "      <th>Exports1</th>\n", "      <th>Imports1</th>\n", "      <th>Deficit</th>\n", "      <th>Exports</th>\n", "      <th>Imports</th>\n", "      <th>Trade Balance</th>\n", "      <th>Imports_Score</th>\n", "      <th>Deficit_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAS Value</td>\n", "      <td>3361</td>\n", "      <td>2024</td>\n", "      <td>MOTOR VEHICLES</td>\n", "      <td>70000797356</td>\n", "      <td>276136328894</td>\n", "      <td>206135531538</td>\n", "      <td>70.000797</td>\n", "      <td>276.136329</td>\n", "      <td>206.135532</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAS Value</td>\n", "      <td>3254</td>\n", "      <td>2024</td>\n", "      <td>PHARMACEUTICALS &amp; MEDICINES</td>\n", "      <td>90872734112</td>\n", "      <td>246094965766</td>\n", "      <td>155222231654</td>\n", "      <td>90.872734</td>\n", "      <td>246.094966</td>\n", "      <td>155.222232</td>\n", "      <td>0.891037</td>\n", "      <td>0.808456</td>\n", "      <td>0.849747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAS Value</td>\n", "      <td>3341</td>\n", "      <td>2024</td>\n", "      <td>COMPUTER EQUIPMENT</td>\n", "      <td>27207942419</td>\n", "      <td>152212075560</td>\n", "      <td>125004133141</td>\n", "      <td>27.207942</td>\n", "      <td>152.212076</td>\n", "      <td>125.004133</td>\n", "      <td>0.550516</td>\n", "      <td>0.694771</td>\n", "      <td>0.622644</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAS Value</td>\n", "      <td>3342</td>\n", "      <td>2024</td>\n", "      <td>COMMUNICATIONS EQUIPMENT</td>\n", "      <td>14984255701</td>\n", "      <td>126284273319</td>\n", "      <td>111300017618</td>\n", "      <td>14.984256</td>\n", "      <td>126.284273</td>\n", "      <td>111.300018</td>\n", "      <td>0.456474</td>\n", "      <td>0.643214</td>\n", "      <td>0.549844</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAS Value</td>\n", "      <td>3363</td>\n", "      <td>2024</td>\n", "      <td>MOTOR VEHICLE PARTS</td>\n", "      <td>49798443457</td>\n", "      <td>141969387660</td>\n", "      <td>92170944203</td>\n", "      <td>49.798443</td>\n", "      <td>141.969388</td>\n", "      <td>92.170944</td>\n", "      <td>0.513365</td>\n", "      <td>0.571247</td>\n", "      <td>0.542306</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Data Type  NAICS  Year                  Description     Exports1  \\\n", "0  FAS Value   3361  2024               MOTOR VEHICLES  70000797356   \n", "1  FAS Value   3254  2024  PHARMACEUTICALS & MEDICINES  90872734112   \n", "2  FAS Value   3341  2024           COMPUTER EQUIPMENT  27207942419   \n", "3  FAS Value   3342  2024     COMMUNICATIONS EQUIPMENT  14984255701   \n", "4  FAS Value   3363  2024          MOTOR VEHICLE PARTS  49798443457   \n", "\n", "       Imports1       Deficit   Exports      Imports  Trade Balance  \\\n", "0  276136328894  206135531538  70.000797  276.136329     206.135532   \n", "1  246094965766  155222231654  90.872734  246.094966     155.222232   \n", "2  152212075560  125004133141  27.207942  152.212076     125.004133   \n", "3  126284273319  111300017618  14.984256  126.284273     111.300018   \n", "4  141969387660   92170944203  49.798443  141.969388      92.170944   \n", "\n", "   Imports_Score  Deficit_Score  Import_Intensity_Score  \n", "0       1.000000       1.000000                1.000000  \n", "1       0.891037       0.808456                0.849747  \n", "2       0.550516       0.694771                0.622644  \n", "3       0.456474       0.643214                0.549844  \n", "4       0.513365       0.571247                0.542306  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["import_df.head()"]}, {"cell_type": "code", "execution_count": 16, "id": "a88b620d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(63, 85)\n"]}], "source": ["print(f\"{len(emp_df), len(import_df)}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "97d035de", "metadata": {}, "outputs": [], "source": ["# First merge the dataframes\n", "combined_naic = pd.merge(\n", "    emp_df[['NAICS', 'industry_name', 'Employment_Score']],\n", "    import_df[['NAICS', 'Import_Intensity_Score', 'Description']], \n", "    on='NAICS',\n", "    how='outer'\n", ")\n", "\n", "# Fill numeric columns with 0.5\n", "combined_naic['Employment_Score'] = combined_naic['Employment_Score'].fillna(0.5)\n", "combined_naic['Import_Intensity_Score'] = combined_naic['Import_Intensity_Score'].fillna(0.5)\n", "\n", "# For description columns, use coalesce to take the first non-null value\n", "combined_naic['industry_description'] = combined_naic['industry_name'].combine_first(combined_naic['Description'])\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "id": "f524fd47", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>industry_name</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Description</th>\n", "      <th>industry_description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>ANIMAL FOODS</td>\n", "      <td>Animal food manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>GRAIN &amp; OILSEED MILLING PRODUCTS</td>\n", "      <td>Grain and oilseed milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>SUGAR &amp; CONFECTIONERY PRODUCTS</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>FRUITS &amp; VEG PRESERVES &amp; SPECIALTY FOODS</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>DAIRY PRODUCTS</td>\n", "      <td>Dairy product manufacturing</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS                                      industry_name  Employment_Score  \\\n", "0   3111                          Animal food manufacturing          0.385857   \n", "1   3112                          Grain and oilseed milling          0.438410   \n", "2   3113      Sugar and confectionery product manufacturing          0.354900   \n", "3   3114  Fruit and vegetable preserving and specialty f...          0.458670   \n", "4   3115                        Dairy product manufacturing          0.458316   \n", "\n", "   Import_Intensity_Score                               Description  \\\n", "0                0.115238                              ANIMAL FOODS   \n", "1                0.159188          GRAIN & OILSEED MILLING PRODUCTS   \n", "2                0.153765            SUGAR & CONFECTIONERY PRODUCTS   \n", "3                0.167181  FRUITS & VEG PRESERVES & SPECIALTY FOODS   \n", "4                0.117253                            DAIRY PRODUCTS   \n", "\n", "                                industry_description  \n", "0                          Animal food manufacturing  \n", "1                          Grain and oilseed milling  \n", "2      Sugar and confectionery product manufacturing  \n", "3  Fruit and vegetable preserving and specialty f...  \n", "4                        Dairy product manufacturing  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_naic.head()"]}, {"cell_type": "code", "execution_count": 19, "id": "c5239b28", "metadata": {}, "outputs": [], "source": ["combined_naic = combined_naic.drop(['industry_name', 'Description'], axis=1)"]}, {"cell_type": "markdown", "id": "9c43d463", "metadata": {}, "source": ["## Extract 3 digit prefixes"]}, {"cell_type": "code", "execution_count": 20, "id": "1be610a0", "metadata": {}, "outputs": [], "source": ["combined_naic['NAICS_3'] = combined_naic['NAICS'].astype(str).str[:3]"]}, {"cell_type": "code", "execution_count": 21, "id": "c72acaef", "metadata": {}, "outputs": [], "source": ["bea_df['BEA_3'] = bea_df['BEA_code'].astype(str).str[:3]\n"]}, {"cell_type": "markdown", "id": "ea1c354a", "metadata": {}, "source": ["## Map NAICS code to BEA code"]}, {"cell_type": "code", "execution_count": 22, "id": "e118eaa7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3116</td>\n", "      <td>0.866124</td>\n", "      <td>0.137305</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3117</td>\n", "      <td>0.352433</td>\n", "      <td>0.127220</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3118</td>\n", "      <td>0.725256</td>\n", "      <td>0.149352</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3119</td>\n", "      <td>0.519237</td>\n", "      <td>0.157582</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3121</td>\n", "      <td>0.659114</td>\n", "      <td>0.206382</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>312</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS  Employment_Score  Import_Intensity_Score  \\\n", "0   3111          0.385857                0.115238   \n", "1   3112          0.438410                0.159188   \n", "2   3113          0.354900                0.153765   \n", "3   3114          0.458670                0.167181   \n", "4   3115          0.458316                0.117253   \n", "5   3116          0.866124                0.137305   \n", "6   3117          0.352433                0.127220   \n", "7   3118          0.725256                0.149352   \n", "8   3119          0.519237                0.157582   \n", "9   3121          0.659114                0.206382   \n", "\n", "                                industry_description NAICS_3  \n", "0                          Animal food manufacturing     311  \n", "1                          Grain and oilseed milling     311  \n", "2      Sugar and confectionery product manufacturing     311  \n", "3  Fruit and vegetable preserving and specialty f...     311  \n", "4                        Dairy product manufacturing     311  \n", "5                 Animal slaughtering and processing     311  \n", "6          Seafood product preparation and packaging     311  \n", "7                Bakeries and tortilla manufacturing     311  \n", "8                           Other food manufacturing     311  \n", "9                             Beverage manufacturing     312  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_naic.head(10)"]}, {"cell_type": "code", "execution_count": 23, "id": "59e99093", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\envs\\autowiz\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from sentence_transformers import SentenceTransformer, util\n", "\n", "# Load the pre-trained sentence embedding model once (outside the function for efficiency)\n", "model = SentenceTransformer('all-MiniLM-L6-v2')\n", "\n", "# Function to find the best BEA description match using semantic matching\n", "def find_best_bea_match(naics_desc, bea_options):\n", "    # Generate embedding for the NAICS description\n", "    naics_embedding = model.encode(naics_desc, convert_to_tensor=True)\n", "    \n", "    # Generate embeddings for the BEA description options\n", "    bea_embeddings = model.encode(bea_options, convert_to_tensor=True)\n", "    \n", "    # Compute cosine similarities between NAICS and BEA embeddings\n", "    similarities = util.pytorch_cos_sim(naics_embedding, bea_embeddings)[0]\n", "    \n", "    # Find the index of the highest similarity score\n", "    best_idx = similarities.argmax().item()\n", "    \n", "    # Return the best matching BEA description\n", "    return bea_options[best_idx]"]}, {"cell_type": "code", "execution_count": 24, "id": "85de7202", "metadata": {}, "outputs": [], "source": ["# Group BEA descriptions by their 3-digit prefix\n", "bea_options_dict = bea_df.groupby('BEA_3')['BEA_description'].apply(list).to_dict()"]}, {"cell_type": "code", "execution_count": 25, "id": "1f10e7af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'311': ['Food and beverage and tobacco products', 'Food manufacturing'], '312': ['Beverage and tobacco product manufacturing'], '313': ['Textile mills and textile product mills'], '315': ['Apparel and leather and allied products'], '321': ['Wood products'], '322': ['Paper products'], '323': ['Printing and related support activities'], '324': ['Petroleum and coal products'], '325': ['Basic chemical manufacturing', 'Chemical products', 'Other chemical manufacturing', 'Pharmaceutical and medicine manufacturing', 'Resin, rubber, and artificial fibers manufacturing'], '326': ['Plastics and rubber products'], '327': ['Nonmetallic mineral products'], '331': ['Iron and steel mills and manufacturing from purchased steel', 'Nonferrous metal production and processing and foundries', 'Primary metals'], '332': ['Fabricated metal products'], '333': ['Agricultural implement manufacturing', 'Construction machinery manufacturing', 'Machinery', 'Mining and oil and gas field machinery manufacturing', 'Other machinery'], '334': ['Communications equipment manufacturing', 'Computer and electronic products', 'Computer and peripheral equipment manufacturing', 'Navigational, measuring, electromedical, and control instruments manufacturing', 'Other computer and electronic product manufacturing', 'Semiconductor and other electronic component manufacturing'], '335': ['Electrical equipment, appliances, and components'], '336': ['Motor vehicles, bodies and trailers, and parts', 'Other transportation equipment'], '337': ['Furniture and related products'], '338': ['Medical equipment and supplies'], '339': ['Medical equipment and supplies manufacturing', 'Miscellaneous manufacturing', 'Other manufacturing', 'Other miscellaneous manufacturing']}\n"]}], "source": ["print(bea_options_dict)"]}, {"cell_type": "code", "execution_count": 26, "id": "990a7762", "metadata": {}, "outputs": [], "source": ["# Function to assign BEA_code based on NAICS data\n", "def get_bea_code(row):\n", "    naics_3 = row['NAICS_3']\n", "    naics_desc = row['industry_description']\n", "    if naics_3 in bea_options_dict:\n", "        bea_options = bea_options_dict[naics_3]\n", "        if len(bea_options) == 1:\n", "            # If only one option, use it directly\n", "            return bea_df[bea_df['BEA_description'] == bea_options[0]]['BEA_code'].values[0]\n", "        else:\n", "            # Use semantic matching to find the best description\n", "            best_match = find_best_bea_match(naics_desc, bea_options)\n", "            return bea_df[bea_df['BEA_description'] == best_match]['BEA_code'].values[0]\n", "    return None"]}, {"cell_type": "code", "execution_count": 28, "id": "f6597aea", "metadata": {}, "outputs": [], "source": ["# Apply the mapping\n", "combined_naic['BEA_code'] = combined_naic.apply(get_bea_code, axis=1)"]}, {"cell_type": "code", "execution_count": 29, "id": "d2ec5424", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3116</td>\n", "      <td>0.866124</td>\n", "      <td>0.137305</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3117</td>\n", "      <td>0.352433</td>\n", "      <td>0.127220</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3118</td>\n", "      <td>0.725256</td>\n", "      <td>0.149352</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3119</td>\n", "      <td>0.519237</td>\n", "      <td>0.157582</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3121</td>\n", "      <td>0.659114</td>\n", "      <td>0.206382</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>312</td>\n", "      <td>3120</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS  Employment_Score  Import_Intensity_Score  \\\n", "0   3111          0.385857                0.115238   \n", "1   3112          0.438410                0.159188   \n", "2   3113          0.354900                0.153765   \n", "3   3114          0.458670                0.167181   \n", "4   3115          0.458316                0.117253   \n", "5   3116          0.866124                0.137305   \n", "6   3117          0.352433                0.127220   \n", "7   3118          0.725256                0.149352   \n", "8   3119          0.519237                0.157582   \n", "9   3121          0.659114                0.206382   \n", "\n", "                                industry_description NAICS_3 BEA_code  \n", "0                          Animal food manufacturing     311     3110  \n", "1                          Grain and oilseed milling     311     3110  \n", "2      Sugar and confectionery product manufacturing     311     3110  \n", "3  Fruit and vegetable preserving and specialty f...     311     3110  \n", "4                        Dairy product manufacturing     311     3110  \n", "5                 Animal slaughtering and processing     311     3110  \n", "6          Seafood product preparation and packaging     311     3110  \n", "7                Bakeries and tortilla manufacturing     311     3110  \n", "8                           Other food manufacturing     311     3110  \n", "9                             Beverage manufacturing     312     3120  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_naic.head(10)"]}, {"cell_type": "code", "execution_count": 30, "id": "a66d4425", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rows with any NaN values:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>3141</td>\n", "      <td>0.147265</td>\n", "      <td>0.167028</td>\n", "      <td>Textile furnishings mills</td>\n", "      <td>314</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>3149</td>\n", "      <td>0.355692</td>\n", "      <td>0.138947</td>\n", "      <td>Other textile product mills</td>\n", "      <td>314</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>3161</td>\n", "      <td>0.500000</td>\n", "      <td>0.112006</td>\n", "      <td>LEATHER &amp; HIDE TANNING</td>\n", "      <td>316</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3162</td>\n", "      <td>0.500000</td>\n", "      <td>0.210280</td>\n", "      <td>FOOTWEAR</td>\n", "      <td>316</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>3169</td>\n", "      <td>0.500000</td>\n", "      <td>0.156348</td>\n", "      <td>OTHER LEATHER PRODUCTS</td>\n", "      <td>316</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    NAICS  Employment_Score  Import_Intensity_Score  \\\n", "14   3141          0.147265                0.167028   \n", "15   3149          0.355692                0.138947   \n", "19   3161          0.500000                0.112006   \n", "20   3162          0.500000                0.210280   \n", "21   3169          0.500000                0.156348   \n", "\n", "           industry_description NAICS_3 BEA_code  \n", "14    Textile furnishings mills     314     None  \n", "15  Other textile product mills     314     None  \n", "19       LEATHER & HIDE TANNING     316     None  \n", "20                     FOOTWEAR     316     None  \n", "21       OTHER LEATHER PRODUCTS     316     None  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["## Checking missing values\n", "# Display rows where any column has NaN\n", "print(\"Rows with any NaN values:\")\n", "combined_naic[combined_naic.isna().any(axis=1)]"]}, {"cell_type": "code", "execution_count": 31, "id": "c8f68ec0", "metadata": {}, "outputs": [], "source": ["# Update BEA codes where they are null and match specific NAICS_3 codes\n", "combined_naic.loc[(combined_naic['BEA_code'].isna()) & (combined_naic['NAICS_3'] == '314'), 'BEA_code'] = '313TT'\n", "combined_naic.loc[(combined_naic['BEA_code'].isna()) & (combined_naic['NAICS_3'] == '316'), 'BEA_code'] = '315AL'\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "id": "db981a20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated rows:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>3141</td>\n", "      <td>0.147265</td>\n", "      <td>0.167028</td>\n", "      <td>Textile furnishings mills</td>\n", "      <td>314</td>\n", "      <td>313TT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>3149</td>\n", "      <td>0.355692</td>\n", "      <td>0.138947</td>\n", "      <td>Other textile product mills</td>\n", "      <td>314</td>\n", "      <td>313TT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>3161</td>\n", "      <td>0.500000</td>\n", "      <td>0.112006</td>\n", "      <td>LEATHER &amp; HIDE TANNING</td>\n", "      <td>316</td>\n", "      <td>315AL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3162</td>\n", "      <td>0.500000</td>\n", "      <td>0.210280</td>\n", "      <td>FOOTWEAR</td>\n", "      <td>316</td>\n", "      <td>315AL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>3169</td>\n", "      <td>0.500000</td>\n", "      <td>0.156348</td>\n", "      <td>OTHER LEATHER PRODUCTS</td>\n", "      <td>316</td>\n", "      <td>315AL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    NAICS  Employment_Score  Import_Intensity_Score  \\\n", "14   3141          0.147265                0.167028   \n", "15   3149          0.355692                0.138947   \n", "19   3161          0.500000                0.112006   \n", "20   3162          0.500000                0.210280   \n", "21   3169          0.500000                0.156348   \n", "\n", "           industry_description NAICS_3 BEA_code  \n", "14    Textile furnishings mills     314    313TT  \n", "15  Other textile product mills     314    313TT  \n", "19       LEATHER & HIDE TANNING     316    315AL  \n", "20                     FOOTWEAR     316    315AL  \n", "21       OTHER LEATHER PRODUCTS     316    315AL  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# Verify the changes\n", "print(\"Updated rows:\")\n", "combined_naic[combined_naic['NAICS'].isin([3141, 3149, 3161, 3162, 3169])]"]}, {"cell_type": "code", "execution_count": 34, "id": "7f655814", "metadata": {}, "outputs": [], "source": ["# Merge NAICS and BEA data on 'BEA_code'\n", "combined_df = pd.merge(\n", "    combined_naic,\n", "    bea_df[['BEA_code', 'BEA_description', 'Fixed_Assets_Score', 'Economic_Heft_Score']],\n", "    on='BEA_code',\n", "    how='left'\n", ")"]}, {"cell_type": "code", "execution_count": 35, "id": "bc00e99e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS</th>\n", "      <th>Employment_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>industry_description</th>\n", "      <th>NAICS_3</th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>0.385857</td>\n", "      <td>0.115238</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3112</td>\n", "      <td>0.438410</td>\n", "      <td>0.159188</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3113</td>\n", "      <td>0.354900</td>\n", "      <td>0.153765</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3114</td>\n", "      <td>0.458670</td>\n", "      <td>0.167181</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3115</td>\n", "      <td>0.458316</td>\n", "      <td>0.117253</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>311</td>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>3371</td>\n", "      <td>0.338024</td>\n", "      <td>0.232060</td>\n", "      <td>Household and institutional furniture and kitc...</td>\n", "      <td>337</td>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>0.370733</td>\n", "      <td>0.287919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>3372</td>\n", "      <td>0.223960</td>\n", "      <td>0.137959</td>\n", "      <td>Office furniture (including fixtures) and othe...</td>\n", "      <td>337</td>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>0.370733</td>\n", "      <td>0.287919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>3379</td>\n", "      <td>0.500000</td>\n", "      <td>0.121508</td>\n", "      <td>FURNITURE RELATED PRODUCTS, NESOI</td>\n", "      <td>337</td>\n", "      <td>337</td>\n", "      <td>Furniture and related products</td>\n", "      <td>0.370733</td>\n", "      <td>0.287919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>3391</td>\n", "      <td>0.540576</td>\n", "      <td>0.262585</td>\n", "      <td>Medical equipment and supplies manufacturing</td>\n", "      <td>339</td>\n", "      <td>3391</td>\n", "      <td>Medical equipment and supplies manufacturing</td>\n", "      <td>0.500000</td>\n", "      <td>0.086173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>3399</td>\n", "      <td>0.463972</td>\n", "      <td>0.442334</td>\n", "      <td>Other miscellaneous manufacturing</td>\n", "      <td>339</td>\n", "      <td>3399</td>\n", "      <td>Other miscellaneous manufacturing</td>\n", "      <td>0.500000</td>\n", "      <td>0.042245</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>86 rows × 9 columns</p>\n", "</div>"], "text/plain": ["    NAICS  Employment_Score  Import_Intensity_Score  \\\n", "0    3111          0.385857                0.115238   \n", "1    3112          0.438410                0.159188   \n", "2    3113          0.354900                0.153765   \n", "3    3114          0.458670                0.167181   \n", "4    3115          0.458316                0.117253   \n", "..    ...               ...                     ...   \n", "81   3371          0.338024                0.232060   \n", "82   3372          0.223960                0.137959   \n", "83   3379          0.500000                0.121508   \n", "84   3391          0.540576                0.262585   \n", "85   3399          0.463972                0.442334   \n", "\n", "                                 industry_description NAICS_3 BEA_code  \\\n", "0                           Animal food manufacturing     311     3110   \n", "1                           Grain and oilseed milling     311     3110   \n", "2       Sugar and confectionery product manufacturing     311     3110   \n", "3   Fruit and vegetable preserving and specialty f...     311     3110   \n", "4                         Dairy product manufacturing     311     3110   \n", "..                                                ...     ...      ...   \n", "81  Household and institutional furniture and kitc...     337      337   \n", "82  Office furniture (including fixtures) and othe...     337      337   \n", "83                  FURNITURE RELATED PRODUCTS, NESOI     337      337   \n", "84       Medical equipment and supplies manufacturing     339     3391   \n", "85                  Other miscellaneous manufacturing     339     3399   \n", "\n", "                                 BEA_description  Fixed_Assets_Score  \\\n", "0                             Food manufacturing            0.498296   \n", "1                             Food manufacturing            0.498296   \n", "2                             Food manufacturing            0.498296   \n", "3                             Food manufacturing            0.498296   \n", "4                             Food manufacturing            0.498296   \n", "..                                           ...                 ...   \n", "81                Furniture and related products            0.370733   \n", "82                Furniture and related products            0.370733   \n", "83                Furniture and related products            0.370733   \n", "84  Medical equipment and supplies manufacturing            0.500000   \n", "85             Other miscellaneous manufacturing            0.500000   \n", "\n", "    Economic_Heft_Score  \n", "0              0.500000  \n", "1              0.500000  \n", "2              0.500000  \n", "3              0.500000  \n", "4              0.500000  \n", "..                  ...  \n", "81             0.287919  \n", "82             0.287919  \n", "83             0.287919  \n", "84             0.086173  \n", "85             0.042245  \n", "\n", "[86 rows x 9 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df.head(100)"]}, {"cell_type": "code", "execution_count": 36, "id": "78adfad0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_10960\\**********.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  final_df.rename(columns={\n"]}], "source": ["# Select and rename columns to match the required format\n", "final_df = combined_df[[\n", "    'BEA_code', 'BEA_description', 'NAICS', 'industry_description',\n", "    'Fixed_Assets_Score', 'Economic_Heft_Score', 'Import_Intensity_Score', 'Employment_Score'\n", "]]\n", "\n", "final_df.rename(columns={\n", "    'BEA_description': 'BEA_industry_description',\n", "    'NAICS': 'NAICS_code',\n", "    'industry_description': 'NAICS_industry_description'\n", "}, inplace=True)"]}, {"cell_type": "code", "execution_count": 39, "id": "b86d964d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3116</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.137305</td>\n", "      <td>0.866124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.127220</td>\n", "      <td>0.352433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.149352</td>\n", "      <td>0.725256</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3119</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.157582</td>\n", "      <td>0.519237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>3121</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.5</td>\n", "      <td>0.206382</td>\n", "      <td>0.659114</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code                    BEA_industry_description  NAICS_code  \\\n", "0     3110                          Food manufacturing        3111   \n", "1     3110                          Food manufacturing        3112   \n", "2     3110                          Food manufacturing        3113   \n", "3     3110                          Food manufacturing        3114   \n", "4     3110                          Food manufacturing        3115   \n", "5     3110                          Food manufacturing        3116   \n", "6     3110                          Food manufacturing        3117   \n", "7     3110                          Food manufacturing        3118   \n", "8     3110                          Food manufacturing        3119   \n", "9     3120  Beverage and tobacco product manufacturing        3121   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.498296   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "5                 Animal slaughtering and processing            0.498296   \n", "6          Seafood product preparation and packaging            0.498296   \n", "7                Bakeries and tortilla manufacturing            0.498296   \n", "8                           Other food manufacturing            0.498296   \n", "9                             Beverage manufacturing            0.228889   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0                  0.5                0.115238          0.385857  \n", "1                  0.5                0.159188          0.438410  \n", "2                  0.5                0.153765          0.354900  \n", "3                  0.5                0.167181          0.458670  \n", "4                  0.5                0.117253          0.458316  \n", "5                  0.5                0.137305          0.866124  \n", "6                  0.5                0.127220          0.352433  \n", "7                  0.5                0.149352          0.725256  \n", "8                  0.5                0.157582          0.519237  \n", "9                  0.5                0.206382          0.659114  "]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df.head(10)"]}, {"cell_type": "code", "execution_count": 37, "id": "51a38ed7", "metadata": {}, "outputs": [], "source": ["final_df.to_excel('Combined_NAICS_BEA_Scores_new.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "a9b95642", "metadata": {}, "source": ["## Adding Factset to NAICS mapping "]}, {"cell_type": "code", "execution_count": 46, "id": "f67b3e9e", "metadata": {}, "outputs": [], "source": ["mapping_df = pd.read_excel('NAICS_to_Factset.xlsx')"]}, {"cell_type": "code", "execution_count": 51, "id": "aa37e1b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["NAICS Code                                                      4453\n", "NAICS Title                         Beer, Wine, and Liquor Retailers\n", "Subgroup Code                                                 3505.0\n", "Subgroup Title                                           Food Retail\n", "Match Rationale    NAICS covers liquor retailing; FactSet include...\n", "Name: 156, dtype: object"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["mapping_df.iloc[156]"]}, {"cell_type": "code", "execution_count": 58, "id": "1c0d5830", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["object\n"]}], "source": ["print(mapping_df['NAICS Code'].dtype)"]}, {"cell_type": "code", "execution_count": 53, "id": "8a171bf4", "metadata": {}, "outputs": [], "source": ["comb_rank_df = pd.read_excel('Combined_NAICS_BEA_Scores_new.xlsx')"]}, {"cell_type": "code", "execution_count": 54, "id": "094e4cfe", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.498296   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0                  0.5                0.115238          0.385857  \n", "1                  0.5                0.159188          0.438410  \n", "2                  0.5                0.153765          0.354900  \n", "3                  0.5                0.167181          0.458670  \n", "4                  0.5                0.117253          0.458316  "]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["comb_rank_df.head()"]}, {"cell_type": "code", "execution_count": 59, "id": "5cae3234", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["object\n"]}], "source": ["print(comb_rank_df['NAICS_code'].dtype)"]}, {"cell_type": "code", "execution_count": 56, "id": "758e12e3", "metadata": {}, "outputs": [], "source": ["comb_rank_df['NAICS_code'] = comb_rank_df['NAICS_code'].astype(str).str.strip()"]}, {"cell_type": "code", "execution_count": 57, "id": "a52a67f7", "metadata": {}, "outputs": [], "source": ["mapping_df['NAICS Code'] = mapping_df['NAICS Code'].astype(str).str.strip()"]}, {"cell_type": "code", "execution_count": 61, "id": "0a162ff0", "metadata": {}, "outputs": [], "source": ["mapping_df.rename(columns={'NAICS Code': 'NAICS_code'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 62, "id": "5d3d44ba", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "You are trying to merge on int64 and object columns for key 'NAICS_code'. If you wish to proceed you should use pd.concat", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[62], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m final_df \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmerge\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m      2\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfinal_df\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m      3\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmapping_df\u001b[49m\u001b[43m[\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mNAICS_code\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSubgroup Code\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSubgroup Title\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m      4\u001b[0m \u001b[43m    \u001b[49m\u001b[43mon\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mNAICS_code\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m      5\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhow\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mleft\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\n\u001b[0;32m      6\u001b[0m \u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\autowiz\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py:170\u001b[0m, in \u001b[0;36mmerge\u001b[1;34m(left, right, how, on, left_on, right_on, left_index, right_index, sort, suffixes, copy, indicator, validate)\u001b[0m\n\u001b[0;32m    155\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _cross_merge(\n\u001b[0;32m    156\u001b[0m         left_df,\n\u001b[0;32m    157\u001b[0m         right_df,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    167\u001b[0m         copy\u001b[38;5;241m=\u001b[39mcopy,\n\u001b[0;32m    168\u001b[0m     )\n\u001b[0;32m    169\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 170\u001b[0m     op \u001b[38;5;241m=\u001b[39m \u001b[43m_MergeOperation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    171\u001b[0m \u001b[43m        \u001b[49m\u001b[43mleft_df\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    172\u001b[0m \u001b[43m        \u001b[49m\u001b[43mright_df\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    173\u001b[0m \u001b[43m        \u001b[49m\u001b[43mhow\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhow\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    174\u001b[0m \u001b[43m        \u001b[49m\u001b[43mon\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mon\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    175\u001b[0m \u001b[43m        \u001b[49m\u001b[43mleft_on\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mleft_on\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    176\u001b[0m \u001b[43m        \u001b[49m\u001b[43mright_on\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mright_on\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    177\u001b[0m \u001b[43m        \u001b[49m\u001b[43mleft_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mleft_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    178\u001b[0m \u001b[43m        \u001b[49m\u001b[43mright_index\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mright_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    179\u001b[0m \u001b[43m        \u001b[49m\u001b[43msort\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    180\u001b[0m \u001b[43m        \u001b[49m\u001b[43msuffixes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msuffixes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    181\u001b[0m \u001b[43m        \u001b[49m\u001b[43mindicator\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindicator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    182\u001b[0m \u001b[43m        \u001b[49m\u001b[43mvalidate\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvalidate\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    183\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    184\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m op\u001b[38;5;241m.\u001b[39mget_result(copy\u001b[38;5;241m=\u001b[39mcopy)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\autowiz\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py:807\u001b[0m, in \u001b[0;36m_MergeOperation.__init__\u001b[1;34m(self, left, right, how, on, left_on, right_on, left_index, right_index, sort, suffixes, indicator, validate)\u001b[0m\n\u001b[0;32m    803\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_tolerance(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mleft_join_keys)\n\u001b[0;32m    805\u001b[0m \u001b[38;5;66;03m# validate the merge keys dtypes. We may need to coerce\u001b[39;00m\n\u001b[0;32m    806\u001b[0m \u001b[38;5;66;03m# to avoid incompatible dtypes\u001b[39;00m\n\u001b[1;32m--> 807\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_maybe_coerce_merge_keys\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    809\u001b[0m \u001b[38;5;66;03m# If argument passed to validate,\u001b[39;00m\n\u001b[0;32m    810\u001b[0m \u001b[38;5;66;03m# check if columns specified as unique\u001b[39;00m\n\u001b[0;32m    811\u001b[0m \u001b[38;5;66;03m# are in fact unique.\u001b[39;00m\n\u001b[0;32m    812\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m validate \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\autowiz\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py:1508\u001b[0m, in \u001b[0;36m_MergeOperation._maybe_coerce_merge_keys\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1502\u001b[0m     \u001b[38;5;66;03m# unless we are merging non-string-like with string-like\u001b[39;00m\n\u001b[0;32m   1503\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m (\n\u001b[0;32m   1504\u001b[0m         inferred_left \u001b[38;5;129;01min\u001b[39;00m string_types \u001b[38;5;129;01mand\u001b[39;00m inferred_right \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m string_types\n\u001b[0;32m   1505\u001b[0m     ) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   1506\u001b[0m         inferred_right \u001b[38;5;129;01min\u001b[39;00m string_types \u001b[38;5;129;01mand\u001b[39;00m inferred_left \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m string_types\n\u001b[0;32m   1507\u001b[0m     ):\n\u001b[1;32m-> 1508\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(msg)\n\u001b[0;32m   1510\u001b[0m \u001b[38;5;66;03m# datetimelikes must match exactly\u001b[39;00m\n\u001b[0;32m   1511\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m needs_i8_conversion(lk\u001b[38;5;241m.\u001b[39mdtype) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m needs_i8_conversion(rk\u001b[38;5;241m.\u001b[39mdtype):\n", "\u001b[1;31mValueError\u001b[0m: You are trying to merge on int64 and object columns for key 'NAICS_code'. If you wish to proceed you should use pd.concat"]}], "source": ["final_df = pd.merge(\n", "    final_df,\n", "    mapping_df[['NAICS_code', 'Subgroup Code', 'Subgroup Title']],\n", "    on='NAICS_code',\n", "    how='left'\n", ")"]}, {"cell_type": "code", "execution_count": 51, "id": "0a3423f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Factset_code</th>\n", "      <th>Factset_title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>2225</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3116</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.137305</td>\n", "      <td>0.866124</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>311a</td>\n", "      <td>Food and beverage and tobacco products</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>0.492662</td>\n", "      <td>0.672787</td>\n", "      <td>0.127220</td>\n", "      <td>0.352433</td>\n", "      <td>2415</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.149352</td>\n", "      <td>0.725256</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3119</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.500000</td>\n", "      <td>0.157582</td>\n", "      <td>0.519237</td>\n", "      <td>2410</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>3121</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.500000</td>\n", "      <td>0.206382</td>\n", "      <td>0.659114</td>\n", "      <td>2420</td>\n", "      <td>Beverages: Non-Alcoholic</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code                    BEA_industry_description NAICS_code  \\\n", "0     3110                          Food manufacturing       3111   \n", "1     311a      Food and beverage and tobacco products       3112   \n", "2     3110                          Food manufacturing       3113   \n", "3     3110                          Food manufacturing       3114   \n", "4     3110                          Food manufacturing       3115   \n", "5     311a      Food and beverage and tobacco products       3116   \n", "6     311a      Food and beverage and tobacco products       3117   \n", "7     3110                          Food manufacturing       3118   \n", "8     3110                          Food manufacturing       3119   \n", "9     3120  Beverage and tobacco product manufacturing       3121   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.492662   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "5                 Animal slaughtering and processing            0.492662   \n", "6          Seafood product preparation and packaging            0.492662   \n", "7                Bakeries and tortilla manufacturing            0.498296   \n", "8                           Other food manufacturing            0.498296   \n", "9                             Beverage manufacturing            0.228889   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0             0.500000                0.115238          0.385857   \n", "1             0.672787                0.159188          0.438410   \n", "2             0.500000                0.153765          0.354900   \n", "3             0.500000                0.167181          0.458670   \n", "4             0.500000                0.117253          0.458316   \n", "5             0.672787                0.137305          0.866124   \n", "6             0.672787                0.127220          0.352433   \n", "7             0.500000                0.149352          0.725256   \n", "8             0.500000                0.157582          0.519237   \n", "9             0.500000                0.206382          0.659114   \n", "\n", "   Factset_code                     Factset_title  \n", "0          2225  Agricultural Commodities/Milling  \n", "1          2225  Agricultural Commodities/Milling  \n", "2          2225  Agricultural Commodities/Milling  \n", "3          2410             Food: Specialty/Candy  \n", "4          2415             Food: Meat/Fish/Dairy  \n", "5          2415             Food: Meat/Fish/Dairy  \n", "6          2415             Food: Meat/Fish/Dairy  \n", "7          2410             Food: Specialty/Candy  \n", "8          2410             Food: Specialty/Candy  \n", "9          2420          Beverages: Non-Alcoholic  "]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df.head(10)"]}, {"cell_type": "code", "execution_count": 48, "id": "d06faa55", "metadata": {}, "outputs": [], "source": ["final_df['Subgroup Code'] = final_df['Subgroup Code'].astype('Int64')"]}, {"cell_type": "code", "execution_count": 50, "id": "d555d515", "metadata": {}, "outputs": [], "source": ["final_df.rename(columns={'Subgroup Code': 'Factset_code', 'Subgroup Title': 'Factset_title' }, inplace=True)"]}, {"cell_type": "code", "execution_count": 53, "id": "d6ce8265", "metadata": {}, "outputs": [], "source": ["final_df.to_excel('Combined_NAICS_BEA_Scores_factset.xlsx', index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}