{"cells": [{"cell_type": "markdown", "id": "f4962f35", "metadata": {}, "source": ["## URL Context"]}, {"cell_type": "code", "execution_count": 5, "id": "70306889", "metadata": {}, "outputs": [], "source": ["from google import genai\n", "from google.genai.types import Tool, GenerateContentConfig, GoogleSearch\n", "import google.genai.types as types\n", "from sec_api import QueryApi, ExtractorApi\n", "from typing import Any, Dict, List, Optional, Tuple\n", "from requests import HTTPError\n", "import json\n", "from enum import Enum\n", "import os\n", "import sys\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "3c29de39", "metadata": {}, "outputs": [], "source": ["client = genai.Client(api_key=\"AIzaSyBqrAc1A0b-xGbYBdgNYVQEnuQQDk4MO3E\")\n", "model_id = \"gemini-2.5-flash-preview-05-20\""]}, {"cell_type": "code", "execution_count": 7, "id": "18bf895e", "metadata": {}, "outputs": [], "source": ["SEC_API_KEY = \"68b9ae6cc72425bdda7ef036f48ab0812bfb1ab2ce5cd686d484b3247d2687d5\""]}, {"cell_type": "code", "execution_count": null, "id": "229a31e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NVIDIA's gross profit for the fiscal year ended January 26, 2025, was $97,858 million. This represents a gross margin of 75.0%. The increase in gross margin was primarily driven by a higher mix of Data Center revenue.\n", "url_metadata=[UrlMetadata(retrieved_url='https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97', url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>)]\n"]}], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "#tools.append(Tool(google_search=types.GoogleSearch))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=\"summarize this and tell me about the gross profit of this year https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97\",\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")\n", "\n", "for each in response.candidates[0].content.parts:\n", "    print(each.text)\n", "# get URLs retrieved for context\n", "print(response.candidates[0].url_context_metadata)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c04c3c0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NVIDIA's cash and cash equivalents for the fiscal year ended January 28, 2024, were $7,280 million.\n", "url_metadata=[UrlMetadata(retrieved_url='https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97', url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>)]\n"]}], "source": ["# targeted query 2\n", "tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "#tools.append(Tool(google_search=types.GoogleSearch))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=\"what is the cash and cash equivalents of this company for last year? https://www.sec.gov/Archives/edgar/data/1045810/000104581025000023/nvda-20250126.htm#if3830601512b46079053ec0daaf407ac_97\",\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")\n", "\n", "for each in response.candidates[0].content.parts:\n", "    print(each.text)\n", "# get URLs retrieved for context\n", "print(response.candidates[0].url_context_metadata)\n"]}, {"cell_type": "markdown", "id": "810d9c83", "metadata": {}, "source": ["```\n", "Pick the SEC filings of companies whose data is not available\n", "put those links in the url context \n", "ask questions\n"]}, {"cell_type": "code", "execution_count": 8, "id": "6dbe584f", "metadata": {}, "outputs": [], "source": ["class SecEdgerImportantInfo(str, Enum):\n", "    COMPANY_NAME = 'companyName'\n", "    # STOCK_SYMBOL = 'ticker'\n", "    # CIK = 'cik'\n", "    FORM_TYPE = 'formType'\n", "    FISCAL_DATE_ENDING = 'periodOfReport'\n", "    # FILING_DATE = 'filedAt'\n", "    FILING_URL = 'linkToFilingDetails'\n", "    # DATA_FILES = 'dataFiles'\n", "\n", "    def __repr__(self) -> str:\n", "        return str.__repr__(self)\n", "    \n", "    def __str__(self) -> str:\n", "        return str.__str__(self)\n", "\n", "    @staticmethod\n", "    def get_all_important_info() -> List[str]:\n", "        return [col.value for col in SecEdgerImportantInfo]"]}, {"cell_type": "code", "execution_count": 9, "id": "9c0592d4", "metadata": {}, "outputs": [], "source": ["def get_filing_info(stock_symbol, file_type: str = \"10-Q\", date_range: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:\n", "    try:\n", "        query_api = QueryApi(api_key=SEC_API_KEY)\n", "        if date_range is None:\n", "            query = f'ticker: {stock_symbol} AND formType: \"{file_type}\"'\n", "        else:\n", "            query = f\"ticker: {stock_symbol} AND formType: \\\"{file_type}\\\" AND periodOfReport: [{date_range['from']} TO {date_range['upto']}]\"\n", "        query_params = {\n", "            \"query\": query,\n", "            \"sort\": [{\"periodOfReport\": {\"order\": \"desc\"}}]\n", "        }\n", "        filings = query_api.get_filings(query=query_params)[\"filings\"]\n", "        report_info = [\n", "            {field: filing[field] for field in SecEdgerImportantInfo.get_all_important_info()} for filing in filings \n", "            if SecEdgerImportantInfo.FISCAL_DATE_ENDING in filing\n", "        ]\n", "        return report_info\n", "    \n", "    except Exception as e:\n", "        raise HTTPError(f'Error occurred while running query_api -> {e}')"]}, {"cell_type": "code", "execution_count": 24, "id": "ed48c076", "metadata": {}, "outputs": [], "source": ["result = get_filing_info(file_type=\"10-Q\", stock_symbol=\"NVDA\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-04-16\"})"]}, {"cell_type": "code", "execution_count": 13, "id": "14582871", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"companyName\": \"NVIDIA CORP\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-10-27\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"NVIDIA CORP\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-07-28\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581024000264/nvda-20240728.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"NVIDIA CORP\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-04-28\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/1045810/000104581024000124/nvda-20240428.htm\"\n", "    }\n", "]\n"]}], "source": ["print(json.dumps(result, indent=4))"]}, {"cell_type": "code", "execution_count": 14, "id": "6d507ca7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filing URL: https://www.sec.gov/Archives/edgar/data/1045810/000104581024000316/nvda-20241027.htm\n"]}], "source": ["filing_url = result[0][SecEdgerImportantInfo.FILING_URL]\n", "print(f\"Filing URL: {filing_url}\")"]}, {"cell_type": "markdown", "id": "feae02cf", "metadata": {}, "source": ["## trying for all 10 Q urls of two companies"]}, {"cell_type": "markdown", "id": "06fe2fc8", "metadata": {}, "source": ["Enerpac Tool Group Corp.(EPAC), Worthington Enterprises, Inc.(WOR)"]}, {"cell_type": "code", "execution_count": 10, "id": "7e071435", "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Optional\n", "from collections import defaultdict\n", "\n", "def get_10Q_url(stock_symbols: List[str], date_range: Optional[Dict[str, str]] = None):\n", "    filing_urls = []\n", "    for stock_symbol in stock_symbols:\n", "        url_list = get_filing_info(stock_symbol, date_range=date_range)\n", "        company_dict = defaultdict(dict)\n", "\n", "        for url in url_list:\n", "            company_name = url[SecEdgerImportantInfo.COMPANY_NAME]\n", "            period = url[SecEdgerImportantInfo.FISCAL_DATE_ENDING]\n", "            filing_url = url[SecEdgerImportantInfo.FILING_URL]\n", "\n", "            company_dict[company_name][period] = filing_url\n", "\n", "        filing_urls.append(dict(company_dict))\n", "        \n", "    return filing_urls\n"]}, {"cell_type": "code", "execution_count": 11, "id": "f667e6f2", "metadata": {}, "outputs": [], "source": ["url_list = get_10Q_url([\"EPAC\", \"WOR\"], date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-29\"})"]}, {"cell_type": "code", "execution_count": 12, "id": "582d7c32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"ENERPAC TOOL GROUP CORP\": {\n", "            \"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695525000008/epac-20250228.htm\",\n", "            \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\",\n", "            \"2024-05-31\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000042/epac-20240531.htm\",\n", "            \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000037/epac-20240229.htm\"\n", "        }\n", "    },\n", "    {\n", "        \"WORTHINGTON ENTERPRISES, INC.\": {\n", "            \"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", "            \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", "            \"2024-08-31\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", "            \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "        }\n", "    }\n", "]\n"]}], "source": ["print(json.dumps(url_list, indent=4))"]}, {"cell_type": "code", "execution_count": 24, "id": "115a7776", "metadata": {}, "outputs": [], "source": ["system_instruction_sec = \"\"\"\\\n", "You are an expert in extracting structured information from official SEC filings of companies.\n", "\n", "You are provided with a list of dictionaries. In each dictionary:\n", "- The key is a company name.\n", "- The value is another dictionary, where:\n", "  - Keys are fiscal quarter ending dates (in YYYY-MM-DD format).\n", "  - Values are URLs to the company's 10-Q filings for those quarters.\n", "\n", "All filings are from the period January 1, 2024 to the present. Your task is to extract relevant United states specific information from each filing under the following three categories and return the output strictly as per the described JSON format.\n", "\n", "Categories of Interest:\n", "\n", "1. Manufacturing Capacity Expansion\n", "   - Extract any statements related to expansion of existing manufacturing facilities or plans to establish new plants/factories in the United States.\n", "   - Include numeric details wherever available:\n", "     - Capital expenditure (e.g., \"$1 billion investment\")\n", "     - Plant size (e.g., \"300,000 sq ft facility\")\n", "     - Projected production capacity (e.g., \"10 million units/year\")\n", "\n", "2. Employment Generation or Hiring Commitments\n", "   - Extract information about hiring plans, job creation commitments, or workforce expansion in the United States.\n", "   - Include specific figures where mentioned:\n", "     - Number of jobs (e.g., \"2,500 new roles\")\n", "     - Duration, location, or business unit if specified.\n", "\n", "3. Actual Production Output\n", "   - Extract actual production data from U.S. operations as disclosed.\n", "   - Metrics may include units produced, tons manufactured, volume output, etc.\n", "\n", "Required Output Format:\n", "\n", "Return a list of dictionaries. Each dictionary corresponds to one company and must follow this exact json structure:\n", "\n", "```json\n", "[\n", "  {\n", "  \"company\": \"<Company Name>\",\n", "  \"capacity_expansion_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"employment_generation_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"production_output\": [\n", "    {\n", "      \"output_details\": \"<Details of actual production figures per quarter>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"summary\": \"<Concise summary of all the findings across the three categories>\"\n", "}\n", "]\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 35, "id": "1dc41d85", "metadata": {}, "outputs": [], "source": ["output_format_sec = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"multi_company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"array\",\n", "            \"items\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"company\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"Name of the company under consideration.\"\n", "                    },\n", "                    \"capacity_expansion_plans\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"plan_details\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Manufacturing capacity expansion or new plant opening plan details of the company in the United States as per the 10-Q SEC filings.\"\n", "                                },\n", "                                \"source\": {\n", "                                    \"type\": \"object\",\n", "                                    \"properties\": {\n", "                                        \"publishing_date\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Quarter ending date provided for the 10-Q SEC filing by the user (YYYY-MM-DD).\"\n", "                                        },\n", "                                        \"section\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Section or heading within the 10-Q SEC filing where the excerpt is found.\"\n", "                                        },\n", "                                        \"url\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"URL of the source 10-Q SEC filing.\"\n", "                                        }\n", "                                    },\n", "                                    \"required\": [\n", "                                        \"publishing_date\",\n", "                                        \"section\",\n", "                                        \"url\"\n", "                                    ],\n", "                                    \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                                }\n", "                            },\n", "                            \"required\": [\n", "                                \"plan_details\",\n", "                                \"source\"\n", "                            ],\n", "                            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                        }\n", "                    },\n", "                    \"employment_generation_plans\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"plan_details\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Employment generation plan details of the company in the United States as per the 10-Q SEC filings.\"\n", "                                },\n", "                                \"source\": {\n", "                                    \"type\": \"object\",\n", "                                    \"properties\": {\n", "                                        \"publishing_date\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Quarter ending date provided for the 10-Q SEC filing by the user (YYYY-MM-DD).\"\n", "                                        },\n", "                                        \"section\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Section or heading within the 10-Q SEC filing where the excerpt is found.\"\n", "                                        },\n", "                                        \"url\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"URL of the source 10-Q SEC filing.\"\n", "                                        }\n", "                                    },\n", "                                    \"required\": [\n", "                                        \"publishing_date\",\n", "                                        \"section\",\n", "                                        \"url\"\n", "                                    ],\n", "                                    \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                                }\n", "                            },\n", "                            \"required\": [\n", "                                \"plan_details\",\n", "                                \"source\"\n", "                            ],\n", "                            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                        }\n", "                    },\n", "                    \"production_output\": {\n", "                        \"type\": \"array\",\n", "                        \"items\": {\n", "                            \"type\": \"object\",\n", "                            \"properties\": {\n", "                                \"output_details\": {\n", "                                    \"type\": \"string\",\n", "                                    \"description\": \"Actual production output (units or volumes produced) per quarter of the company in the United States as reported in 10-Q SEC filings.\"\n", "                                },\n", "                                \"source\": {\n", "                                    \"type\": \"object\",\n", "                                    \"properties\": {\n", "                                        \"publishing_date\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Quarter ending date provided for the 10-Q SEC filing by the user (YYYY-MM-DD).\"\n", "                                        },\n", "                                        \"section\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"Section or heading within the 10-Q SEC filing where the excerpt is found.\"\n", "                                        },\n", "                                        \"url\": {\n", "                                            \"type\": \"string\",\n", "                                            \"description\": \"URL of the source 10-Q SEC filing.\"\n", "                                        }\n", "                                    },\n", "                                    \"required\": [\n", "                                        \"publishing_date\",\n", "                                        \"section\",\n", "                                        \"url\"\n", "                                    ],\n", "                                    \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                                }\n", "                            },\n", "                            \"required\": [\n", "                                \"output_details\",\n", "                                \"source\"\n", "                            ],\n", "                            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                        }\n", "                    },\n", "                    \"summary\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"Short overview of all plan details and output details.\"\n", "                    }\n", "                },\n", "                \"required\": [\n", "                    \"company\",\n", "                    \"capacity_expansion_plans\",\n", "                    \"employment_generation_plans\",\n", "                    \"production_output\",\n", "                    \"summary\"\n", "                ],\n", "                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "            }\n", "        }\n", "    }\n", "}\n"]}, {"cell_type": "code", "execution_count": 15, "id": "40b007aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{\"ENERPAC TOOL GROUP CORP\": {\"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695525000008/epac-20250228.htm\", \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\", \"2024-05-31\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000042/epac-20240531.htm\", \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000037/epac-20240229.htm\"}}, {\"WORTHINGTON ENTERPRISES, INC.\": {\"2025-02-28\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\", \"2024-11-30\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\", \"2024-08-31\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\", \"2024-02-29\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"}}]\n", "<class 'str'>\n"]}], "source": ["quarterly_urls = json.dumps(url_list)\n", "print(quarterly_urls)\n", "print(type(quarterly_urls))"]}, {"cell_type": "code", "execution_count": 54, "id": "b466ddad", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents= quarterly_urls,\n", "    config=GenerateContentConfig(\n", "        system_instruction = system_instruction_sec,\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")\n", "\n", "# for each in response.candidates[0].content.parts:\n", "#     print(each.text)\n", "# # get URLs retrieved for context\n", "# print(response.candidates[0].url_context_metadata)\n"]}, {"cell_type": "code", "execution_count": 57, "id": "3ca4bdb2", "metadata": {}, "outputs": [], "source": ["raw_text = response.candidates[0].content.parts[0].text\n"]}, {"cell_type": "code", "execution_count": 58, "id": "90da5a90", "metadata": {}, "outputs": [], "source": ["import re\n", "cleaned_json_str = re.sub(r\"^```json\\s*|\\s*```$\", \"\", raw_text.strip())\n", "parsed_data = json.loads(cleaned_json_str)"]}, {"cell_type": "code", "execution_count": 61, "id": "a0c10474", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"company\": \"ENERPAC TOOL GROUP CORP\",\n", "        \"capacity_expansion_plans\": [\n", "            {\n", "                \"plan_details\": \"Increased capital expenditures relating to build-out costs for the Company's new headquarters location in Milwaukee, Wisconsin, with an anticipated move-in date later in fiscal 2025. This contributed to a $2.8 million increase in Land, buildings and improvements from August 31, 2024 to November 30, 2024.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-11-30\",\n", "                    \"section\": \"Item 2\\u2014Management's Discussion and Analysis of Financial Condition and Results of Operations, Cash Flows and Liquidity & Note 1. Basis of Presentation - Property Plant and Equipment\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/6955/000000695524000054/epac-20241130.htm\"\n", "                }\n", "            }\n", "        ],\n", "        \"employment_generation_plans\": [],\n", "        \"production_output\": [],\n", "        \"summary\": \"ENERPAC TOOL GROUP CORP. made capital expenditures for the build-out of a new headquarters in Milwaukee, Wisconsin, impacting their property, plant and equipment balance. No explicit employment generation plans or specific U.S. production output figures were found in the available filings.\"\n", "    },\n", "    {\n", "        \"company\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"capacity_expansion_plans\": [\n", "            {\n", "                \"plan_details\": \"Ongoing capital expenditures on property, plant and equipment in the U.S. 'Buildings and improvements' increased by $6,206 thousand, 'Machinery and equipment' by $34,842 thousand, and 'Construction in progress' by $2,826 thousand during the three months ended November 30, 2024.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-11-30\",\n", "                    \"section\": \"Item 1. Financial Statements - Consolidated Balance Sheets\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "                }\n", "            },\n", "            {\n", "                \"plan_details\": \"Ongoing capital expenditures on property, plant and equipment in the U.S. 'Buildings and improvements' increased by $5,776 thousand, 'Machinery and equipment' by $22,414 thousand, and 'Construction in progress' by $9,337 thousand during the three months ended August 31, 2024.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-08-31\",\n", "                    \"section\": \"Item 1. Financial Statements - Consolidated Balance Sheets\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "                }\n", "            }\n", "        ],\n", "        \"employment_generation_plans\": [],\n", "        \"production_output\": [\n", "            {\n", "                \"output_details\": \"Total reportable segments volume for the three months ended November 30, 2024, was 19,264,673 units, a decrease from 19,840,403 units in the prior-year period. This includes Consumer Products at 16,170,556 units (up from 16,031,583) and Building Products at 3,094,117 units (down from 3,808,820). Note: These volumes are for global segments and are not explicitly stated as U.S. only production.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-11-30\",\n", "                    \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "                }\n", "            },\n", "            {\n", "                \"output_details\": \"Total reportable segments volume for the three months ended August 31, 2024, was 19,264,673 units, a decrease from 19,946,709 units in the prior-year period. This includes Consumer Products at 16,170,556 units (up from 16,031,583) and Building Products at 3,094,117 units (down from 3,808,820). Note: These volumes are for global segments and are not explicitly stated as U.S. only production.\",\n", "                \"source\": {\n", "                    \"publishing_date\": \"2024-08-31\",\n", "                    \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                    \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "                }\n", "            }\n", "        ],\n", "        \"summary\": \"WORTHINGTON ENTERPRISES, INC. continued to invest in property, plant, and equipment in the U.S. through capital expenditures, with notable increases in buildings, machinery, and construction in progress. The company reported global production volumes for its Consumer Products and Building Products segments, but no specific U.S.-only production data was available. No explicit employment generation plans were disclosed; rather, discussions around 'restructuring activities' implied workforce adjustments.\"\n", "    }\n", "]\n"]}], "source": ["print(json.dumps(parsed_data, indent=4))"]}, {"cell_type": "markdown", "id": "389e2a3a", "metadata": {}, "source": ["## Single company extraction"]}, {"cell_type": "code", "execution_count": 38, "id": "fb94362e", "metadata": {}, "outputs": [], "source": ["single_company_data = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of a company.\n", "The company of interest is WORTHINGTON ENTERPRISES. And url to access its different SEC reports are as follows:\n", "10-K report url - \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "\n", "10-Q report urls - \n", "[\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\",\n", " \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "]\n", "              \n", "All filings are from the period January 1, 2024 to the present. Your task is to extract relevant United States-specific information from these filings under the following three categories and return the output strictly in the described JSON format.\n", "\n", "Categories of Interest:\n", "\n", "1. Manufacturing Capacity Expansion\n", "   - Extract any statements related to expansion of existing manufacturing facilities or plans to establish new plants/factories in the United States.\n", "   - Include numeric details wherever available:\n", "     - Capital expenditure (e.g., \"$1 billion investment\")\n", "     - Plant size (e.g., \"300,000 sq ft facility\")\n", "     - Projected production capacity (e.g., \"10 million units/year\")\n", "\n", "2. Employment Generation or Hiring Commitments\n", "   - Extract information about hiring plans, job creation commitments, or workforce expansion in the United States.\n", "   - Include specific figures where mentioned:\n", "     - Number of jobs (e.g., \"2,500 new roles\")\n", "     - Duration, location, or business unit if specified.\n", "\n", "3. Actual Production Output\n", "   - Extract actual production data from U.S. operations as disclosed.\n", "   - Metrics may include units produced, tons manufactured, volume output, etc.\n", "\n", "Required Output Format:\n", "\n", "Return the output in JSON format with following top level fields:\n", "- `\"company\"`: The name of the company as a string.\n", "- `\"capacity_expansion_plans\"`: A list of dictionaries, each capturing one U.S.-based manufacturing capacity expansion initiative.\n", "- `\"employment_generation_plans\"`: A list of dictionaries, each capturing one hiring or job creation plan.\n", "- `\"production_output\"`: A list of dictionaries, each capturing actual production figures or details.\n", "- `\"summary\"`: A concise narrative summary of findings across all categories.\n", "\n", "JSON schema format for the output generation is given below:\n", "```\n", " {\n", "  \"company\": \"<Company Name>\",\n", "  \"capacity_expansion_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of the expansion plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"employment_generation_plans\": [\n", "    {\n", "      \"plan_details\": \"<Detailed description of hiring or job creation plan>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"production_output\": [\n", "    {\n", "      \"output_details\": \"<Details of actual production figures per quarter>\",\n", "      \"source\": {\n", "        \"publishing_date\": \"<YYYY-MM-DD>\",\n", "        \"section\": \"<Section name or number if available>\",\n", "        \"url\": \"<Link to the SEC filing>\"\n", "      }\n", "    },\n", "    ...\n", "  ],\n", "  \"summary\": \"<Concise summary of all the findings across the three categories>\"\n", "}\n", "\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "fbd976b0", "metadata": {}, "outputs": [], "source": ["result_10k = get_filing_info(file_type=\"10-K\", stock_symbol=\"WOR\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-29\"})\n", "result_10q = get_filing_info(file_type=\"10-Q\", stock_symbol=\"WOR\", date_range={\"from\": \"2024-01-01\", \"upto\": \"2025-05-29\"})\n"]}, {"cell_type": "code", "execution_count": 35, "id": "0a167348", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-K\",\n", "        \"periodOfReport\": \"2024-05-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "    }\n", "]\n", "**************************************************\n", "[\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2025-02-28\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025052413/wor-20250228.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-11-30\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017025004028/wor-20241130.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-08-31\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "    },\n", "    {\n", "        \"companyName\": \"WORTHINGTON ENTERPRISES, INC.\",\n", "        \"formType\": \"10-Q\",\n", "        \"periodOfReport\": \"2024-02-29\",\n", "        \"linkToFilingDetails\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024042963/wor-20240229.htm\"\n", "    }\n", "]\n", "**************************************************\n"]}], "source": ["\n", "print(json.dumps(result_10k, indent=4))\n", "print(\"*\"*50)\n", "print(json.dumps(result_10q, indent=4))\n", "print(\"*\"*50)\n"]}, {"cell_type": "code", "execution_count": 43, "id": "cbe49a24", "metadata": {}, "outputs": [], "source": ["tools = []\n", "tools.append(Tool(url_context=types.UrlContext))\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=single_company_data,\n", "    config=GenerateContentConfig(\n", "        tools=tools,\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 44, "id": "b0461f33", "metadata": {}, "outputs": [], "source": ["raw_text = response.candidates[0].content.parts[0].text"]}, {"cell_type": "code", "execution_count": 45, "id": "a64b4ead", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"company\": \"WORTHINGTON ENTERPRISES\",\n", "    \"capacity_expansion_plans\": [\n", "        {\n", "            \"plan_details\": \"On February 1, 2024, Worthington Enterprises acquired an 80% ownership stake in Halo, an asset-light business focused on outdoor cooking solutions. This acquisition included one manufacturing facility located in Kentucky, United States. The total purchase price for this acquisition was approximately $9.6 million.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-05-31\",\n", "                \"section\": \"Item 1. Business - Other Business Developments / Note Q \\u2013 Acquisitions\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "            }\n", "        }\n", "    ],\n", "    \"employment_generation_plans\": [],\n", "    \"production_output\": [\n", "        {\n", "            \"output_details\": \"For the fiscal year ended May 31, 2024, the Consumer Products segment produced 66,632,148 units. The Building Products segment produced 14,157,050 units. The consolidated total volume from continuing operations was 81,312,367 units, primarily representing U.S. operations.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-05-31\",\n", "                \"section\": \"Item 7. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024087983/wor-20240531.htm\"\n", "            }\n", "        },\n", "        {\n", "            \"output_details\": \"For the three months ended August 31, 2024 (first quarter of fiscal year 2025), the Consumer Products segment produced 16,170,556 units. The Building Products segment produced 3,094,117 units. The consolidated total volume from continuing operations was 19,264,673 units, primarily representing U.S. operations.\",\n", "            \"source\": {\n", "                \"publishing_date\": \"2024-08-31\",\n", "                \"section\": \"Item 2. Management's Discussion and Analysis of Financial Condition and Results of Operations - Net Sales and Volume\",\n", "                \"url\": \"https://www.sec.gov/Archives/edgar/data/108516/000095017024113225/wor-20240831.htm\"\n", "            }\n", "        }\n", "    ],\n", "    \"summary\": \"WORTHINGTON ENTERPRISES undertook one significant U.S.-based manufacturing initiative by acquiring an 80% stake in Halo on February 1, 2024, for approximately $9.6 million, adding a manufacturing facility in Kentucky. [1] No specific new hiring or job creation commitments were disclosed in the reviewed filings. For actual production output, the company's Consumer Products segment produced 66.6 million units and the Building Products segment produced 14.2 million units for the fiscal year ended May 31, 2024. [1] For the first quarter of fiscal year 2025 (three months ended August 31, 2024), the Consumer Products segment produced 16.2 million units, and the Building Products segment produced 3.1 million units. [4] The majority of these operations and their reported production volumes are U.S.-based. [1]\"\n", "}\n"]}], "source": ["import re\n", "cleaned_json_str = re.sub(r\"^```json\\s*|\\s*```$\", \"\", raw_text.strip())\n", "parsed_data = json.loads(cleaned_json_str)\n", "print(json.dumps(parsed_data, indent=4))"]}, {"cell_type": "markdown", "id": "69cff65a", "metadata": {}, "source": ["## Separate extraction for each company"]}, {"cell_type": "code", "execution_count": null, "id": "c5da06e9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}