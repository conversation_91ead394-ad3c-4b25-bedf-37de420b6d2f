from logger import log
import config

# ---- Entering the Sell Put State ---

def check_entry_conditions(stock_ticker: str, stock_price: float, option_premium: float, option_margin: float) -> bool:
    """
    Checks if the conditions are met to enter a new Sell Put trade.
    """
    intrinsic_value_si = config.INTRINSIC_VALUES.get(stock_ticker)
    if not intrinsic_value_si:
        log.warning(f"No Intrinsic Value (Si) found for {stock_ticker} in config.py. Skipping.")
        return False
        
    if stock_price < intrinsic_value_si:
        log.info(f"{stock_ticker}: Entry condition NOT MET. Stock price {stock_price} is not >=Si {intrinsic_value_si}.")
        return False
    
    if option_margin == 0:
        log.warning(f"{stock_ticker}: Margin is zero, cannot calculate premium percentage.")
        return False
        
    premium_percentage = option_premium / option_margin
    
    if premium_percentage < config.MIN_PREMIUM_PERCENT:
        log.info(f"{stock_ticker}: Entry condition NOT MET. Premium % ({premium_percentage:.2f}%) is < required {config.MIN_PREMIUM_PERCENT}%.")
        return False
        
    log.info(f"{stock_ticker}: All entry conditions MET. (Stock Price: {stock_price} >= Si: {intrinsic_value_si}, Premium: {premium_percentage:.2f}% >= {config.MIN_PREMIUM_PERCENT}%)")
    return True


def calculate_rollover_profit_or_loss(current_option_price: float, original_option_premium_per_share: float, lot_size: int) -> float:
    """
    Calculates the profit or loss to close the current position by buying it back.
    This is based on the formula: ( original_option_premium_per_share - current_option_price ) * lot_size
    
    Args:
        current_option_price (float): The current market price (per share).
        original_option_premium_per_share (float): The premium (per share) received when the option was sold.
        lot_size (int): The number of shares in one lot.

    Returns:
        float: The profit or loss required to close the position.
    """
    # Loss per share is the difference between what we pay now and what we received then.
    profit_or_loss_per_share =  original_option_premium_per_share - current_option_price 
        
    total_profit_or_loss = profit_or_loss_per_share * lot_size
    if total_profit_or_loss > 0:
        log.info(f"Calculated profit to close: ({current_option_price} - {original_option_premium_per_share}) * {lot_size} = ${total_profit_or_loss:.2f}")
    else:
        log.info(f"Calculated loss to close: ({current_option_price} - {original_option_premium_per_share}) * {lot_size} = ${total_profit_or_loss:.2f}")
    return total_profit_or_loss


def decide_rollover(loss_to_close: float, new_premium_receivable: float) -> str:
    """
    Decides whether to roll over an ITM option based on comparing the closing loss
    to the new premium. This function's name and arguments are now clearer.

    Args:
        loss_to_close (float): The calculated loss to buy back the current ITM option.
        new_premium_receivable (float): The total premium expected from selling the next month's option.

    Returns:
        str: "ROLLOVER" or "FULFILL_OBLIGATION".
    """
    log.info(f"Rollover Check: New Premium (${new_premium_receivable:.2f}) vs. Loss to Close (${loss_to_close:.2f})")
    
    if new_premium_receivable > abs(loss_to_close):
        decision = "ROLLOVER"
        log.info(f"Decision: ROLLOVER. The new premium covers the loss.")
    else:
        decision = "FULFILL_OBLIGATION"
        log.info(f"Decision: FULFILL_OBLIGATION. New premium is not sufficient.s")
        
    return decision
