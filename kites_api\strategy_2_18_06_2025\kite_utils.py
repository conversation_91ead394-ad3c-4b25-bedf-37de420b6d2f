from logger import log
import pandas as pd
from kiteconnect import KiteConnect
import config  


# --- Initialization ---

def initialize_kite():
    """
    Initializes and returns a KiteConnect object after authentication.
    """
    try:
        kite = KiteConnect(api_key=config.API_KEY)
        kite.set_access_token(config.ACCESS_TOKEN)
        
        log.info("Kite API connection initialized successfully.")
        
        return kite

    except Exception as e:
        log.error(f"Error initializing Kite API: {e}")
        raise

# --- Instrument Management ---

def fetch_and_save_instruments(kite):
    """
    Fetches all instruments from Kite and saves them to a CSV for faster access later.
    """
    try:
        log.info(f"Fetching all instruments")
        instruments_list = kite.instruments()
        
        instruments_df = pd.DataFrame(instruments_list)
        
        # Save to a CSV file in our data directory
        file_path = f"{config.DATA_DIR}/instruments.csv"
        instruments_df.to_csv(file_path, index=False)
        
        log.info(f"Successfully saved instruments to {file_path}")
        return instruments_df

    except Exception as e:
        log.error(f"Error fetching or saving instruments: {e}")
        return None

# --- Data Fetching Functions ---

def get_quotes(kite, instruments_list):
    try:
        log.info(f"Fetching quotes for {len(instruments_list)} instruments")
        quotes = kite.quote(instruments_list)
        return quotes
    except Exception as e:
        log.error(f"Error fetching quotes: {e}")
        return None

# get margin
def get_margin(kite, orders_list):
    try:
        log.info(f"Fetching margin for {len(orders_list)} orders")
        margins = kite.order_margins(params=orders_list)
        return margins
    except Exception as e:
        log.error(f"Error fetching margin: {e}")
        return None

if __name__ == "__main__":
    kite = initialize_kite()
    instruments_df = fetch_and_save_instruments(kite)
    print(instruments_df.head())