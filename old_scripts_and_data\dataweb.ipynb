{"cells": [{"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [], "source": ["import pandas as pd    \n", "import requests "]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["token = 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDAwOTg5IiwianRpIjoiOWEwZjY5MjgtNDM0MC00Y2NiLWI5NDYtODQ0ODEzZGNiMThkIiwiaXNzIjoiZGF0YXdlYiIsImlhdCI6MTc0MzU4NDI1NiwiZXhwIjoxNzQ0NzkzODU2fQ.SmhfqV5TIl1miKxGgXt3-gdxt6e9UZL5eStpyTc4tiReQyvbxheRpAYcXw-IdodYZ_7xy33sN-TiRnfv9QPLxw'\n", "baseUrl = 'https://datawebws.usitc.gov/dataweb'\n", "headers = {\n", "    \"Content-Type\": \"application/json; charset=utf-8\", \n", "    \"Authorization\": \"Bearer \" + token\n", "}\n", "\n", "requests.packages.urllib3.disable_warnings() "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Set starting query"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is a basic template that can be made more complicated with the various steps "]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [], "source": ["basicQuery = {\n", "    \"savedQueryName\":\"\",\n", "    \"savedQueryDesc\":\"\",\n", "    \"isOwner\":True,\n", "    \"runMonthly\":<PERSON><PERSON><PERSON>,\n", "    \"reportOptions\":{\n", "        \"tradeType\":\"Import\",\n", "        \"classificationSystem\":\"HTS\"\n", "    },\n", "    \"searchOptions\":{\n", "        \"MiscGroup\":{\n", "            \"districts\":{\n", "                \"aggregation\":\"Aggregate District\",\n", "                \"districtGroups\":{\n", "                    \"userGroups\":[]\n", "                },\n", "                \"districts\":[],\n", "                \"districtsExpanded\":\n", "                    [\n", "                        {\n", "                            \"name\":\"All Districts\",\n", "                            \"value\":\"all\"\n", "                        }\n", "                    ],\n", "                \"districtsSelectType\":\"all\"\n", "            },\n", "            \"importPrograms\":{\n", "                \"aggregation\":None,\n", "                \"importPrograms\":[],\n", "                \"programsSelectType\":\"all\"\n", "            },\n", "            \"extImportPrograms\":{\n", "                \"aggregation\":\"Aggregate CSC\",\n", "                \"extImportPrograms\":[],\n", "                \"extImportProgramsExpanded\":[],\n", "                \"programsSelectType\":\"all\"\n", "            },\n", "            \"provisionCodes\":{\n", "                \"aggregation\":\"Aggregate RPCODE\",\n", "                \"provisionCodesSelectType\":\"all\",\n", "                \"rateProvisionCodes\":[],\n", "                \"rateProvisionCodesExpanded\":[]\n", "            }\n", "        },\n", "        \"commodities\":{\n", "            \"aggregation\":\"Aggregate Commodities\",\n", "            \"codeDisplayFormat\":\"YES\",\n", "            \"commodities\":[],\n", "            \"commoditiesExpanded\":[],\n", "            \"commoditiesManual\":\"\",\n", "            \"commodityGroups\":{\n", "                \"systemGroups\":[],\n", "                \"userGroups\":[]\n", "            },\n", "            \"commoditySelectType\":\"all\",\n", "            \"granularity\":\"2\",\n", "            \"groupGranularity\":None,\n", "            \"searchGranularity\":None\n", "        },\n", "        \"componentSettings\":{\n", "            \"dataToReport\":\n", "                [\n", "                    \"CONS_FIR_UNIT_QUANT\"\n", "                ],\n", "            \"scale\":\"1\",\n", "            \"timeframeSelectType\":\"fullYears\",\n", "            \"years\":\n", "                [\n", "                    \"2022\",\"2023\"\n", "                ],\n", "            \"startDate\":None,\n", "            \"endDate\":None,\n", "            \"startMonth\":None,\n", "            \"endMonth\":None,\n", "            \"yearsTimeline\":\"Annual\"\n", "        },\n", "        \"countries\":{\n", "            \"aggregation\":\"Aggregate Countries\",\n", "            \"countries\":[],\n", "            \"countriesExpanded\":\n", "                [\n", "                    {\n", "                        \"name\":\"All Countries\",\n", "                        \"value\":\"all\"\n", "                    }\n", "                ],\n", "            \"countriesSelectType\":\"all\",\n", "            \"countryGroups\":{\n", "                \"systemGroups\":[],\n", "                \"userGroups\":[]\n", "            }\n", "        }\n", "    },\n", "    \"sortingAndDataFormat\":{\n", "        \"DataSort\":{\n", "            \"columnOrder\":[],\n", "            \"fullColumnOrder\":[],\n", "            \"sortOrder\":[]\n", "        },\n", "        \"reportCustomizations\":{\n", "            \"exportCombineTables\":False,\n", "            \"showAllSubtotal\":True,\n", "            \"subtotalRecords\":\"\",\n", "            \"totalRecords\":\"20000\",\n", "            \"exportRawData\":False\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [], "source": ["requestData = basicQuery"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```\n", "Can check all the saved queries with account and use\n", "response = requests.get(baseUrl+\"/api/v2/savedQuery/getAllSavedQueries\", \n", "                        headers=headers, verify=False)\n", "response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Calling the API, first basic query"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [], "source": ["response = requests.post(baseUrl+'/api/v2/report2/runReport', \n", "                         headers=headers, json=requestData, verify=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Restructuring the data in tabular form"]}, {"cell_type": "markdown", "metadata": {}, "source": ["there is also a more dynamic function at the end of this document that extracts the data from the JSON regardless of schema, and it will be used in subsequent examples."]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'dto': {'title': None, 'subtitle': None, 'tables': [{'subheads': [], 'subHeads': ['Imports For Consumption', 'Annual Data'], 'totalRowSize': 34, 'name': 'Imports for Consumption: First Unit of Quantity (In Actual Units of Quantity)', 'query': None, 'tableInfo': {'tabName': 'First Unit of Quantity', 'sortColumn2': '', 'dataToReportDesc': 'First Unit of Quantity', 'sortColumn3': '', 'sortColumn1': '', 'name': 'Imports for Consumption: First Unit of Quantity (In Actual Units of Quantity)', 'ExtendedImportPrograms': 'ALL CSC2', 'countries': '', 'rateProvisionText': 'ALL RPCODES', 'commoditiesDesc': 'All Import Commodities', 'importProgram': 'ALL CSC'}, 'column_groups': [{'name': None, 'label': None, 'columns': [{'fieldName': None, 'label': 'Quantity Description'}]}, {'name': None, 'label': 'In Actual Units of Quantity', 'columns': [{'fieldName': None, 'label': '2022'}, {'fieldName': None, 'label': '2023'}]}], 'row_groups': [{'columnInfo': [{'year': None, 'type': 'units_description', 'columnLabel': 'Quantity Description', 'queryResultLabel': 'units_description', 'columnIndex': 1, 'special': False}, {'year': None, 'type': 'data', 'columnLabel': '2022', 'queryResultLabel': '2022', 'columnIndex': 2, 'special': False}, {'year': None, 'type': 'data', 'columnLabel': '2023', 'queryResultLabel': '2023', 'columnIndex': 3, 'special': False}], 'name': None, 'label': None, 'rows': [], 'rowsNew': [{'rowEntries': [{'value': 'Gigabecquerels'}, {'value': '6,833,938,792', 'suppressed': 0}, {'value': '5,000,226,087', 'suppressed': 0}]}, {'rowEntries': [{'value': 'barrels'}, {'value': '2,850,339,397', 'suppressed': 0}, {'value': '2,899,910,162', 'suppressed': 0}]}, {'rowEntries': [{'value': 'carats'}, {'value': '19,048,159,143', 'suppressed': 0}, {'value': '13,334,122,985', 'suppressed': 0}]}, {'rowEntries': [{'value': 'clean yield kilograms'}, {'value': '2,304,469', 'suppressed': 0}, {'value': '1,755,500', 'suppressed': 0}]}, {'rowEntries': [{'value': 'component grams'}, {'value': '4,793,407,322', 'suppressed': 0}, {'value': '5,325,815,217', 'suppressed': 0}]}, {'rowEntries': [{'value': 'component kilograms'}, {'value': '27,311,393', 'suppressed': 0}, {'value': '24,903,186', 'suppressed': 0}]}, {'rowEntries': [{'value': 'component tons'}, {'value': '7,022,285', 'suppressed': 11}, {'value': '6,010,401', 'suppressed': 12}]}, {'rowEntries': [{'value': 'cubic meters'}, {'value': '83,606,909,486', 'suppressed': 0}, {'value': '80,999,273,629', 'suppressed': 0}]}, {'rowEntries': [{'value': 'doses'}, {'value': '6,090,151', 'suppressed': 0}, {'value': '5,585,238', 'suppressed': 0}]}, {'rowEntries': [{'value': 'dozen pairs'}, {'value': '7,651,298,766', 'suppressed': 0}, {'value': '6,448,084,262', 'suppressed': 0}]}, {'rowEntries': [{'value': 'dozen pieces'}, {'value': '171,313,996', 'suppressed': 0}, {'value': '107,115,973', 'suppressed': 0}]}, {'rowEntries': [{'value': 'dozens'}, {'value': '2,553,188,434', 'suppressed': 0}, {'value': '2,054,147,694', 'suppressed': 0}]}, {'rowEntries': [{'value': 'fiber meters'}, {'value': '23,317,888,037', 'suppressed': 0}, {'value': '16,948,767,193', 'suppressed': 0}]}, {'rowEntries': [{'value': 'grams'}, {'value': '24,221,528,459', 'suppressed': 0}, {'value': '22,396,837,985', 'suppressed': 0}]}, {'rowEntries': [{'value': 'gross'}, {'value': '211,256,642', 'suppressed': 0}, {'value': '158,146,863', 'suppressed': 0}]}, {'rowEntries': [{'value': 'hundred units'}, {'value': '11,223,627', 'suppressed': 0}, {'value': '6,658,149', 'suppressed': 0}]}, {'rowEntries': [{'value': 'kilograms'}, {'value': '211,622,639,744', 'suppressed': 492}, {'value': '199,141,949,717', 'suppressed': 449}]}, {'rowEntries': [{'value': 'linear meters'}, {'value': '369,414', 'suppressed': 0}, {'value': '87,367', 'suppressed': 0}]}, {'rowEntries': [{'value': 'liters'}, {'value': '23,698,272,683', 'suppressed': 0}, {'value': '23,008,097,630', 'suppressed': 0}]}, {'rowEntries': [{'value': 'megawatt hours'}, {'value': '65,406,188', 'suppressed': 0}, {'value': '49,443,342', 'suppressed': 0}]}, {'rowEntries': [{'value': 'meters'}, {'value': '3,576,846,744', 'suppressed': 0}, {'value': '3,166,835,426', 'suppressed': 0}]}, {'rowEntries': [{'value': 'metric tons'}, {'value': '170,183,838', 'suppressed': 4}, {'value': '160,816,974', 'suppressed': 5}]}, {'rowEntries': [{'value': 'no units collected'}, {'value': '872,349', 'suppressed': 0}, {'value': '8,287,741', 'suppressed': 0}]}, {'rowEntries': [{'value': 'number'}, {'value': '641,546,108,865', 'suppressed': 159}, {'value': '545,733,746,478', 'suppressed': 168}]}, {'rowEntries': [{'value': 'pack'}, {'value': '293,381,714', 'suppressed': 0}, {'value': '297,143,986', 'suppressed': 0}]}, {'rowEntries': [{'value': 'pairs'}, {'value': '2,907,849,639', 'suppressed': 0}, {'value': '2,195,377,830', 'suppressed': 0}]}, {'rowEntries': [{'value': 'pieces'}, {'value': '1,875,546,678', 'suppressed': 0}, {'value': '1,450,115,442', 'suppressed': 0}]}, {'rowEntries': [{'value': 'proof liters'}, {'value': '1,102,663,205', 'suppressed': 0}, {'value': '931,015,283', 'suppressed': 0}]}, {'rowEntries': [{'value': 'square centimeters'}, {'value': '183,612,319,165', 'suppressed': 0}, {'value': '169,237,329,049', 'suppressed': 0}]}, {'rowEntries': [{'value': 'square meters'}, {'value': '12,442,814,454', 'suppressed': 0}, {'value': '11,391,803,397', 'suppressed': 0}]}, {'rowEntries': [{'value': 'squares'}, {'value': '1,531,326', 'suppressed': 0}, {'value': '1,092,364', 'suppressed': 0}]}, {'rowEntries': [{'value': 'thousand meters'}, {'value': '165,548', 'suppressed': 0}, {'value': '93,931', 'suppressed': 0}]}, {'rowEntries': [{'value': 'thousand units'}, {'value': '567,347,981', 'suppressed': 0}, {'value': '393,631,775', 'suppressed': 0}]}, {'rowEntries': [{'value': 'thousands of cubic meters'}, {'value': '259,855', 'suppressed': 7}, {'value': '285,984', 'suppressed': 0}]}], 'subtotals': []}], 'total': {'values': [], 'warning': None}, 'label': None, 'tab_name': 'First Unit of Quantity', 'rows': []}], 'errors': [], 'complexityThreshold': False, 'problemSql': None, 'needMoreTime': None, 'isOwner': None, 'htsChanges': {'changes': None, 'validTimeSeriesNums': None, 'validTimeSeries': None, 'isValid': None}, 'hts2Selections': ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99']}}\n"]}], "source": ["print(response.json())"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Quantity Description</th>\n", "      <th>2022</th>\n", "      <th>2023</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Gigabecquerels</td>\n", "      <td>6,833,938,792</td>\n", "      <td>5,000,226,087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>barrels</td>\n", "      <td>2,850,339,397</td>\n", "      <td>2,899,910,162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>carats</td>\n", "      <td>19,048,159,143</td>\n", "      <td>13,334,122,985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>clean yield kilograms</td>\n", "      <td>2,304,469</td>\n", "      <td>1,755,500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>component grams</td>\n", "      <td>4,793,407,322</td>\n", "      <td>5,325,815,217</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Quantity Description            2022            2023\n", "0         Gigabecquerels   6,833,938,792   5,000,226,087\n", "1                barrels   2,850,339,397   2,899,910,162\n", "2                 carats  19,048,159,143  13,334,122,985\n", "3  clean yield kilograms       2,304,469       1,755,500\n", "4        component grams   4,793,407,322   5,325,815,217"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["columns = []\n", "columns.append(response.json()['dto']['tables'][0]['column_groups'][0]['columns'][0]['label'])\n", "columns.append(response.json()['dto']['tables'][0]['column_groups'][1]['columns'][0]['label'])\n", "columns.append(response.json()['dto']['tables'][0]['column_groups'][1]['columns'][1]['label'])\n", "\n", "data = [[x[0]['value'], x[1]['value'], x[2]['value']] for x in [x['rowEntries'] \n", "                        for x in response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew']]]\n", "\n", "df = pd.DataFrame(data, columns = columns)\n", "\n", "df.head() # Shows first 5 rows in table"]}, {"cell_type": "raw", "metadata": {}, "source": ["response is associated quantities with differnt units\n", "Trade Type: Import\n", "\n", "You are requesting import data, meaning the response shows quantities of goods imported into the U.S..\n", "\n", "Classification System: HTS (Harmonized Tariff Schedule)\n", "\n", "The data is categorized using the HTS system, which classifies imported goods based on tariff codes.\n", "\n", "Commodities: All Commodities (or a specific selection)\n", "\n", "Since you did not specify particular commodity codes in \"commodities\": [], the query might be pulling all available commodities unless restricted by another parameter.\n", "\n", "Geographic Region: All U.S. Districts\n", "\n", "The \"districtsSelectType\": \"all\" setting indicates that the data aggregates imports from all U.S. customs districts.\n", "\n", "Country Selection: All Countries\n", "\n", "\"countriesSelectType\": \"all\" means that imports from all countries are included in the results.\n", "\n", "Timeframe: Full Years (2022 & 2023)\n", "\n", "You specified \"years\": [\"2022\", \"2023\"], so the response contains data for these two years.\n", "\n", "Data Metric: Quantities of Goods\n", "\n", "The \"dataToReport\": [\"CONS_FIR_UNIT_QUANT\"] setting means that the response includes imported quantities, rather than monetary values (e.g., dollars)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Updating existing query"]}, {"cell_type": "code", "execution_count": 124, "metadata": {}, "outputs": [], "source": ["tfcsExampleQuery = basicQuery.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Trade Flow Options https://dataweb.usitc.gov/assets/content/query-builder/trade-flow.help.md\n", "1. Import: Imports for Consumption\n", "2. Export: Domestic Exports\n", "3. GenImp: General <PERSON>\n", "4. TotExp: Total Exports\n", "5. Balance: Trade Balance\n", "6. ForeignExp: Foreign Exports\n", "7. ImpExp: Imports and Exports"]}, {"cell_type": "code", "execution_count": 125, "metadata": {}, "outputs": [], "source": ["tfcsExampleQuery['reportOptions']['tradeType'] = 'GenImp'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Classification System Options\n", "1. QUICK: Quick Query\n", "2. HTS: HTS Items\n", "3. SIC: SIC Codes (1989-2001)\n", "4. SITC: SITC Codes\n", "5. NAIC: NAICS Codes (1997-present)\n", "6. EXPERT: Expert Mode\n", "https://dataweb.usitc.gov/assets/content/query-builder/classification-system.help.md"]}, {"cell_type": "code", "execution_count": 126, "metadata": {}, "outputs": [], "source": ["tfcsExampleQuery['reportOptions']['classificationSystem'] = 'NAIC'"]}, {"cell_type": "code", "execution_count": 127, "metadata": {}, "outputs": [], "source": ["response = requests.post(baseUrl+\"/api/v2/report2/runReport\", \n", "                         headers=headers, json=tfcsExampleQuery, verify=False)"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'dto': {'title': None, 'subtitle': None, 'tables': [{'subheads': [], 'subHeads': ['General Imports', 'Annual Data'], 'totalRowSize': 34, 'name': 'General Imports: First Unit of Quantity (In Actual Units of Quantity)', 'query': None, 'tableInfo': {'tabName': 'First Unit of Quantity', 'sortColumn2': '', 'dataToReportDesc': 'First Unit of Quantity', 'sortColumn3': '', 'sortColumn1': '', 'name': 'General Imports: First Unit of Quantity (In Actual Units of Quantity)', 'ExtendedImportPrograms': 'ALL CSC2', 'countries': '', 'rateProvisionText': 'ALL RPCODES', 'commoditiesDesc': 'All Import Commodities', 'importProgram': 'ALL CSC'}, 'column_groups': [{'name': None, 'label': None, 'columns': [{'fieldName': None, 'label': 'Quantity Description'}]}, {'name': None, 'label': 'In Actual Units of Quantity', 'columns': [{'fieldName': None, 'label': '2022'}, {'fieldName': None, 'label': '2023'}]}], 'row_groups': [{'columnInfo': [{'year': None, 'type': 'units_description', 'columnLabel': 'Quantity Description', 'queryResultLabel': 'units_description', 'columnIndex': 1, 'special': False}, {'year': None, 'type': 'data', 'columnLabel': '2022', 'queryResultLabel': '2022', 'columnIndex': 2, 'special': False}, {'year': None, 'type': 'data', 'columnLabel': '2023', 'queryResultLabel': '2023', 'columnIndex': 3, 'special': False}], 'name': None, 'label': None, 'rows': [], 'rowsNew': [{'rowEntries': [{'value': 'Gigabecquerels'}, {'value': '6,833,938,792', 'suppressed': 0}, {'value': '5,000,226,087', 'suppressed': 0}]}, {'rowEntries': [{'value': 'barrels'}, {'value': '2,850,339,397', 'suppressed': 0}, {'value': '2,899,910,162', 'suppressed': 0}]}, {'rowEntries': [{'value': 'carats'}, {'value': '19,048,159,143', 'suppressed': 0}, {'value': '13,334,122,985', 'suppressed': 0}]}, {'rowEntries': [{'value': 'clean yield kilograms'}, {'value': '2,304,469', 'suppressed': 0}, {'value': '1,755,500', 'suppressed': 0}]}, {'rowEntries': [{'value': 'component grams'}, {'value': '4,793,407,322', 'suppressed': 0}, {'value': '5,325,815,217', 'suppressed': 0}]}, {'rowEntries': [{'value': 'component kilograms'}, {'value': '27,311,393', 'suppressed': 0}, {'value': '24,903,186', 'suppressed': 0}]}, {'rowEntries': [{'value': 'component tons'}, {'value': '7,022,285', 'suppressed': 11}, {'value': '6,010,401', 'suppressed': 12}]}, {'rowEntries': [{'value': 'cubic meters'}, {'value': '83,606,909,486', 'suppressed': 0}, {'value': '80,999,273,629', 'suppressed': 0}]}, {'rowEntries': [{'value': 'doses'}, {'value': '6,090,151', 'suppressed': 0}, {'value': '5,585,238', 'suppressed': 0}]}, {'rowEntries': [{'value': 'dozen pairs'}, {'value': '7,651,298,766', 'suppressed': 0}, {'value': '6,448,084,262', 'suppressed': 0}]}, {'rowEntries': [{'value': 'dozen pieces'}, {'value': '171,313,996', 'suppressed': 0}, {'value': '107,115,973', 'suppressed': 0}]}, {'rowEntries': [{'value': 'dozens'}, {'value': '2,553,188,434', 'suppressed': 0}, {'value': '2,054,147,694', 'suppressed': 0}]}, {'rowEntries': [{'value': 'fiber meters'}, {'value': '23,317,888,037', 'suppressed': 0}, {'value': '16,948,767,193', 'suppressed': 0}]}, {'rowEntries': [{'value': 'grams'}, {'value': '24,221,528,459', 'suppressed': 0}, {'value': '22,396,837,985', 'suppressed': 0}]}, {'rowEntries': [{'value': 'gross'}, {'value': '211,256,642', 'suppressed': 0}, {'value': '158,146,863', 'suppressed': 0}]}, {'rowEntries': [{'value': 'hundred units'}, {'value': '11,223,627', 'suppressed': 0}, {'value': '6,658,149', 'suppressed': 0}]}, {'rowEntries': [{'value': 'kilograms'}, {'value': '211,622,639,744', 'suppressed': 492}, {'value': '199,141,949,717', 'suppressed': 449}]}, {'rowEntries': [{'value': 'linear meters'}, {'value': '369,414', 'suppressed': 0}, {'value': '87,367', 'suppressed': 0}]}, {'rowEntries': [{'value': 'liters'}, {'value': '23,698,272,683', 'suppressed': 0}, {'value': '23,008,097,630', 'suppressed': 0}]}, {'rowEntries': [{'value': 'megawatt hours'}, {'value': '65,406,188', 'suppressed': 0}, {'value': '49,443,342', 'suppressed': 0}]}, {'rowEntries': [{'value': 'meters'}, {'value': '3,576,846,744', 'suppressed': 0}, {'value': '3,166,835,426', 'suppressed': 0}]}, {'rowEntries': [{'value': 'metric tons'}, {'value': '170,183,838', 'suppressed': 4}, {'value': '160,816,974', 'suppressed': 5}]}, {'rowEntries': [{'value': 'no units collected'}, {'value': '872,349', 'suppressed': 0}, {'value': '8,287,741', 'suppressed': 0}]}, {'rowEntries': [{'value': 'number'}, {'value': '641,546,108,865', 'suppressed': 159}, {'value': '545,733,746,478', 'suppressed': 168}]}, {'rowEntries': [{'value': 'pack'}, {'value': '293,381,714', 'suppressed': 0}, {'value': '297,143,986', 'suppressed': 0}]}, {'rowEntries': [{'value': 'pairs'}, {'value': '2,907,849,639', 'suppressed': 0}, {'value': '2,195,377,830', 'suppressed': 0}]}, {'rowEntries': [{'value': 'pieces'}, {'value': '1,875,546,678', 'suppressed': 0}, {'value': '1,450,115,442', 'suppressed': 0}]}, {'rowEntries': [{'value': 'proof liters'}, {'value': '1,102,663,205', 'suppressed': 0}, {'value': '931,015,283', 'suppressed': 0}]}, {'rowEntries': [{'value': 'square centimeters'}, {'value': '183,612,319,165', 'suppressed': 0}, {'value': '169,237,329,049', 'suppressed': 0}]}, {'rowEntries': [{'value': 'square meters'}, {'value': '12,442,814,454', 'suppressed': 0}, {'value': '11,391,803,397', 'suppressed': 0}]}, {'rowEntries': [{'value': 'squares'}, {'value': '1,531,326', 'suppressed': 0}, {'value': '1,092,364', 'suppressed': 0}]}, {'rowEntries': [{'value': 'thousand meters'}, {'value': '165,548', 'suppressed': 0}, {'value': '93,931', 'suppressed': 0}]}, {'rowEntries': [{'value': 'thousand units'}, {'value': '567,347,981', 'suppressed': 0}, {'value': '393,631,775', 'suppressed': 0}]}, {'rowEntries': [{'value': 'thousands of cubic meters'}, {'value': '259,855', 'suppressed': 7}, {'value': '285,984', 'suppressed': 0}]}], 'subtotals': []}], 'total': {'values': [], 'warning': None}, 'label': None, 'tab_name': 'First Unit of Quantity', 'rows': []}], 'errors': [], 'complexityThreshold': False, 'problemSql': None, 'needMoreTime': None, 'isOwner': None, 'htsChanges': {'changes': None, 'validTimeSeriesNums': None, 'validTimeSeries': None, 'isValid': None}, 'hts2Selections': []}}\n"]}], "source": ["print(response.json())"]}, {"cell_type": "code", "execution_count": 129, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Quantity Description</th>\n", "      <th>2022</th>\n", "      <th>2023</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Gigabecquerels</td>\n", "      <td>6,833,938,792</td>\n", "      <td>5,000,226,087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>barrels</td>\n", "      <td>2,850,339,397</td>\n", "      <td>2,899,910,162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>carats</td>\n", "      <td>19,048,159,143</td>\n", "      <td>13,334,122,985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>clean yield kilograms</td>\n", "      <td>2,304,469</td>\n", "      <td>1,755,500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>component grams</td>\n", "      <td>4,793,407,322</td>\n", "      <td>5,325,815,217</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Quantity Description            2022            2023\n", "0         Gigabecquerels   6,833,938,792   5,000,226,087\n", "1                barrels   2,850,339,397   2,899,910,162\n", "2                 carats  19,048,159,143  13,334,122,985\n", "3  clean yield kilograms       2,304,469       1,755,500\n", "4        component grams   4,793,407,322   5,325,815,217"]}, "execution_count": 129, "metadata": {}, "output_type": "execute_result"}], "source": ["columns = []\n", "columns.append(response.json()['dto']['tables'][0]['column_groups'][0]['columns'][0]['label'])\n", "columns.append(response.json()['dto']['tables'][0]['column_groups'][1]['columns'][0]['label'])\n", "columns.append(response.json()['dto']['tables'][0]['column_groups'][1]['columns'][1]['label'])\n", "\n", "data = [[x[0]['value'], x[1]['value'], x[2]['value']] for x in [x['rowEntries'] \n", "                        for x in response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew']]]\n", "\n", "df = pd.DataFrame(data, columns = columns)\n", "\n", "df.head() # Shows first 5 rows in table"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Date and years"]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [], "source": ["timeFrameExample = basicQuery.copy()"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["To select entire years, set timeframeSelectType to fullYears and provide the list of years in years. Setting yearsTimeline to Annual will aggregate the data by calendar year."]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [], "source": ["timeFrameExample['searchOptions']['componentSettings']['timeframeSelectType'] = 'fullYears'\n", "timeFrameExample['searchOptions']['componentSettings']['years'] = ['2020', '2021', '2022', '2023']\n", "timeFrameExample['searchOptions']['componentSettings']['yearsTimeline'] = 'Annual'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["yearsTimeline can also be set to Monthly"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [], "source": ["timeFrameExample['searchOptions']['componentSettings']['timeframeSelectType'] = 'fullYears'\n", "timeFrameExample['searchOptions']['componentSettings']['years'] = ['2023']\n", "timeFrameExample['searchOptions']['componentSettings']['yearsTimeline'] = 'Monthly'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["More precise date ranges can be selected by setting startDate and endDate fields in MM/YYYY format. To select the date range using these fields, you will also need to set timeframeSelectType to specificDateRange"]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [], "source": ["timeFrameExample['searchOptions']['componentSettings']['startDate'] = '06/2022'\n", "timeFrameExample['searchOptions']['componentSettings']['endDate'] = '10/2023'\n", "timeFrameExample['searchOptions']['componentSettings']['timeframeSelectType'] = 'specificDateRange'\n", "timeFrameExample['searchOptions']['componentSettings']['yearsTimeline'] = 'Monthly'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Helper functions"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [], "source": ["def getData(dataGroups):\n", "    data = []\n", "    for row in dataGroups:\n", "        rowData = []\n", "        for field in row['rowEntries']:\n", "            rowData.append(field['value'])\n", "        data.append(rowData)\n", "    return data"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [], "source": ["def getColumns(columnGroups, prevCols = None):\n", "    if prevCols is None:\n", "        columns = []\n", "    else:\n", "        columns = prevCols\n", "    for group in columnGroups:\n", "        if isinstance(group, dict) and 'columns' in group.keys():\n", "            getColumns(group['columns'], columns)\n", "        elif isinstance(group, dict) and 'label' in group.keys():\n", "            columns.append(group['label'])\n", "        elif isinstance(group, list):\n", "            getColumns(group, columns)\n", "    return columns"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [], "source": ["def printQueryResults(headers, requestData):\n", "    response = requests.post(baseUrl+\"/api/v2/report2/runReport\", \n", "                            headers=headers, json=requestData, verify=False)\n", "\n", "    columns = getColumns(response.json()['dto']['tables'][0]['column_groups'])\n", "\n", "    data = getData(response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew'])\n", "\n", "    df = pd.DataFrame(data, columns = columns)\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Year</th>\n", "      <th>Quantity Description</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022</td>\n", "      <td>Gigabecquerels</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>681,880,636</td>\n", "      <td>576,467,557</td>\n", "      <td>672,906,314</td>\n", "      <td>682,456,340</td>\n", "      <td>371,103,194</td>\n", "      <td>646,425,276</td>\n", "      <td>299,674,440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023</td>\n", "      <td>Gigabecquerels</td>\n", "      <td>371,609,833</td>\n", "      <td>412,472,574</td>\n", "      <td>435,610,699</td>\n", "      <td>504,368,542</td>\n", "      <td>382,053,703</td>\n", "      <td>387,902,062</td>\n", "      <td>415,131,835</td>\n", "      <td>315,729,893</td>\n", "      <td>422,110,553</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022</td>\n", "      <td>barrels</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>245,414,685</td>\n", "      <td>245,923,686</td>\n", "      <td>250,017,365</td>\n", "      <td>231,127,916</td>\n", "      <td>223,380,278</td>\n", "      <td>234,929,574</td>\n", "      <td>230,203,463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023</td>\n", "      <td>barrels</td>\n", "      <td>244,188,926</td>\n", "      <td>233,315,708</td>\n", "      <td>245,916,506</td>\n", "      <td>230,098,013</td>\n", "      <td>254,223,947</td>\n", "      <td>245,570,523</td>\n", "      <td>236,032,923</td>\n", "      <td>262,266,387</td>\n", "      <td>237,985,482</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022</td>\n", "      <td>carats</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>977,642,714</td>\n", "      <td>1,227,619,428</td>\n", "      <td>2,956,839,850</td>\n", "      <td>1,714,986,864</td>\n", "      <td>2,163,220,447</td>\n", "      <td>1,848,539,345</td>\n", "      <td>2,396,324,024</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Year Quantity Description      January     February        March  \\\n", "0  2022       Gigabecquerels                                          \n", "1  2023       Gigabecquerels  371,609,833  412,472,574  435,610,699   \n", "2  2022              barrels                                          \n", "3  2023              barrels  244,188,926  233,315,708  245,916,506   \n", "4  2022               carats                                          \n", "\n", "         April          May         June           July         August  \\\n", "0                            681,880,636    576,467,557    672,906,314   \n", "1  504,368,542  382,053,703  387,902,062    415,131,835    315,729,893   \n", "2                            245,414,685    245,923,686    250,017,365   \n", "3  230,098,013  254,223,947  245,570,523    236,032,923    262,266,387   \n", "4                            977,642,714  1,227,619,428  2,956,839,850   \n", "\n", "       September        October       November       December  \n", "0    682,456,340    371,103,194    646,425,276    299,674,440  \n", "1    422,110,553                                               \n", "2    231,127,916    223,380,278    234,929,574    230,203,463  \n", "3    237,985,482                                               \n", "4  1,714,986,864  2,163,220,447  1,848,539,345  2,396,324,024  "]}, "execution_count": 137, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, timeFrameExample).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Countries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Countries can be specified in the query in a couple of different ways. The first is to select individual countries manually, but you can also select specific country groups that are managed by the Dataweb application or groups that you saved to your Dataweb user account."]}, {"cell_type": "markdown", "metadata": {}, "source": ["More information for the endpoints used in Step 3 can be found in Swagger:\n", "\n", "Run Report: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Run%20Query/runReport\n", "Get All Countries: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Countries/getAllCountries\n", "Get User's Country Saved Country Groups: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Countries/getAllUserGroupsWithCountries"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [], "source": ["countriesExampleQuery = basicQuery.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["retrieve the list of countries..."]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>iso2</th>\n", "      <th>iso3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Afghanistan - AF - AFG</td>\n", "      <td>5310</td>\n", "      <td>AF</td>\n", "      <td>AFG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Albania - AL - ALB</td>\n", "      <td>4810</td>\n", "      <td>AL</td>\n", "      <td>ALB</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Algeria - DZ - DZA</td>\n", "      <td>7210</td>\n", "      <td>DZ</td>\n", "      <td>DZA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>American Samoa - AS - ASM</td>\n", "      <td>9510</td>\n", "      <td>AS</td>\n", "      <td>ASM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Andorra - AD - AND</td>\n", "      <td>4271</td>\n", "      <td>AD</td>\n", "      <td>AND</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        name value iso2 iso3\n", "0     Afghanistan - AF - AFG  5310   AF  AFG\n", "1         Albania - AL - ALB  4810   AL  ALB\n", "2         Algeria - DZ - DZA  7210   DZ  DZA\n", "3  American Samoa - AS - ASM  9510   AS  ASM\n", "4         Andorra - AD - AND  4271   AD  AND"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["response = requests.get(baseUrl+\"/api/v2/country/getAllCountries\", \n", "                         headers=headers, verify=False)\n", "df = pd.<PERSON><PERSON><PERSON>e(response.json()['options'])\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["select those countries we are interested in "]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [], "source": ["countries = []\n", "countries.append(response.json()['options'][1])\n", "countries.append(response.json()['options'][4])"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'Albania - AL - ALB', 'value': '4810', 'iso2': 'AL', 'iso3': 'ALB'},\n", " {'name': 'Andorra - AD - AND', 'value': '4271', 'iso2': 'AD', 'iso3': 'AND'}]"]}, "execution_count": 141, "metadata": {}, "output_type": "execute_result"}], "source": ["countries"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [], "source": ["countriesExampleQuery['searchOptions']['countries']['countries'] = [x['value'] for x in countries]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```\n", "Can also load a country group that you saved under your Dataweb user account.\n", "response = requests.get(baseUrl+\"/api/v2/country/getAllUserGroupsWithCountries\", \n", "                         headers=headers, verify=False)\n", "response.json()\n", "countryGroups = []\n", "countryGroups.append(response.json['options'][0])\n", "countryGroups.append(response.json['options'][1])\n", "countryGroups"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Year</th>\n", "      <th>Quantity Description</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022</td>\n", "      <td>Gigabecquerels</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>681,880,636</td>\n", "      <td>576,467,557</td>\n", "      <td>672,906,314</td>\n", "      <td>682,456,340</td>\n", "      <td>371,103,194</td>\n", "      <td>646,425,276</td>\n", "      <td>299,674,440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023</td>\n", "      <td>Gigabecquerels</td>\n", "      <td>371,609,833</td>\n", "      <td>412,472,574</td>\n", "      <td>435,610,699</td>\n", "      <td>504,368,542</td>\n", "      <td>382,053,703</td>\n", "      <td>387,902,062</td>\n", "      <td>415,131,835</td>\n", "      <td>315,729,893</td>\n", "      <td>422,110,553</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022</td>\n", "      <td>barrels</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>245,414,685</td>\n", "      <td>245,923,686</td>\n", "      <td>250,017,365</td>\n", "      <td>231,127,916</td>\n", "      <td>223,380,278</td>\n", "      <td>234,929,574</td>\n", "      <td>230,203,463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023</td>\n", "      <td>barrels</td>\n", "      <td>244,188,926</td>\n", "      <td>233,315,708</td>\n", "      <td>245,916,506</td>\n", "      <td>230,098,013</td>\n", "      <td>254,223,947</td>\n", "      <td>245,570,523</td>\n", "      <td>236,032,923</td>\n", "      <td>262,266,387</td>\n", "      <td>237,985,482</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022</td>\n", "      <td>carats</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>977,642,714</td>\n", "      <td>1,227,619,428</td>\n", "      <td>2,956,839,850</td>\n", "      <td>1,714,986,864</td>\n", "      <td>2,163,220,447</td>\n", "      <td>1,848,539,345</td>\n", "      <td>2,396,324,024</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Year Quantity Description      January     February        March  \\\n", "0  2022       Gigabecquerels                                          \n", "1  2023       Gigabecquerels  371,609,833  412,472,574  435,610,699   \n", "2  2022              barrels                                          \n", "3  2023              barrels  244,188,926  233,315,708  245,916,506   \n", "4  2022               carats                                          \n", "\n", "         April          May         June           July         August  \\\n", "0                            681,880,636    576,467,557    672,906,314   \n", "1  504,368,542  382,053,703  387,902,062    415,131,835    315,729,893   \n", "2                            245,414,685    245,923,686    250,017,365   \n", "3  230,098,013  254,223,947  245,570,523    236,032,923    262,266,387   \n", "4                            977,642,714  1,227,619,428  2,956,839,850   \n", "\n", "       September        October       November       December  \n", "0    682,456,340    371,103,194    646,425,276    299,674,440  \n", "1    422,110,553                                               \n", "2    231,127,916    223,380,278    234,929,574    230,203,463  \n", "3    237,985,482                                               \n", "4  1,714,986,864  2,163,220,447  1,848,539,345  2,396,324,024  "]}, "execution_count": 143, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, countriesExampleQuery).head()"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'savedQueryName': '',\n", " 'savedQueryDesc': '',\n", " 'isOwner': True,\n", " 'runMonthly': <PERSON><PERSON><PERSON>,\n", " 'reportOptions': {'tradeType': 'GenImp', 'classificationSystem': 'NAIC'},\n", " 'searchOptions': {'MiscGroup': {'districts': {'aggregation': 'Aggregate District',\n", "    'districtGroups': {'userGroups': []},\n", "    'districts': [],\n", "    'districtsExpanded': [{'name': 'All Districts', 'value': 'all'}],\n", "    'districtsSelectType': 'all'},\n", "   'importPrograms': {'aggregation': None,\n", "    'importPrograms': [],\n", "    'programsSelectType': 'all'},\n", "   'extImportPrograms': {'aggregation': 'Aggregate CSC',\n", "    'extImportPrograms': [],\n", "    'extImportProgramsExpanded': [],\n", "    'programsSelectType': 'all'},\n", "   'provisionCodes': {'aggregation': 'Aggregate RPCODE',\n", "    'provisionCodesSelectType': 'all',\n", "    'rateProvisionCodes': [],\n", "    'rateProvisionCodesExpanded': []}},\n", "  'commodities': {'aggregation': 'Aggregate Commodities',\n", "   'codeDisplayFormat': 'YES',\n", "   'commodities': [],\n", "   'commoditiesExpanded': [],\n", "   'commoditiesManual': '',\n", "   'commodityGroups': {'systemGroups': [], 'userGroups': []},\n", "   'commoditySelectType': 'all',\n", "   'granularity': '2',\n", "   'groupGranularity': None,\n", "   'searchGranularity': None},\n", "  'componentSettings': {'dataToReport': ['CONS_FIR_UNIT_QUANT'],\n", "   'scale': '1',\n", "   'timeframeSelectType': 'specificDateRange',\n", "   'years': ['2023'],\n", "   'startDate': '06/2022',\n", "   'endDate': '10/2023',\n", "   'startMonth': None,\n", "   'endMonth': None,\n", "   'yearsTimeline': 'Monthly'},\n", "  'countries': {'aggregation': 'Aggregate Countries',\n", "   'countries': ['4810', '4271'],\n", "   'countriesExpanded': [{'name': 'All Countries', 'value': 'all'}],\n", "   'countriesSelectType': 'all',\n", "   'countryGroups': {'systemGroups': [], 'userGroups': []}}},\n", " 'sortingAndDataFormat': {'DataSort': {'columnOrder': [],\n", "   'fullColumnOrder': [],\n", "   'sortOrder': []},\n", "  'reportCustomizations': {'exportCombineTables': False,\n", "   'showAllSubtotal': True,\n", "   'subtotalRecords': '',\n", "   'totalRecords': '20000',\n", "   'exportRawData': False}}}"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["countriesExampleQuery"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>Year</th>\n", "      <th>Quantity Description</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>carats</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>349,878</td>\n", "      <td>68</td>\n", "      <td>5,381,366</td>\n", "      <td>3,800,048</td>\n", "      <td>1,645</td>\n", "      <td>1,224,871</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>carats</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>79,635</td>\n", "      <td>0</td>\n", "      <td>1,857</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2,645,006</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>dozens</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>49</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>dozens</td>\n", "      <td>171</td>\n", "      <td>43</td>\n", "      <td>0</td>\n", "      <td>237</td>\n", "      <td>101</td>\n", "      <td>101</td>\n", "      <td>13</td>\n", "      <td>3</td>\n", "      <td>17</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>grams</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>grams</td>\n", "      <td>210</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>705</td>\n", "      <td>0</td>\n", "      <td>56</td>\n", "      <td>1,700</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>kilograms</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>261,313</td>\n", "      <td>111,635</td>\n", "      <td>191,749</td>\n", "      <td>110,772</td>\n", "      <td>73,243</td>\n", "      <td>643,708</td>\n", "      <td>250,141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>kilograms</td>\n", "      <td>148,201</td>\n", "      <td>347,295</td>\n", "      <td>463,211</td>\n", "      <td>155,083</td>\n", "      <td>480,789</td>\n", "      <td>262,012</td>\n", "      <td>83,387</td>\n", "      <td>153,442</td>\n", "      <td>100,681</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>liters</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40,354</td>\n", "      <td>0</td>\n", "      <td>17,670</td>\n", "      <td>10,632</td>\n", "      <td>3,468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>liters</td>\n", "      <td>9,600</td>\n", "      <td>44,190</td>\n", "      <td>105,184</td>\n", "      <td>103,612</td>\n", "      <td>106,362</td>\n", "      <td>5,569</td>\n", "      <td>18,316</td>\n", "      <td>2,459</td>\n", "      <td>37,178</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>metric tons</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>no units collected</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>no units collected</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>number</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2,846</td>\n", "      <td>2,742</td>\n", "      <td>2,567</td>\n", "      <td>1,288</td>\n", "      <td>0</td>\n", "      <td>983</td>\n", "      <td>6,510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>number</td>\n", "      <td>132</td>\n", "      <td>4,700</td>\n", "      <td>8,778</td>\n", "      <td>1,347</td>\n", "      <td>10,467</td>\n", "      <td>1,510</td>\n", "      <td>23</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>pairs</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>pairs</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>51</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Afghanistan</td>\n", "      <td>2022</td>\n", "      <td>square meters</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>4,703</td>\n", "      <td>4,417</td>\n", "      <td>6,054</td>\n", "      <td>21,196</td>\n", "      <td>3,608</td>\n", "      <td>6,227</td>\n", "      <td>8,075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>square meters</td>\n", "      <td>3,812</td>\n", "      <td>2,583</td>\n", "      <td>9,278</td>\n", "      <td>7,603</td>\n", "      <td>5,760</td>\n", "      <td>6,171</td>\n", "      <td>2,540</td>\n", "      <td>31,835</td>\n", "      <td>11,841</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>thousand units</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Country  Year Quantity Description  January February    March  \\\n", "0   Afghanistan  2022               carats                              \n", "1   Afghanistan  2023               carats        3        0   79,635   \n", "2   Afghanistan  2022               dozens                              \n", "3   Afghanistan  2023               dozens      171       43        0   \n", "4   Afghanistan  2022                grams                              \n", "5   Afghanistan  2023                grams      210        4        0   \n", "6   Afghanistan  2022            kilograms                              \n", "7   Afghanistan  2023            kilograms  148,201  347,295  463,211   \n", "8   Afghanistan  2022               liters                              \n", "9   Afghanistan  2023               liters    9,600   44,190  105,184   \n", "10  Afghanistan  2023          metric tons        0        0        4   \n", "11  Afghanistan  2022   no units collected                              \n", "12  Afghanistan  2023   no units collected        0        0        0   \n", "13  Afghanistan  2022               number                              \n", "14  Afghanistan  2023               number      132    4,700    8,778   \n", "15  Afghanistan  2022                pairs                              \n", "16  Afghanistan  2023                pairs        1        0        0   \n", "17  Afghanistan  2022        square meters                              \n", "18  Afghanistan  2023        square meters    3,812    2,583    9,278   \n", "19  Afghanistan  2023       thousand units        0        0        0   \n", "\n", "      April      May     June     July     August  September October  \\\n", "0                     349,878       68  5,381,366  3,800,048   1,645   \n", "1         0    1,857        0        0  2,645,006          0           \n", "2                           2        1          1         49       1   \n", "3       237      101      101       13          3         17           \n", "4                           0        0          0          0       0   \n", "5        11        0      705        0         56      1,700           \n", "6                     261,313  111,635    191,749    110,772  73,243   \n", "7   155,083  480,789  262,012   83,387    153,442    100,681           \n", "8                           0        0     40,354          0  17,670   \n", "9   103,612  106,362    5,569   18,316      2,459     37,178           \n", "10        0        0        2        0          0          2           \n", "11                          0        0          0          0       0   \n", "12        0        0        0        0          0          0           \n", "13                      2,846    2,742      2,567      1,288       0   \n", "14    1,347   10,467    1,510       23          2          0           \n", "15                          0        0          6          0       0   \n", "16        0        1        0       51          0          0           \n", "17                      4,703    4,417      6,054     21,196   3,608   \n", "18    7,603    5,760    6,171    2,540     31,835     11,841           \n", "19        0        0        0        0          0          0           \n", "\n", "     November December  \n", "0   1,224,871        3  \n", "1                       \n", "2           9      301  \n", "3                       \n", "4          32        8  \n", "5                       \n", "6     643,708  250,141  \n", "7                       \n", "8      10,632    3,468  \n", "9                       \n", "10                      \n", "11          0        0  \n", "12                      \n", "13        983    6,510  \n", "14                      \n", "15          0        0  \n", "16                      \n", "17      6,227    8,075  \n", "18                      \n", "19                      "]}, "execution_count": 145, "metadata": {}, "output_type": "execute_result"}], "source": ["countriesExampleQuery['searchOptions']['countries']['aggregation']='Break Out Countries'\n", "\n", "printQueryResults(headers, countriesExampleQuery).head(20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Commodities"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [], "source": ["commoditiesExampleQuery = basicQuery.copy()"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [], "source": ["options = {'tradeType': \"Import\", 'classificationSystem': \"HTS\", 'timeframesSelectedTab': \"fullYears\"}"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'options': [], 'settings': None}"]}, "execution_count": 148, "metadata": {}, "output_type": "execute_result"}], "source": ["response = requests.post(baseUrl+\"/api/v2/commodity/getAllUserGroupsWithCommodities\", \n", "                         headers=headers, json=options, verify=False)\n", "response.json()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Step 5: Programs\n", "1. Programs are not available when you select Domestic Exports, Total Exports, General Imports, Foreign Exports, or Trade Balance under Trade Flow in Step 1.\n", "\n", "2. To select Programs, please select Imports For Consumption under Trade Flow in Step 1.\n", "\n", "3. To start, let's reset the query to the basic.\n", "\n", "4. More information for the endpoints used in Step 5 can be found in Swagger:\n", "\n", "(a) Run Report: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Run%20Query/runReport <br>\n", "(b) Get Program List: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Query%20Info/getImportPrograms"]}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [], "source": ["programsExampleQuery = basicQuery.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Dataweb manages a list of programs, the list of which can be retrieved through the getImportPrograms endpoint.."]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [], "source": ["response = requests.post(baseUrl+\"/api/v2/query/getImportPrograms\", \n", "                         json={\"tradeType\":\"Import\"}, headers=headers, verify=False)"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>has<PERSON><PERSON><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00 - No program claimed</td>\n", "      <td>00</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01 - Unknown country</td>\n", "      <td>01</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2 - 807 Commodity</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5 - Folklore Merchandise</td>\n", "      <td>5</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A - GSP (excluding GSP for LDBC only)</td>\n", "      <td>A</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>A+ - GSP for LDBC countries only</td>\n", "      <td>A+</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>AU - Australia-U.S. Free Trade Agreement</td>\n", "      <td>AU</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>B - Auto Pact</td>\n", "      <td>B</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>BH - Bahrain-U.S.</td>\n", "      <td>BH</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C - Civil Aircraft</td>\n", "      <td>C</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>CA - NAFTA-CA</td>\n", "      <td>CA</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>CL - Chile-U.S.</td>\n", "      <td>CL</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CO - Colombia-U.S.</td>\n", "      <td>CO</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>D - AGOA (excluding GSP)</td>\n", "      <td>D</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>E - Caribbean (CBI)</td>\n", "      <td>E</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>HH - Haiti-specific preferences</td>\n", "      <td>HH</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>IL - Israel- U.S.</td>\n", "      <td>IL</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>J - Andean Act (excluding ATPDEA)</td>\n", "      <td>J</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>J* - Andean Act (excluding ATPDEA)</td>\n", "      <td>J*</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>J+ - ATPDEA</td>\n", "      <td>J+</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>JO - Jordan-U.S.</td>\n", "      <td>JO</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>JP - Japan-U.S.</td>\n", "      <td>JP</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>K - Pharmaceuticals</td>\n", "      <td>K</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>KR - Korea-U.S.</td>\n", "      <td>KR</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td><PERSON> <PERSON> <PERSON><PERSON></td>\n", "      <td>L</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>MA - Morocco-U.S.</td>\n", "      <td>MA</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MX - NAFTA-MX</td>\n", "      <td>MX</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>N - W. Bank, Gaza &amp; Qualifying Ind Zones</td>\n", "      <td>N</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>NP - Nepal Preference</td>\n", "      <td>NP</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>OM - Oman-U.S.</td>\n", "      <td>OM</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>P - DR-CAFTA (excluding DR-CAFTA Plus)</td>\n", "      <td>P</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>P+ - DR-CAFTA Plus</td>\n", "      <td>P+</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>PA - Panama-U.S.</td>\n", "      <td>PA</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>PE - Peru-U.S.</td>\n", "      <td>PE</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>R - CBTPA</td>\n", "      <td>R</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>S - Mexico-Canada-U.S.</td>\n", "      <td>S</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>S+ - Mexico-Canada-U.S.</td>\n", "      <td>S+</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>SG - Singapore-U.S.</td>\n", "      <td>SG</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>W - Puerto Rico-CBI</td>\n", "      <td>W</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>X - Canada-U.S.(pre 94)</td>\n", "      <td>X</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>Y - Possession preferences</td>\n", "      <td>Y</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>Z - Marshall Islands</td>\n", "      <td>Z</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                        name value hasChildren\n", "0                    00 - No program claimed    00        None\n", "1                       01 - Unknown country    01        None\n", "2                          2 - 807 Commodity     2        None\n", "3                   5 - Folklore Merchandise     5        None\n", "4      A - GSP (excluding GSP for LDBC only)     A        None\n", "5           A+ - GSP for LDBC countries only    A+        None\n", "6   AU - Australia-U.S. Free Trade Agreement    AU        None\n", "7                              B - Auto Pact     B        None\n", "8                          BH - Bahrain-U.S.    BH        None\n", "9                         C - Civil Aircraft     C        None\n", "10                             CA - NAFTA-CA    CA        None\n", "11                           CL - Chile-U.S.    CL        None\n", "12                        CO - Colombia-U.S.    CO        None\n", "13                  D - AGOA (excluding GSP)     D        None\n", "14                       E - Caribbean (CBI)     E        None\n", "15           HH - Haiti-specific preferences    HH        None\n", "16                         IL - Israel- U.S.    IL        None\n", "17         J - Andean Act (excluding ATPDEA)     J        None\n", "18        J* - Andean Act (excluding ATPDEA)    J*        None\n", "19                               J+ - ATPDEA    J+        None\n", "20                          JO - Jordan-U.S.    JO        None\n", "21                           JP - Japan-U.S.    JP        None\n", "22                       K - Pharmaceuticals     K        None\n", "23                           KR - Korea-U.S.    KR        None\n", "24                                  L - Dyes     L        None\n", "25                         MA - Morocco-U.S.    MA        None\n", "26                             MX - NAFTA-MX    MX        None\n", "27  N - W. Bank, Gaza & Qualifying Ind Zones     N        None\n", "28                     NP - Nepal Preference    NP        None\n", "29                            OM - Oman-U.S.    OM        None\n", "30    P - DR-CAFTA (excluding DR-CAFTA Plus)     P        None\n", "31                        P+ - DR-CAFTA Plus    P+        None\n", "32                          PA - Panama-U.S.    PA        None\n", "33                            PE - Peru-U.S.    PE        None\n", "34                                 R - CBTPA     R        None\n", "35                    S - Mexico-Canada-U.S.     S        None\n", "36                   S+ - Mexico-Canada-U.S.    S+        None\n", "37                       SG - Singapore-U.S.    SG        None\n", "38                       W - Puerto Rico-CBI     W        None\n", "39                   X - Canada-U.S.(pre 94)     X        None\n", "40                Y - Possession preferences     Y        None\n", "41                      Z - Marshall Islands     Z        None"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.<PERSON><PERSON><PERSON>e(response.json()['options'])\n", "df.head(100)"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["The codes you've retrieved from the API represent various **U.S. trade program indicators** used to classify imports under specific trade agreements or provisions. These indicators help determine eligibility for special tariff rates or exemptions. Below is an explanation of each code:\n", "\n", "1. **00 - No program claimed**:\n", "   - Indicates that the import **does not** fall under any special trade program or agreement.\n", "\n", "2. **01 - Unknown country**:\n", "   - Used when the **country of origin** for the imported goods is **unknown**.\n", "\n", "3. **2 - 807 Commodity**:\n", "   - Refers to imports under **HTSUS subheading 9802.00.80**, previously known as item 807. This provision allows U.S. goods that were exported for processing or assembly abroad to be returned with duty assessed only on the **value added** during foreign processing, not on the U.S. content.\n", "\n", "4. **5 - Folklore Merchandise**:\n", "   - Pertains to imports of **folklore handicrafts** that may qualify for **duty-free treatment** under certain trade agreements, provided they meet specific criteria.\n", "\n", "5. **A - GSP (excluding GSP for LDBC only)**:\n", "   - Stands for the **Generalized System of Preferences**, a program that offers **duty-free treatment** to eligible products imported from designated **developing countries**.\n", "\n", "6. **A+ - GSP for LDBC countries only**:\n", "   - A subset of the GSP program, this applies exclusively to **Least Developed Beneficiary Countries (LDBCs)**, granting them duty-free benefits for a broader range of products.\n", "\n", "7. **AU - Australia-U.S. Free Trade Agreement**:\n", "   - Denotes imports qualifying under the **Australia-United States Free Trade Agreement (AUSFTA)**, which provides for **reduced or eliminated tariffs** on goods traded between the U.S. and Australia.\n", "\n", "8. **B - Auto Pact**:\n", "   - Refers to the **Automotive Products Trade Act (APTA)**, an agreement between the U.S. and Canada that allowed for **duty-free trade** of certain automotive products. Note: This has been largely superseded by subsequent trade agreements.\n", "\n", "9. **BH - Bahrain-U.S. Free Trade Agreement**:\n", "   - Indicates imports eligible under the **Bahrain-United States Free Trade Agreement (BHFTA)**, facilitating **duty-free or reduced-duty** treatment for qualifying goods between the two countries.\n", "\n", "10. **C - Civil Aircraft**:\n", "    - Pertains to the **Agreement on Trade in Civil Aircraft**, under which imports of **civil aircraft and related parts** may enter the U.S. **duty-free**.\n", "\n", "These codes are essential for importers and customs officials to correctly apply tariff treatments based on the specific trade agreements or provisions applicable to the imported goods. "]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [], "source": ["programs = []\n", "programs.append(response.json()['options'][9])"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'C - Civil Aircraft', 'value': 'C', 'hasChildren': None}]"]}, "execution_count": 153, "metadata": {}, "output_type": "execute_result"}], "source": ["programs"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [], "source": ["programsExampleQuery['searchOptions']['MiscGroup']['extImportPrograms']['aggregation'] = 'Aggregate CSC' # or 'Break Out CSC'\n", "programsExampleQuery['searchOptions']['MiscGroup']['extImportPrograms']['extImportPrograms'] = [x['value'] for x in programs]\n", "programsExampleQuery['searchOptions']['MiscGroup']['extImportPrograms']['extImportProgramsExpanded'] = []\n", "programsExampleQuery['searchOptions']['MiscGroup']['extImportPrograms']['programsSelectType'] = 'list'"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>Year</th>\n", "      <th>Quantity Description</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Afghanistan</td>\n", "      <td>2023</td>\n", "      <td>kilograms</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Albania</td>\n", "      <td>2022</td>\n", "      <td>number</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Algeria</td>\n", "      <td>2022</td>\n", "      <td>kilograms</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Algeria</td>\n", "      <td>2023</td>\n", "      <td>kilograms</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Algeria</td>\n", "      <td>2022</td>\n", "      <td>number</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Country  Year Quantity Description January February March April May  \\\n", "0  Afghanistan  2023            kilograms       0        4     0     0   0   \n", "1      Albania  2022               number                                    \n", "2      Algeria  2022            kilograms                                    \n", "3      Algeria  2023            kilograms       0        0     0     1   0   \n", "4      Algeria  2022               number                                    \n", "\n", "  June July August September October November December  \n", "0    0    0      0         0                            \n", "1    0    0      1         0       0        0        0  \n", "2    2    0      0         0       0        1        0  \n", "3    0    0      0         0                            \n", "4    0    0      3         5       0        0        5  "]}, "execution_count": 155, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, programsExampleQuery).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Rate provision code"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. Rate Provision codes can also be added to queries. A list of RP codes can be retrieved using getRPCodesList. An example follows, but first we'll reset our query back to the basic query provided at the end of this document.\n", "\n", "2. More information for the endpoints used in Step 6 can be found in Swagger:\n", "\n", "Run Report: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Run%20Query/runReport <br>\n", "Get Rate Provision Code List: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Query%20Info/getRPCodesList"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [], "source": ["rateProvisionExample = basicQuery.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's get a list of saved RPCodes ..."]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "      <th>has<PERSON><PERSON><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00 - Free into bonded warehouse or FTZ</td>\n", "      <td>00</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10 - Free under HS Chapters 1-98</td>\n", "      <td>10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11 - Entered into U.S. Virgin Islands</td>\n", "      <td>11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>13 - Free, processed for export</td>\n", "      <td>13</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>14 - Free, items for vessels and aircraft</td>\n", "      <td>14</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                        name value hasChildren\n", "0     00 - Free into bonded warehouse or FTZ    00        None\n", "1           10 - Free under HS Chapters 1-98    10        None\n", "2      11 - Entered into U.S. Virgin Islands    11        None\n", "3            13 - Free, processed for export    13        None\n", "4  14 - Free, items for vessels and aircraft    14        None"]}, "execution_count": 157, "metadata": {}, "output_type": "execute_result"}], "source": ["response = requests.post(baseUrl+\"/api/v2/query/getRPCodesList\", \n", "                         headers=headers, json={\"tradeType\":\"Import\"}, verify=False)\n", "df = pd.<PERSON><PERSON><PERSON>e(response.json()['options'])\n", "df.head()"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [], "source": ["rpCodes = []\n", "rpCodes.append(response.json()['options'][1])"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [], "source": ["rateProvisionExample['searchOptions']['MiscGroup']['provisionCodes']['provisionCodesSelectType'] = 'list'\n", "rateProvisionExample['searchOptions']['MiscGroup']['provisionCodes']['rateProvisionCodes'] = [x['value'] for x in rpCodes]\n", "rateProvisionExample['searchOptions']['MiscGroup']['provisionCodes']['rateProvisionCodesExpanded'] = rpCodes"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>Year</th>\n", "      <th>Quantity Description</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [Country, Year, Quantity Description, January, February, March, April, May, June, July, August, September, October, November, December]\n", "Index: []"]}, "execution_count": 160, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, rateProvisionExample).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Step 7: Districts\n", "1. Districts can be added in one of two ways:\n", "\n", "2. As a user-saved group\n", "    Individually by ID\n", "    An example of each option can be found below, but first, let's reset our query back to the basic query example.\n", "\n", "3. More information for the endpoints used in Step 7 can be found in Swagger:\n", "\n", "4. Run Report: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Run%20Query/runReport\n", "    Get User-Saved District Groups: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Districts/getAllUserGroupsWithDistricts\n", "    Get List of All Districts: https://datawebws.usitc.gov/dataweb/swagger-ui/index.html#/Districts/getAllDistricts"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [], "source": ["districtsExample = basicQuery.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": [" a full list of districts can be downloaded using getAllDistricts"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [], "source": ["response = requests.get(baseUrl+\"/api/v2/district/getAllDistricts\", \n", "                        headers=headers, verify=False)"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'An<PERSON><PERSON>, AK', 'value': '31', 'hasChildren': None},\n", " {'name': 'Baltimore, MD', 'value': '13', 'hasChildren': None}]"]}, "execution_count": 163, "metadata": {}, "output_type": "execute_result"}], "source": ["districts = []\n", "districts.append(response.json()['options'][0])\n", "districts.append(response.json()['options'][1])\n", "districts"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [], "source": ["districtsExample['searchOptions']['MiscGroup']['districts']['districts'] = [x['value'] for x in districts]\n", "districtsExample['searchOptions']['MiscGroup']['districts']['districtsExpanded'] = districts\n", "districtsExample['searchOptions']['MiscGroup']['districts']['districtsSelectType'] = 'list'"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>Year</th>\n", "      <th>Quantity Description</th>\n", "      <th>January</th>\n", "      <th>February</th>\n", "      <th>March</th>\n", "      <th>April</th>\n", "      <th>May</th>\n", "      <th>June</th>\n", "      <th>July</th>\n", "      <th>August</th>\n", "      <th>September</th>\n", "      <th>October</th>\n", "      <th>November</th>\n", "      <th>December</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [Country, Year, Quantity Description, January, February, March, April, May, June, July, August, September, October, November, December]\n", "Index: []"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, districtsExample).head()"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}