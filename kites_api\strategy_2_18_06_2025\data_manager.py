import pandas as pd
import os
from logger import log
import config

# --- Column Definitions ---

ACTIVE_OPTIONS_COLS = [
    'instrument_token','tradingsymbol', 'name', 'expiry', 'strike', 
    'lot_size', 'instrument_type', 'exchange', 'trading_price_of_stock_at_option_sell',
    'Intrinsic_value', 'price_of_option_at_option_sell', 'premium_received', 
    'total_margin', 'premium_to_margin_percentage', 'current_stock_price', 
    'current_option_price', 'premium_paid_if_today_squareoff', 'current_profit_or_loss_on_option'
]

STOCK_SHEET_COLS = [
    'name', 'quantity', 'purchase_price', 'intrinsic_value', 'total_amount_paid', 
    'current_price'
]

TRANSACTION_SHEET_COLS = [
    'transaction_date',       
    'transaction_type',       
    'name',                   
    'tradingsymbol',          
    'instrument_token',       
    'expiry',                 
    'strike',                 
    'instrument_type',      
    'lot_size',
    'option_price_at_sell',
    'premium_received',
    'total_margin', 
    'option_price_at_exp',
    'premium_paid',           
    'amount_paid_buy_stock',
    'amount_received_sell_stock'     
]

# --- Helper Function for Loading/Creating Sheets ---

def _load_or_create_sheet(file_path, columns):
    """
    Loads an Excel sheet if it exists, otherwise creates it with specified columns.
    This is a private helper function (indicated by the underscore).
    """
    if os.path.exists(file_path):
        log.info(f"Loading existing sheet: {os.path.basename(file_path)}")
        return pd.read_excel(file_path)
    else:
        log.warning(f"{os.path.basename(file_path)} not found. Creating a new empty sheet.")
        df = pd.DataFrame(columns=columns)
        # Using .to_excel to ensure format is consistent
        df.to_excel(file_path, index=False)
        return df

# --- Public Functions to Interact with Data Sheets ---

def get_active_options():
    """Reads the active options sheet."""
    return _load_or_create_sheet(config.ACTIVE_OPTIONS_FILE, ACTIVE_OPTIONS_COLS)

def get_stock_holdings():
    """Reads the stock holdings sheet."""
    return _load_or_create_sheet(config.STOCK_SHEET_FILE, STOCK_SHEET_COLS)

def save_active_options(df):
    """Saves the DataFrame to the active options Excel file."""
    log.info("Saving updated data to active_options.xlsx")
    df.to_excel(config.ACTIVE_OPTIONS_FILE, index=False)

def save_stock_holdings(df):
    """Saves the DataFrame to the stock holdings Excel file."""
    log.info("Saving updated data to stock_sheet.xlsx")
    df.to_excel(config.STOCK_SHEET_FILE, index=False)

def log_transaction(transaction_data: dict):
    """
    Logs a new transaction by appending it to the transaction sheet.
    
    Args:
        transaction_data (dict): A dictionary where keys match the transaction sheet columns.
    """
    try:
        log.info(f"Logging new transaction: {transaction_data.get('tradingsymbol', 'N/A')}")
        transactions_df = _load_or_create_sheet(config.TRANSACTION_SHEET_FILE, TRANSACTION_SHEET_COLS)
        
        new_transaction_df = pd.DataFrame([transaction_data])
        
        updated_transactions_df = pd.concat([transactions_df, new_transaction_df], ignore_index=True)
        
        updated_transactions_df.to_excel(config.TRANSACTION_SHEET_FILE, index=False)
        log.info("Transaction logged successfully.")
    except Exception as e:
        log.error(f"Failed to log transaction. Error: {e}", exc_info=True)


