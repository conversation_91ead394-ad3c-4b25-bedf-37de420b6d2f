{"cells": [{"cell_type": "code", "execution_count": 1, "id": "df1ff9cf", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "id": "655a5288", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>ff_segment_type</th>\n", "      <th>ff_segment_num</th>\n", "      <th>adjdate_x</th>\n", "      <th>currency_x</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>opinc</th>\n", "      <th>assets</th>\n", "      <th>...</th>\n", "      <th>ff_dep_chg_cf</th>\n", "      <th>ff_assets_nonperf</th>\n", "      <th>ff_dps_all</th>\n", "      <th>ff_bk_com_eq_tier1_tot</th>\n", "      <th>ff_misc_net_oth</th>\n", "      <th>ff_cap_lease_curr</th>\n", "      <th>ff_curr_ins_ben</th>\n", "      <th>ff_intang_devt</th>\n", "      <th>ff_net_inc_aft_pfd</th>\n", "      <th>ff_oper_lease_repay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2224.7</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Europe/Africa</td>\n", "      <td>1.6</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>NaN</td>\n", "      <td>-45.6</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Rest of World</td>\n", "      <td>51.3</td>\n", "      <td>-0.5</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2332.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>44.4</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 154 columns</p>\n", "</div>"], "text/plain": ["    fsym_id       date ff_segment_type  ff_segment_num adjdate_x currency_x  \\\n", "0  C2083V-R 2020-10-31             REG               1       NaT        USD   \n", "1  C2083V-R 2020-10-31             REG               2       NaT        USD   \n", "2  C2083V-R 2020-10-31             REG               3       NaT        USD   \n", "3  C2083V-R 2020-10-31             REG               4       NaT        USD   \n", "4  C2083V-R 2021-10-31             REG               1       NaT        USD   \n", "\n", "                      label   sales  opinc  assets  ...  ff_dep_chg_cf  \\\n", "0  United States and Canada  2224.7    NaN     NaN  ...              0   \n", "1             Europe/Africa     1.6    NaN     NaN  ...              0   \n", "2             United States     NaN  -45.6     NaN  ...              0   \n", "3             Rest of World    51.3   -0.5     NaN  ...              0   \n", "4  United States and Canada  2332.0    NaN     NaN  ...              0   \n", "\n", "   ff_assets_nonperf ff_dps_all ff_bk_com_eq_tier1_tot ff_misc_net_oth  \\\n", "0                NaN       0.10                    NaN               0   \n", "1                NaN       0.10                    NaN               0   \n", "2                NaN       0.10                    NaN               0   \n", "3                NaN       0.10                    NaN               0   \n", "4                NaN       0.15                    NaN               0   \n", "\n", "  ff_cap_lease_curr  ff_curr_ins_ben ff_intang_devt  ff_net_inc_aft_pfd  \\\n", "0               NaN              NaN            NaN               -30.5   \n", "1               NaN              NaN            NaN               -30.5   \n", "2               NaN              NaN            NaN               -30.5   \n", "3               NaN              NaN            NaN               -30.5   \n", "4               NaN              NaN            NaN                44.4   \n", "\n", "  ff_oper_lease_repay  \n", "0                 NaN  \n", "1                 NaN  \n", "2                 NaN  \n", "3                 NaN  \n", "4                 NaN  \n", "\n", "[5 rows x 154 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["region_df = pd.read_excel(\"Motor Vehicles_region.xlsx\")\n", "region_df.head()"]}, {"cell_type": "markdown", "id": "31b50f8b", "metadata": {}, "source": ["## Checking missing value in sales and ff_sales column"]}, {"cell_type": "code", "execution_count": 3, "id": "759d4469", "metadata": {}, "outputs": [], "source": ["# To see rows with missing values in either column\n", "rows_with_missing = region_df[region_df['sales'].isna()]\n"]}, {"cell_type": "code", "execution_count": 5, "id": "824b0794", "metadata": {}, "outputs": [{"data": {"text/plain": ["52"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(rows_with_missing)"]}, {"cell_type": "code", "execution_count": 6, "id": "4fef3b79", "metadata": {}, "outputs": [], "source": ["rows_with_missing_ff_sales = region_df[region_df['ff_sales'].isna()]"]}, {"cell_type": "code", "execution_count": 7, "id": "d7b3ad1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(rows_with_missing_ff_sales)"]}, {"cell_type": "markdown", "id": "e227c28f", "metadata": {}, "source": ["### fill the missing sales value with 0"]}, {"cell_type": "code", "execution_count": 8, "id": "e44b88a0", "metadata": {}, "outputs": [], "source": ["region_df['sales'] = region_df['sales'].fillna(0)"]}, {"cell_type": "code", "execution_count": 9, "id": "9c5fd196", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in 'sales' column after filling: 0\n"]}], "source": ["print(\"Missing values in 'sales' column after filling:\", region_df['sales'].isna().sum())"]}, {"cell_type": "markdown", "id": "b6295af4", "metadata": {}, "source": ["## Building the mapping"]}, {"cell_type": "code", "execution_count": 10, "id": "dd4164f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['United States and Canada' 'Europe/Africa' 'United States'\n", " 'Rest of World' 'North America' 'Europe, Africa and Middle East'\n", " 'Other North America' 'Rest of the World' 'China' 'Mexico' 'Netherlands'\n", " 'EMEA' 'Japan' 'Canada' 'Australia and New Zealand' 'Thailand'\n", " 'Other Countries' 'Reconciling Items' 'Other International' 'Germany'\n", " 'Saudi Arabia' 'Middle East' 'Other international' 'Non-United States'\n", " 'Foreign' 'United Kingdom' 'Europe' 'South America'\n", " 'China (including Taiwan)' 'Global' 'Corporate Other'\n", " 'International Markets Group' 'All Other']\n"]}], "source": ["# unique regions\n", "unique_labels = region_df['label'].unique()\n", "print(unique_labels)"]}, {"cell_type": "code", "execution_count": 11, "id": "f2028544", "metadata": {}, "outputs": [{"data": {"text/plain": ["33"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(unique_labels)"]}, {"cell_type": "code", "execution_count": 16, "id": "6294d805", "metadata": {}, "outputs": [], "source": ["standard_region_dict = {\n", "    \"US\": ['united states and canada', 'united states', 'north america', 'other north america', 'canada', 'mexico','south america'],\n", "    \"Europe\": ['netherlands', 'germany', 'united kingdom', 'europe'],\n", "    \"EMEA\": ['europe, africa and middle east', 'emea', 'europe/africa', ],\n", "    \"middle east\":['saudi arabia', 'middle east'],\n", "    \"others\": ['rest of the world', 'other countries','other intrnational', 'corporate other','non-united states','foreign', 'all other', 'international markets group','global'],\n", "    \"china\": ['china', 'china(including taiwan)']\n", "\n", "}"]}, {"cell_type": "code", "execution_count": 64, "id": "a19b39b0", "metadata": {}, "outputs": [], "source": ["def get_standardized_region(label):\n", "    label_lower = label.lower()\n", "    for standard_region, keywords in standard_region_dict.items():\n", "        if any(keyword == label_lower for keyword in keywords):\n", "            return standard_region\n", "    return label_lower\n"]}, {"cell_type": "code", "execution_count": 20, "id": "5d1b0c49", "metadata": {}, "outputs": [], "source": ["region_df['standardized_region'] = region_df['label'].apply(get_standardized_region)"]}, {"cell_type": "code", "execution_count": 25, "id": "7999768c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Europe/Africa</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Europe/Africa</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Europe/Africa</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       label standardized_region\n", "0   United States and Canada                  US\n", "1              Europe/Africa              Europe\n", "2              United States                  US\n", "3              Rest of World       rest of world\n", "4   United States and Canada                  US\n", "5              Europe/Africa              Europe\n", "6              United States                  US\n", "7              Rest of World       rest of world\n", "8   United States and Canada                  US\n", "9              United States                  US\n", "10             Europe/Africa              Europe\n", "11             Rest of World       rest of world\n", "12  United States and Canada                  US\n", "13             United States                  US\n", "14             Rest of World       rest of world\n", "15             North America                  US\n", "16             United States                  US\n", "17             Rest of World       rest of world\n", "18             United States                  US\n", "19             United States                  US"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["region_df[['label', 'standardized_region']].head(20)"]}, {"cell_type": "markdown", "id": "21e47ff0", "metadata": {}, "source": ["## Tackling each company one by one"]}, {"cell_type": "code", "execution_count": 26, "id": "35fd175d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['REV Group, Inc.' 'Rivian Automotive, Inc.' 'Oshkosh Corp.'\n", " 'Harley-Davidson, Inc.' 'Tesla, Inc.' 'Lucid Group, Inc.'\n", " 'General Motors Co.' 'Miller Industries, Inc. (Tennessee)'\n", " 'Blue Bird Corp.' 'Ford Motor Co.']\n"]}], "source": ["# unique companies present\n", "unique_companies = region_df['entity_proper_name'].unique()\n", "print(unique_companies)"]}, {"cell_type": "markdown", "id": "67e93fbf", "metadata": {}, "source": ["### REV group, Inc"]}, {"cell_type": "code", "execution_count": 27, "id": "12eac49e", "metadata": {}, "outputs": [], "source": ["# Filter rows where entity_proper_name is 'REV Group, Inc.'\n", "rev_group_df = region_df[region_df['entity_proper_name'] == 'REV Group, Inc.']"]}, {"cell_type": "code", "execution_count": 28, "id": "de6c75de", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>ff_segment_type</th>\n", "      <th>ff_segment_num</th>\n", "      <th>adjdate_x</th>\n", "      <th>currency_x</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>opinc</th>\n", "      <th>assets</th>\n", "      <th>...</th>\n", "      <th>ff_assets_nonperf</th>\n", "      <th>ff_dps_all</th>\n", "      <th>ff_bk_com_eq_tier1_tot</th>\n", "      <th>ff_misc_net_oth</th>\n", "      <th>ff_cap_lease_curr</th>\n", "      <th>ff_curr_ins_ben</th>\n", "      <th>ff_intang_devt</th>\n", "      <th>ff_net_inc_aft_pfd</th>\n", "      <th>ff_oper_lease_repay</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2224.7</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Europe/Africa</td>\n", "      <td>1.6</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>-45.6</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Rest of World</td>\n", "      <td>51.3</td>\n", "      <td>-0.5</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2332.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>44.4</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 155 columns</p>\n", "</div>"], "text/plain": ["    fsym_id       date ff_segment_type  ff_segment_num adjdate_x currency_x  \\\n", "0  C2083V-R 2020-10-31             REG               1       NaT        USD   \n", "1  C2083V-R 2020-10-31             REG               2       NaT        USD   \n", "2  C2083V-R 2020-10-31             REG               3       NaT        USD   \n", "3  C2083V-R 2020-10-31             REG               4       NaT        USD   \n", "4  C2083V-R 2021-10-31             REG               1       NaT        USD   \n", "\n", "                      label   sales  opinc  assets  ...  ff_assets_nonperf  \\\n", "0  United States and Canada  2224.7    NaN     NaN  ...                NaN   \n", "1             Europe/Africa     1.6    NaN     NaN  ...                NaN   \n", "2             United States     0.0  -45.6     NaN  ...                NaN   \n", "3             Rest of World    51.3   -0.5     NaN  ...                NaN   \n", "4  United States and Canada  2332.0    NaN     NaN  ...                NaN   \n", "\n", "   ff_dps_all ff_bk_com_eq_tier1_tot ff_misc_net_oth ff_cap_lease_curr  \\\n", "0        0.10                    NaN               0               NaN   \n", "1        0.10                    NaN               0               NaN   \n", "2        0.10                    NaN               0               NaN   \n", "3        0.10                    NaN               0               NaN   \n", "4        0.15                    NaN               0               NaN   \n", "\n", "  ff_curr_ins_ben  ff_intang_devt ff_net_inc_aft_pfd  ff_oper_lease_repay  \\\n", "0             NaN             NaN              -30.5                  NaN   \n", "1             NaN             NaN              -30.5                  NaN   \n", "2             NaN             NaN              -30.5                  NaN   \n", "3             NaN             NaN              -30.5                  NaN   \n", "4             NaN             NaN               44.4                  NaN   \n", "\n", "  standardized_region  \n", "0                  US  \n", "1              Europe  \n", "2                  US  \n", "3       rest of world  \n", "4                  US  \n", "\n", "[5 rows x 155 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["rev_group_df.head()"]}, {"cell_type": "code", "execution_count": 29, "id": "4b5d45f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["18"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(rev_group_df)"]}, {"cell_type": "code", "execution_count": 31, "id": "b51a782c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-10-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2021-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2021-10-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2021-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2022-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2022-10-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2022-10-31</td>\n", "      <td>Europe/Africa</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2022-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-10-31</td>\n", "      <td>United States and Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-10-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-10-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-10-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-10-31</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date                     label standardized_region\n", "0  2020-10-31  United States and Canada                  US\n", "1  2020-10-31             Europe/Africa              Europe\n", "2  2020-10-31             United States                  US\n", "3  2020-10-31             Rest of World       rest of world\n", "4  2021-10-31  United States and Canada                  US\n", "5  2021-10-31             Europe/Africa              Europe\n", "6  2021-10-31             United States                  US\n", "7  2021-10-31             Rest of World       rest of world\n", "8  2022-10-31  United States and Canada                  US\n", "9  2022-10-31             United States                  US\n", "10 2022-10-31             Europe/Africa              Europe\n", "11 2022-10-31             Rest of World       rest of world\n", "12 2023-10-31  United States and Canada                  US\n", "13 2023-10-31             United States                  US\n", "14 2023-10-31             Rest of World       rest of world\n", "15 2024-10-31             North America                  US\n", "16 2024-10-31             United States                  US\n", "17 2024-10-31             Rest of World       rest of world"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["rev_group_df[['date','label','standardized_region']].head(20)"]}, {"cell_type": "code", "execution_count": 34, "id": "a0c919e7", "metadata": {}, "outputs": [], "source": ["rev_group_df.to_excel(\"REV_group_sales.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": 35, "id": "270a8387", "metadata": {}, "outputs": [], "source": ["region_sales_sum = rev_group_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n"]}, {"cell_type": "code", "execution_count": 36, "id": "71deade7", "metadata": {}, "outputs": [], "source": ["ff_sales_per_date = rev_group_df[['date', 'ff_sales']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": 37, "id": "45262d87", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>ff_sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-10-31</td>\n", "      <td>2277.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-10-31</td>\n", "      <td>2380.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2022-10-31</td>\n", "      <td>2331.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-10-31</td>\n", "      <td>2638.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-10-31</td>\n", "      <td>2380.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date  ff_sales\n", "0  2020-10-31    2277.6\n", "4  2021-10-31    2380.8\n", "8  2022-10-31    2331.6\n", "12 2023-10-31    2638.0\n", "15 2024-10-31    2380.2"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["ff_sales_per_date.head(20)"]}, {"cell_type": "code", "execution_count": 38, "id": "933d22af", "metadata": {}, "outputs": [], "source": ["merged = pd.merge(region_sales_sum, ff_sales_per_date, on='date', how='left')"]}, {"cell_type": "code", "execution_count": 41, "id": "1dd908de", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "      <th>ff_sales</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-10-31</td>\n", "      <td>Europe</td>\n", "      <td>1.6</td>\n", "      <td>2277.6</td>\n", "      <td>0.000702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-10-31</td>\n", "      <td>US</td>\n", "      <td>2224.7</td>\n", "      <td>2277.6</td>\n", "      <td>0.976774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-10-31</td>\n", "      <td>rest of world</td>\n", "      <td>51.3</td>\n", "      <td>2277.6</td>\n", "      <td>0.022524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-10-31</td>\n", "      <td>Europe</td>\n", "      <td>6.4</td>\n", "      <td>2380.8</td>\n", "      <td>0.002688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-10-31</td>\n", "      <td>US</td>\n", "      <td>2332.0</td>\n", "      <td>2380.8</td>\n", "      <td>0.979503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2021-10-31</td>\n", "      <td>rest of world</td>\n", "      <td>42.4</td>\n", "      <td>2380.8</td>\n", "      <td>0.017809</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2022-10-31</td>\n", "      <td>Europe</td>\n", "      <td>0.5</td>\n", "      <td>2331.6</td>\n", "      <td>0.000214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2022-10-31</td>\n", "      <td>US</td>\n", "      <td>2310.2</td>\n", "      <td>2331.6</td>\n", "      <td>0.990822</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2022-10-31</td>\n", "      <td>rest of world</td>\n", "      <td>20.9</td>\n", "      <td>2331.6</td>\n", "      <td>0.008964</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-10-31</td>\n", "      <td>US</td>\n", "      <td>2613.7</td>\n", "      <td>2638.0</td>\n", "      <td>0.990788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-10-31</td>\n", "      <td>rest of world</td>\n", "      <td>24.3</td>\n", "      <td>2638.0</td>\n", "      <td>0.009212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-10-31</td>\n", "      <td>US</td>\n", "      <td>2363.4</td>\n", "      <td>2380.2</td>\n", "      <td>0.992942</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-10-31</td>\n", "      <td>rest of world</td>\n", "      <td>16.8</td>\n", "      <td>2380.2</td>\n", "      <td>0.007058</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date standardized_region   sales  ff_sales  sales_percnt_of_ff\n", "0  2020-10-31              Europe     1.6    2277.6            0.000702\n", "1  2020-10-31                  US  2224.7    2277.6            0.976774\n", "2  2020-10-31       rest of world    51.3    2277.6            0.022524\n", "3  2021-10-31              Europe     6.4    2380.8            0.002688\n", "4  2021-10-31                  US  2332.0    2380.8            0.979503\n", "5  2021-10-31       rest of world    42.4    2380.8            0.017809\n", "6  2022-10-31              Europe     0.5    2331.6            0.000214\n", "7  2022-10-31                  US  2310.2    2331.6            0.990822\n", "8  2022-10-31       rest of world    20.9    2331.6            0.008964\n", "9  2023-10-31                  US  2613.7    2638.0            0.990788\n", "10 2023-10-31       rest of world    24.3    2638.0            0.009212\n", "11 2024-10-31                  US  2363.4    2380.2            0.992942\n", "12 2024-10-31       rest of world    16.8    2380.2            0.007058"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["merged.head(20)"]}, {"cell_type": "code", "execution_count": 40, "id": "e1fcb37c", "metadata": {}, "outputs": [], "source": ["merged['sales_percnt_of_ff'] = (merged['sales'] / merged['ff_sales'])"]}, {"cell_type": "code", "execution_count": 43, "id": "2abc18f8", "metadata": {}, "outputs": [], "source": ["rev_group_df = pd.merge(\n", "    rev_group_df,\n", "    merged[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")\n"]}, {"cell_type": "code", "execution_count": 44, "id": "956a1dec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>ff_segment_type</th>\n", "      <th>ff_segment_num</th>\n", "      <th>adjdate_x</th>\n", "      <th>currency_x</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>opinc</th>\n", "      <th>assets</th>\n", "      <th>...</th>\n", "      <th>ff_dps_all</th>\n", "      <th>ff_bk_com_eq_tier1_tot</th>\n", "      <th>ff_misc_net_oth</th>\n", "      <th>ff_cap_lease_curr</th>\n", "      <th>ff_curr_ins_ben</th>\n", "      <th>ff_intang_devt</th>\n", "      <th>ff_net_inc_aft_pfd</th>\n", "      <th>ff_oper_lease_repay</th>\n", "      <th>standardized_region</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2224.7</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.976774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Europe/Africa</td>\n", "      <td>1.6</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>Europe</td>\n", "      <td>0.000702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>-45.6</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.976774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C2083V-R</td>\n", "      <td>2020-10-31</td>\n", "      <td>REG</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Rest of World</td>\n", "      <td>51.3</td>\n", "      <td>-0.5</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-30.5</td>\n", "      <td>NaN</td>\n", "      <td>rest of world</td>\n", "      <td>0.022524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2332.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>44.4</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.979503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Europe/Africa</td>\n", "      <td>6.4</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>44.4</td>\n", "      <td>NaN</td>\n", "      <td>Europe</td>\n", "      <td>0.002688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>-45.6</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>44.4</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.979503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C2083V-R</td>\n", "      <td>2021-10-31</td>\n", "      <td>REG</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Rest of World</td>\n", "      <td>42.4</td>\n", "      <td>-0.5</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>44.4</td>\n", "      <td>NaN</td>\n", "      <td>rest of world</td>\n", "      <td>0.017809</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C2083V-R</td>\n", "      <td>2022-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2310.2</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>15.2</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.990822</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C2083V-R</td>\n", "      <td>2022-10-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>15.2</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.990822</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>C2083V-R</td>\n", "      <td>2022-10-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Europe/Africa</td>\n", "      <td>0.5</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>15.2</td>\n", "      <td>NaN</td>\n", "      <td>Europe</td>\n", "      <td>0.000214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C2083V-R</td>\n", "      <td>2022-10-31</td>\n", "      <td>REG</td>\n", "      <td>4</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Rest of World</td>\n", "      <td>20.9</td>\n", "      <td>-0.2</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>15.2</td>\n", "      <td>NaN</td>\n", "      <td>rest of world</td>\n", "      <td>0.008964</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>C2083V-R</td>\n", "      <td>2023-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States and Canada</td>\n", "      <td>2613.7</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.20</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>45.3</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.990788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>C2083V-R</td>\n", "      <td>2023-10-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>58.9</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.20</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>45.3</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.990788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>C2083V-R</td>\n", "      <td>2023-10-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Rest of World</td>\n", "      <td>24.3</td>\n", "      <td>-0.7</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.20</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>45.3</td>\n", "      <td>NaN</td>\n", "      <td>rest of world</td>\n", "      <td>0.009212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>C2083V-R</td>\n", "      <td>2024-10-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>North America</td>\n", "      <td>2363.4</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>3.20</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>257.6</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.992942</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>C2083V-R</td>\n", "      <td>2024-10-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>340.4</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>3.20</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>257.6</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>0.992942</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>C2083V-R</td>\n", "      <td>2024-10-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>Rest of World</td>\n", "      <td>16.8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>3.20</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>257.6</td>\n", "      <td>NaN</td>\n", "      <td>rest of world</td>\n", "      <td>0.007058</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18 rows × 156 columns</p>\n", "</div>"], "text/plain": ["     fsym_id       date ff_segment_type  ff_segment_num adjdate_x currency_x  \\\n", "0   C2083V-R 2020-10-31             REG               1       NaT        USD   \n", "1   C2083V-R 2020-10-31             REG               2       NaT        USD   \n", "2   C2083V-R 2020-10-31             REG               3       NaT        USD   \n", "3   C2083V-R 2020-10-31             REG               4       NaT        USD   \n", "4   C2083V-R 2021-10-31             REG               1       NaT        USD   \n", "5   C2083V-R 2021-10-31             REG               2       NaT        USD   \n", "6   C2083V-R 2021-10-31             REG               3       NaT        USD   \n", "7   C2083V-R 2021-10-31             REG               4       NaT        USD   \n", "8   C2083V-R 2022-10-31             REG               1       NaT        USD   \n", "9   C2083V-R 2022-10-31             REG               2       NaT        USD   \n", "10  C2083V-R 2022-10-31             REG               3       NaT        USD   \n", "11  C2083V-R 2022-10-31             REG               4       NaT        USD   \n", "12  C2083V-R 2023-10-31             REG               1       NaT        USD   \n", "13  C2083V-R 2023-10-31             REG               2       NaT        USD   \n", "14  C2083V-R 2023-10-31             REG               3       NaT        USD   \n", "15  C2083V-R 2024-10-31             REG               1       NaT        USD   \n", "16  C2083V-R 2024-10-31             REG               2       NaT        USD   \n", "17  C2083V-R 2024-10-31             REG               3       NaT        USD   \n", "\n", "                       label   sales  opinc  assets  ...  ff_dps_all  \\\n", "0   United States and Canada  2224.7    NaN     NaN  ...        0.10   \n", "1              Europe/Africa     1.6    NaN     NaN  ...        0.10   \n", "2              United States     0.0  -45.6     NaN  ...        0.10   \n", "3              Rest of World    51.3   -0.5     NaN  ...        0.10   \n", "4   United States and Canada  2332.0    NaN     NaN  ...        0.15   \n", "5              Europe/Africa     6.4    NaN     NaN  ...        0.15   \n", "6              United States     0.0  -45.6     NaN  ...        0.15   \n", "7              Rest of World    42.4   -0.5     NaN  ...        0.15   \n", "8   United States and Canada  2310.2    NaN     NaN  ...        0.15   \n", "9              United States     0.0   20.0     NaN  ...        0.15   \n", "10             Europe/Africa     0.5    NaN     NaN  ...        0.15   \n", "11             Rest of World    20.9   -0.2     NaN  ...        0.15   \n", "12  United States and Canada  2613.7    NaN     NaN  ...        0.20   \n", "13             United States     0.0   58.9     NaN  ...        0.20   \n", "14             Rest of World    24.3   -0.7     NaN  ...        0.20   \n", "15             North America  2363.4    NaN     NaN  ...        3.20   \n", "16             United States     0.0  340.4     NaN  ...        3.20   \n", "17             Rest of World    16.8    NaN     NaN  ...        3.20   \n", "\n", "    ff_bk_com_eq_tier1_tot ff_misc_net_oth ff_cap_lease_curr ff_curr_ins_ben  \\\n", "0                      NaN               0               NaN             NaN   \n", "1                      NaN               0               NaN             NaN   \n", "2                      NaN               0               NaN             NaN   \n", "3                      NaN               0               NaN             NaN   \n", "4                      NaN               0               NaN             NaN   \n", "5                      NaN               0               NaN             NaN   \n", "6                      NaN               0               NaN             NaN   \n", "7                      NaN               0               NaN             NaN   \n", "8                      NaN               0               NaN             NaN   \n", "9                      NaN               0               NaN             NaN   \n", "10                     NaN               0               NaN             NaN   \n", "11                     NaN               0               NaN             NaN   \n", "12                     NaN               0               NaN             NaN   \n", "13                     NaN               0               NaN             NaN   \n", "14                     NaN               0               NaN             NaN   \n", "15                     NaN               0               NaN             NaN   \n", "16                     NaN               0               NaN             NaN   \n", "17                     NaN               0               NaN             NaN   \n", "\n", "   ff_intang_devt  ff_net_inc_aft_pfd ff_oper_lease_repay  \\\n", "0             NaN               -30.5                 NaN   \n", "1             NaN               -30.5                 NaN   \n", "2             NaN               -30.5                 NaN   \n", "3             NaN               -30.5                 NaN   \n", "4             NaN                44.4                 NaN   \n", "5             NaN                44.4                 NaN   \n", "6             NaN                44.4                 NaN   \n", "7             NaN                44.4                 NaN   \n", "8             NaN                15.2                 NaN   \n", "9             NaN                15.2                 NaN   \n", "10            NaN                15.2                 NaN   \n", "11            NaN                15.2                 NaN   \n", "12            NaN                45.3                 NaN   \n", "13            NaN                45.3                 NaN   \n", "14            NaN                45.3                 NaN   \n", "15            NaN               257.6                 NaN   \n", "16            NaN               257.6                 NaN   \n", "17            NaN               257.6                 NaN   \n", "\n", "    standardized_region sales_percnt_of_ff  \n", "0                    US           0.976774  \n", "1                Europe           0.000702  \n", "2                    US           0.976774  \n", "3         rest of world           0.022524  \n", "4                    US           0.979503  \n", "5                Europe           0.002688  \n", "6                    US           0.979503  \n", "7         rest of world           0.017809  \n", "8                    US           0.990822  \n", "9                    US           0.990822  \n", "10               Europe           0.000214  \n", "11        rest of world           0.008964  \n", "12                   US           0.990788  \n", "13                   US           0.990788  \n", "14        rest of world           0.009212  \n", "15                   US           0.992942  \n", "16                   US           0.992942  \n", "17        rest of world           0.007058  \n", "\n", "[18 rows x 156 columns]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["rev_group_df.head(20)"]}, {"cell_type": "markdown", "id": "e5ca3931", "metadata": {}, "source": ["### Rivian Automotive, Inc."]}, {"cell_type": "code", "execution_count": 45, "id": "55a3fca6", "metadata": {}, "outputs": [], "source": ["# Filter rows where entity_proper_name is 'Rivian Automotive, Inc.\n", "rivian_df = region_df[region_df['entity_proper_name'] == 'Rivian Automotive, Inc.']"]}, {"cell_type": "code", "execution_count": 46, "id": "6b36ef32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>ff_segment_type</th>\n", "      <th>ff_segment_num</th>\n", "      <th>adjdate_x</th>\n", "      <th>currency_x</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>opinc</th>\n", "      <th>assets</th>\n", "      <th>...</th>\n", "      <th>ff_assets_nonperf</th>\n", "      <th>ff_dps_all</th>\n", "      <th>ff_bk_com_eq_tier1_tot</th>\n", "      <th>ff_misc_net_oth</th>\n", "      <th>ff_cap_lease_curr</th>\n", "      <th>ff_curr_ins_ben</th>\n", "      <th>ff_intang_devt</th>\n", "      <th>ff_net_inc_aft_pfd</th>\n", "      <th>ff_oper_lease_repay</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2020-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>-1021.0</td>\n", "      <td>4602.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1019.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2021-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>55.0</td>\n", "      <td>-4220.0</td>\n", "      <td>22294.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-4688.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2022-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>1658.0</td>\n", "      <td>-6856.0</td>\n", "      <td>17876.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-6752.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2023-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>4434.0</td>\n", "      <td>-5739.0</td>\n", "      <td>16778.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-5432.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2024-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>4970.0</td>\n", "      <td>-4689.0</td>\n", "      <td>15410.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-4747.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 155 columns</p>\n", "</div>"], "text/plain": ["     fsym_id       date ff_segment_type  ff_segment_num adjdate_x currency_x  \\\n", "18  G7RF6F-R 2020-12-31             REG               1       NaT        USD   \n", "19  G7RF6F-R 2021-12-31             REG               1       NaT        USD   \n", "20  G7RF6F-R 2022-12-31             REG               1       NaT        USD   \n", "21  G7RF6F-R 2023-12-31             REG               1       NaT        USD   \n", "22  G7RF6F-R 2024-12-31             REG               1       NaT        USD   \n", "\n", "            label   sales   opinc   assets  ...  ff_assets_nonperf  \\\n", "18  United States     0.0 -1021.0   4602.0  ...                NaN   \n", "19  United States    55.0 -4220.0  22294.0  ...                NaN   \n", "20  United States  1658.0 -6856.0  17876.0  ...                NaN   \n", "21  United States  4434.0 -5739.0  16778.0  ...                NaN   \n", "22  United States  4970.0 -4689.0  15410.0  ...                NaN   \n", "\n", "    ff_dps_all ff_bk_com_eq_tier1_tot ff_misc_net_oth ff_cap_lease_curr  \\\n", "18         NaN                    NaN              -1               NaN   \n", "19         0.0                    NaN               0               NaN   \n", "20         0.0                    NaN               0               NaN   \n", "21         0.0                    NaN               0               3.0   \n", "22         0.0                    NaN               0               2.0   \n", "\n", "   ff_curr_ins_ben  ff_intang_devt ff_net_inc_aft_pfd  ff_oper_lease_repay  \\\n", "18             NaN             NaN            -1019.0                  NaN   \n", "19             NaN             NaN            -4688.0                  NaN   \n", "20             NaN             NaN            -6752.0                  NaN   \n", "21             NaN             NaN            -5432.0                  NaN   \n", "22             NaN             NaN            -4747.0                  NaN   \n", "\n", "   standardized_region  \n", "18                  US  \n", "19                  US  \n", "20                  US  \n", "21                  US  \n", "22                  US  \n", "\n", "[5 rows x 155 columns]"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["rivian_df.head()"]}, {"cell_type": "code", "execution_count": 47, "id": "2e6ffa76", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["len(rivian_df)"]}, {"cell_type": "code", "execution_count": 48, "id": "19fca127", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2020-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2021-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date          label standardized_region\n", "18 2020-12-31  United States                  US\n", "19 2021-12-31  United States                  US\n", "20 2022-12-31  United States                  US\n", "21 2023-12-31  United States                  US\n", "22 2024-12-31  United States                  US"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["rivian_df[['date','label','standardized_region']].head(20)"]}, {"cell_type": "code", "execution_count": 49, "id": "c6193bc0", "metadata": {}, "outputs": [], "source": ["region_sales_sum_2 = rivian_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n"]}, {"cell_type": "code", "execution_count": 51, "id": "244f9daa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>US</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-12-31</td>\n", "      <td>US</td>\n", "      <td>55.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-12-31</td>\n", "      <td>US</td>\n", "      <td>1658.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-12-31</td>\n", "      <td>US</td>\n", "      <td>4434.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-31</td>\n", "      <td>US</td>\n", "      <td>4970.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date standardized_region   sales\n", "0 2020-12-31                  US     0.0\n", "1 2021-12-31                  US    55.0\n", "2 2022-12-31                  US  1658.0\n", "3 2023-12-31                  US  4434.0\n", "4 2024-12-31                  US  4970.0"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["region_sales_sum_2.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "8b03938c", "metadata": {}, "outputs": [], "source": ["ff_sales_per_date_2 = rivian_df[['date', 'ff_sales']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": 54, "id": "460ec659", "metadata": {}, "outputs": [], "source": ["merged_2 = pd.merge(region_sales_sum_2, ff_sales_per_date_2, on='date', how='left')"]}, {"cell_type": "code", "execution_count": 55, "id": "601cfd19", "metadata": {}, "outputs": [], "source": ["merged_2['sales_percnt_of_ff'] = (merged_2['sales'] / merged_2['ff_sales'])"]}, {"cell_type": "code", "execution_count": 56, "id": "f83831bf", "metadata": {}, "outputs": [], "source": ["rivian_df = pd.merge(\n", "    rivian_df,\n", "    merged_2[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")\n"]}, {"cell_type": "code", "execution_count": 57, "id": "13332e50", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>ff_segment_type</th>\n", "      <th>ff_segment_num</th>\n", "      <th>adjdate_x</th>\n", "      <th>currency_x</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>opinc</th>\n", "      <th>assets</th>\n", "      <th>...</th>\n", "      <th>ff_dps_all</th>\n", "      <th>ff_bk_com_eq_tier1_tot</th>\n", "      <th>ff_misc_net_oth</th>\n", "      <th>ff_cap_lease_curr</th>\n", "      <th>ff_curr_ins_ben</th>\n", "      <th>ff_intang_devt</th>\n", "      <th>ff_net_inc_aft_pfd</th>\n", "      <th>ff_oper_lease_repay</th>\n", "      <th>standardized_region</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2020-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>0.0</td>\n", "      <td>-1021.0</td>\n", "      <td>4602.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1019.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2021-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>55.0</td>\n", "      <td>-4220.0</td>\n", "      <td>22294.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-4688.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2022-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>1658.0</td>\n", "      <td>-6856.0</td>\n", "      <td>17876.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-6752.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2023-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>4434.0</td>\n", "      <td>-5739.0</td>\n", "      <td>16778.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-5432.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>G7RF6F-R</td>\n", "      <td>2024-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>NaT</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>4970.0</td>\n", "      <td>-4689.0</td>\n", "      <td>15410.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-4747.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 156 columns</p>\n", "</div>"], "text/plain": ["    fsym_id       date ff_segment_type  ff_segment_num adjdate_x currency_x  \\\n", "0  G7RF6F-R 2020-12-31             REG               1       NaT        USD   \n", "1  G7RF6F-R 2021-12-31             REG               1       NaT        USD   \n", "2  G7RF6F-R 2022-12-31             REG               1       NaT        USD   \n", "3  G7RF6F-R 2023-12-31             REG               1       NaT        USD   \n", "4  G7RF6F-R 2024-12-31             REG               1       NaT        USD   \n", "\n", "           label   sales   opinc   assets  ...  ff_dps_all  \\\n", "0  United States     0.0 -1021.0   4602.0  ...         NaN   \n", "1  United States    55.0 -4220.0  22294.0  ...         0.0   \n", "2  United States  1658.0 -6856.0  17876.0  ...         0.0   \n", "3  United States  4434.0 -5739.0  16778.0  ...         0.0   \n", "4  United States  4970.0 -4689.0  15410.0  ...         0.0   \n", "\n", "   ff_bk_com_eq_tier1_tot ff_misc_net_oth ff_cap_lease_curr ff_curr_ins_ben  \\\n", "0                     NaN              -1               NaN             NaN   \n", "1                     NaN               0               NaN             NaN   \n", "2                     NaN               0               NaN             NaN   \n", "3                     NaN               0               3.0             NaN   \n", "4                     NaN               0               2.0             NaN   \n", "\n", "  ff_intang_devt  ff_net_inc_aft_pfd ff_oper_lease_repay  standardized_region  \\\n", "0            NaN             -1019.0                 NaN                   US   \n", "1            NaN             -4688.0                 NaN                   US   \n", "2            NaN             -6752.0                 NaN                   US   \n", "3            NaN             -5432.0                 NaN                   US   \n", "4            NaN             -4747.0                 NaN                   US   \n", "\n", "  sales_percnt_of_ff  \n", "0                NaN  \n", "1                1.0  \n", "2                1.0  \n", "3                1.0  \n", "4                1.0  \n", "\n", "[5 rows x 156 columns]"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["rivian_df.head(20)"]}, {"cell_type": "markdown", "id": "cf8a95e2", "metadata": {}, "source": ["### 'Oshkosh Corp.'"]}, {"cell_type": "code", "execution_count": 58, "id": "e5a51080", "metadata": {}, "outputs": [], "source": ["oshkosh_df = region_df[region_df['entity_proper_name'] == 'Oshkosh Corp.']"]}, {"cell_type": "code", "execution_count": 62, "id": "c2abf002", "metadata": {}, "outputs": [], "source": ["oshkosh_df = oshkosh_df.drop(columns=['standardized_region'])"]}, {"cell_type": "code", "execution_count": 65, "id": "39ec79bd", "metadata": {}, "outputs": [], "source": ["oshkosh_df['standardized_region'] = oshkosh_df['label'].apply(get_standardized_region)"]}, {"cell_type": "code", "execution_count": 66, "id": "c5653b1c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2020-09-30</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2020-09-30</td>\n", "      <td>Europe, Africa and Middle East</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2020-09-30</td>\n", "      <td>Other North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2020-09-30</td>\n", "      <td>Rest of the World</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2021-09-30</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2021-09-30</td>\n", "      <td>Europe, Africa and Middle East</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2021-09-30</td>\n", "      <td>Other North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2021-09-30</td>\n", "      <td>Rest of the World</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2022-12-31</td>\n", "      <td>Europe, Africa and Middle East</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2022-12-31</td>\n", "      <td>Other North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2022-12-31</td>\n", "      <td>Rest of the World</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2023-12-31</td>\n", "      <td>Europe, Africa and Middle East</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2023-12-31</td>\n", "      <td>Other North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2023-12-31</td>\n", "      <td>China</td>\n", "      <td>china</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>2023-12-31</td>\n", "      <td>Mexico</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>2023-12-31</td>\n", "      <td>Netherlands</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>2023-12-31</td>\n", "      <td>Rest of the World</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date                           label standardized_region\n", "23 2020-09-30                   United States                  US\n", "24 2020-09-30  Europe, Africa and Middle East                EMEA\n", "25 2020-09-30             Other North America                  US\n", "26 2020-09-30               Rest of the World              others\n", "27 2021-09-30                   United States                  US\n", "28 2021-09-30  Europe, Africa and Middle East                EMEA\n", "29 2021-09-30             Other North America                  US\n", "30 2021-09-30               Rest of the World              others\n", "31 2022-12-31                   United States                  US\n", "32 2022-12-31  Europe, Africa and Middle East                EMEA\n", "33 2022-12-31             Other North America                  US\n", "34 2022-12-31               Rest of the World              others\n", "35 2023-12-31                   United States                  US\n", "36 2023-12-31  Europe, Africa and Middle East                EMEA\n", "37 2023-12-31             Other North America                  US\n", "38 2023-12-31                           China               china\n", "39 2023-12-31                          Mexico                  US\n", "40 2023-12-31                     Netherlands              Europe\n", "41 2023-12-31               Rest of the World              others\n", "42 2024-12-31                   United States                  US"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["oshkosh_df[['date','label','standardized_region']].head(20)"]}, {"cell_type": "code", "execution_count": 67, "id": "5eb9c61f", "metadata": {}, "outputs": [], "source": ["region_sales_sum_3 = oshkosh_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()"]}, {"cell_type": "code", "execution_count": 68, "id": "be9e2c56", "metadata": {}, "outputs": [], "source": ["ff_sales_per_date_3 = oshkosh_df[['date', 'ff_sales']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": 69, "id": "7101aef1", "metadata": {}, "outputs": [], "source": ["merged_3 = pd.merge(region_sales_sum_3, ff_sales_per_date_3, on='date', how='left')"]}, {"cell_type": "code", "execution_count": 70, "id": "1c6ff755", "metadata": {}, "outputs": [], "source": ["merged_3['sales_percnt_of_ff'] = (merged_3['sales'] / merged_3['ff_sales'])"]}, {"cell_type": "code", "execution_count": 71, "id": "5ad8a264", "metadata": {}, "outputs": [], "source": ["oshkosh_df = pd.merge(\n", "    oshkosh_df,\n", "    merged_3[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "80f0b504", "metadata": {}, "source": ["### Harley-Davidson, Inc."]}, {"cell_type": "code", "execution_count": 72, "id": "3e21a656", "metadata": {}, "outputs": [], "source": ["harley_df = region_df[region_df['entity_proper_name'] == 'Harley-Davidson, Inc.']"]}, {"cell_type": "code", "execution_count": 104, "id": "2ca86da0", "metadata": {}, "outputs": [{"data": {"text/plain": ["35"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["len(harley_df)"]}, {"cell_type": "code", "execution_count": 105, "id": "1e3a99a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-12-31</td>\n", "      <td>Japan</td>\n", "      <td>japan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-12-31</td>\n", "      <td>Australia and New Zealand</td>\n", "      <td>australia and new zealand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2020-12-31</td>\n", "      <td>Thailand</td>\n", "      <td>thailand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2020-12-31</td>\n", "      <td>Other Countries</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2021-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2021-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2021-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2021-12-31</td>\n", "      <td>Japan</td>\n", "      <td>japan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2021-12-31</td>\n", "      <td>Australia and New Zealand</td>\n", "      <td>australia and new zealand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2021-12-31</td>\n", "      <td>Thailand</td>\n", "      <td>thailand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2021-12-31</td>\n", "      <td>Other Countries</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2022-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2022-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2022-12-31</td>\n", "      <td>Japan</td>\n", "      <td>japan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2022-12-31</td>\n", "      <td>Australia and New Zealand</td>\n", "      <td>australia and new zealand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2022-12-31</td>\n", "      <td>Thailand</td>\n", "      <td>thailand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2022-12-31</td>\n", "      <td>Other Countries</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2023-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2023-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2023-12-31</td>\n", "      <td>Japan</td>\n", "      <td>japan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2023-12-31</td>\n", "      <td>Australia and New Zealand</td>\n", "      <td>australia and new zealand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2023-12-31</td>\n", "      <td>Thailand</td>\n", "      <td>thailand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2023-12-31</td>\n", "      <td>Other Countries</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2024-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>EMEA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2024-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2024-12-31</td>\n", "      <td>Japan</td>\n", "      <td>japan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2024-12-31</td>\n", "      <td>Australia and New Zealand</td>\n", "      <td>australia and new zealand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-12-31</td>\n", "      <td>Thailand</td>\n", "      <td>thailand</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2024-12-31</td>\n", "      <td>Other Countries</td>\n", "      <td>others</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date                      label        standardized_region\n", "0  2020-12-31              United States                         US\n", "1  2020-12-31                       EMEA                       EMEA\n", "2  2020-12-31                      Japan                      japan\n", "3  2020-12-31                     Canada                         US\n", "4  2020-12-31  Australia and New Zealand  australia and new zealand\n", "5  2020-12-31                   Thailand                   thailand\n", "6  2020-12-31            Other Countries                     others\n", "7  2021-12-31              United States                         US\n", "8  2021-12-31                       EMEA                       EMEA\n", "9  2021-12-31                     Canada                         US\n", "10 2021-12-31                      Japan                      japan\n", "11 2021-12-31  Australia and New Zealand  australia and new zealand\n", "12 2021-12-31                   Thailand                   thailand\n", "13 2021-12-31            Other Countries                     others\n", "14 2022-12-31              United States                         US\n", "15 2022-12-31                       EMEA                       EMEA\n", "16 2022-12-31                     Canada                         US\n", "17 2022-12-31                      Japan                      japan\n", "18 2022-12-31  Australia and New Zealand  australia and new zealand\n", "19 2022-12-31                   Thailand                   thailand\n", "20 2022-12-31            Other Countries                     others\n", "21 2023-12-31              United States                         US\n", "22 2023-12-31                       EMEA                       EMEA\n", "23 2023-12-31                     Canada                         US\n", "24 2023-12-31                      Japan                      japan\n", "25 2023-12-31  Australia and New Zealand  australia and new zealand\n", "26 2023-12-31                   Thailand                   thailand\n", "27 2023-12-31            Other Countries                     others\n", "28 2024-12-31              United States                         US\n", "29 2024-12-31                       EMEA                       EMEA\n", "30 2024-12-31                     Canada                         US\n", "31 2024-12-31                      Japan                      japan\n", "32 2024-12-31  Australia and New Zealand  australia and new zealand\n", "33 2024-12-31                   Thailand                   thailand\n", "34 2024-12-31            Other Countries                     others"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["harley_df[['date','label','standardized_region']].head(40)"]}, {"cell_type": "code", "execution_count": 74, "id": "813ce3d1", "metadata": {}, "outputs": [], "source": ["region_sales_sum_4 = harley_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n", "ff_sales_per_date_4 = harley_df[['date', 'ff_sales']].drop_duplicates()\n", "merged_4 = pd.merge(region_sales_sum_4, ff_sales_per_date_4, on='date', how='left')\n", "merged_4['sales_percnt_of_ff'] = (merged_4['sales'] / merged_4['ff_sales'])\n"]}, {"cell_type": "code", "execution_count": 75, "id": "620bd7f7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "      <th>ff_sales</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>598.243</td>\n", "      <td>4054.377</td>\n", "      <td>0.147555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-31</td>\n", "      <td>US</td>\n", "      <td>2921.153</td>\n", "      <td>4054.377</td>\n", "      <td>0.720494</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-12-31</td>\n", "      <td>australia and new zealand</td>\n", "      <td>107.891</td>\n", "      <td>4054.377</td>\n", "      <td>0.026611</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-12-31</td>\n", "      <td>japan</td>\n", "      <td>137.815</td>\n", "      <td>4054.377</td>\n", "      <td>0.033992</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-12-31</td>\n", "      <td>others</td>\n", "      <td>289.275</td>\n", "      <td>4054.377</td>\n", "      <td>0.071349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2020-12-31</td>\n", "      <td>thailand</td>\n", "      <td>0.000</td>\n", "      <td>4054.377</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2021-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>717.405</td>\n", "      <td>5336.308</td>\n", "      <td>0.134438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2021-12-31</td>\n", "      <td>US</td>\n", "      <td>3988.288</td>\n", "      <td>5336.308</td>\n", "      <td>0.747387</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2021-12-31</td>\n", "      <td>australia and new zealand</td>\n", "      <td>138.036</td>\n", "      <td>5336.308</td>\n", "      <td>0.025867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2021-12-31</td>\n", "      <td>japan</td>\n", "      <td>150.253</td>\n", "      <td>5336.308</td>\n", "      <td>0.028157</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2021-12-31</td>\n", "      <td>others</td>\n", "      <td>342.326</td>\n", "      <td>5336.308</td>\n", "      <td>0.064150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2021-12-31</td>\n", "      <td>thailand</td>\n", "      <td>0.000</td>\n", "      <td>5336.308</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2022-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>699.144</td>\n", "      <td>5755.130</td>\n", "      <td>0.121482</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2022-12-31</td>\n", "      <td>US</td>\n", "      <td>4317.708</td>\n", "      <td>5755.130</td>\n", "      <td>0.750236</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2022-12-31</td>\n", "      <td>australia and new zealand</td>\n", "      <td>147.551</td>\n", "      <td>5755.130</td>\n", "      <td>0.025638</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2022-12-31</td>\n", "      <td>japan</td>\n", "      <td>175.292</td>\n", "      <td>5755.130</td>\n", "      <td>0.030458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2022-12-31</td>\n", "      <td>others</td>\n", "      <td>415.435</td>\n", "      <td>5755.130</td>\n", "      <td>0.072185</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2022-12-31</td>\n", "      <td>thailand</td>\n", "      <td>0.000</td>\n", "      <td>5755.130</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-12-31</td>\n", "      <td>EMEA</td>\n", "      <td>644.835</td>\n", "      <td>5836.478</td>\n", "      <td>0.110484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2023-12-31</td>\n", "      <td>US</td>\n", "      <td>4481.846</td>\n", "      <td>5836.478</td>\n", "      <td>0.767902</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date        standardized_region     sales  ff_sales  \\\n", "0  2020-12-31                       EMEA   598.243  4054.377   \n", "1  2020-12-31                         US  2921.153  4054.377   \n", "2  2020-12-31  australia and new zealand   107.891  4054.377   \n", "3  2020-12-31                      japan   137.815  4054.377   \n", "4  2020-12-31                     others   289.275  4054.377   \n", "5  2020-12-31                   thailand     0.000  4054.377   \n", "6  2021-12-31                       EMEA   717.405  5336.308   \n", "7  2021-12-31                         US  3988.288  5336.308   \n", "8  2021-12-31  australia and new zealand   138.036  5336.308   \n", "9  2021-12-31                      japan   150.253  5336.308   \n", "10 2021-12-31                     others   342.326  5336.308   \n", "11 2021-12-31                   thailand     0.000  5336.308   \n", "12 2022-12-31                       EMEA   699.144  5755.130   \n", "13 2022-12-31                         US  4317.708  5755.130   \n", "14 2022-12-31  australia and new zealand   147.551  5755.130   \n", "15 2022-12-31                      japan   175.292  5755.130   \n", "16 2022-12-31                     others   415.435  5755.130   \n", "17 2022-12-31                   thailand     0.000  5755.130   \n", "18 2023-12-31                       EMEA   644.835  5836.478   \n", "19 2023-12-31                         US  4481.846  5836.478   \n", "\n", "    sales_percnt_of_ff  \n", "0             0.147555  \n", "1             0.720494  \n", "2             0.026611  \n", "3             0.033992  \n", "4             0.071349  \n", "5             0.000000  \n", "6             0.134438  \n", "7             0.747387  \n", "8             0.025867  \n", "9             0.028157  \n", "10            0.064150  \n", "11            0.000000  \n", "12            0.121482  \n", "13            0.750236  \n", "14            0.025638  \n", "15            0.030458  \n", "16            0.072185  \n", "17            0.000000  \n", "18            0.110484  \n", "19            0.767902  "]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_4.head(20)"]}, {"cell_type": "code", "execution_count": 76, "id": "e3c9d80a", "metadata": {}, "outputs": [], "source": ["harley_df = pd.merge(\n", "    harley_df,\n", "    merged_4[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "eb110bd2", "metadata": {}, "source": ["### Tesla, Inc."]}, {"cell_type": "code", "execution_count": 77, "id": "a27e8223", "metadata": {}, "outputs": [], "source": ["Tesla_df = region_df[region_df['entity_proper_name'] == 'Tesla, Inc.']"]}, {"cell_type": "code", "execution_count": 78, "id": "1f08e3f1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>fsym_id</th>\n", "      <th>date</th>\n", "      <th>ff_segment_type</th>\n", "      <th>ff_segment_num</th>\n", "      <th>adjdate_x</th>\n", "      <th>currency_x</th>\n", "      <th>label</th>\n", "      <th>sales</th>\n", "      <th>opinc</th>\n", "      <th>assets</th>\n", "      <th>...</th>\n", "      <th>ff_assets_nonperf</th>\n", "      <th>ff_dps_all</th>\n", "      <th>ff_bk_com_eq_tier1_tot</th>\n", "      <th>ff_misc_net_oth</th>\n", "      <th>ff_cap_lease_curr</th>\n", "      <th>ff_curr_ins_ben</th>\n", "      <th>ff_intang_devt</th>\n", "      <th>ff_net_inc_aft_pfd</th>\n", "      <th>ff_oper_lease_repay</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>2020-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>2022-08-25</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>15207.0</td>\n", "      <td>-198.0</td>\n", "      <td>15989.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>-31</td>\n", "      <td>415.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>690.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>2020-12-31</td>\n", "      <td>REG</td>\n", "      <td>2</td>\n", "      <td>2022-08-25</td>\n", "      <td>USD</td>\n", "      <td>China</td>\n", "      <td>6662.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>-31</td>\n", "      <td>415.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>690.0</td>\n", "      <td>NaN</td>\n", "      <td>china</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>2020-12-31</td>\n", "      <td>REG</td>\n", "      <td>3</td>\n", "      <td>2022-08-25</td>\n", "      <td>USD</td>\n", "      <td>Reconciling Items</td>\n", "      <td>0.0</td>\n", "      <td>141.0</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>-31</td>\n", "      <td>415.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>690.0</td>\n", "      <td>NaN</td>\n", "      <td>reconciling items</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>2020-12-31</td>\n", "      <td>REG</td>\n", "      <td>4</td>\n", "      <td>2022-08-25</td>\n", "      <td>USD</td>\n", "      <td>Other International</td>\n", "      <td>9667.0</td>\n", "      <td>1211.0</td>\n", "      <td>2737.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>-31</td>\n", "      <td>415.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>690.0</td>\n", "      <td>NaN</td>\n", "      <td>other international</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>2021-12-31</td>\n", "      <td>REG</td>\n", "      <td>1</td>\n", "      <td>2022-08-25</td>\n", "      <td>USD</td>\n", "      <td>United States</td>\n", "      <td>23973.0</td>\n", "      <td>-130.0</td>\n", "      <td>19026.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>501.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5524.0</td>\n", "      <td>NaN</td>\n", "      <td>US</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 155 columns</p>\n", "</div>"], "text/plain": ["     fsym_id       date ff_segment_type  ff_segment_num  adjdate_x currency_x  \\\n", "84  Q2YN1N-R 2020-12-31             REG               1 2022-08-25        USD   \n", "85  Q2YN1N-R 2020-12-31             REG               2 2022-08-25        USD   \n", "86  Q2YN1N-R 2020-12-31             REG               3 2022-08-25        USD   \n", "87  Q2YN1N-R 2020-12-31             REG               4 2022-08-25        USD   \n", "88  Q2YN1N-R 2021-12-31             REG               1 2022-08-25        USD   \n", "\n", "                  label    sales   opinc   assets  ...  ff_assets_nonperf  \\\n", "84        United States  15207.0  -198.0  15989.0  ...                NaN   \n", "85                China   6662.0     NaN      NaN  ...                NaN   \n", "86    Reconciling Items      0.0   141.0      NaN  ...                NaN   \n", "87  Other International   9667.0  1211.0   2737.0  ...                NaN   \n", "88        United States  23973.0  -130.0  19026.0  ...                NaN   \n", "\n", "    ff_dps_all ff_bk_com_eq_tier1_tot ff_misc_net_oth ff_cap_lease_curr  \\\n", "84         0.0                    NaN             -31             415.0   \n", "85         0.0                    NaN             -31             415.0   \n", "86         0.0                    NaN             -31             415.0   \n", "87         0.0                    NaN             -31             415.0   \n", "88         0.0                    NaN               5             501.0   \n", "\n", "   ff_curr_ins_ben  ff_intang_devt ff_net_inc_aft_pfd  ff_oper_lease_repay  \\\n", "84             NaN             NaN              690.0                  NaN   \n", "85             NaN             NaN              690.0                  NaN   \n", "86             NaN             NaN              690.0                  NaN   \n", "87             NaN             NaN              690.0                  NaN   \n", "88             NaN             NaN             5524.0                  NaN   \n", "\n", "    standardized_region  \n", "84                   US  \n", "85                china  \n", "86    reconciling items  \n", "87  other international  \n", "88                   US  \n", "\n", "[5 rows x 155 columns]"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["Tesla_df.head()"]}, {"cell_type": "code", "execution_count": 103, "id": "c57ec3b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-31</td>\n", "      <td>China</td>\n", "      <td>china</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-12-31</td>\n", "      <td>Reconciling Items</td>\n", "      <td>reconciling items</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-12-31</td>\n", "      <td>Other International</td>\n", "      <td>other international</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2021-12-31</td>\n", "      <td>China</td>\n", "      <td>china</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2021-12-31</td>\n", "      <td>Reconciling Items</td>\n", "      <td>reconciling items</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2021-12-31</td>\n", "      <td>Germany</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2021-12-31</td>\n", "      <td>Other International</td>\n", "      <td>other international</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2022-12-31</td>\n", "      <td>China</td>\n", "      <td>china</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2022-12-31</td>\n", "      <td>Reconciling Items</td>\n", "      <td>reconciling items</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2022-12-31</td>\n", "      <td>Germany</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2022-12-31</td>\n", "      <td>Other International</td>\n", "      <td>other international</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-12-31</td>\n", "      <td>China</td>\n", "      <td>china</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-12-31</td>\n", "      <td>Reconciling Items</td>\n", "      <td>reconciling items</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-12-31</td>\n", "      <td>Germany</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-12-31</td>\n", "      <td>Other International</td>\n", "      <td>other international</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-12-31</td>\n", "      <td>China</td>\n", "      <td>china</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-12-31</td>\n", "      <td>Reconciling Items</td>\n", "      <td>reconciling items</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-12-31</td>\n", "      <td>Germany</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-12-31</td>\n", "      <td>Other International</td>\n", "      <td>other international</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date                label  standardized_region\n", "0  2020-12-31        United States                   US\n", "1  2020-12-31                China                china\n", "2  2020-12-31    Reconciling Items    reconciling items\n", "3  2020-12-31  Other International  other international\n", "4  2021-12-31        United States                   US\n", "5  2021-12-31                China                china\n", "6  2021-12-31    Reconciling Items    reconciling items\n", "7  2021-12-31              Germany               Europe\n", "8  2021-12-31  Other International  other international\n", "9  2022-12-31        United States                   US\n", "10 2022-12-31                China                china\n", "11 2022-12-31    Reconciling Items    reconciling items\n", "12 2022-12-31              Germany               Europe\n", "13 2022-12-31  Other International  other international\n", "14 2023-12-31        United States                   US\n", "15 2023-12-31                China                china\n", "16 2023-12-31    Reconciling Items    reconciling items\n", "17 2023-12-31              Germany               Europe\n", "18 2023-12-31  Other International  other international\n", "19 2024-12-31        United States                   US\n", "20 2024-12-31                China                china\n", "21 2024-12-31    Reconciling Items    reconciling items\n", "22 2024-12-31              Germany               Europe\n", "23 2024-12-31  Other International  other international"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["Tesla_df[['date','label','standardized_region']].head(25)"]}, {"cell_type": "code", "execution_count": 80, "id": "83466449", "metadata": {}, "outputs": [], "source": ["region_sales_sum_5 = Tesla_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n", "ff_sales_per_date_5 = Tesla_df[['date', 'ff_sales']].drop_duplicates()\n", "merged_5 = pd.merge(region_sales_sum_5, ff_sales_per_date_5, on='date', how='left')\n", "merged_5['sales_percnt_of_ff'] = (merged_5['sales'] / merged_5['ff_sales'])"]}, {"cell_type": "code", "execution_count": 102, "id": "5b309dbc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "      <th>ff_sales</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>US</td>\n", "      <td>15207.0</td>\n", "      <td>31536.0</td>\n", "      <td>0.482211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-31</td>\n", "      <td>china</td>\n", "      <td>6662.0</td>\n", "      <td>31536.0</td>\n", "      <td>0.211251</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-12-31</td>\n", "      <td>other international</td>\n", "      <td>9667.0</td>\n", "      <td>31536.0</td>\n", "      <td>0.306539</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-12-31</td>\n", "      <td>reconciling items</td>\n", "      <td>0.0</td>\n", "      <td>31536.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-12-31</td>\n", "      <td>Europe</td>\n", "      <td>0.0</td>\n", "      <td>53823.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2021-12-31</td>\n", "      <td>US</td>\n", "      <td>23973.0</td>\n", "      <td>53823.0</td>\n", "      <td>0.445404</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2021-12-31</td>\n", "      <td>china</td>\n", "      <td>13844.0</td>\n", "      <td>53823.0</td>\n", "      <td>0.257213</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2021-12-31</td>\n", "      <td>other international</td>\n", "      <td>16006.0</td>\n", "      <td>53823.0</td>\n", "      <td>0.297382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2021-12-31</td>\n", "      <td>reconciling items</td>\n", "      <td>0.0</td>\n", "      <td>53823.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2022-12-31</td>\n", "      <td>Europe</td>\n", "      <td>0.0</td>\n", "      <td>81462.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2022-12-31</td>\n", "      <td>US</td>\n", "      <td>40553.0</td>\n", "      <td>81462.0</td>\n", "      <td>0.497815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2022-12-31</td>\n", "      <td>china</td>\n", "      <td>18145.0</td>\n", "      <td>81462.0</td>\n", "      <td>0.222742</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2022-12-31</td>\n", "      <td>other international</td>\n", "      <td>22764.0</td>\n", "      <td>81462.0</td>\n", "      <td>0.279443</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2022-12-31</td>\n", "      <td>reconciling items</td>\n", "      <td>0.0</td>\n", "      <td>81462.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-12-31</td>\n", "      <td>Europe</td>\n", "      <td>0.0</td>\n", "      <td>96773.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-12-31</td>\n", "      <td>US</td>\n", "      <td>45235.0</td>\n", "      <td>96773.0</td>\n", "      <td>0.467434</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-12-31</td>\n", "      <td>china</td>\n", "      <td>21745.0</td>\n", "      <td>96773.0</td>\n", "      <td>0.224701</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-12-31</td>\n", "      <td>other international</td>\n", "      <td>29793.0</td>\n", "      <td>96773.0</td>\n", "      <td>0.307865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-12-31</td>\n", "      <td>reconciling items</td>\n", "      <td>0.0</td>\n", "      <td>96773.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-12-31</td>\n", "      <td>Europe</td>\n", "      <td>0.0</td>\n", "      <td>97690.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-12-31</td>\n", "      <td>US</td>\n", "      <td>47725.0</td>\n", "      <td>97690.0</td>\n", "      <td>0.488535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-12-31</td>\n", "      <td>china</td>\n", "      <td>20944.0</td>\n", "      <td>97690.0</td>\n", "      <td>0.214392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-12-31</td>\n", "      <td>other international</td>\n", "      <td>29021.0</td>\n", "      <td>97690.0</td>\n", "      <td>0.297072</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-12-31</td>\n", "      <td>reconciling items</td>\n", "      <td>0.0</td>\n", "      <td>97690.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date  standardized_region    sales  ff_sales  sales_percnt_of_ff\n", "0  2020-12-31                   US  15207.0   31536.0            0.482211\n", "1  2020-12-31                china   6662.0   31536.0            0.211251\n", "2  2020-12-31  other international   9667.0   31536.0            0.306539\n", "3  2020-12-31    reconciling items      0.0   31536.0            0.000000\n", "4  2021-12-31               Europe      0.0   53823.0            0.000000\n", "5  2021-12-31                   US  23973.0   53823.0            0.445404\n", "6  2021-12-31                china  13844.0   53823.0            0.257213\n", "7  2021-12-31  other international  16006.0   53823.0            0.297382\n", "8  2021-12-31    reconciling items      0.0   53823.0            0.000000\n", "9  2022-12-31               Europe      0.0   81462.0            0.000000\n", "10 2022-12-31                   US  40553.0   81462.0            0.497815\n", "11 2022-12-31                china  18145.0   81462.0            0.222742\n", "12 2022-12-31  other international  22764.0   81462.0            0.279443\n", "13 2022-12-31    reconciling items      0.0   81462.0            0.000000\n", "14 2023-12-31               Europe      0.0   96773.0            0.000000\n", "15 2023-12-31                   US  45235.0   96773.0            0.467434\n", "16 2023-12-31                china  21745.0   96773.0            0.224701\n", "17 2023-12-31  other international  29793.0   96773.0            0.307865\n", "18 2023-12-31    reconciling items      0.0   96773.0            0.000000\n", "19 2024-12-31               Europe      0.0   97690.0            0.000000\n", "20 2024-12-31                   US  47725.0   97690.0            0.488535\n", "21 2024-12-31                china  20944.0   97690.0            0.214392\n", "22 2024-12-31  other international  29021.0   97690.0            0.297072\n", "23 2024-12-31    reconciling items      0.0   97690.0            0.000000"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_5.head(25)"]}, {"cell_type": "code", "execution_count": 83, "id": "332da0f1", "metadata": {}, "outputs": [], "source": ["Tesla_df = pd.merge(\n", "    Tesla_df,\n", "    merged_5[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "240d1c74", "metadata": {}, "source": ["### Lucid Group, Inc."]}, {"cell_type": "code", "execution_count": 85, "id": "662f2fc4", "metadata": {}, "outputs": [], "source": ["Lucid_df = region_df[region_df['entity_proper_name'] == 'Lucid Group, Inc.']"]}, {"cell_type": "code", "execution_count": 87, "id": "8ba1940d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>2020-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109</th>\n", "      <td>2021-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>2024-12-31</td>\n", "      <td>Saudi Arabia</td>\n", "      <td>middle east</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>2024-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>2024-12-31</td>\n", "      <td>Middle East</td>\n", "      <td>middle east</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>2024-12-31</td>\n", "      <td>Other international</td>\n", "      <td>other international</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date                label  standardized_region\n", "108 2020-12-31        United States                   US\n", "109 2021-12-31        United States                   US\n", "110 2022-12-31        United States                   US\n", "111 2023-12-31        United States                   US\n", "112 2024-12-31        United States                   US\n", "113 2024-12-31         Saudi Arabia          middle east\n", "114 2024-12-31        North America                   US\n", "115 2024-12-31          Middle East          middle east\n", "116 2024-12-31  Other international  other international"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["Lucid_df[['date','label','standardized_region']].head(20)"]}, {"cell_type": "code", "execution_count": 88, "id": "36e5dc6d", "metadata": {}, "outputs": [], "source": ["region_sales_sum_6 = Lucid_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n", "ff_sales_per_date_6 = Lucid_df[['date', 'ff_sales']].drop_duplicates()\n", "merged_6 = pd.merge(region_sales_sum_6, ff_sales_per_date_6, on='date', how='left')\n", "merged_6['sales_percnt_of_ff'] = (merged_6['sales'] / merged_6['ff_sales'])"]}, {"cell_type": "code", "execution_count": 90, "id": "d96ce950", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "      <th>ff_sales</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>US</td>\n", "      <td>3.976</td>\n", "      <td>0.000</td>\n", "      <td>inf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-12-31</td>\n", "      <td>US</td>\n", "      <td>27.111</td>\n", "      <td>27.111</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-12-31</td>\n", "      <td>US</td>\n", "      <td>608.181</td>\n", "      <td>608.181</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-12-31</td>\n", "      <td>US</td>\n", "      <td>595.271</td>\n", "      <td>595.271</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-31</td>\n", "      <td>US</td>\n", "      <td>598.022</td>\n", "      <td>807.832</td>\n", "      <td>0.740280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-12-31</td>\n", "      <td>middle east</td>\n", "      <td>194.052</td>\n", "      <td>807.832</td>\n", "      <td>0.240213</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-12-31</td>\n", "      <td>other international</td>\n", "      <td>15.758</td>\n", "      <td>807.832</td>\n", "      <td>0.019507</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date  standardized_region    sales  ff_sales  sales_percnt_of_ff\n", "0 2020-12-31                   US    3.976     0.000                 inf\n", "1 2021-12-31                   US   27.111    27.111            1.000000\n", "2 2022-12-31                   US  608.181   608.181            1.000000\n", "3 2023-12-31                   US  595.271   595.271            1.000000\n", "4 2024-12-31                   US  598.022   807.832            0.740280\n", "5 2024-12-31          middle east  194.052   807.832            0.240213\n", "6 2024-12-31  other international   15.758   807.832            0.019507"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_6.head(20)"]}, {"cell_type": "code", "execution_count": 89, "id": "3d1a600c", "metadata": {}, "outputs": [], "source": ["Lucid_df = pd.merge(\n", "    Lucid_df,\n", "    merged_6[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "9bbfa36c", "metadata": {}, "source": ["### General Motors Co."]}, {"cell_type": "code", "execution_count": 91, "id": "adefc8fa", "metadata": {}, "outputs": [], "source": ["GM_df = region_df[region_df['entity_proper_name'] == 'General Motors Co.']"]}, {"cell_type": "code", "execution_count": 95, "id": "3ab56974", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>2020-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>2020-12-31</td>\n", "      <td>Non-United States</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>2021-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>2021-12-31</td>\n", "      <td>Non-United States</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>121</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>122</th>\n", "      <td>2022-12-31</td>\n", "      <td>Non-United States</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>2023-12-31</td>\n", "      <td>Non-United States</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>2024-12-31</td>\n", "      <td>Non-United States</td>\n", "      <td>others</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date              label standardized_region\n", "117 2020-12-31      United States                  US\n", "118 2020-12-31  Non-United States              others\n", "119 2021-12-31      United States                  US\n", "120 2021-12-31  Non-United States              others\n", "121 2022-12-31      United States                  US\n", "122 2022-12-31  Non-United States              others\n", "123 2023-12-31      United States                  US\n", "124 2023-12-31  Non-United States              others\n", "125 2024-12-31      United States                  US\n", "126 2024-12-31  Non-United States              others"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["GM_df[['date','label','standardized_region']].head(20)"]}, {"cell_type": "code", "execution_count": 93, "id": "93beee98", "metadata": {}, "outputs": [], "source": ["GM_df = GM_df.drop(columns=['standardized_region'])"]}, {"cell_type": "code", "execution_count": 94, "id": "a2b1cd59", "metadata": {}, "outputs": [], "source": ["GM_df['standardized_region'] = GM_df['label'].apply(get_standardized_region)"]}, {"cell_type": "code", "execution_count": 96, "id": "f02847f5", "metadata": {}, "outputs": [], "source": ["region_sales_sum_7 = GM_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n", "ff_sales_per_date_7 = GM_df[['date', 'ff_sales']].drop_duplicates()\n", "merged_7 = pd.merge(region_sales_sum_7, ff_sales_per_date_7, on='date', how='left')\n", "merged_7['sales_percnt_of_ff'] = (merged_7['sales'] / merged_7['ff_sales'])"]}, {"cell_type": "code", "execution_count": 97, "id": "623f3e75", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "      <th>ff_sales</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>US</td>\n", "      <td>101431.0</td>\n", "      <td>122485.0</td>\n", "      <td>0.828110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-31</td>\n", "      <td>others</td>\n", "      <td>21054.0</td>\n", "      <td>122485.0</td>\n", "      <td>0.171890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-12-31</td>\n", "      <td>US</td>\n", "      <td>104483.0</td>\n", "      <td>127004.0</td>\n", "      <td>0.822675</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-12-31</td>\n", "      <td>others</td>\n", "      <td>22521.0</td>\n", "      <td>127004.0</td>\n", "      <td>0.177325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-12-31</td>\n", "      <td>US</td>\n", "      <td>127833.0</td>\n", "      <td>156735.0</td>\n", "      <td>0.815600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2022-12-31</td>\n", "      <td>others</td>\n", "      <td>28902.0</td>\n", "      <td>156735.0</td>\n", "      <td>0.184400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-12-31</td>\n", "      <td>US</td>\n", "      <td>139605.0</td>\n", "      <td>171842.0</td>\n", "      <td>0.812403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-12-31</td>\n", "      <td>others</td>\n", "      <td>32237.0</td>\n", "      <td>171842.0</td>\n", "      <td>0.187597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-12-31</td>\n", "      <td>US</td>\n", "      <td>154111.0</td>\n", "      <td>187442.0</td>\n", "      <td>0.822180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-12-31</td>\n", "      <td>others</td>\n", "      <td>33331.0</td>\n", "      <td>187442.0</td>\n", "      <td>0.177820</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date standardized_region     sales  ff_sales  sales_percnt_of_ff\n", "0 2020-12-31                  US  101431.0  122485.0            0.828110\n", "1 2020-12-31              others   21054.0  122485.0            0.171890\n", "2 2021-12-31                  US  104483.0  127004.0            0.822675\n", "3 2021-12-31              others   22521.0  127004.0            0.177325\n", "4 2022-12-31                  US  127833.0  156735.0            0.815600\n", "5 2022-12-31              others   28902.0  156735.0            0.184400\n", "6 2023-12-31                  US  139605.0  171842.0            0.812403\n", "7 2023-12-31              others   32237.0  171842.0            0.187597\n", "8 2024-12-31                  US  154111.0  187442.0            0.822180\n", "9 2024-12-31              others   33331.0  187442.0            0.177820"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_7.head(20)"]}, {"cell_type": "code", "execution_count": 98, "id": "1f5c098d", "metadata": {}, "outputs": [], "source": ["GM_df = pd.merge(\n", "    GM_df,\n", "    merged_7[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "f30ea9db", "metadata": {}, "source": ["### 'Miller Industries, Inc. (Tennessee)'"]}, {"cell_type": "code", "execution_count": 99, "id": "873e3387", "metadata": {}, "outputs": [], "source": ["Miller_df = region_df[region_df['entity_proper_name'] == 'Miller Industries, Inc. (Tennessee)']"]}, {"cell_type": "code", "execution_count": 106, "id": "3032db5c", "metadata": {}, "outputs": [{"data": {"text/plain": ["15"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["len(Miller_df)"]}, {"cell_type": "code", "execution_count": 107, "id": "86647be0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>2020-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>2020-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>129</th>\n", "      <td>2020-12-31</td>\n", "      <td>Foreign</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>2021-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131</th>\n", "      <td>2021-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>132</th>\n", "      <td>2021-12-31</td>\n", "      <td>Foreign</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>2022-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>2022-12-31</td>\n", "      <td>Foreign</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>2023-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>2023-12-31</td>\n", "      <td>Foreign</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>2024-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>2024-12-31</td>\n", "      <td>Foreign</td>\n", "      <td>others</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date          label standardized_region\n", "127 2020-12-31  North America                  US\n", "128 2020-12-31  United States                  US\n", "129 2020-12-31        Foreign              others\n", "130 2021-12-31  North America                  US\n", "131 2021-12-31  United States                  US\n", "132 2021-12-31        Foreign              others\n", "133 2022-12-31  North America                  US\n", "134 2022-12-31  United States                  US\n", "135 2022-12-31        Foreign              others\n", "136 2023-12-31  North America                  US\n", "137 2023-12-31  United States                  US\n", "138 2023-12-31        Foreign              others\n", "139 2024-12-31  North America                  US\n", "140 2024-12-31  United States                  US\n", "141 2024-12-31        Foreign              others"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["Miller_df[['date','label','standardized_region']].head(20)"]}, {"cell_type": "code", "execution_count": 108, "id": "ed9ce3e0", "metadata": {}, "outputs": [], "source": ["region_sales_sum_8 = Miller_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n", "ff_sales_per_date_8 = Miller_df[['date', 'ff_sales']].drop_duplicates()\n", "merged_8 = pd.merge(region_sales_sum_8, ff_sales_per_date_8, on='date', how='left')\n", "merged_8['sales_percnt_of_ff'] = (merged_8['sales'] / merged_8['ff_sales'])"]}, {"cell_type": "code", "execution_count": 109, "id": "d929ba11", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "      <th>ff_sales</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>US</td>\n", "      <td>556.540</td>\n", "      <td>651.286</td>\n", "      <td>0.854525</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-31</td>\n", "      <td>others</td>\n", "      <td>94.746</td>\n", "      <td>651.286</td>\n", "      <td>0.145475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-12-31</td>\n", "      <td>US</td>\n", "      <td>627.573</td>\n", "      <td>717.476</td>\n", "      <td>0.874695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-12-31</td>\n", "      <td>others</td>\n", "      <td>89.903</td>\n", "      <td>717.476</td>\n", "      <td>0.125305</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-12-31</td>\n", "      <td>US</td>\n", "      <td>765.307</td>\n", "      <td>848.456</td>\n", "      <td>0.902000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2022-12-31</td>\n", "      <td>others</td>\n", "      <td>83.149</td>\n", "      <td>848.456</td>\n", "      <td>0.098000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-12-31</td>\n", "      <td>US</td>\n", "      <td>1038.964</td>\n", "      <td>1153.354</td>\n", "      <td>0.900820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-12-31</td>\n", "      <td>others</td>\n", "      <td>114.390</td>\n", "      <td>1153.354</td>\n", "      <td>0.099180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-12-31</td>\n", "      <td>US</td>\n", "      <td>1131.834</td>\n", "      <td>1257.500</td>\n", "      <td>0.900067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-12-31</td>\n", "      <td>others</td>\n", "      <td>125.666</td>\n", "      <td>1257.500</td>\n", "      <td>0.099933</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date standardized_region     sales  ff_sales  sales_percnt_of_ff\n", "0 2020-12-31                  US   556.540   651.286            0.854525\n", "1 2020-12-31              others    94.746   651.286            0.145475\n", "2 2021-12-31                  US   627.573   717.476            0.874695\n", "3 2021-12-31              others    89.903   717.476            0.125305\n", "4 2022-12-31                  US   765.307   848.456            0.902000\n", "5 2022-12-31              others    83.149   848.456            0.098000\n", "6 2023-12-31                  US  1038.964  1153.354            0.900820\n", "7 2023-12-31              others   114.390  1153.354            0.099180\n", "8 2024-12-31                  US  1131.834  1257.500            0.900067\n", "9 2024-12-31              others   125.666  1257.500            0.099933"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_8.head(20)"]}, {"cell_type": "code", "execution_count": 110, "id": "6ad8442a", "metadata": {}, "outputs": [], "source": ["Miller_df = pd.merge(\n", "    Miller_df,\n", "    merged_8[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "ff8887cb", "metadata": {}, "source": ["### 'Blue Bird Corp.'"]}, {"cell_type": "code", "execution_count": 111, "id": "ace0a7bb", "metadata": {}, "outputs": [], "source": ["BB_df = region_df[region_df['entity_proper_name'] == 'Blue Bird Corp.']"]}, {"cell_type": "code", "execution_count": 112, "id": "3f5b34bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["15"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["len(BB_df)"]}, {"cell_type": "code", "execution_count": 113, "id": "77600461", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>2020-09-30</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>2020-09-30</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>2020-09-30</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>2021-09-30</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>2021-09-30</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>2021-09-30</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>2022-09-30</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>2022-09-30</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>2022-09-30</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>2023-09-30</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>2023-09-30</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>2023-09-30</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>2024-09-30</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>2024-09-30</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>2024-09-30</td>\n", "      <td>Rest of World</td>\n", "      <td>rest of world</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date          label standardized_region\n", "142 2020-09-30  United States                  US\n", "143 2020-09-30         Canada                  US\n", "144 2020-09-30  Rest of World       rest of world\n", "145 2021-09-30  United States                  US\n", "146 2021-09-30         Canada                  US\n", "147 2021-09-30  Rest of World       rest of world\n", "148 2022-09-30  United States                  US\n", "149 2022-09-30         Canada                  US\n", "150 2022-09-30  Rest of World       rest of world\n", "151 2023-09-30  United States                  US\n", "152 2023-09-30         Canada                  US\n", "153 2023-09-30  Rest of World       rest of world\n", "154 2024-09-30  United States                  US\n", "155 2024-09-30         Canada                  US\n", "156 2024-09-30  Rest of World       rest of world"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["BB_df[['date','label','standardized_region']].head(20)"]}, {"cell_type": "code", "execution_count": 114, "id": "2b16975d", "metadata": {}, "outputs": [], "source": ["region_sales_sum_9 = BB_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n", "ff_sales_per_date_9 = BB_df[['date', 'ff_sales']].drop_duplicates()\n", "merged_9 = pd.merge(region_sales_sum_9, ff_sales_per_date_9, on='date', how='left')\n", "merged_9['sales_percnt_of_ff'] = (merged_9['sales'] / merged_9['ff_sales'])"]}, {"cell_type": "code", "execution_count": 115, "id": "7bc37416", "metadata": {}, "outputs": [], "source": ["BB_df = pd.merge(\n", "    BB_df,\n", "    merged_9[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "cd2bd762", "metadata": {}, "source": ["### 'Ford Motor Co.'"]}, {"cell_type": "code", "execution_count": 116, "id": "2e0e14f4", "metadata": {}, "outputs": [], "source": ["Ford_df = region_df[region_df['entity_proper_name'] == 'Ford Motor Co.']"]}, {"cell_type": "code", "execution_count": 117, "id": "6d5348f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["49"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["len(Ford_df)"]}, {"cell_type": "code", "execution_count": 123, "id": "4c00a1e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>label</th>\n", "      <th>standardized_region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>2020-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>2020-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>2020-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>2020-12-31</td>\n", "      <td>Germany</td>\n", "      <td>germany</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>2020-12-31</td>\n", "      <td>United Kingdom</td>\n", "      <td>united kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>2020-12-31</td>\n", "      <td>Mexico</td>\n", "      <td>mexico</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>2020-12-31</td>\n", "      <td>Europe</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>2020-12-31</td>\n", "      <td>South America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>2020-12-31</td>\n", "      <td>China (including Taiwan)</td>\n", "      <td>china (including taiwan)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>166</th>\n", "      <td>2020-12-31</td>\n", "      <td>Global</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>167</th>\n", "      <td>2020-12-31</td>\n", "      <td>Corporate Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>2020-12-31</td>\n", "      <td>International Markets Group</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>2020-12-31</td>\n", "      <td>All Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>2021-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>2021-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>172</th>\n", "      <td>2021-12-31</td>\n", "      <td>United Kingdom</td>\n", "      <td>united kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>2021-12-31</td>\n", "      <td>Germany</td>\n", "      <td>germany</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>2021-12-31</td>\n", "      <td>Mexico</td>\n", "      <td>mexico</td>\n", "    </tr>\n", "    <tr>\n", "      <th>175</th>\n", "      <td>2021-12-31</td>\n", "      <td>Europe</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>2021-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>2021-12-31</td>\n", "      <td>South America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>2021-12-31</td>\n", "      <td>China (including Taiwan)</td>\n", "      <td>china (including taiwan)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>2021-12-31</td>\n", "      <td>Global</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>2021-12-31</td>\n", "      <td>Corporate Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>2021-12-31</td>\n", "      <td>International Markets Group</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>2021-12-31</td>\n", "      <td>All Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>2022-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>2022-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>2022-12-31</td>\n", "      <td>United Kingdom</td>\n", "      <td>united kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>2022-12-31</td>\n", "      <td>Germany</td>\n", "      <td>germany</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>2022-12-31</td>\n", "      <td>Mexico</td>\n", "      <td>mexico</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2022-12-31</td>\n", "      <td>Europe</td>\n", "      <td>Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>2022-12-31</td>\n", "      <td>North America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>2022-12-31</td>\n", "      <td>South America</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>2022-12-31</td>\n", "      <td>China (including Taiwan)</td>\n", "      <td>china (including taiwan)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>2022-12-31</td>\n", "      <td>Global</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>2022-12-31</td>\n", "      <td>Corporate Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>2022-12-31</td>\n", "      <td>International Markets Group</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>2022-12-31</td>\n", "      <td>All Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>2023-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>2023-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>2023-12-31</td>\n", "      <td>United Kingdom</td>\n", "      <td>united kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>2023-12-31</td>\n", "      <td>Mexico</td>\n", "      <td>mexico</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>2023-12-31</td>\n", "      <td>All Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>2024-12-31</td>\n", "      <td>United States</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>2024-12-31</td>\n", "      <td>Canada</td>\n", "      <td>US</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>2024-12-31</td>\n", "      <td>United Kingdom</td>\n", "      <td>united kingdom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>2024-12-31</td>\n", "      <td>Mexico</td>\n", "      <td>mexico</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>2024-12-31</td>\n", "      <td>All Other</td>\n", "      <td>others</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date                        label       standardized_region\n", "157 2020-12-31                United States                        US\n", "158 2020-12-31                North America                        US\n", "159 2020-12-31                       Canada                        US\n", "160 2020-12-31                      Germany                   germany\n", "161 2020-12-31               United Kingdom            united kingdom\n", "162 2020-12-31                       Mexico                    mexico\n", "163 2020-12-31                       Europe                    Europe\n", "164 2020-12-31                South America                        US\n", "165 2020-12-31     China (including Taiwan)  china (including taiwan)\n", "166 2020-12-31                       Global                    others\n", "167 2020-12-31              Corporate Other                    others\n", "168 2020-12-31  International Markets Group                    others\n", "169 2020-12-31                    All Other                    others\n", "170 2021-12-31                United States                        US\n", "171 2021-12-31                       Canada                        US\n", "172 2021-12-31               United Kingdom            united kingdom\n", "173 2021-12-31                      Germany                   germany\n", "174 2021-12-31                       Mexico                    mexico\n", "175 2021-12-31                       Europe                    Europe\n", "176 2021-12-31                North America                        US\n", "177 2021-12-31                South America                        US\n", "178 2021-12-31     China (including Taiwan)  china (including taiwan)\n", "179 2021-12-31                       Global                    others\n", "180 2021-12-31              Corporate Other                    others\n", "181 2021-12-31  International Markets Group                    others\n", "182 2021-12-31                    All Other                    others\n", "183 2022-12-31                United States                        US\n", "184 2022-12-31                       Canada                        US\n", "185 2022-12-31               United Kingdom            united kingdom\n", "186 2022-12-31                      Germany                   germany\n", "187 2022-12-31                       Mexico                    mexico\n", "188 2022-12-31                       Europe                    Europe\n", "189 2022-12-31                North America                        US\n", "190 2022-12-31                South America                        US\n", "191 2022-12-31     China (including Taiwan)  china (including taiwan)\n", "192 2022-12-31                       Global                    others\n", "193 2022-12-31              Corporate Other                    others\n", "194 2022-12-31  International Markets Group                    others\n", "195 2022-12-31                    All Other                    others\n", "196 2023-12-31                United States                        US\n", "197 2023-12-31                       Canada                        US\n", "198 2023-12-31               United Kingdom            united kingdom\n", "199 2023-12-31                       Mexico                    mexico\n", "200 2023-12-31                    All Other                    others\n", "201 2024-12-31                United States                        US\n", "202 2024-12-31                       Canada                        US\n", "203 2024-12-31               United Kingdom            united kingdom\n", "204 2024-12-31                       Mexico                    mexico\n", "205 2024-12-31                    All Other                    others"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["Ford_df[['date','label','standardized_region']].head(50)"]}, {"cell_type": "code", "execution_count": 119, "id": "a9208e78", "metadata": {}, "outputs": [], "source": ["standard_region_dict_2 = {\n", "    \"US\": ['united states and canada', 'united states', 'north america', 'other north america', 'canada', 'south america'],\n", "    \"Europe\": ['netherlands', 'europe'],\n", "    \"EMEA\": ['europe, africa and middle east', 'emea', 'europe/africa', ],\n", "    \"middle east\":['saudi arabia', 'middle east'],\n", "    \"others\": ['rest of the world', 'other countries','other intrnational', 'corporate other','non-united states','foreign', 'all other', 'international markets group','global'],\n", "    \"china\": ['china', 'china(including taiwan)']\n", "\n", "}"]}, {"cell_type": "code", "execution_count": 120, "id": "463c01b7", "metadata": {}, "outputs": [], "source": ["def get_standardized_region_2(label):\n", "    label_lower = label.lower()\n", "    for standard_region, keywords in standard_region_dict_2.items():\n", "        if any(keyword == label_lower for keyword in keywords):\n", "            return standard_region\n", "    return label_lower"]}, {"cell_type": "code", "execution_count": 121, "id": "911c3146", "metadata": {}, "outputs": [], "source": ["Ford_df = Ford_df.drop(columns=['standardized_region'])"]}, {"cell_type": "code", "execution_count": 122, "id": "6ec66ca6", "metadata": {}, "outputs": [], "source": ["Ford_df['standardized_region'] = Ford_df['label'].apply(get_standardized_region_2)"]}, {"cell_type": "code", "execution_count": 124, "id": "b3fcaeda", "metadata": {}, "outputs": [], "source": ["region_sales_sum_10 = Ford_df.groupby(['date', 'standardized_region'])['sales'].sum().reset_index()\n", "ff_sales_per_date_10 = Ford_df[['date', 'ff_sales']].drop_duplicates()\n", "merged_10 = pd.merge(region_sales_sum_10, ff_sales_per_date_10, on='date', how='left')\n", "merged_10['sales_percnt_of_ff'] = (merged_10['sales'] / merged_10['ff_sales'])"]}, {"cell_type": "code", "execution_count": 126, "id": "9253d95f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>standardized_region</th>\n", "      <th>sales</th>\n", "      <th>ff_sales</th>\n", "      <th>sales_percnt_of_ff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-12-31</td>\n", "      <td>Europe</td>\n", "      <td>0.0</td>\n", "      <td>127144.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-31</td>\n", "      <td>US</td>\n", "      <td>91246.0</td>\n", "      <td>127144.0</td>\n", "      <td>0.717659</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-12-31</td>\n", "      <td>china (including taiwan)</td>\n", "      <td>0.0</td>\n", "      <td>127144.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-12-31</td>\n", "      <td>germany</td>\n", "      <td>6526.0</td>\n", "      <td>127144.0</td>\n", "      <td>0.051328</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-12-31</td>\n", "      <td>mexico</td>\n", "      <td>1030.0</td>\n", "      <td>127144.0</td>\n", "      <td>0.008101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2020-12-31</td>\n", "      <td>others</td>\n", "      <td>22232.0</td>\n", "      <td>127144.0</td>\n", "      <td>0.174857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2020-12-31</td>\n", "      <td>united kingdom</td>\n", "      <td>6110.0</td>\n", "      <td>127144.0</td>\n", "      <td>0.048056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2021-12-31</td>\n", "      <td>Europe</td>\n", "      <td>0.0</td>\n", "      <td>136341.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2021-12-31</td>\n", "      <td>US</td>\n", "      <td>98165.0</td>\n", "      <td>136341.0</td>\n", "      <td>0.719996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2021-12-31</td>\n", "      <td>china (including taiwan)</td>\n", "      <td>0.0</td>\n", "      <td>136341.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2021-12-31</td>\n", "      <td>germany</td>\n", "      <td>6237.0</td>\n", "      <td>136341.0</td>\n", "      <td>0.045746</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2021-12-31</td>\n", "      <td>mexico</td>\n", "      <td>1440.0</td>\n", "      <td>136341.0</td>\n", "      <td>0.010562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2021-12-31</td>\n", "      <td>others</td>\n", "      <td>22892.0</td>\n", "      <td>136341.0</td>\n", "      <td>0.167903</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2021-12-31</td>\n", "      <td>united kingdom</td>\n", "      <td>7607.0</td>\n", "      <td>136341.0</td>\n", "      <td>0.055794</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2022-12-31</td>\n", "      <td>Europe</td>\n", "      <td>0.0</td>\n", "      <td>158057.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2022-12-31</td>\n", "      <td>US</td>\n", "      <td>118071.0</td>\n", "      <td>158057.0</td>\n", "      <td>0.747015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2022-12-31</td>\n", "      <td>china (including taiwan)</td>\n", "      <td>0.0</td>\n", "      <td>158057.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2022-12-31</td>\n", "      <td>germany</td>\n", "      <td>6471.0</td>\n", "      <td>158057.0</td>\n", "      <td>0.040941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2022-12-31</td>\n", "      <td>mexico</td>\n", "      <td>1813.0</td>\n", "      <td>158057.0</td>\n", "      <td>0.011471</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2022-12-31</td>\n", "      <td>others</td>\n", "      <td>23482.0</td>\n", "      <td>158057.0</td>\n", "      <td>0.148567</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date       standardized_region     sales  ff_sales  \\\n", "0  2020-12-31                    Europe       0.0  127144.0   \n", "1  2020-12-31                        US   91246.0  127144.0   \n", "2  2020-12-31  china (including taiwan)       0.0  127144.0   \n", "3  2020-12-31                   germany    6526.0  127144.0   \n", "4  2020-12-31                    mexico    1030.0  127144.0   \n", "5  2020-12-31                    others   22232.0  127144.0   \n", "6  2020-12-31            united kingdom    6110.0  127144.0   \n", "7  2021-12-31                    Europe       0.0  136341.0   \n", "8  2021-12-31                        US   98165.0  136341.0   \n", "9  2021-12-31  china (including taiwan)       0.0  136341.0   \n", "10 2021-12-31                   germany    6237.0  136341.0   \n", "11 2021-12-31                    mexico    1440.0  136341.0   \n", "12 2021-12-31                    others   22892.0  136341.0   \n", "13 2021-12-31            united kingdom    7607.0  136341.0   \n", "14 2022-12-31                    Europe       0.0  158057.0   \n", "15 2022-12-31                        US  118071.0  158057.0   \n", "16 2022-12-31  china (including taiwan)       0.0  158057.0   \n", "17 2022-12-31                   germany    6471.0  158057.0   \n", "18 2022-12-31                    mexico    1813.0  158057.0   \n", "19 2022-12-31                    others   23482.0  158057.0   \n", "\n", "    sales_percnt_of_ff  \n", "0             0.000000  \n", "1             0.717659  \n", "2             0.000000  \n", "3             0.051328  \n", "4             0.008101  \n", "5             0.174857  \n", "6             0.048056  \n", "7             0.000000  \n", "8             0.719996  \n", "9             0.000000  \n", "10            0.045746  \n", "11            0.010562  \n", "12            0.167903  \n", "13            0.055794  \n", "14            0.000000  \n", "15            0.747015  \n", "16            0.000000  \n", "17            0.040941  \n", "18            0.011471  \n", "19            0.148567  "]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_10.head(20)"]}, {"cell_type": "code", "execution_count": 125, "id": "2dae3a8a", "metadata": {}, "outputs": [], "source": ["Ford_df = pd.merge(\n", "    Ford_df,\n", "    merged_10[['date', 'standardized_region', 'sales_percnt_of_ff']],\n", "    on=['date', 'standardized_region'],\n", "    how='left'\n", ")"]}, {"cell_type": "markdown", "id": "c4632f92", "metadata": {}, "source": ["### Total data"]}, {"cell_type": "code", "execution_count": 127, "id": "cb4d77a6", "metadata": {}, "outputs": [], "source": ["combined_df = pd.concat([rev_group_df, rivian_df, oshkosh_df, harley_df, Tesla_df, Lucid_df, GM_df, Miller_df, BB_df, Ford_df], axis=0, ignore_index=True)\n"]}, {"cell_type": "code", "execution_count": 128, "id": "80e4eef5", "metadata": {}, "outputs": [{"data": {"text/plain": ["206"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["len(region_df)"]}, {"cell_type": "code", "execution_count": 129, "id": "586e4d09", "metadata": {}, "outputs": [{"data": {"text/plain": ["206"]}, "execution_count": 129, "metadata": {}, "output_type": "execute_result"}], "source": ["len(combined_df)"]}, {"cell_type": "code", "execution_count": 130, "id": "41ada9d7", "metadata": {}, "outputs": [], "source": ["combined_df.to_excel(\"mfg_region_sales.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}