{"cells": [{"cell_type": "markdown", "id": "2ffd85bb", "metadata": {}, "source": ["## Market quotes data"]}, {"cell_type": "code", "execution_count": 1, "id": "2d70904f", "metadata": {}, "outputs": [], "source": ["from kiteconnect import KiteConnect\n", "import requests"]}, {"cell_type": "code", "execution_count": 2, "id": "77649fc0", "metadata": {}, "outputs": [], "source": ["api_key = \"3q0r6l1fb689slhk\"\n", "access_token = \"l4N6qQrZd1wdOCF5lslocRpGfleSQGzL\""]}, {"cell_type": "code", "execution_count": 3, "id": "e0d53e75", "metadata": {}, "outputs": [], "source": ["\n", "kite = KiteConnect(api_key=api_key)\n", "kite.set_access_token(access_token)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "01568d72", "metadata": {}, "outputs": [], "source": ["try:\n", "    quote = kite.quote(\"NSE:INFY\") # Infosys market quote data\n", "except Exception as e:\n", "    print(f\"Error fetching quote: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f06877c1", "metadata": {}, "outputs": [], "source": ["quote"]}, {"cell_type": "code", "execution_count": 8, "id": "9c7c2909", "metadata": {}, "outputs": [], "source": ["infy_data = quote[\"NSE:INFY\"]"]}, {"cell_type": "code", "execution_count": null, "id": "7fd4d2e6", "metadata": {}, "outputs": [], "source": ["infy_data"]}, {"cell_type": "markdown", "id": "d60ee20a", "metadata": {}, "source": ["## options on infy for various strikes and expiry dates"]}, {"cell_type": "markdown", "id": "67e3c2bf", "metadata": {}, "source": ["### fetching instruments"]}, {"cell_type": "code", "execution_count": 4, "id": "9261e270", "metadata": {}, "outputs": [], "source": ["# Function to fetch instruments\n", "def get_instruments():\n", "    try:\n", "        # API endpoint for instruments\n", "        url = \"https://api.kite.trade/instruments\"\n", "        headers = {\n", "            \"X-Kite-Version\": \"3\",\n", "            \"Authorization\": f\"token {api_key}:{access_token}\"\n", "        }\n", "        \n", "        # Send request\n", "        response = requests.get(url, headers=headers)\n", "        \n", "        # Check if request was successful\n", "        if response.status_code == 200:\n", "            # Print first 100 bytes to check response\n", "            print(\"First 100 bytes of response:\", response.content[:100])\n", "            \n", "            # Save response to a file\n", "            with open(\"instruments.csv\", \"wb\") as file:\n", "                file.write(response.content)\n", "            print(\"Instrument list saved to instruments.csv\")\n", "            \n", "            # Check if response is gzipped\n", "            if response.content.startswith(b'\\x1f\\x8b'):\n", "                print(\"Response is gzipped\")\n", "            else:\n", "                print(\"Response is not gzipped\")\n", "                \n", "            return True\n", "        else:\n", "            print(\"Error: Status code\", response.status_code)\n", "            print(\"Response:\", response.text)\n", "            return False\n", "            \n", "    except Exception as e:\n", "        print(\"Error fetching instruments:\", str(e))\n", "        return False"]}, {"cell_type": "code", "execution_count": 5, "id": "3f65ba41", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 100 bytes of response: b'instrument_token,exchange_token,tradingsymbol,name,last_price,expiry,strike,tick_size,lot_size,instr'\n", "Instrument list saved to instruments.csv\n", "Response is not gzipped\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["get_instruments()"]}, {"cell_type": "code", "execution_count": 6, "id": "3ee118e5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "instruments_df = pd.read_csv(\"instruments.csv\")"]}, {"cell_type": "code", "execution_count": 7, "id": "e8a81053", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>*********</td>\n", "      <td>874879</td>\n", "      <td>BANKEX25MAYFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>*********</td>\n", "      <td>1100996</td>\n", "      <td>BANKEX25JUNFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-06-24</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>*********</td>\n", "      <td>1141118</td>\n", "      <td>BANKEX25JULFUT</td>\n", "      <td>BANKEX</td>\n", "      <td>0</td>\n", "      <td>2025-07-29</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>30</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>*********</td>\n", "      <td>1104009</td>\n", "      <td>SENSEX25520FUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-20</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>*********</td>\n", "      <td>874759</td>\n", "      <td>SENSEX25MAYFUT</td>\n", "      <td>SENSEX</td>\n", "      <td>0</td>\n", "      <td>2025-05-27</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>20</td>\n", "      <td>FUT</td>\n", "      <td>BFO-FUT</td>\n", "      <td>BFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_token  exchange_token   tradingsymbol    name  last_price  \\\n", "0         *********          874879  BANKEX25MAYFUT  BANKEX           0   \n", "1         *********         1100996  BANKEX25JUNFUT  BANKEX           0   \n", "2         *********         1141118  BANKEX25JULFUT  BANKEX           0   \n", "3         *********         1104009  SENSEX25520FUT  SENSEX           0   \n", "4         *********          874759  SENSEX25MAYFUT  SENSEX           0   \n", "\n", "       expiry  strike  tick_size  lot_size instrument_type  segment exchange  \n", "0  2025-05-27     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "1  2025-06-24     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "2  2025-07-29     0.0       0.05        30             FUT  BFO-FUT      BFO  \n", "3  2025-05-20     0.0       0.05        20             FUT  BFO-FUT      BFO  \n", "4  2025-05-27     0.0       0.05        20             FUT  BFO-FUT      BFO  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["instruments_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cbba7c26", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "\n", "instruments_df[\"expiry\"] = pd.to_datetime(instruments_df[\"expiry\"], errors=\"coerce\")\n", "\n", "# 1. Define date window\n", "today     = pd.Timestamp.today().normalize()\n", "one_month = today + pd.DateOffset(months=1)\n", "\n", "# 2. Build masks\n", "mask_symbol = instruments_df[\"tradingsymbol\"].str.startswith(\"INFY\", na=False)\n", "\n", "mask_optype = instruments_df[\"instrument_type\"].isin([\"CE\", \"PE\"])\n", "\n", "mask_expiry = (instruments_df[\"expiry\"] > today) & (instruments_df[\"expiry\"] <= one_month)\n", "\n", "infosys_opts = instruments_df[mask_symbol & mask_optype & mask_expiry]\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "id": "21ebb3c4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_token</th>\n", "      <th>exchange_token</th>\n", "      <th>tradingsymbol</th>\n", "      <th>name</th>\n", "      <th>last_price</th>\n", "      <th>expiry</th>\n", "      <th>strike</th>\n", "      <th>tick_size</th>\n", "      <th>lot_size</th>\n", "      <th>instrument_type</th>\n", "      <th>segment</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>66676</th>\n", "      <td>27076098</td>\n", "      <td>105766</td>\n", "      <td>INFY25MAY1580CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1580.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66677</th>\n", "      <td>27076354</td>\n", "      <td>105767</td>\n", "      <td>INFY25MAY1580PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1580.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66678</th>\n", "      <td>24356610</td>\n", "      <td>95143</td>\n", "      <td>INFY25MAY1560CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1560.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66679</th>\n", "      <td>24356866</td>\n", "      <td>95144</td>\n", "      <td>INFY25MAY1560PE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1560.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>PE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66680</th>\n", "      <td>24357122</td>\n", "      <td>95145</td>\n", "      <td>INFY25MAY1600CE</td>\n", "      <td>INFY</td>\n", "      <td>0</td>\n", "      <td>2025-05-29</td>\n", "      <td>1600.0</td>\n", "      <td>0.05</td>\n", "      <td>400</td>\n", "      <td>CE</td>\n", "      <td>NFO-OPT</td>\n", "      <td>NFO</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument_token  exchange_token    tradingsymbol  name  last_price  \\\n", "66676          27076098          105766  INFY25MAY1580CE  INFY           0   \n", "66677          27076354          105767  INFY25MAY1580PE  INFY           0   \n", "66678          24356610           95143  INFY25MAY1560CE  INFY           0   \n", "66679          24356866           95144  INFY25MAY1560PE  INFY           0   \n", "66680          24357122           95145  INFY25MAY1600CE  INFY           0   \n", "\n", "          expiry  strike  tick_size  lot_size instrument_type  segment  \\\n", "66676 2025-05-29  1580.0       0.05       400              CE  NFO-OPT   \n", "66677 2025-05-29  1580.0       0.05       400              PE  NFO-OPT   \n", "66678 2025-05-29  1560.0       0.05       400              CE  NFO-OPT   \n", "66679 2025-05-29  1560.0       0.05       400              PE  NFO-OPT   \n", "66680 2025-05-29  1600.0       0.05       400              CE  NFO-OPT   \n", "\n", "      exchange  \n", "66676      NFO  \n", "66677      NFO  \n", "66678      NFO  \n", "66679      NFO  \n", "66680      NFO  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["infosys_opts.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "3414f829", "metadata": {}, "outputs": [{"data": {"text/plain": ["70"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(infosys_opts)"]}, {"cell_type": "code", "execution_count": 19, "id": "12708b4c", "metadata": {}, "outputs": [], "source": ["instruments = infosys_opts.apply(\n", "    lambda row: f\"{row['exchange']}:{row['tradingsymbol']}\", axis=1\n", ").tolist()\n"]}, {"cell_type": "code", "execution_count": 21, "id": "ca8edff4", "metadata": {}, "outputs": [{"data": {"text/plain": ["70"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(instruments)"]}, {"cell_type": "code", "execution_count": 22, "id": "de952c09", "metadata": {}, "outputs": [], "source": ["quotes = kite.quote(*instruments)"]}, {"cell_type": "code", "execution_count": null, "id": "f8427c7a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'NFO:INFY25MAY1140CE': {'instrument_token': 27047426,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 40),\n", "  'last_trade_time': datetime.datetime(2025, 4, 24, 9, 46, 17),\n", "  'last_price': 339,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 27600,\n", "  'sell_quantity': 28800,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 400,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 353.4,\n", "  'upper_circuit_limit': 510.3,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 339},\n", "  'depth': {'buy': [{'price': 442.5, 'quantity': 1600, 'orders': 1},\n", "    {'price': 442.3, 'quantity': 2000, 'orders': 1},\n", "    {'price': 440.15, 'quantity': 4000, 'orders': 1},\n", "    {'price': 439.1, 'quantity': 4000, 'orders': 1},\n", "    {'price': 398.3, 'quantity': 16000, 'orders': 1}],\n", "   'sell': [{'price': 459.65, 'quantity': 1200, 'orders': 1},\n", "    {'price': 459.7, 'quantity': 1600, 'orders': 1},\n", "    {'price': 459.8, 'quantity': 4000, 'orders': 1},\n", "    {'price': 459.95, 'quantity': 2000, 'orders': 1},\n", "    {'price': 467.6, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1140PE': {'instrument_token': 27051010,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 6, 9),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 10, 20, 46),\n", "  'last_price': 0.4,\n", "  'last_quantity': 1600,\n", "  'buy_quantity': 166400,\n", "  'sell_quantity': 46800,\n", "  'volume': 3600,\n", "  'average_price': 0.4,\n", "  'oi': 254000,\n", "  'oi_day_high': 254400,\n", "  'oi_day_low': 254000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 20.55,\n", "  'ohlc': {'open': 0.55, 'high': 0.55, 'low': 0.3, 'close': 0.55},\n", "  'depth': {'buy': [{'price': 0.4, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.35, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.3, 'quantity': 5600, 'orders': 3},\n", "    {'price': 0.25, 'quantity': 22000, 'orders': 5},\n", "    {'price': 0.2, 'quantity': 16400, 'orders': 6}],\n", "   'sell': [{'price': 0.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.5, 'quantity': 9600, 'orders': 6},\n", "    {'price': 0.55, 'quantity': 5600, 'orders': 5},\n", "    {'price': 0.6, 'quantity': 4800, 'orders': 6},\n", "    {'price': 0.65, 'quantity': 2000, 'orders': 3}]}},\n", " 'NFO:INFY25MAY1160PE': {'instrument_token': 24351746,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 5, 53),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 41, 49),\n", "  'last_price': 0.45,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 166800,\n", "  'sell_quantity': 46800,\n", "  'volume': 10400,\n", "  'average_price': 0.5,\n", "  'oi': 187200,\n", "  'oi_day_high': 188000,\n", "  'oi_day_low': 186400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 20.5,\n", "  'ohlc': {'open': 0.55, 'high': 0.55, 'low': 0.4, 'close': 0.5},\n", "  'depth': {'buy': [{'price': 0.4, 'quantity': 800, 'orders': 1},\n", "    {'price': 0.35, 'quantity': 5600, 'orders': 5},\n", "    {'price': 0.3, 'quantity': 15200, 'orders': 8},\n", "    {'price': 0.25, 'quantity': 24800, 'orders': 10},\n", "    {'price': 0.2, 'quantity': 26400, 'orders': 7}],\n", "   'sell': [{'price': 0.5, 'quantity': 9200, 'orders': 3},\n", "    {'price': 0.55, 'quantity': 4000, 'orders': 7},\n", "    {'price': 0.6, 'quantity': 2800, 'orders': 7},\n", "    {'price': 0.65, 'quantity': 3600, 'orders': 8},\n", "    {'price': 0.7, 'quantity': 2000, 'orders': 5}]}},\n", " 'NFO:INFY25MAY1180PE': {'instrument_token': 27051522,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 8, 39),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 59, 32),\n", "  'last_price': 0.65,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 205200,\n", "  'sell_quantity': 22400,\n", "  'volume': 1200,\n", "  'average_price': 0.61,\n", "  'oi': 56800,\n", "  'oi_day_high': 57600,\n", "  'oi_day_low': 56800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 20.85,\n", "  'ohlc': {'open': 0.6, 'high': 0.65, 'low': 0.6, 'close': 0.85},\n", "  'depth': {'buy': [{'price': 0.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.5, 'quantity': 4400, 'orders': 2},\n", "    {'price': 0.45, 'quantity': 2400, 'orders': 2},\n", "    {'price': 0.4, 'quantity': 5200, 'orders': 3},\n", "    {'price': 0.35, 'quantity': 12400, 'orders': 2}],\n", "   'sell': [{'price': 0.65, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.7, 'quantity': 800, 'orders': 2},\n", "    {'price': 0.75, 'quantity': 1600, 'orders': 4},\n", "    {'price': 0.8, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.85, 'quantity': 2400, 'orders': 3}]}},\n", " 'NFO:INFY25MAY1200CE': {'instrument_token': 24352002,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 7, 12, 48, 39),\n", "  'last_price': 315,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 40400,\n", "  'sell_quantity': 32000,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 2000,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 293.55,\n", "  'upper_circuit_limit': 450.45,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 315},\n", "  'depth': {'buy': [{'price': 383.55, 'quantity': 12800, 'orders': 4},\n", "    {'price': 382.95, 'quantity': 1600, 'orders': 1},\n", "    {'price': 382.75, 'quantity': 2000, 'orders': 1},\n", "    {'price': 380.55, 'quantity': 4000, 'orders': 1},\n", "    {'price': 379.55, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 395.2, 'quantity': 4400, 'orders': 1},\n", "    {'price': 399.1, 'quantity': 2000, 'orders': 1},\n", "    {'price': 400.9, 'quantity': 4000, 'orders': 1},\n", "    {'price': 401.85, 'quantity': 1600, 'orders': 1},\n", "    {'price': 406.05, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1200PE': {'instrument_token': 24352258,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 43),\n", "  'last_price': 0.65,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 537600,\n", "  'sell_quantity': 90000,\n", "  'volume': 86400,\n", "  'average_price': 0.68,\n", "  'oi': 1312400,\n", "  'oi_day_high': 1338000,\n", "  'oi_day_low': 1311600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 20.9,\n", "  'ohlc': {'open': 0.75, 'high': 0.75, 'low': 0.6, 'close': 0.9},\n", "  'depth': {'buy': [{'price': 0.65, 'quantity': 16800, 'orders': 6},\n", "    {'price': 0.6, 'quantity': 23600, 'orders': 11},\n", "    {'price': 0.55, 'quantity': 43200, 'orders': 12},\n", "    {'price': 0.5, 'quantity': 20000, 'orders': 8},\n", "    {'price': 0.45, 'quantity': 14800, 'orders': 8}],\n", "   'sell': [{'price': 0.7, 'quantity': 4000, 'orders': 6},\n", "    {'price': 0.75, 'quantity': 6000, 'orders': 8},\n", "    {'price': 0.8, 'quantity': 4000, 'orders': 7},\n", "    {'price': 0.85, 'quantity': 2000, 'orders': 4},\n", "    {'price': 0.9, 'quantity': 16400, 'orders': 6}]}},\n", " 'NFO:INFY25MAY1220PE': {'instrument_token': 27052034,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 33),\n", "  'last_trade_time': datetime.datetime(2025, 5, 13, 15, 27, 39),\n", "  'last_price': 0.9,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 185600,\n", "  'sell_quantity': 42400,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 197200,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 20.9,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 0.9},\n", "  'depth': {'buy': [{'price': 0.5, 'quantity': 2800, 'orders': 5},\n", "    {'price': 0.45, 'quantity': 1200, 'orders': 2},\n", "    {'price': 0.4, 'quantity': 2000, 'orders': 3},\n", "    {'price': 0.35, 'quantity': 25600, 'orders': 4},\n", "    {'price': 0.3, 'quantity': 22000, 'orders': 5}],\n", "   'sell': [{'price': 0.7, 'quantity': 800, 'orders': 2},\n", "    {'price': 0.75, 'quantity': 800, 'orders': 2},\n", "    {'price': 0.8, 'quantity': 4400, 'orders': 4},\n", "    {'price': 0.9, 'quantity': 5200, 'orders': 2},\n", "    {'price': 0.95, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1240CE': {'instrument_token': 24352514,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 31),\n", "  'last_trade_time': datetime.datetime(2024, 9, 26, 11, 32, 47),\n", "  'last_price': 0,\n", "  'last_quantity': 250,\n", "  'buy_quantity': 28800,\n", "  'sell_quantity': 28800,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 253.7,\n", "  'upper_circuit_limit': 410.6,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 546.6},\n", "  'depth': {'buy': [{'price': 344, 'quantity': 1200, 'orders': 1},\n", "    {'price': 343.95, 'quantity': 1600, 'orders': 1},\n", "    {'price': 343.15, 'quantity': 2000, 'orders': 1},\n", "    {'price': 341.95, 'quantity': 4000, 'orders': 1},\n", "    {'price': 339.95, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 360.5, 'quantity': 1200, 'orders': 1},\n", "    {'price': 360.55, 'quantity': 2000, 'orders': 1},\n", "    {'price': 360.6, 'quantity': 4000, 'orders': 1},\n", "    {'price': 360.8, 'quantity': 1600, 'orders': 1},\n", "    {'price': 368.2, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1240PE': {'instrument_token': 24352770,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 5, 53),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 3, 22),\n", "  'last_price': 0.85,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 227600,\n", "  'sell_quantity': 60000,\n", "  'volume': 10000,\n", "  'average_price': 0.78,\n", "  'oi': 62800,\n", "  'oi_day_high': 66000,\n", "  'oi_day_low': 62800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.15,\n", "  'ohlc': {'open': 0.8, 'high': 0.85, 'low': 0.75, 'close': 1.15},\n", "  'depth': {'buy': [{'price': 0.75, 'quantity': 4400, 'orders': 4},\n", "    {'price': 0.7, 'quantity': 800, 'orders': 2},\n", "    {'price': 0.65, 'quantity': 5600, 'orders': 6},\n", "    {'price': 0.6, 'quantity': 2400, 'orders': 4},\n", "    {'price': 0.55, 'quantity': 2800, 'orders': 3}],\n", "   'sell': [{'price': 0.9, 'quantity': 800, 'orders': 2},\n", "    {'price': 0.95, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.1, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.15, 'quantity': 400, 'orders': 1},\n", "    {'price': 1.2, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1260CE': {'instrument_token': 27052290,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 41),\n", "  'last_trade_time': datetime.datetime(2025, 4, 22, 15, 27, 49),\n", "  'last_price': 178.05,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 28800,\n", "  'sell_quantity': 15200,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 800,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 233.75,\n", "  'upper_circuit_limit': 390.65,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 178.05},\n", "  'depth': {'buy': [{'price': 323.2, 'quantity': 1200, 'orders': 1},\n", "    {'price': 323.15, 'quantity': 2000, 'orders': 1},\n", "    {'price': 322.55, 'quantity': 1600, 'orders': 1},\n", "    {'price': 320.15, 'quantity': 4000, 'orders': 1},\n", "    {'price': 320, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 340.45, 'quantity': 1200, 'orders': 1},\n", "    {'price': 340.5, 'quantity': 1600, 'orders': 1},\n", "    {'price': 340.55, 'quantity': 2000, 'orders': 1},\n", "    {'price': 340.65, 'quantity': 4000, 'orders': 1},\n", "    {'price': 348.2, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1260PE': {'instrument_token': 27053058,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 3, 22),\n", "  'last_price': 0.85,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 260800,\n", "  'sell_quantity': 60000,\n", "  'volume': 5600,\n", "  'average_price': 0.83,\n", "  'oi': 117200,\n", "  'oi_day_high': 120000,\n", "  'oi_day_low': 117200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.25,\n", "  'ohlc': {'open': 0.85, 'high': 0.9, 'low': 0.75, 'close': 1.25},\n", "  'depth': {'buy': [{'price': 0.8, 'quantity': 1600, 'orders': 4},\n", "    {'price': 0.75, 'quantity': 1200, 'orders': 3},\n", "    {'price': 0.7, 'quantity': 3600, 'orders': 3},\n", "    {'price': 0.65, 'quantity': 800, 'orders': 2},\n", "    {'price': 0.6, 'quantity': 800, 'orders': 2}],\n", "   'sell': [{'price': 0.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.95, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.15, 'quantity': 400, 'orders': 1},\n", "    {'price': 1.2, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1280CE': {'instrument_token': 24353026,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 4, 29, 10, 0, 23),\n", "  'last_price': 212.5,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 28800,\n", "  'sell_quantity': 30000,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 38400,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 213.85,\n", "  'upper_circuit_limit': 370.65,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 212.5},\n", "  'depth': {'buy': [{'price': 303.7, 'quantity': 1200, 'orders': 1},\n", "    {'price': 303.65, 'quantity': 1600, 'orders': 1},\n", "    {'price': 303.15, 'quantity': 2000, 'orders': 1},\n", "    {'price': 300.9, 'quantity': 4000, 'orders': 1},\n", "    {'price': 300, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 317.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 317.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 321.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 321.5, 'quantity': 2000, 'orders': 1},\n", "    {'price': 322.25, 'quantity': 1200, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1280PE': {'instrument_token': 24353282,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 5, 53),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 3, 22),\n", "  'last_price': 0.95,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 258800,\n", "  'sell_quantity': 58800,\n", "  'volume': 6400,\n", "  'average_price': 0.85,\n", "  'oi': 187200,\n", "  'oi_day_high': 187600,\n", "  'oi_day_low': 186000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.4,\n", "  'ohlc': {'open': 0.85, 'high': 0.95, 'low': 0.85, 'close': 1.4},\n", "  'depth': {'buy': [{'price': 0.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.85, 'quantity': 800, 'orders': 2},\n", "    {'price': 0.8, 'quantity': 5600, 'orders': 7},\n", "    {'price': 0.75, 'quantity': 2800, 'orders': 5},\n", "    {'price': 0.7, 'quantity': 2800, 'orders': 4}],\n", "   'sell': [{'price': 0.95, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.05, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.1, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.25, 'quantity': 800, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1300CE': {'instrument_token': 27053314,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 11, 46, 22),\n", "  'last_price': 290,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 48000,\n", "  'sell_quantity': 54400,\n", "  'volume': 2800,\n", "  'average_price': 291.88,\n", "  'oi': 78000,\n", "  'oi_day_high': 80000,\n", "  'oi_day_low': 78000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 195.1,\n", "  'upper_circuit_limit': 351.7,\n", "  'ohlc': {'open': 293.95, 'high': 294.25, 'low': 290, 'close': 273.4},\n", "  'depth': {'buy': [{'price': 289.85, 'quantity': 400, 'orders': 1},\n", "    {'price': 284.65, 'quantity': 12800, 'orders': 4},\n", "    {'price': 284.6, 'quantity': 6800, 'orders': 2},\n", "    {'price': 284.4, 'quantity': 1600, 'orders': 1},\n", "    {'price': 283.15, 'quantity': 6000, 'orders': 2}],\n", "   'sell': [{'price': 294.35, 'quantity': 400, 'orders': 1},\n", "    {'price': 300, 'quantity': 19600, 'orders': 6},\n", "    {'price': 300.05, 'quantity': 2000, 'orders': 1},\n", "    {'price': 301.1, 'quantity': 1600, 'orders': 1},\n", "    {'price': 302.9, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1300PE': {'instrument_token': 27053570,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 6, 30),\n", "  'last_price': 1.15,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 356400,\n", "  'sell_quantity': 95600,\n", "  'volume': 103200,\n", "  'average_price': 1.18,\n", "  'oi': 1082400,\n", "  'oi_day_high': 1104000,\n", "  'oi_day_low': 1072800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.7,\n", "  'ohlc': {'open': 1.2, 'high': 1.4, 'low': 1.05, 'close': 1.7},\n", "  'depth': {'buy': [{'price': 1.15, 'quantity': 5200, 'orders': 4},\n", "    {'price': 1.1, 'quantity': 2400, 'orders': 4},\n", "    {'price': 1.05, 'quantity': 12800, 'orders': 9},\n", "    {'price': 1, 'quantity': 16000, 'orders': 14},\n", "    {'price': 0.95, 'quantity': 15200, 'orders': 8}],\n", "   'sell': [{'price': 1.2, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.25, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.3, 'quantity': 2000, 'orders': 4},\n", "    {'price': 1.35, 'quantity': 2000, 'orders': 5},\n", "    {'price': 1.4, 'quantity': 14400, 'orders': 6}]}},\n", " 'NFO:INFY25MAY1320CE': {'instrument_token': 24353538,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 8, 9, 19, 22),\n", "  'last_price': 200.55,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 28000,\n", "  'sell_quantity': 28000,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 87200,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 174.35,\n", "  'upper_circuit_limit': 330.65,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 200.55},\n", "  'depth': {'buy': [{'price': 268.75, 'quantity': 400, 'orders': 1},\n", "    {'price': 263.35, 'quantity': 2000, 'orders': 1},\n", "    {'price': 263.15, 'quantity': 1600, 'orders': 1},\n", "    {'price': 262.55, 'quantity': 4000, 'orders': 1},\n", "    {'price': 260.2, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 276.1, 'quantity': 400, 'orders': 1},\n", "    {'price': 280.3, 'quantity': 2000, 'orders': 1},\n", "    {'price': 281.1, 'quantity': 1600, 'orders': 1},\n", "    {'price': 281.35, 'quantity': 4000, 'orders': 1},\n", "    {'price': 287.3, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1320PE': {'instrument_token': 24353794,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 51, 1),\n", "  'last_price': 1.25,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 233200,\n", "  'sell_quantity': 69200,\n", "  'volume': 6400,\n", "  'average_price': 1.29,\n", "  'oi': 174400,\n", "  'oi_day_high': 174800,\n", "  'oi_day_low': 173600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.75,\n", "  'ohlc': {'open': 1.6, 'high': 1.6, 'low': 1.2, 'close': 1.75},\n", "  'depth': {'buy': [{'price': 1.2, 'quantity': 800, 'orders': 1},\n", "    {'price': 1.15, 'quantity': 1600, 'orders': 4},\n", "    {'price': 1.1, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.05, 'quantity': 2400, 'orders': 4},\n", "    {'price': 1, 'quantity': 800, 'orders': 2}],\n", "   'sell': [{'price': 1.3, 'quantity': 1600, 'orders': 2},\n", "    {'price': 1.35, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.4, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.45, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.5, 'quantity': 800, 'orders': 2}]}},\n", " 'NFO:INFY25MAY1340CE': {'instrument_token': 27053826,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 13, 15, 17, 10),\n", "  'last_price': 230.05,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 28000,\n", "  'sell_quantity': 28400,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 87200,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 152.15,\n", "  'upper_circuit_limit': 307.95,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 230.05},\n", "  'depth': {'buy': [{'price': 248.35, 'quantity': 400, 'orders': 1},\n", "    {'price': 243.8, 'quantity': 1600, 'orders': 1},\n", "    {'price': 243.55, 'quantity': 2000, 'orders': 1},\n", "    {'price': 241.85, 'quantity': 4000, 'orders': 1},\n", "    {'price': 240.4, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 256, 'quantity': 400, 'orders': 1},\n", "    {'price': 256.05, 'quantity': 400, 'orders': 1},\n", "    {'price': 260.7, 'quantity': 2000, 'orders': 1},\n", "    {'price': 261.4, 'quantity': 1600, 'orders': 1},\n", "    {'price': 262.1, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1340PE': {'instrument_token': 27054082,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 5, 15),\n", "  'last_price': 1.35,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 259600,\n", "  'sell_quantity': 68000,\n", "  'volume': 60400,\n", "  'average_price': 1.36,\n", "  'oi': 310400,\n", "  'oi_day_high': 314800,\n", "  'oi_day_low': 300000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 22,\n", "  'ohlc': {'open': 1.35, 'high': 1.55, 'low': 1.05, 'close': 2},\n", "  'depth': {'buy': [{'price': 1.3, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.25, 'quantity': 2400, 'orders': 5},\n", "    {'price': 1.2, 'quantity': 2400, 'orders': 6},\n", "    {'price': 1.15, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.1, 'quantity': 2000, 'orders': 2}],\n", "   'sell': [{'price': 1.4, 'quantity': 1600, 'orders': 3},\n", "    {'price': 1.45, 'quantity': 1600, 'orders': 4},\n", "    {'price': 1.5, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.55, 'quantity': 1600, 'orders': 4},\n", "    {'price': 1.6, 'quantity': 1200, 'orders': 3}]}},\n", " 'NFO:INFY25MAY1360CE': {'instrument_token': 24354050,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 13, 9, 54, 1),\n", "  'last_price': 240.25,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 36000,\n", "  'sell_quantity': 44800,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 90800,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 162.85,\n", "  'upper_circuit_limit': 317.65,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 240.25},\n", "  'depth': {'buy': [{'price': 229.15, 'quantity': 400, 'orders': 1},\n", "    {'price': 224.7, 'quantity': 1600, 'orders': 1},\n", "    {'price': 224.2, 'quantity': 2000, 'orders': 1},\n", "    {'price': 222.6, 'quantity': 4000, 'orders': 1},\n", "    {'price': 221, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 236.15, 'quantity': 800, 'orders': 2},\n", "    {'price': 236.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 236.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 236.4, 'quantity': 400, 'orders': 1},\n", "    {'price': 237.3, 'quantity': 3600, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1360PE': {'instrument_token': 24354306,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 4, 25),\n", "  'last_price': 1.6,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 287600,\n", "  'sell_quantity': 86000,\n", "  'volume': 58800,\n", "  'average_price': 1.61,\n", "  'oi': 368400,\n", "  'oi_day_high': 373200,\n", "  'oi_day_low': 361600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 22.3,\n", "  'ohlc': {'open': 1.95, 'high': 1.95, 'low': 1.55, 'close': 2.3},\n", "  'depth': {'buy': [{'price': 1.5, 'quantity': 3200, 'orders': 7},\n", "    {'price': 1.45, 'quantity': 2800, 'orders': 7},\n", "    {'price': 1.4, 'quantity': 2800, 'orders': 7},\n", "    {'price': 1.35, 'quantity': 2400, 'orders': 6},\n", "    {'price': 1.3, 'quantity': 1600, 'orders': 4}],\n", "   'sell': [{'price': 1.6, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.65, 'quantity': 2400, 'orders': 6},\n", "    {'price': 1.7, 'quantity': 4400, 'orders': 6},\n", "    {'price': 1.75, 'quantity': 2800, 'orders': 7},\n", "    {'price': 1.8, 'quantity': 2800, 'orders': 7}]}},\n", " 'NFO:INFY25MAY1380CE': {'instrument_token': 27070466,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 11, 48, 14),\n", "  'last_price': 213.1,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 39600,\n", "  'sell_quantity': 47600,\n", "  'volume': 1200,\n", "  'average_price': 216.61,\n", "  'oi': 90800,\n", "  'oi_day_high': 91200,\n", "  'oi_day_low': 90800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 120.45,\n", "  'upper_circuit_limit': 273.75,\n", "  'ohlc': {'open': 218.35, 'high': 218.4, 'low': 213.1, 'close': 197.1},\n", "  'depth': {'buy': [{'price': 210.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.35, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.25, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 216.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 216.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 216.7, 'quantity': 400, 'orders': 1},\n", "    {'price': 216.8, 'quantity': 400, 'orders': 1},\n", "    {'price': 216.85, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1380PE': {'instrument_token': 27070722,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_price': 1.9,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 366400,\n", "  'sell_quantity': 83600,\n", "  'volume': 57600,\n", "  'average_price': 1.93,\n", "  'oi': 316000,\n", "  'oi_day_high': 316000,\n", "  'oi_day_low': 307200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 22.9,\n", "  'ohlc': {'open': 2.5, 'high': 2.5, 'low': 1.85, 'close': 2.9},\n", "  'depth': {'buy': [{'price': 1.9, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.85, 'quantity': 2000, 'orders': 4},\n", "    {'price': 1.8, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.75, 'quantity': 2800, 'orders': 7},\n", "    {'price': 1.7, 'quantity': 2400, 'orders': 6}],\n", "   'sell': [{'price': 2, 'quantity': 800, 'orders': 2},\n", "    {'price': 2.05, 'quantity': 800, 'orders': 2},\n", "    {'price': 2.1, 'quantity': 2400, 'orders': 6},\n", "    {'price': 2.15, 'quantity': 1200, 'orders': 2},\n", "    {'price': 2.2, 'quantity': 800, 'orders': 2}]}},\n", " 'NFO:INFY25MAY1400CE': {'instrument_token': 24354562,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 11, 56, 48),\n", "  'last_price': 192,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 49200,\n", "  'sell_quantity': 48000,\n", "  'volume': 4400,\n", "  'average_price': 191.97,\n", "  'oi': 252800,\n", "  'oi_day_high': 253600,\n", "  'oi_day_low': 252800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 102.95,\n", "  'upper_circuit_limit': 254.05,\n", "  'ohlc': {'open': 192, 'high': 196, 'low': 190, 'close': 178.5},\n", "  'depth': {'buy': [{'price': 192.45, 'quantity': 800, 'orders': 2},\n", "    {'price': 192.4, 'quantity': 1200, 'orders': 3},\n", "    {'price': 190.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 190.85, 'quantity': 800, 'orders': 2},\n", "    {'price': 190.8, 'quantity': 9600, 'orders': 5}],\n", "   'sell': [{'price': 195, 'quantity': 400, 'orders': 1},\n", "    {'price': 195.95, 'quantity': 400, 'orders': 1},\n", "    {'price': 197.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 197.65, 'quantity': 2400, 'orders': 1},\n", "    {'price': 199, 'quantity': 2000, 'orders': 2}]}},\n", " 'NFO:INFY25MAY1400PE': {'instrument_token': 24354818,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 4),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 16),\n", "  'last_price': 2.15,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 470000,\n", "  'sell_quantity': 116000,\n", "  'volume': 204400,\n", "  'average_price': 2.41,\n", "  'oi': 1008800,\n", "  'oi_day_high': 1022000,\n", "  'oi_day_low': 1007600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 23.45,\n", "  'ohlc': {'open': 2.9, 'high': 3, 'low': 2.1, 'close': 3.45},\n", "  'depth': {'buy': [{'price': 2.1, 'quantity': 6000, 'orders': 12},\n", "    {'price': 2.05, 'quantity': 3200, 'orders': 7},\n", "    {'price': 2, 'quantity': 3200, 'orders': 7},\n", "    {'price': 1.95, 'quantity': 1600, 'orders': 4},\n", "    {'price': 1.9, 'quantity': 5600, 'orders': 4}],\n", "   'sell': [{'price': 2.2, 'quantity': 3200, 'orders': 7},\n", "    {'price': 2.25, 'quantity': 3600, 'orders': 8},\n", "    {'price': 2.3, 'quantity': 9600, 'orders': 10},\n", "    {'price': 2.35, 'quantity': 13200, 'orders': 8},\n", "    {'price': 2.4, 'quantity': 2800, 'orders': 5}]}},\n", " 'NFO:INFY25MAY1420CE': {'instrument_token': 27070978,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 13, 14, 9, 59),\n", "  'last_price': 158.1,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 37200,\n", "  'sell_quantity': 45600,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 109600,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 84.2,\n", "  'upper_circuit_limit': 232,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 158.1},\n", "  'depth': {'buy': [{'price': 171.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 171.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 170.7, 'quantity': 800, 'orders': 1},\n", "    {'price': 166.1, 'quantity': 1600, 'orders': 1},\n", "    {'price': 165.15, 'quantity': 2000, 'orders': 1}],\n", "   'sell': [{'price': 178.85, 'quantity': 400, 'orders': 1},\n", "    {'price': 178.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 179, 'quantity': 400, 'orders': 1},\n", "    {'price': 179.05, 'quantity': 4400, 'orders': 2},\n", "    {'price': 181.7, 'quantity': 2000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1420PE': {'instrument_token': 27071234,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 36),\n", "  'last_price': 2.5,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 450000,\n", "  'sell_quantity': 117600,\n", "  'volume': 76800,\n", "  'average_price': 2.7,\n", "  'oi': 238000,\n", "  'oi_day_high': 249200,\n", "  'oi_day_low': 233200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 24.15,\n", "  'ohlc': {'open': 3.15, 'high': 3.2, 'low': 2.45, 'close': 4.15},\n", "  'depth': {'buy': [{'price': 2.5, 'quantity': 4000, 'orders': 3},\n", "    {'price': 2.45, 'quantity': 2400, 'orders': 6},\n", "    {'price': 2.4, 'quantity': 4000, 'orders': 10},\n", "    {'price': 2.35, 'quantity': 1200, 'orders': 3},\n", "    {'price': 2.3, 'quantity': 1600, 'orders': 4}],\n", "   'sell': [{'price': 2.55, 'quantity': 2400, 'orders': 6},\n", "    {'price': 2.6, 'quantity': 2400, 'orders': 6},\n", "    {'price': 2.65, 'quantity': 8800, 'orders': 10},\n", "    {'price': 2.7, 'quantity': 6000, 'orders': 6},\n", "    {'price': 2.75, 'quantity': 7600, 'orders': 7}]}},\n", " 'NFO:INFY25MAY1440CE': {'instrument_token': 24355074,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 11, 59, 16),\n", "  'last_price': 152,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 48400,\n", "  'sell_quantity': 47600,\n", "  'volume': 2000,\n", "  'average_price': 150.94,\n", "  'oi': 243600,\n", "  'oi_day_high': 245200,\n", "  'oi_day_low': 243600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 70.35,\n", "  'upper_circuit_limit': 213.65,\n", "  'ohlc': {'open': 150.95, 'high': 152, 'low': 149.85, 'close': 142},\n", "  'depth': {'buy': [{'price': 153.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 153.4, 'quantity': 400, 'orders': 1},\n", "    {'price': 152.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 152.1, 'quantity': 400, 'orders': 1},\n", "    {'price': 152.05, 'quantity': 800, 'orders': 1}],\n", "   'sell': [{'price': 156.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 157.2, 'quantity': 800, 'orders': 1},\n", "    {'price': 158.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 158.9, 'quantity': 2400, 'orders': 1},\n", "    {'price': 159.1, 'quantity': 1200, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1440PE': {'instrument_token': 24355330,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 11),\n", "  'last_price': 2.9,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 457600,\n", "  'sell_quantity': 164400,\n", "  'volume': 208800,\n", "  'average_price': 3.23,\n", "  'oi': 402400,\n", "  'oi_day_high': 407200,\n", "  'oi_day_low': 373200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 25.1,\n", "  'ohlc': {'open': 4, 'high': 4.3, 'low': 2.85, 'close': 5.1},\n", "  'depth': {'buy': [{'price': 2.85, 'quantity': 1600, 'orders': 4},\n", "    {'price': 2.8, 'quantity': 5200, 'orders': 9},\n", "    {'price': 2.75, 'quantity': 3200, 'orders': 3},\n", "    {'price': 2.7, 'quantity': 6400, 'orders': 3},\n", "    {'price': 2.65, 'quantity': 2000, 'orders': 1}],\n", "   'sell': [{'price': 2.9, 'quantity': 2000, 'orders': 4},\n", "    {'price': 2.95, 'quantity': 6400, 'orders': 12},\n", "    {'price': 3, 'quantity': 4800, 'orders': 9},\n", "    {'price': 3.05, 'quantity': 4400, 'orders': 8},\n", "    {'price': 3.1, 'quantity': 4000, 'orders': 8}]}},\n", " 'NFO:INFY25MAY1460CE': {'instrument_token': 27071490,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 29, 26),\n", "  'last_price': 135,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 44000,\n", "  'sell_quantity': 52000,\n", "  'volume': 4000,\n", "  'average_price': 134.5,\n", "  'oi': 130400,\n", "  'oi_day_high': 132000,\n", "  'oi_day_low': 130400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 52.3,\n", "  'upper_circuit_limit': 189.7,\n", "  'ohlc': {'open': 129, 'high': 137, 'low': 129, 'close': 121},\n", "  'depth': {'buy': [{'price': 133.7, 'quantity': 800, 'orders': 2},\n", "    {'price': 133.65, 'quantity': 400, 'orders': 1},\n", "    {'price': 132.5, 'quantity': 800, 'orders': 1},\n", "    {'price': 131.6, 'quantity': 1600, 'orders': 1},\n", "    {'price': 130.2, 'quantity': 1200, 'orders': 1}],\n", "   'sell': [{'price': 136.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 136.95, 'quantity': 800, 'orders': 2},\n", "    {'price': 137, 'quantity': 400, 'orders': 1},\n", "    {'price': 137.05, 'quantity': 400, 'orders': 1},\n", "    {'price': 137.1, 'quantity': 1200, 'orders': 3}]}},\n", " 'NFO:INFY25MAY1460PE': {'instrument_token': 27071746,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 5, 29),\n", "  'last_price': 3.7,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 531200,\n", "  'sell_quantity': 188400,\n", "  'volume': 168000,\n", "  'average_price': 4.11,\n", "  'oi': 476400,\n", "  'oi_day_high': 478000,\n", "  'oi_day_low': 455200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 26.45,\n", "  'ohlc': {'open': 5.65, 'high': 5.65, 'low': 3.6, 'close': 6.45},\n", "  'depth': {'buy': [{'price': 3.65, 'quantity': 2800, 'orders': 5},\n", "    {'price': 3.6, 'quantity': 7600, 'orders': 11},\n", "    {'price': 3.55, 'quantity': 6400, 'orders': 7},\n", "    {'price': 3.5, 'quantity': 5200, 'orders': 4},\n", "    {'price': 3.45, 'quantity': 2800, 'orders': 3}],\n", "   'sell': [{'price': 3.7, 'quantity': 4400, 'orders': 5},\n", "    {'price': 3.75, 'quantity': 8000, 'orders': 12},\n", "    {'price': 3.8, 'quantity': 6800, 'orders': 8},\n", "    {'price': 3.85, 'quantity': 3600, 'orders': 5},\n", "    {'price': 3.9, 'quantity': 4000, 'orders': 6}]}},\n", " 'NFO:INFY25MAY1480CE': {'instrument_token': 24355586,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 30, 19),\n", "  'last_price': 116.25,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 70000,\n", "  'sell_quantity': 64800,\n", "  'volume': 33200,\n", "  'average_price': 117.3,\n", "  'oi': 428000,\n", "  'oi_day_high': 446000,\n", "  'oi_day_low': 428000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 35.45,\n", "  'upper_circuit_limit': 165.45,\n", "  'ohlc': {'open': 110.65, 'high': 120.45, 'low': 110.65, 'close': 100.45},\n", "  'depth': {'buy': [{'price': 115.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 115.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 114.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 114.45, 'quantity': 800, 'orders': 2},\n", "    {'price': 113.55, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 118.65, 'quantity': 3600, 'orders': 9},\n", "    {'price': 118.7, 'quantity': 400, 'orders': 1},\n", "    {'price': 119.8, 'quantity': 1200, 'orders': 1},\n", "    {'price': 120.3, 'quantity': 1600, 'orders': 4},\n", "    {'price': 120.85, 'quantity': 2400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1480PE': {'instrument_token': 24355842,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 7),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 9, 20),\n", "  'last_price': 4.9,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 559200,\n", "  'sell_quantity': 213200,\n", "  'volume': 272800,\n", "  'average_price': 5.64,\n", "  'oi': 579600,\n", "  'oi_day_high': 600400,\n", "  'oi_day_low': 574000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 28.6,\n", "  'ohlc': {'open': 8.1, 'high': 8.1, 'low': 4.7, 'close': 8.6},\n", "  'depth': {'buy': [{'price': 4.75, 'quantity': 8000, 'orders': 10},\n", "    {'price': 4.7, 'quantity': 8000, 'orders': 9},\n", "    {'price': 4.65, 'quantity': 8000, 'orders': 7},\n", "    {'price': 4.6, 'quantity': 5600, 'orders': 5},\n", "    {'price': 4.55, 'quantity': 4800, 'orders': 3}],\n", "   'sell': [{'price': 4.85, 'quantity': 4000, 'orders': 5},\n", "    {'price': 4.9, 'quantity': 10800, 'orders': 14},\n", "    {'price': 4.95, 'quantity': 9200, 'orders': 10},\n", "    {'price': 5, 'quantity': 8000, 'orders': 9},\n", "    {'price': 5.05, 'quantity': 4800, 'orders': 6}]}},\n", " 'NFO:INFY25MAY1500CE': {'instrument_token': 27072002,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 9, 23),\n", "  'last_price': 97.2,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 83600,\n", "  'sell_quantity': 78800,\n", "  'volume': 204000,\n", "  'average_price': 96.77,\n", "  'oi': 1181200,\n", "  'oi_day_high': 1187200,\n", "  'oi_day_low': 1145600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 22.85,\n", "  'upper_circuit_limit': 144.05,\n", "  'ohlc': {'open': 87.65, 'high': 102.6, 'low': 87.5, 'close': 83.45},\n", "  'depth': {'buy': [{'price': 97.85, 'quantity': 400, 'orders': 1},\n", "    {'price': 97.8, 'quantity': 400, 'orders': 1},\n", "    {'price': 97.7, 'quantity': 400, 'orders': 1},\n", "    {'price': 97.65, 'quantity': 400, 'orders': 1},\n", "    {'price': 97.4, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 98.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 98.65, 'quantity': 400, 'orders': 1},\n", "    {'price': 98.8, 'quantity': 400, 'orders': 1},\n", "    {'price': 98.85, 'quantity': 400, 'orders': 1},\n", "    {'price': 98.9, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1500PE': {'instrument_token': 27072258,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_price': 6.6,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 518000,\n", "  'sell_quantity': 212400,\n", "  'volume': 898400,\n", "  'average_price': 7.42,\n", "  'oi': 1276800,\n", "  'oi_day_high': 1300000,\n", "  'oi_day_low': 1242000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 31.45,\n", "  'ohlc': {'open': 10.15, 'high': 10.2, 'low': 6.4, 'close': 11.45},\n", "  'depth': {'buy': [{'price': 6.55, 'quantity': 6400, 'orders': 9},\n", "    {'price': 6.5, 'quantity': 21600, 'orders': 15},\n", "    {'price': 6.45, 'quantity': 8400, 'orders': 11},\n", "    {'price': 6.4, 'quantity': 8000, 'orders': 10},\n", "    {'price': 6.35, 'quantity': 3600, 'orders': 5}],\n", "   'sell': [{'price': 6.65, 'quantity': 6400, 'orders': 10},\n", "    {'price': 6.7, 'quantity': 8000, 'orders': 11},\n", "    {'price': 6.75, 'quantity': 9200, 'orders': 13},\n", "    {'price': 6.8, 'quantity': 6000, 'orders': 10},\n", "    {'price': 6.85, 'quantity': 6000, 'orders': 9}]}},\n", " 'NFO:INFY25MAY1520CE': {'instrument_token': 24356098,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 55, 55),\n", "  'last_price': 75.5,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 84400,\n", "  'sell_quantity': 78800,\n", "  'volume': 54400,\n", "  'average_price': 80.33,\n", "  'oi': 709600,\n", "  'oi_day_high': 710400,\n", "  'oi_day_low': 700800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 13.15,\n", "  'upper_circuit_limit': 124.25,\n", "  'ohlc': {'open': 76, 'high': 84.85, 'low': 73.7, 'close': 68.7},\n", "  'depth': {'buy': [{'price': 80.8, 'quantity': 400, 'orders': 1},\n", "    {'price': 80.75, 'quantity': 800, 'orders': 2},\n", "    {'price': 80.7, 'quantity': 400, 'orders': 1},\n", "    {'price': 80.65, 'quantity': 400, 'orders': 1},\n", "    {'price': 80.25, 'quantity': 800, 'orders': 2}],\n", "   'sell': [{'price': 81.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 81.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 81.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 81.5, 'quantity': 800, 'orders': 2},\n", "    {'price': 81.55, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1520PE': {'instrument_token': 24356354,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 57),\n", "  'last_price': 9.15,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 496400,\n", "  'sell_quantity': 181600,\n", "  'volume': 381600,\n", "  'average_price': 10.5,\n", "  'oi': 824400,\n", "  'oi_day_high': 831600,\n", "  'oi_day_low': 814400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 38.45,\n", "  'ohlc': {'open': 14, 'high': 14.6, 'low': 8.95, 'close': 15.55},\n", "  'depth': {'buy': [{'price': 9.1, 'quantity': 4000, 'orders': 5},\n", "    {'price': 9.05, 'quantity': 5200, 'orders': 7},\n", "    {'price': 9, 'quantity': 6800, 'orders': 9},\n", "    {'price': 8.95, 'quantity': 3200, 'orders': 4},\n", "    {'price': 8.9, 'quantity': 2400, 'orders': 3}],\n", "   'sell': [{'price': 9.2, 'quantity': 3200, 'orders': 4},\n", "    {'price': 9.25, 'quantity': 6000, 'orders': 7},\n", "    {'price': 9.3, 'quantity': 8000, 'orders': 12},\n", "    {'price': 9.35, 'quantity': 7200, 'orders': 10},\n", "    {'price': 9.4, 'quantity': 6400, 'orders': 9}]}},\n", " 'NFO:INFY25MAY1540CE': {'instrument_token': 27074306,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 34),\n", "  'last_price': 64.5,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 73600,\n", "  'sell_quantity': 96800,\n", "  'volume': 258800,\n", "  'average_price': 63.76,\n", "  'oi': 664800,\n", "  'oi_day_high': 676800,\n", "  'oi_day_low': 664400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 3.2,\n", "  'upper_circuit_limit': 103.2,\n", "  'ohlc': {'open': 56.15, 'high': 68.75, 'low': 55.1, 'close': 53.2},\n", "  'depth': {'buy': [{'price': 64.55, 'quantity': 800, 'orders': 2},\n", "    {'price': 64.5, 'quantity': 1200, 'orders': 3},\n", "    {'price': 64.4, 'quantity': 400, 'orders': 1},\n", "    {'price': 64.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 64.2, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 64.85, 'quantity': 400, 'orders': 1},\n", "    {'price': 64.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 64.95, 'quantity': 1600, 'orders': 4},\n", "    {'price': 65.1, 'quantity': 400, 'orders': 1},\n", "    {'price': 65.15, 'quantity': 800, 'orders': 2}]}},\n", " 'NFO:INFY25MAY1540PE': {'instrument_token': 27074562,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_price': 13.05,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 557600,\n", "  'sell_quantity': 160800,\n", "  'volume': 783200,\n", "  'average_price': 14.57,\n", "  'oi': 560400,\n", "  'oi_day_high': 607200,\n", "  'oi_day_low': 550400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 49.7,\n", "  'ohlc': {'open': 22, 'high': 22, 'low': 12.65, 'close': 21.25},\n", "  'depth': {'buy': [{'price': 13, 'quantity': 3600, 'orders': 7},\n", "    {'price': 12.95, 'quantity': 4800, 'orders': 8},\n", "    {'price': 12.9, 'quantity': 9600, 'orders': 13},\n", "    {'price': 12.85, 'quantity': 6000, 'orders': 11},\n", "    {'price': 12.8, 'quantity': 4000, 'orders': 8}],\n", "   'sell': [{'price': 13.1, 'quantity': 800, 'orders': 2},\n", "    {'price': 13.15, 'quantity': 4000, 'orders': 6},\n", "    {'price': 13.2, 'quantity': 6400, 'orders': 9},\n", "    {'price': 13.25, 'quantity': 6000, 'orders': 8},\n", "    {'price': 13.3, 'quantity': 4400, 'orders': 8}]}},\n", " 'NFO:INFY25MAY1560CE': {'instrument_token': 24356610,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 36),\n", "  'last_price': 50,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 124000,\n", "  'sell_quantity': 109200,\n", "  'volume': 840000,\n", "  'average_price': 48.99,\n", "  'oi': 1302800,\n", "  'oi_day_high': 1384000,\n", "  'oi_day_low': 1302800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 84.85,\n", "  'ohlc': {'open': 40.85, 'high': 53.8, 'low': 40.7, 'close': 40.7},\n", "  'depth': {'buy': [{'price': 49.85, 'quantity': 800, 'orders': 2},\n", "    {'price': 49.8, 'quantity': 800, 'orders': 2},\n", "    {'price': 49.75, 'quantity': 1600, 'orders': 4},\n", "    {'price': 49.7, 'quantity': 1200, 'orders': 3},\n", "    {'price': 49.65, 'quantity': 800, 'orders': 2}],\n", "   'sell': [{'price': 50.05, 'quantity': 400, 'orders': 1},\n", "    {'price': 50.1, 'quantity': 800, 'orders': 2},\n", "    {'price': 50.15, 'quantity': 2400, 'orders': 3},\n", "    {'price': 50.2, 'quantity': 1600, 'orders': 4},\n", "    {'price': 50.25, 'quantity': 1600, 'orders': 4}]}},\n", " 'NFO:INFY25MAY1560PE': {'instrument_token': 24356866,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 8),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_price': 18.2,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 465600,\n", "  'sell_quantity': 184800,\n", "  'volume': 816800,\n", "  'average_price': 20.17,\n", "  'oi': 753600,\n", "  'oi_day_high': 791600,\n", "  'oi_day_low': 752800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 62.7,\n", "  'ohlc': {'open': 25.65, 'high': 26.55, 'low': 17.55, 'close': 28.4},\n", "  'depth': {'buy': [{'price': 18.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 18.1, 'quantity': 2800, 'orders': 7},\n", "    {'price': 18.05, 'quantity': 3600, 'orders': 6},\n", "    {'price': 18, 'quantity': 7200, 'orders': 9},\n", "    {'price': 17.95, 'quantity': 4400, 'orders': 7}],\n", "   'sell': [{'price': 18.3, 'quantity': 2400, 'orders': 6},\n", "    {'price': 18.35, 'quantity': 4800, 'orders': 8},\n", "    {'price': 18.4, 'quantity': 6800, 'orders': 8},\n", "    {'price': 18.45, 'quantity': 4000, 'orders': 6},\n", "    {'price': 18.5, 'quantity': 4000, 'orders': 7}]}},\n", " 'NFO:INFY25MAY1580CE': {'instrument_token': 27076098,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 20),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_price': 37.3,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 279200,\n", "  'sell_quantity': 214400,\n", "  'volume': 2472400,\n", "  'average_price': 37.12,\n", "  'oi': 755200,\n", "  'oi_day_high': 944000,\n", "  'oi_day_low': 755200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 68.55,\n", "  'ohlc': {'open': 30.45, 'high': 40.75, 'low': 30, 'close': 30.35},\n", "  'depth': {'buy': [{'price': 37.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 37.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 37.15, 'quantity': 1200, 'orders': 2},\n", "    {'price': 37.1, 'quantity': 800, 'orders': 2},\n", "    {'price': 37.05, 'quantity': 800, 'orders': 2}],\n", "   'sell': [{'price': 37.45, 'quantity': 1200, 'orders': 2},\n", "    {'price': 37.5, 'quantity': 2400, 'orders': 6},\n", "    {'price': 37.55, 'quantity': 3200, 'orders': 5},\n", "    {'price': 37.6, 'quantity': 3200, 'orders': 7},\n", "    {'price': 37.65, 'quantity': 2800, 'orders': 5}]}},\n", " 'NFO:INFY25MAY1580PE': {'instrument_token': 27076354,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 14),\n", "  'last_price': 25.5,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 354800,\n", "  'sell_quantity': 157200,\n", "  'volume': 1147600,\n", "  'average_price': 27.37,\n", "  'oi': 493600,\n", "  'oi_day_high': 594800,\n", "  'oi_day_low': 493600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 77.85,\n", "  'ohlc': {'open': 36.7, 'high': 36.7, 'low': 24.45, 'close': 37.6},\n", "  'depth': {'buy': [{'price': 25.45, 'quantity': 800, 'orders': 2},\n", "    {'price': 25.4, 'quantity': 2000, 'orders': 5},\n", "    {'price': 25.35, 'quantity': 3200, 'orders': 5},\n", "    {'price': 25.3, 'quantity': 2800, 'orders': 6},\n", "    {'price': 25.25, 'quantity': 1600, 'orders': 3}],\n", "   'sell': [{'price': 25.65, 'quantity': 1600, 'orders': 4},\n", "    {'price': 25.7, 'quantity': 2000, 'orders': 3},\n", "    {'price': 25.75, 'quantity': 5200, 'orders': 7},\n", "    {'price': 25.8, 'quantity': 2800, 'orders': 5},\n", "    {'price': 25.85, 'quantity': 2000, 'orders': 5}]}},\n", " 'NFO:INFY25MAY1600CE': {'instrument_token': 24357122,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 20),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 17),\n", "  'last_price': 26.65,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 478400,\n", "  'sell_quantity': 563200,\n", "  'volume': 4779600,\n", "  'average_price': 26.81,\n", "  'oi': 2989600,\n", "  'oi_day_high': 3150400,\n", "  'oi_day_low': 2988000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 53.95,\n", "  'ohlc': {'open': 22.55, 'high': 29.45, 'low': 21, 'close': 21.6},\n", "  'depth': {'buy': [{'price': 26.4, 'quantity': 1200, 'orders': 3},\n", "    {'price': 26.35, 'quantity': 800, 'orders': 2},\n", "    {'price': 26.3, 'quantity': 2000, 'orders': 4},\n", "    {'price': 26.25, 'quantity': 2800, 'orders': 4},\n", "    {'price': 26.2, 'quantity': 3600, 'orders': 7}],\n", "   'sell': [{'price': 26.6, 'quantity': 3600, 'orders': 7},\n", "    {'price': 26.65, 'quantity': 1600, 'orders': 4},\n", "    {'price': 26.7, 'quantity': 3600, 'orders': 7},\n", "    {'price': 26.75, 'quantity': 3200, 'orders': 8},\n", "    {'price': 26.8, 'quantity': 3600, 'orders': 7}]}},\n", " 'NFO:INFY25MAY1600PE': {'instrument_token': 24357378,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 6),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 53),\n", "  'last_price': 34.55,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 181600,\n", "  'sell_quantity': 131600,\n", "  'volume': 1078800,\n", "  'average_price': 36.47,\n", "  'oi': 1085200,\n", "  'oi_day_high': 1183200,\n", "  'oi_day_low': 1085200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 2.95,\n", "  'upper_circuit_limit': 95.15,\n", "  'ohlc': {'open': 46.25, 'high': 46.3, 'low': 33.05, 'close': 49.05},\n", "  'depth': {'buy': [{'price': 34.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 34.4, 'quantity': 800, 'orders': 2},\n", "    {'price': 34.35, 'quantity': 1600, 'orders': 3},\n", "    {'price': 34.3, 'quantity': 4000, 'orders': 7},\n", "    {'price': 34.25, 'quantity': 1200, 'orders': 3}],\n", "   'sell': [{'price': 34.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 34.65, 'quantity': 800, 'orders': 2},\n", "    {'price': 34.7, 'quantity': 1600, 'orders': 4},\n", "    {'price': 34.75, 'quantity': 1600, 'orders': 3},\n", "    {'price': 34.8, 'quantity': 1200, 'orders': 3}]}},\n", " 'NFO:INFY25MAY1620CE': {'instrument_token': 27077890,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 20),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 8),\n", "  'last_price': 18.1,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 534400,\n", "  'sell_quantity': 312400,\n", "  'volume': 1728800,\n", "  'average_price': 18.4,\n", "  'oi': 820400,\n", "  'oi_day_high': 988400,\n", "  'oi_day_low': 812400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 41.95,\n", "  'ohlc': {'open': 15.2, 'high': 20.6, 'low': 15, 'close': 15.15},\n", "  'depth': {'buy': [{'price': 18.05, 'quantity': 800, 'orders': 2},\n", "    {'price': 18, 'quantity': 2800, 'orders': 6},\n", "    {'price': 17.95, 'quantity': 5200, 'orders': 7},\n", "    {'price': 17.9, 'quantity': 10000, 'orders': 11},\n", "    {'price': 17.85, 'quantity': 5600, 'orders': 7}],\n", "   'sell': [{'price': 18.2, 'quantity': 2000, 'orders': 5},\n", "    {'price': 18.25, 'quantity': 2800, 'orders': 6},\n", "    {'price': 18.3, 'quantity': 7200, 'orders': 11},\n", "    {'price': 18.35, 'quantity': 5200, 'orders': 8},\n", "    {'price': 18.4, 'quantity': 8000, 'orders': 8}]}},\n", " 'NFO:INFY25MAY1620PE': {'instrument_token': 27078146,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 18),\n", "  'last_price': 46.3,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 98000,\n", "  'sell_quantity': 97600,\n", "  'volume': 225200,\n", "  'average_price': 47.49,\n", "  'oi': 194000,\n", "  'oi_day_high': 237600,\n", "  'oi_day_low': 187200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 10.75,\n", "  'upper_circuit_limit': 114.05,\n", "  'ohlc': {'open': 54.4, 'high': 54.75, 'low': 44.35, 'close': 62.4},\n", "  'depth': {'buy': [{'price': 46.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 46.2, 'quantity': 800, 'orders': 2},\n", "    {'price': 46.15, 'quantity': 800, 'orders': 2},\n", "    {'price': 46.1, 'quantity': 400, 'orders': 1},\n", "    {'price': 46.05, 'quantity': 2400, 'orders': 3}],\n", "   'sell': [{'price': 46.45, 'quantity': 1200, 'orders': 3},\n", "    {'price': 46.5, 'quantity': 800, 'orders': 2},\n", "    {'price': 46.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 46.6, 'quantity': 1200, 'orders': 3},\n", "    {'price': 46.65, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1640CE': {'instrument_token': 24357634,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 8),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 42),\n", "  'last_price': 11.85,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 575600,\n", "  'sell_quantity': 274800,\n", "  'volume': 2027200,\n", "  'average_price': 12.18,\n", "  'oi': 1894400,\n", "  'oi_day_high': 2098000,\n", "  'oi_day_low': 1889600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 32,\n", "  'ohlc': {'open': 10.25, 'high': 13.8, 'low': 9.9, 'close': 10.25},\n", "  'depth': {'buy': [{'price': 11.8, 'quantity': 5600, 'orders': 6},\n", "    {'price': 11.75, 'quantity': 8000, 'orders': 11},\n", "    {'price': 11.7, 'quantity': 12000, 'orders': 11},\n", "    {'price': 11.65, 'quantity': 5600, 'orders': 10},\n", "    {'price': 11.6, 'quantity': 7200, 'orders': 8}],\n", "   'sell': [{'price': 11.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 11.95, 'quantity': 4800, 'orders': 8},\n", "    {'price': 12, 'quantity': 10000, 'orders': 10},\n", "    {'price': 12.05, 'quantity': 10400, 'orders': 10},\n", "    {'price': 12.1, 'quantity': 10000, 'orders': 8}]}},\n", " 'NFO:INFY25MAY1640PE': {'instrument_token': 24357890,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 57, 37),\n", "  'last_price': 66.05,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 81600,\n", "  'sell_quantity': 79600,\n", "  'volume': 66000,\n", "  'average_price': 60.82,\n", "  'oi': 216800,\n", "  'oi_day_high': 233600,\n", "  'oi_day_low': 216400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 20.6,\n", "  'upper_circuit_limit': 134.1,\n", "  'ohlc': {'open': 64.85, 'high': 66.7, 'low': 57.7, 'close': 77.35},\n", "  'depth': {'buy': [{'price': 59.4, 'quantity': 400, 'orders': 1},\n", "    {'price': 59.35, 'quantity': 400, 'orders': 1},\n", "    {'price': 59.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 59.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 59.15, 'quantity': 1600, 'orders': 1}],\n", "   'sell': [{'price': 60.05, 'quantity': 400, 'orders': 1},\n", "    {'price': 60.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 60.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 60.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 60.65, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1660CE': {'instrument_token': 27080962,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11, 17),\n", "  'last_price': 7.8,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 586000,\n", "  'sell_quantity': 232000,\n", "  'volume': 1369200,\n", "  'average_price': 8.05,\n", "  'oi': 890800,\n", "  'oi_day_high': 1004400,\n", "  'oi_day_low': 852000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 27.05,\n", "  'ohlc': {'open': 7, 'high': 9.25, 'low': 6.55, 'close': 7.05},\n", "  'depth': {'buy': [{'price': 7.75, 'quantity': 7200, 'orders': 10},\n", "    {'price': 7.7, 'quantity': 8800, 'orders': 13},\n", "    {'price': 7.65, 'quantity': 9200, 'orders': 14},\n", "    {'price': 7.6, 'quantity': 6400, 'orders': 11},\n", "    {'price': 7.55, 'quantity': 18000, 'orders': 19}],\n", "   'sell': [{'price': 7.85, 'quantity': 2400, 'orders': 4},\n", "    {'price': 7.9, 'quantity': 8400, 'orders': 12},\n", "    {'price': 7.95, 'quantity': 10800, 'orders': 12},\n", "    {'price': 8, 'quantity': 8800, 'orders': 13},\n", "    {'price': 8.05, 'quantity': 4400, 'orders': 8}]}},\n", " 'NFO:INFY25MAY1660PE': {'instrument_token': 27081218,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 3, 53),\n", "  'last_price': 78,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 61200,\n", "  'sell_quantity': 62800,\n", "  'volume': 57200,\n", "  'average_price': 76.55,\n", "  'oi': 110400,\n", "  'oi_day_high': 110800,\n", "  'oi_day_low': 97200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 33.1,\n", "  'upper_circuit_limit': 155.6,\n", "  'ohlc': {'open': 80, 'high': 82.7, 'low': 73, 'close': 94.35},\n", "  'depth': {'buy': [{'price': 75.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 75.1, 'quantity': 400, 'orders': 1},\n", "    {'price': 74.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 74.75, 'quantity': 400, 'orders': 1},\n", "    {'price': 74.65, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 76.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 76.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 76.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 76.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 76.65, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1680CE': {'instrument_token': 24358146,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 37),\n", "  'last_price': 5.05,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 557600,\n", "  'sell_quantity': 200400,\n", "  'volume': 747600,\n", "  'average_price': 5.29,\n", "  'oi': 1266800,\n", "  'oi_day_high': 1294000,\n", "  'oi_day_low': 1256400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 24.9,\n", "  'ohlc': {'open': 4.95, 'high': 6.15, 'low': 4.3, 'close': 4.9},\n", "  'depth': {'buy': [{'price': 5, 'quantity': 1200, 'orders': 2},\n", "    {'price': 4.95, 'quantity': 12400, 'orders': 15},\n", "    {'price': 4.9, 'quantity': 10800, 'orders': 17},\n", "    {'price': 4.85, 'quantity': 8000, 'orders': 12},\n", "    {'price': 4.8, 'quantity': 6800, 'orders': 10}],\n", "   'sell': [{'price': 5.05, 'quantity': 3200, 'orders': 4},\n", "    {'price': 5.1, 'quantity': 8400, 'orders': 12},\n", "    {'price': 5.15, 'quantity': 7600, 'orders': 10},\n", "    {'price': 5.2, 'quantity': 8400, 'orders': 10},\n", "    {'price': 5.25, 'quantity': 6400, 'orders': 7}]}},\n", " 'NFO:INFY25MAY1680PE': {'instrument_token': 24358402,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 36, 35),\n", "  'last_price': 91.45,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 54000,\n", "  'sell_quantity': 68000,\n", "  'volume': 22800,\n", "  'average_price': 92.96,\n", "  'oi': 40000,\n", "  'oi_day_high': 40000,\n", "  'oi_day_low': 29600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 43.95,\n", "  'upper_circuit_limit': 174.15,\n", "  'ohlc': {'open': 94.25, 'high': 99.35, 'low': 89.6, 'close': 109.05},\n", "  'depth': {'buy': [{'price': 92.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 92.1, 'quantity': 400, 'orders': 1},\n", "    {'price': 91.95, 'quantity': 400, 'orders': 1},\n", "    {'price': 91.7, 'quantity': 400, 'orders': 1},\n", "    {'price': 91.55, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 93.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 93.5, 'quantity': 400, 'orders': 1},\n", "    {'price': 93.6, 'quantity': 400, 'orders': 1},\n", "    {'price': 93.7, 'quantity': 400, 'orders': 1},\n", "    {'price': 93.75, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1700CE': {'instrument_token': 27081986,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 11),\n", "  'last_price': 3.5,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 628800,\n", "  'sell_quantity': 412400,\n", "  'volume': 2018000,\n", "  'average_price': 3.88,\n", "  'oi': 2087200,\n", "  'oi_day_high': 2525200,\n", "  'oi_day_low': 2061200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 23.65,\n", "  'ohlc': {'open': 4.1, 'high': 4.45, 'low': 3.1, 'close': 3.65},\n", "  'depth': {'buy': [{'price': 3.55, 'quantity': 4000, 'orders': 5},\n", "    {'price': 3.5, 'quantity': 7200, 'orders': 13},\n", "    {'price': 3.45, 'quantity': 12400, 'orders': 16},\n", "    {'price': 3.4, 'quantity': 11200, 'orders': 16},\n", "    {'price': 3.35, 'quantity': 15600, 'orders': 15}],\n", "   'sell': [{'price': 3.6, 'quantity': 1200, 'orders': 2},\n", "    {'price': 3.65, 'quantity': 10400, 'orders': 12},\n", "    {'price': 3.7, 'quantity': 8400, 'orders': 11},\n", "    {'price': 3.75, 'quantity': 8800, 'orders': 9},\n", "    {'price': 3.8, 'quantity': 8400, 'orders': 8}]}},\n", " 'NFO:INFY25MAY1700PE': {'instrument_token': 27082242,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 10, 36),\n", "  'last_price': 111,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 55200,\n", "  'sell_quantity': 62400,\n", "  'volume': 35600,\n", "  'average_price': 114.69,\n", "  'oi': 149600,\n", "  'oi_day_high': 150000,\n", "  'oi_day_low': 140000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 61.4,\n", "  'upper_circuit_limit': 198.1,\n", "  'ohlc': {'open': 127, 'high': 127, 'low': 108.35, 'close': 129.75},\n", "  'depth': {'buy': [{'price': 110.5, 'quantity': 400, 'orders': 1},\n", "    {'price': 110.45, 'quantity': 800, 'orders': 2},\n", "    {'price': 110.4, 'quantity': 400, 'orders': 1},\n", "    {'price': 110.05, 'quantity': 400, 'orders': 1},\n", "    {'price': 110, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 112.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 112.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 112.6, 'quantity': 800, 'orders': 2},\n", "    {'price': 112.65, 'quantity': 400, 'orders': 1},\n", "    {'price': 112.7, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1720CE': {'instrument_token': 24358658,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 6, 28),\n", "  'last_price': 2.2,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 462000,\n", "  'sell_quantity': 165600,\n", "  'volume': 389600,\n", "  'average_price': 2.54,\n", "  'oi': 559200,\n", "  'oi_day_high': 591600,\n", "  'oi_day_low': 556000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 22.5,\n", "  'ohlc': {'open': 2.5, 'high': 3, 'low': 2, 'close': 2.5},\n", "  'depth': {'buy': [{'price': 2.25, 'quantity': 11200, 'orders': 14},\n", "    {'price': 2.2, 'quantity': 12000, 'orders': 19},\n", "    {'price': 2.15, 'quantity': 10800, 'orders': 17},\n", "    {'price': 2.1, 'quantity': 8000, 'orders': 12},\n", "    {'price': 2.05, 'quantity': 6800, 'orders': 10}],\n", "   'sell': [{'price': 2.35, 'quantity': 10400, 'orders': 10},\n", "    {'price': 2.4, 'quantity': 10000, 'orders': 10},\n", "    {'price': 2.45, 'quantity': 9200, 'orders': 8},\n", "    {'price': 2.5, 'quantity': 16000, 'orders': 11},\n", "    {'price': 2.55, 'quantity': 8800, 'orders': 7}]}},\n", " 'NFO:INFY25MAY1720PE': {'instrument_token': 24358914,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 51),\n", "  'last_price': 136.85,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 44800,\n", "  'sell_quantity': 52400,\n", "  'volume': 1600,\n", "  'average_price': 135.51,\n", "  'oi': 12800,\n", "  'oi_day_high': 13200,\n", "  'oi_day_low': 12400,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 74.75,\n", "  'upper_circuit_limit': 216.65,\n", "  'ohlc': {'open': 139.3, 'high': 139.3, 'low': 131, 'close': 145.7},\n", "  'depth': {'buy': [{'price': 127.5, 'quantity': 800, 'orders': 2},\n", "    {'price': 127.45, 'quantity': 800, 'orders': 2},\n", "    {'price': 127.4, 'quantity': 400, 'orders': 1},\n", "    {'price': 125.55, 'quantity': 1200, 'orders': 3},\n", "    {'price': 125.3, 'quantity': 2400, 'orders': 1}],\n", "   'sell': [{'price': 131.5, 'quantity': 800, 'orders': 2},\n", "    {'price': 131.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 131.85, 'quantity': 400, 'orders': 1},\n", "    {'price': 132.25, 'quantity': 400, 'orders': 1},\n", "    {'price': 132.4, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1740CE': {'instrument_token': 27082498,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 6, 7),\n", "  'last_price': 1.5,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 474800,\n", "  'sell_quantity': 123200,\n", "  'volume': 201200,\n", "  'average_price': 1.81,\n", "  'oi': 729600,\n", "  'oi_day_high': 738400,\n", "  'oi_day_low': 728000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.75,\n", "  'ohlc': {'open': 1.85, 'high': 2.15, 'low': 1.3, 'close': 1.75},\n", "  'depth': {'buy': [{'price': 1.45, 'quantity': 1600, 'orders': 4},\n", "    {'price': 1.4, 'quantity': 2400, 'orders': 6},\n", "    {'price': 1.35, 'quantity': 6000, 'orders': 7},\n", "    {'price': 1.3, 'quantity': 14000, 'orders': 5},\n", "    {'price': 1.25, 'quantity': 4400, 'orders': 4}],\n", "   'sell': [{'price': 1.5, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.55, 'quantity': 2000, 'orders': 5},\n", "    {'price': 1.6, 'quantity': 3200, 'orders': 5},\n", "    {'price': 1.65, 'quantity': 6000, 'orders': 7},\n", "    {'price': 1.7, 'quantity': 3200, 'orders': 5}]}},\n", " 'NFO:INFY25MAY1740PE': {'instrument_token': 27082754,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 5, 12, 15, 15),\n", "  'last_price': 115.15,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 34000,\n", "  'sell_quantity': 32400,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 10400,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 96.65,\n", "  'upper_circuit_limit': 242.65,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 115.15},\n", "  'depth': {'buy': [{'price': 146.2, 'quantity': 800, 'orders': 2},\n", "    {'price': 146.15, 'quantity': 400, 'orders': 1},\n", "    {'price': 146.05, 'quantity': 400, 'orders': 1},\n", "    {'price': 145.95, 'quantity': 400, 'orders': 1},\n", "    {'price': 145.2, 'quantity': 4400, 'orders': 2}],\n", "   'sell': [{'price': 155.2, 'quantity': 1200, 'orders': 1},\n", "    {'price': 155.25, 'quantity': 1600, 'orders': 1},\n", "    {'price': 155.35, 'quantity': 3600, 'orders': 1},\n", "    {'price': 157.7, 'quantity': 6000, 'orders': 2},\n", "    {'price': 160.9, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1760CE': {'instrument_token': 24359170,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 4, 27),\n", "  'last_price': 1.15,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 429200,\n", "  'sell_quantity': 93600,\n", "  'volume': 96800,\n", "  'average_price': 1.36,\n", "  'oi': 287600,\n", "  'oi_day_high': 306000,\n", "  'oi_day_low': 287600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.4,\n", "  'ohlc': {'open': 1.45, 'high': 1.65, 'low': 1.05, 'close': 1.4},\n", "  'depth': {'buy': [{'price': 1.15, 'quantity': 3600, 'orders': 6},\n", "    {'price': 1.1, 'quantity': 2800, 'orders': 6},\n", "    {'price': 1.05, 'quantity': 1600, 'orders': 4},\n", "    {'price': 1, 'quantity': 2400, 'orders': 5},\n", "    {'price': 0.95, 'quantity': 1200, 'orders': 3}],\n", "   'sell': [{'price': 1.2, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.25, 'quantity': 1600, 'orders': 4},\n", "    {'price': 1.3, 'quantity': 2000, 'orders': 5},\n", "    {'price': 1.35, 'quantity': 4000, 'orders': 6},\n", "    {'price': 1.4, 'quantity': 2000, 'orders': 5}]}},\n", " 'NFO:INFY25MAY1760PE': {'instrument_token': 24359426,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 13, 12, 59, 6),\n", "  'last_price': 184.3,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 31600,\n", "  'sell_quantity': 32400,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 15200,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 109.7,\n", "  'upper_circuit_limit': 258.9,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 184.3},\n", "  'depth': {'buy': [{'price': 165.05, 'quantity': 400, 'orders': 1},\n", "    {'price': 165, 'quantity': 3600, 'orders': 1},\n", "    {'price': 160.9, 'quantity': 2000, 'orders': 1},\n", "    {'price': 159.05, 'quantity': 1600, 'orders': 1},\n", "    {'price': 159, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 175.45, 'quantity': 1600, 'orders': 1},\n", "    {'price': 177, 'quantity': 400, 'orders': 1},\n", "    {'price': 177.1, 'quantity': 3600, 'orders': 1},\n", "    {'price': 177.2, 'quantity': 400, 'orders': 1},\n", "    {'price': 177.3, 'quantity': 400, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1780CE': {'instrument_token': 27083010,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 41),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 1, 6),\n", "  'last_price': 1.05,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 143200,\n", "  'sell_quantity': 62800,\n", "  'volume': 32800,\n", "  'average_price': 1.23,\n", "  'oi': 61200,\n", "  'oi_day_high': 61600,\n", "  'oi_day_low': 58000,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21.25,\n", "  'ohlc': {'open': 1.55, 'high': 1.6, 'low': 1, 'close': 1.25},\n", "  'depth': {'buy': [{'price': 0.95, 'quantity': 2000, 'orders': 4},\n", "    {'price': 0.9, 'quantity': 3200, 'orders': 4},\n", "    {'price': 0.85, 'quantity': 2800, 'orders': 5},\n", "    {'price': 0.8, 'quantity': 2000, 'orders': 5},\n", "    {'price': 0.75, 'quantity': 1200, 'orders': 3}],\n", "   'sell': [{'price': 1.05, 'quantity': 800, 'orders': 2},\n", "    {'price': 1.1, 'quantity': 400, 'orders': 1},\n", "    {'price': 1.15, 'quantity': 1200, 'orders': 2},\n", "    {'price': 1.2, 'quantity': 1200, 'orders': 3},\n", "    {'price': 1.25, 'quantity': 1600, 'orders': 4}]}},\n", " 'NFO:INFY25MAY1780PE': {'instrument_token': 27083266,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 19),\n", "  'last_trade_time': datetime.datetime(2025, 2, 27, 15, 28, 43),\n", "  'last_price': 0,\n", "  'last_quantity': 450,\n", "  'buy_quantity': 31200,\n", "  'sell_quantity': 32400,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 132,\n", "  'upper_circuit_limit': 283.6,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 180.75},\n", "  'depth': {'buy': [{'price': 184.75, 'quantity': 3600, 'orders': 1},\n", "    {'price': 180.85, 'quantity': 2000, 'orders': 1},\n", "    {'price': 179, 'quantity': 1600, 'orders': 1},\n", "    {'price': 177.9, 'quantity': 4000, 'orders': 1},\n", "    {'price': 173.9, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 195.45, 'quantity': 1200, 'orders': 1},\n", "    {'price': 195.5, 'quantity': 3600, 'orders': 1},\n", "    {'price': 195.8, 'quantity': 1600, 'orders': 1},\n", "    {'price': 197.1, 'quantity': 2000, 'orders': 1},\n", "    {'price': 198.2, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1800CE': {'instrument_token': 24359682,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 13, 4, 16),\n", "  'last_price': 0.85,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 646000,\n", "  'sell_quantity': 170800,\n", "  'volume': 206800,\n", "  'average_price': 0.93,\n", "  'oi': 909600,\n", "  'oi_day_high': 931200,\n", "  'oi_day_low': 909600,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 21,\n", "  'ohlc': {'open': 1.05, 'high': 1.15, 'low': 0.8, 'close': 1},\n", "  'depth': {'buy': [{'price': 0.85, 'quantity': 12000, 'orders': 8},\n", "    {'price': 0.8, 'quantity': 24400, 'orders': 16},\n", "    {'price': 0.75, 'quantity': 26800, 'orders': 12},\n", "    {'price': 0.7, 'quantity': 32000, 'orders': 12},\n", "    {'price': 0.65, 'quantity': 27200, 'orders': 11}],\n", "   'sell': [{'price': 0.9, 'quantity': 9200, 'orders': 10},\n", "    {'price': 0.95, 'quantity': 7200, 'orders': 10},\n", "    {'price': 1, 'quantity': 22400, 'orders': 12},\n", "    {'price': 1.05, 'quantity': 9600, 'orders': 9},\n", "    {'price': 1.1, 'quantity': 7200, 'orders': 8}]}},\n", " 'NFO:INFY25MAY1800PE': {'instrument_token': 24359938,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 9, 20, 23),\n", "  'last_price': 210,\n", "  'last_quantity': 800,\n", "  'buy_quantity': 39600,\n", "  'sell_quantity': 50800,\n", "  'volume': 800,\n", "  'average_price': 210,\n", "  'oi': 66800,\n", "  'oi_day_high': 66800,\n", "  'oi_day_low': 66800,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 152.1,\n", "  'upper_circuit_limit': 305.4,\n", "  'ohlc': {'open': 210, 'high': 210, 'low': 210, 'close': 228.75},\n", "  'depth': {'buy': [{'price': 205.9, 'quantity': 400, 'orders': 1},\n", "    {'price': 205.85, 'quantity': 400, 'orders': 1},\n", "    {'price': 205.8, 'quantity': 800, 'orders': 2},\n", "    {'price': 205.75, 'quantity': 400, 'orders': 1},\n", "    {'price': 205.65, 'quantity': 400, 'orders': 1}],\n", "   'sell': [{'price': 210.3, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.35, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.45, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.8, 'quantity': 400, 'orders': 1},\n", "    {'price': 210.9, 'quantity': 2000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1840CE': {'instrument_token': 24360194,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 11, 3),\n", "  'last_trade_time': datetime.datetime(2025, 5, 14, 12, 58, 37),\n", "  'last_price': 0.65,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 72000,\n", "  'sell_quantity': 66000,\n", "  'volume': 41600,\n", "  'average_price': 0.78,\n", "  'oi': 59600,\n", "  'oi_day_high': 60400,\n", "  'oi_day_low': 45200,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 20.85,\n", "  'ohlc': {'open': 0.9, 'high': 1, 'low': 0.65, 'close': 0.85},\n", "  'depth': {'buy': [{'price': 0.65, 'quantity': 9600, 'orders': 7},\n", "    {'price': 0.6, 'quantity': 4000, 'orders': 9},\n", "    {'price': 0.55, 'quantity': 4000, 'orders': 8},\n", "    {'price': 0.5, 'quantity': 6800, 'orders': 8},\n", "    {'price': 0.45, 'quantity': 2400, 'orders': 6}],\n", "   'sell': [{'price': 0.75, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.8, 'quantity': 3200, 'orders': 3},\n", "    {'price': 0.85, 'quantity': 4000, 'orders': 8},\n", "    {'price': 0.9, 'quantity': 2400, 'orders': 6},\n", "    {'price': 0.95, 'quantity': 3600, 'orders': 7}]}},\n", " 'NFO:INFY25MAY1840PE': {'instrument_token': 24360450,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 31),\n", "  'last_trade_time': datetime.datetime(2024, 6, 27, 15, 9, 32),\n", "  'last_price': 0,\n", "  'last_quantity': 650,\n", "  'buy_quantity': 28800,\n", "  'sell_quantity': 28800,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 188.95,\n", "  'upper_circuit_limit': 344.25,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 123.95},\n", "  'depth': {'buy': [{'price': 241.65, 'quantity': 1200, 'orders': 1},\n", "    {'price': 241.6, 'quantity': 1600, 'orders': 1},\n", "    {'price': 239.3, 'quantity': 2000, 'orders': 1},\n", "    {'price': 237.4, 'quantity': 4000, 'orders': 1},\n", "    {'price': 231.65, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 255.4, 'quantity': 1200, 'orders': 1},\n", "    {'price': 255.45, 'quantity': 1600, 'orders': 1},\n", "    {'price': 256.25, 'quantity': 2000, 'orders': 1},\n", "    {'price': 258.85, 'quantity': 4000, 'orders': 1},\n", "    {'price': 259.4, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY1880CE': {'instrument_token': 24360706,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 31),\n", "  'last_trade_time': datetime.datetime(2024, 6, 27, 15, 9, 32),\n", "  'last_price': 0,\n", "  'last_quantity': 650,\n", "  'buy_quantity': 16800,\n", "  'sell_quantity': 30000,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 0.05,\n", "  'upper_circuit_limit': 20.15,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 66.1},\n", "  'depth': {'buy': [{'price': 0.55, 'quantity': 400, 'orders': 1},\n", "    {'price': 0.25, 'quantity': 2000, 'orders': 1},\n", "    {'price': 0.2, 'quantity': 8000, 'orders': 1},\n", "    {'price': 0.15, 'quantity': 4000, 'orders': 1},\n", "    {'price': 0.1, 'quantity': 2400, 'orders': 1}],\n", "   'sell': [{'price': 0.8, 'quantity': 1200, 'orders': 2},\n", "    {'price': 0.85, 'quantity': 1600, 'orders': 4},\n", "    {'price': 0.9, 'quantity': 8000, 'orders': 1},\n", "    {'price': 1, 'quantity': 400, 'orders': 1},\n", "    {'price': 1.45, 'quantity': 2800, 'orders': 2}]}},\n", " 'NFO:INFY25MAY1880PE': {'instrument_token': 24360962,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 31),\n", "  'last_trade_time': datetime.datetime(2024, 6, 27, 15, 2, 51),\n", "  'last_price': 0,\n", "  'last_quantity': 650,\n", "  'buy_quantity': 28800,\n", "  'sell_quantity': 28800,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 0,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 228.1,\n", "  'upper_circuit_limit': 384.4,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 148.5},\n", "  'depth': {'buy': [{'price': 281.45, 'quantity': 1200, 'orders': 1},\n", "    {'price': 281.4, 'quantity': 1600, 'orders': 1},\n", "    {'price': 279.25, 'quantity': 2000, 'orders': 1},\n", "    {'price': 277.2, 'quantity': 4000, 'orders': 1},\n", "    {'price': 271.55, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 296.05, 'quantity': 1200, 'orders': 1},\n", "    {'price': 296.1, 'quantity': 2000, 'orders': 1},\n", "    {'price': 296.25, 'quantity': 1600, 'orders': 1},\n", "    {'price': 298.65, 'quantity': 4000, 'orders': 1},\n", "    {'price': 299.3, 'quantity': 4000, 'orders': 1}]}},\n", " 'NFO:INFY25MAY2000PE': {'instrument_token': 24362498,\n", "  'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 31),\n", "  'last_trade_time': datetime.datetime(2025, 5, 12, 9, 51, 2),\n", "  'last_price': 435,\n", "  'last_quantity': 400,\n", "  'buy_quantity': 12400,\n", "  'sell_quantity': 11200,\n", "  'volume': 0,\n", "  'average_price': 0,\n", "  'oi': 68800,\n", "  'oi_day_high': 0,\n", "  'oi_day_low': 0,\n", "  'net_change': 0,\n", "  'lower_circuit_limit': 347.35,\n", "  'upper_circuit_limit': 504.25,\n", "  'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 435},\n", "  'depth': {'buy': [{'price': 400.35, 'quantity': 800, 'orders': 1},\n", "    {'price': 400.3, 'quantity': 1600, 'orders': 1},\n", "    {'price': 398, 'quantity': 2000, 'orders': 1},\n", "    {'price': 396.1, 'quantity': 4000, 'orders': 1},\n", "    {'price': 390.35, 'quantity': 4000, 'orders': 1}],\n", "   'sell': [{'price': 410.35, 'quantity': 400, 'orders': 1},\n", "    {'price': 414.45, 'quantity': 800, 'orders': 1},\n", "    {'price': 415.95, 'quantity': 2000, 'orders': 1},\n", "    {'price': 418.75, 'quantity': 4000, 'orders': 1},\n", "    {'price': 419.1, 'quantity': 4000, 'orders': 1}]}}}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["quotes"]}, {"cell_type": "code", "execution_count": 24, "id": "e9059b0f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'instrument_token': 27047426,\n", " 'timestamp': datetime.datetime(2025, 5, 14, 13, 10, 40),\n", " 'last_trade_time': datetime.datetime(2025, 4, 24, 9, 46, 17),\n", " 'last_price': 339,\n", " 'last_quantity': 400,\n", " 'buy_quantity': 27600,\n", " 'sell_quantity': 28800,\n", " 'volume': 0,\n", " 'average_price': 0,\n", " 'oi': 400,\n", " 'oi_day_high': 0,\n", " 'oi_day_low': 0,\n", " 'net_change': 0,\n", " 'lower_circuit_limit': 353.4,\n", " 'upper_circuit_limit': 510.3,\n", " 'ohlc': {'open': 0, 'high': 0, 'low': 0, 'close': 339},\n", " 'depth': {'buy': [{'price': 442.5, 'quantity': 1600, 'orders': 1},\n", "   {'price': 442.3, 'quantity': 2000, 'orders': 1},\n", "   {'price': 440.15, 'quantity': 4000, 'orders': 1},\n", "   {'price': 439.1, 'quantity': 4000, 'orders': 1},\n", "   {'price': 398.3, 'quantity': 16000, 'orders': 1}],\n", "  'sell': [{'price': 459.65, 'quantity': 1200, 'orders': 1},\n", "   {'price': 459.7, 'quantity': 1600, 'orders': 1},\n", "   {'price': 459.8, 'quantity': 4000, 'orders': 1},\n", "   {'price': 459.95, 'quantity': 2000, 'orders': 1},\n", "   {'price': 467.6, 'quantity': 4000, 'orders': 1}]}}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["quotes['NFO:INFY25MAY1140CE']"]}, {"cell_type": "markdown", "id": "ac0831dd", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "Although these fields may feel dense at first, they all fall into a few basic categories:\n", "\n", "1. **Identifiers & Timing**: how we know *which* contract and *when* data refers to.\n", "2. **Trade Details**: what the *last* trade looked like.\n", "3. **Aggregated Activity**: totals of buying, selling, and volume.\n", "4. **Price Metrics & Changes**: current price, how it’s moved, and average costs.\n", "5. **Limits & Ranges**: exchange‑imposed price bounds and daily high/low info.\n", "6. **Depth**: the order book’s top bids and asks, showing market eagerness.\n", "\n", "---\n", "\n", "## 1. Identifiers & Timing\n", "\n", "These let you pin down *which* instrument this is and *when* events occurred.\n", "\n", "* **instrument\\_token**\n", "  A unique numeric ID that Zerodha assigns to each contract every trading day. Think of it as the contract’s “student ID”—it tells the system exactly which option you’re working with ([kite.trade][1]).\n", "\n", "* **timestamp**\n", "  The exact moment (date & time) when this data snapshot was generated. In your example, `2025‑05‑14 13:10:40` tells you that all the stats below are as of 1:10 PM IST on May 14, 2025.\n", "\n", "* **last\\_trade\\_time**\n", "  When the *most recent* actual trade occurred. Here, the last exchange transaction was at `2025‑04‑24 09:46:17`. It can differ from the current `timestamp` if no trades happened in the interim ([Investopedia][2]).\n", "\n", "---\n", "\n", "## 2. Trade Details\n", "\n", "What happened in the *last* deal on the exchange:\n", "\n", "* **last\\_price**\n", "  The price at which the **last** contract was traded (called LTP or “last traded price”). For example, ₹339 means the most recent buyer and seller agreed on ₹339 per option share ([Investopedia][2]).\n", "\n", "* **last\\_quantity**\n", "  How many contracts changed hands in that single last trade—400 in your case ([kite.trade][3]).\n", "\n", "---\n", "\n", "## 3. Aggregated Activity\n", "\n", "Totals over the trading session, showing overall buying, selling, and turnover.\n", "\n", "* **buy\\_quantity** & **sell\\_quantity**\n", "  These are the sum of all buy orders and sell orders executed so far today—27,600 contracts bought, 28,800 sold. Think of it as two buckets tallying every share bought vs. sold since open ([Coinbase][4]).\n", "\n", "* **volume**\n", "  The *number* of contracts that have traded today. Often it equals the sum of all trade quantities; sometimes exchanges report it differently, but it shows how busy this contract has been ([Investopedia][5]).\n", "\n", "* **average\\_price**\n", "  The average price at which trades have occurred today—if you weighted every trade by its size and averaged out. (Here it’s zero, likely because the exchange hasn’t reported it yet.) ([The Motley Fool][6]).\n", "\n", "---\n", "\n", "## 4. Price Metrics & Changes\n", "\n", "Putting that last price into context with ongoing trends.\n", "\n", "* **net\\_change**\n", "  The difference between **today’s** last traded price and **yesterday’s** closing price—how much the contract has gained or lost. A net\\_change of `0` means it’s unchanged from the previous close ([Investopedia][7]).\n", "\n", "* **oi (Open Interest)**\n", "  The total number of outstanding (open) option contracts that *haven’t* been exercised or closed. It tells you how many bets remain live—here, 400 outstanding contracts ([Investopedia][8]).\n", "\n", "* **oi\\_day\\_high** & **oi\\_day\\_low**\n", "  The highest and lowest open interest recorded *today*. These track shifts in the number of open bets—both zero here, perhaps because OI updates happen only end‑of‑day on some exchanges.\n", "\n", "---\n", "\n", "## 5. Limits & Ranges\n", "\n", "Exchanges set daily boundaries to curb extreme volatility.\n", "\n", "* **lower\\_circuit\\_limit** & **upper\\_circuit\\_limit**\n", "  The minimum and maximum prices allowed today—₹353.40 (lower) and ₹510.30 (upper) in your example. Once prices hit these, trading pauses to prevent runaway moves ([5paisa][9]).\n", "\n", "* **ohlc**\n", "  A mini‑bundle of key daily price points:\n", "\n", "  * **open**: price at today’s first trade\n", "  * **high**: highest price so far\n", "  * **low**: lowest price so far\n", "  * **close**: price at the last available trade\n", "    In your payload, only `close` is populated (₹339), showing it as the day’s #1 anchor point ([Wikipedia][10]).\n", "\n", "---\n", "\n", "## 6. <PERSON><PERSON><PERSON>\n", "\n", "A peek into the order book’s *top* bids (buyers) and asks (sellers).\n", "\n", "* **depth.buy**\n", "  A list of up to 5 current best **bid** orders:\n", "\n", "  1. **price** buyers are willing to pay\n", "  2. **quantity** how many contracts at that price\n", "  3. **orders** how many distinct orders sit there\n", "     E.g., a bid at ₹442.50 for 1,600 contracts ([Investopedia][11]).\n", "\n", "* **depth.sell**\n", "  Similarly, the top 5 **ask** orders (prices sellers want). E.g., sellers offering at ₹459.65 for 1,200 contracts ([Investopedia][11]).\n", "\n", "---\n", "\n", "### Putting It All Together\n", "\n", "* **Identifiers & Timing** tell you *which* contract and *when* you’re looking at.\n", "* **Trade & Aggregates** give both the *last* deal and the *total* session activity.\n", "* **Price Metrics** show current price, how it’s moved, and average costs.\n", "* **Limits & Ranges** show daily allowed boundaries and high/low.\n", "* **Depth** reveals the nearest buyers and sellers still waiting in the book.\n", "\n"]}, {"cell_type": "markdown", "id": "bd552ecf", "metadata": {}, "source": ["## Margin calculation API"]}, {"cell_type": "markdown", "id": "9413bcce", "metadata": {}, "source": ["### Fetch order margins"]}, {"cell_type": "code", "execution_count": 25, "id": "78159112", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Order Margins: [{'type': 'equity', 'tradingsymbol': 'INFY', 'exchange': 'NSE', 'span': 0, 'exposure': 0, 'option_premium': 0, 'additional': 0, 'bo': 0, 'cash': 0, 'var': 31848, 'pnl': {'realised': 0, 'unrealised': 0}, 'leverage': 5, 'charges': {'transaction_tax': 0, 'transaction_tax_type': 'stt', 'exchange_turnover_charge': 4.888668, 'sebi_turnover_charge': 0.15924, 'brokerage': 20, 'stamp_duty': 5, 'gst': {'igst': 4.50862344, 'cgst': 0, 'sgst': 0, 'total': 4.50862344}, 'total': 34.55653144}, 'total': 31848}]\n"]}], "source": ["# 3. Define the order(s) whose margin you want to simulate\n", "orders = [\n", "    {\n", "        \"exchange\":       \"NSE\",         # e.g. NSE, NFO :contentReference[oaicite:7]{index=7}\n", "        \"tradingsymbol\":  \"INFY\",        # instrument code :contentReference[oaicite:8]{index=8}\n", "        \"transaction_type\":\"BUY\",        # BUY or SELL :contentReference[oaicite:9]{index=9}\n", "        \"variety\":        \"regular\",     # regular, amo, co, etc. :contentReference[oaicite:10]{index=10}\n", "        \"product\":        \"MIS\",         # CNC, NRML, MIS, etc. :contentReference[oaicite:11]{index=11}\n", "        \"order_type\":     \"MARKET\",      # MARKET, LIMIT, SL, etc. :contentReference[oaicite:12]{index=12}\n", "        \"quantity\":       100,           # number of shares or lots :contentReference[oaicite:13]{index=13}\n", "        \"price\":          0,             # for MARKET orders can be 0 :contentReference[oaicite:14]{index=14}\n", "        \"trigger_price\":  0              # for SL orders only :contentReference[oaicite:15]{index=15}\n", "    }\n", "]\n", "\n", "# 4. Call the Order Margins API\n", "margins_response = kite.order_margins(params=orders)\n", "print(\"Order Margins:\", margins_response)\n"]}, {"cell_type": "code", "execution_count": 26, "id": "4740243c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'type': 'equity',\n", "  'tradingsymbol': 'INFY',\n", "  'exchange': 'NSE',\n", "  'span': 0,\n", "  'exposure': 0,\n", "  'option_premium': 0,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 31848,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 5,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': 'stt',\n", "   'exchange_turnover_charge': 4.888668,\n", "   'sebi_turnover_charge': 0.15924,\n", "   'brokerage': 20,\n", "   'stamp_duty': 5,\n", "   'gst': {'igst': 4.50862344, 'cgst': 0, 'sgst': 0, 'total': 4.50862344},\n", "   'total': 34.55653144},\n", "  'total': 31848}]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["margins_response"]}, {"cell_type": "markdown", "id": "d49b3be7", "metadata": {}, "source": ["\n", "---\n", "\n", "### Payload Parameters\n", "\n", "#### 1. `exchange`\n", "\n", "The `exchange` field tells Kite Connect **which market** the instrument is listed on—such as `NSE` for equity or `NFO` for derivative options/futures ([kite.trade][1]).\n", "\n", "#### 2. `tradingsymbol`\n", "\n", "`tradingsymbol` is the **unique code** for the instrument you want to trade—for example, `\"INFY\"` for Infosys equity or `\"INFY25MAY1580CE\"` for a specific call option ([kite.trade][1]).\n", "\n", "#### 3. `transaction_type`\n", "\n", "This indicates your **intent**: `\"BUY\"` if you want to acquire the instrument, or `\"SELL\"` if you want to offload it ([kite.trade][1]).\n", "\n", "#### 4. `variety`\n", "\n", "`variety` defines the **order category** or “flavor”—common values include:\n", "\n", "* `regular` for standard intraday or delivery orders\n", "* `amo` for After Market Orders\n", "* `co` for Cover Orders\n", "* `bo` for Bracket Orders ([kite.trade][1]).\n", "\n", "#### 5. `product`\n", "\n", "The `product` parameter controls **margin treatment**:\n", "\n", "* `CNC` (Cash & Carry) locks full capital for delivery trades\n", "* `MIS` (Margin Intraday Squareoff) uses leverage for intraday trades\n", "* `NRML` (Normal) for futures/options without intraday square‑off ([kite.trade][1]).\n", "\n", "#### 6. `order_type`\n", "\n", "This specifies **how the price is set**:\n", "\n", "* `MARKET` executes immediately at the prevailing price\n", "* `LIMIT` executes only at or better than a specified price\n", "* `SL` (Stop‑Loss) and `SL-M` (Stop‑Loss Market) trigger a market or limit order when a price threshold is hit ([kite.trade][2]).\n", "\n", "#### 7. `quantity`\n", "\n", "`quantity` is simply the **number of units** you wish to trade: shares for equity or the lot-equivalent number for futures and options ([kite.trade][1]).\n", "\n", "#### 8. `price`\n", "\n", "For **LIMIT** orders, `price` is the exact rate at which you want to transact; for **MARKET** orders it’s conventionally set to `0` because the trade happens at best available rates ([kite.trade][1]).\n", "\n", "#### 9. `trigger_price`\n", "\n", "Used only with **SL**, **SL-M**, or **CO** orders, `trigger_price` defines the **threshold** at which your stop‑loss order converts into a market or limit order ([kite.trade][3]).\n", "\n", "---\n", "\n", "### How It All Fits Together\n", "\n", "These parameters collectively tell the Kite Connect API **which instrument**, **which exchange**, **whether you’re buying or selling**, **what kind of order** you need, and **how many units** to execute—ensuring the platform can perform margin checks, risk validations, and order routing correctly before sending your trade to the exchange.\n"]}, {"cell_type": "markdown", "id": "22d52a42", "metadata": {}, "source": ["### Fetch Fetch Basket (Spread) Margins: <br>\n", "For multi‑leg orders (e.g. option spreads), you can compute net margins and spread benefits:"]}, {"cell_type": "code", "execution_count": 31, "id": "1f8fbcaa", "metadata": {}, "outputs": [], "source": ["# 2. Define your two-leg INFY spread\n", "basket_orders = [\n", "    {\n", "        \"exchange\":        \"NFO\",                # NFO segment for Options\n", "        \"tradingsymbol\":   \"INFY25MAY1580CE\",    # Call option\n", "        \"transaction_type\":\"BUY\",\n", "        \"variety\":         \"regular\",\n", "        \"product\":         \"NRML\",               # NRML for position carry\n", "        \"order_type\":      \"MARKET\",\n", "        \"quantity\":        75,                   # Lot size (e.g. 75)\n", "        \"price\":           0,\n", "        \"trigger_price\":   0\n", "    },\n", "    {\n", "        \"exchange\":        \"NFO\",\n", "        \"tradingsymbol\":   \"INFY25MAY1580PE\",    # Put option\n", "        \"transaction_type\":\"SELL\",\n", "        \"variety\":         \"regular\",\n", "        \"product\":         \"NRML\",\n", "        \"order_type\":      \"MARKET\",\n", "        \"quantity\":        75,\n", "        \"price\":           0,\n", "        \"trigger_price\":   0\n", "    }\n", "]\n", "\n", "# 3. <PERSON>tch basket margins (considering your existing positions)\n", "basket_margins = kite.basket_order_margins(\n", "    params=basket_orders,\n", "    consider_positions=True,   # include your open positions in the calculation\n", "    mode=\"full\"                # could also use \"compact\" to just get totals\n", ")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "id": "900a12b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'initial': {'type': '',\n", "  'tradingsymbol': '',\n", "  'exchange': '',\n", "  'span': 16773,\n", "  'exposure': 4242.656250000001,\n", "  'option_premium': 2958.75,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 0,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': '',\n", "   'exchange_turnover_charge': 0,\n", "   'sebi_turnover_charge': 0,\n", "   'brokerage': 0,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 0, 'cgst': 0, 'sgst': 0, 'total': 0},\n", "   'total': 0},\n", "  'total': 23974.40625},\n", " 'final': {'type': '',\n", "  'tradingsymbol': '',\n", "  'exchange': '',\n", "  'span': 16773,\n", "  'exposure': 4242.656250000001,\n", "  'option_premium': 1170,\n", "  'additional': 0,\n", "  'bo': 0,\n", "  'cash': 0,\n", "  'var': 0,\n", "  'pnl': {'realised': 0, 'unrealised': 0},\n", "  'leverage': 0,\n", "  'charges': {'transaction_tax': 0,\n", "   'transaction_tax_type': '',\n", "   'exchange_turnover_charge': 0,\n", "   'sebi_turnover_charge': 0,\n", "   'brokerage': 0,\n", "   'stamp_duty': 0,\n", "   'gst': {'igst': 0, 'cgst': 0, 'sgst': 0, 'total': 0},\n", "   'total': 0},\n", "  'total': 22185.65625},\n", " 'orders': [{'type': 'equity',\n", "   'tradingsymbol': 'INFY25MAY1580CE',\n", "   'exchange': 'NFO',\n", "   'span': 0,\n", "   'exposure': 0,\n", "   'option_premium': 2958.75,\n", "   'additional': 0,\n", "   'bo': 0,\n", "   'cash': 0,\n", "   'var': 0,\n", "   'pnl': {'realised': 0, 'unrealised': 0},\n", "   'leverage': 1,\n", "   'charges': {'transaction_tax': 0,\n", "    'transaction_tax_type': 'stt',\n", "    'exchange_turnover_charge': 1.*********,\n", "    'sebi_turnover_charge': 0.00295875,\n", "    'brokerage': 20,\n", "    'stamp_duty': 0,\n", "    'gst': {'igst': 3.7897564725, 'cgst': 0, 'sgst': 0, 'total': 3.7897564725},\n", "    'total': 24.8439590975},\n", "   'total': 2958.75},\n", "  {'type': 'equity',\n", "   'tradingsymbol': 'INFY25MAY1580PE',\n", "   'exchange': 'NFO',\n", "   'span': 16773,\n", "   'exposure': 4242.656250000001,\n", "   'option_premium': 0,\n", "   'additional': 0,\n", "   'bo': 0,\n", "   'cash': 0,\n", "   'var': 0,\n", "   'pnl': {'realised': 0, 'unrealised': 0},\n", "   'leverage': 1,\n", "   'charges': {'transaction_tax': 1.78875,\n", "    'transaction_tax_type': 'stt',\n", "    'exchange_turnover_charge': 0.6355428750000001,\n", "    'sebi_turnover_charge': 0.00178875,\n", "    'brokerage': 20,\n", "    'stamp_duty': 0,\n", "    'gst': {'igst': 3.*********4999997,\n", "     'cgst': 0,\n", "     'sgst': 0,\n", "     'total': 3.*********4999997},\n", "    'total': 26.1408013175},\n", "   'total': 21015.65625}],\n", " 'charges': {'transaction_tax': 0,\n", "  'transaction_tax_type': '',\n", "  'exchange_turnover_charge': 0,\n", "  'sebi_turnover_charge': 0.0047475,\n", "  'brokerage': 40,\n", "  'stamp_duty': 0,\n", "  'gst': {'igst': 0, 'cgst': 0, 'sgst': 0, 'total': 0},\n", "  'total': 0}}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["basket_margins"]}, {"cell_type": "markdown", "id": "99a1c2bd", "metadata": {}, "source": ["### Summary\n", "\n", "When you call the **Basket Margins** API, the response gives you two main margin “blocks”—**initial** and **final**—alongside a per‑order breakdown and an overall **charges** summary.\n", "\n", "* **Initial** margins (`initial`) show the total capital you’d need if each leg were treated independently (no hedging benefits) ([kite.trade][1]).\n", "* **Final** margins (`final`) reflect net requirements after offsetting hedged positions, giving you your true blocked capital when both legs run together ([kite.trade][1]).\n", "* The **orders** array details margin components and charges for each option leg.\n", "* The **charges** object at the root aggregates certain fees (like brokerage) across the entire basket.\n", "\n", "Below is a field‑by‑field explanation of what each term means.\n", "\n", "---\n", "\n", "### Initial vs Final Blocks\n", "\n", "* **`initial.total`**\n", "  The sum of all margin components (SPAN + Exposure + Option Premium + ...) **without** any spread benefits. It’s your “raw” block if legs weren’t hedged ([kite.trade][1]).\n", "* **`final.total`**\n", "  Your net required margin **after** accounting for hedge offsets between legs (e.g., call vs. put of same strike/expiry), reflecting true capital commitment ([kite.trade][1]).\n", "\n", "Both `initial` and `final` include these shared components:\n", "\n", "* **`span`**: SPAN margin is the core risk‑based margin set by the exchange using a standardized portfolio analysis of risk over one day ([kite.trade][2], [Investopedia][3]).\n", "* **`exposure`**: Exposure margin (sometimes called “additional exposure”) covers extreme market moves beyond SPAN, typically a fixed % of notional for options and futures ([Zerodha Support][4]).\n", "* **`option_premium`**: For short (sold) options, this is the total premium you receive which offsets margin; for long options it’s zero here. It’s the net premium value credited against margin ([kite.trade][2], [Zerodha Support][5]).\n", "* **`additional`**: Any ad‑hoc margins or buffers the broker adds (often zero unless special conditions apply) ([kite.trade][2]).\n", "* **`bo`**: Margin specifically for Bracket Orders (if used), otherwise zero ([kite.trade][2]).\n", "* **`cash`**: Direct cash margin blocked, outside of SPAN/Exposure (rarely used in derivative margins) ([kite.trade][2]).\n", "* **`var`**: Value‑At‑Risk margin, a statistical measure of potential loss under normal market conditions over a specified time frame ([Zerodha Support][6]).\n", "* **`pnl`**:\n", "\n", "  * **`realised`**: Profits or losses already settled that affect margin.\n", "  * **`unrealised`**: Mark‑to‑market P\\&L on open positions, credited or debited from margin ([kite.trade][2]).\n", "* **`leverage`**: Shows effective leverage multiplier granted for this instrument or strategy (e.g., 1×, 2×) ([kite.trade][2]).\n", "* **`charges`**: A nested breakdown of transaction levies, described in detail below.\n", "\n", "---\n", "\n", "### Per‑Order Breakdown (`orders` Array)\n", "\n", "Each element in the `orders` list mirrors the fields above **for that single leg**, plus:\n", "\n", "* **`type`** & **`tradingsymbol`** & **`exchange`**: Identify the instrument you’re calculating for (e.g., `INFY25MAY1580CE` on `NFO`) ([kite.trade][2]).\n", "* **`total`**: The sum of all margin components for **that leg alone** ([kite.trade][2]).\n", "\n", "Example for the CE leg:\n", "\n", "* `span`: 0 (no SPAN on pure option premium) ([kite.trade][2]).\n", "* `exposure`: 0 (covered by option premium) ([kite.trade][2]).\n", "* `option_premium`: 2958.75 (your short premium received) ([Zerodha Support][5]).\n", "* `total`: 2958.75 (net block before charges) ([kite.trade][2]).\n", "\n", "---\n", "\n", "### Charges Breakdown\n", "\n", "Inside each leg’s `charges`, and at the root `charges`, you see:\n", "\n", "* **`transaction_tax`** & **`transaction_tax_type`** (STT/CTT)\n", "* **`exchange_turnover_charge`**: Fee levied by the exchange on turnover ([kite.trade][2]).\n", "* **`sebi_turnover_charge`**: SEBI’s regulatory charge ([kite.trade][2]).\n", "* **`brokerage`**: Your broker’s fee on the notional or a minimum per order ([kite.trade][2]).\n", "* **`stamp_duty`**: Government stamp duty on transactions ([kite.trade][2]).\n", "* **`gst`**: Goods & Services Tax on brokerage and other charges, broken into IGST, CGST, SGST, and total ([kite.trade][2]).\n", "* **`total`**: Sum of all above fees for that leg or for the overall basket ([kite.trade][2]).\n", "\n", "In your example:\n", "\n", "* **CE leg charges total** ≈ ₹ 24.84\n", "* **PE leg charges total** ≈ ₹ 26.14\n", "* **Basket-level charges** (combined brokerage, SEBI fees, etc.) appear under the root `charges`.\n", "\n", "---\n"]}, {"cell_type": "markdown", "id": "57305471", "metadata": {}, "source": ["## Historical candle data"]}, {"cell_type": "code", "execution_count": 34, "id": "768f2306", "metadata": {}, "outputs": [], "source": ["tradingsymbol = \"INFY25MAY1580CE\"\n", "token = 27076098\t\n", "from_date = \"2025-02-14\"  # 3 months ago from May 14, 2025\n", "to_date = \"2025-05-14\"\n", "interval=\"day\""]}, {"cell_type": "code", "execution_count": 36, "id": "fece53a1", "metadata": {}, "outputs": [], "source": ["data = kite.historical_data(\n", "            instrument_token=token,\n", "            from_date=from_date,\n", "            to_date=to_date,\n", "            interval=interval,\n", "            continuous=0,  # Single contract, not continuous\n", "            oi=1  # Include open interest\n", "        )\n"]}, {"cell_type": "code", "execution_count": 37, "id": "7213cca9", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'date': datetime.datetime(2025, 3, 28, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 70.55,\n", "  'high': 72.35,\n", "  'low': 70.55,\n", "  'close': 71.65,\n", "  'volume': 1600,\n", "  'oi': 1200},\n", " {'date': datetime.datetime(2025, 4, 1, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 50.85,\n", "  'high': 50.85,\n", "  'low': 50.85,\n", "  'close': 50.85,\n", "  'volume': 400,\n", "  'oi': 1200},\n", " {'date': datetime.datetime(2025, 4, 2, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 50.85,\n", "  'high': 50.85,\n", "  'low': 50.85,\n", "  'close': 50.85,\n", "  'volume': 0,\n", "  'oi': 1200},\n", " {'date': datetime.datetime(2025, 4, 3, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 50.85,\n", "  'high': 50.85,\n", "  'low': 50.85,\n", "  'close': 50.85,\n", "  'volume': 0,\n", "  'oi': 1200},\n", " {'date': datetime.datetime(2025, 4, 4, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 50.85,\n", "  'high': 50.85,\n", "  'low': 50.85,\n", "  'close': 50.85,\n", "  'volume': 0,\n", "  'oi': 1200},\n", " {'date': datetime.datetime(2025, 4, 7, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 50.85,\n", "  'high': 50.85,\n", "  'low': 50.85,\n", "  'close': 50.85,\n", "  'volume': 0,\n", "  'oi': 1200},\n", " {'date': datetime.datetime(2025, 4, 8, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 50.85,\n", "  'high': 50.85,\n", "  'low': 50.85,\n", "  'close': 50.85,\n", "  'volume': 0,\n", "  'oi': 1200},\n", " {'date': datetime.datetime(2025, 4, 9, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 19.8,\n", "  'high': 19.8,\n", "  'low': 17.7,\n", "  'close': 17.75,\n", "  'volume': 2000,\n", "  'oi': 2800},\n", " {'date': datetime.datetime(2025, 4, 11, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 20,\n", "  'high': 21.85,\n", "  'low': 19.25,\n", "  'close': 21.05,\n", "  'volume': 2000,\n", "  'oi': 2800},\n", " {'date': datetime.datetime(2025, 4, 15, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 18,\n", "  'high': 20,\n", "  'low': 18,\n", "  'close': 19.2,\n", "  'volume': 16400,\n", "  'oi': 8800},\n", " {'date': datetime.datetime(2025, 4, 16, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 20.15,\n", "  'high': 20.15,\n", "  'low': 14.3,\n", "  'close': 15.7,\n", "  'volume': 23200,\n", "  'oi': 22000},\n", " {'date': datetime.datetime(2025, 4, 17, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 11,\n", "  'high': 17.75,\n", "  'low': 9.5,\n", "  'close': 15.2,\n", "  'volume': 68800,\n", "  'oi': 36000},\n", " {'date': datetime.datetime(2025, 4, 21, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 15.35,\n", "  'high': 17.1,\n", "  'low': 9.5,\n", "  'close': 12.55,\n", "  'volume': 546000,\n", "  'oi': 218400},\n", " {'date': datetime.datetime(2025, 4, 22, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 10.15,\n", "  'high': 10.15,\n", "  'low': 7.9,\n", "  'close': 8.25,\n", "  'volume': 294800,\n", "  'oi': 280800},\n", " {'date': datetime.datetime(2025, 4, 23, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 10.4,\n", "  'high': 18.35,\n", "  'low': 10.4,\n", "  'close': 15.85,\n", "  'volume': 1037200,\n", "  'oi': 188800},\n", " {'date': datetime.datetime(2025, 4, 24, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 15.75,\n", "  'high': 16.55,\n", "  'low': 12.7,\n", "  'close': 13.9,\n", "  'volume': 618800,\n", "  'oi': 238000},\n", " {'date': datetime.datetime(2025, 4, 25, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 14.05,\n", "  'high': 19.8,\n", "  'low': 12.9,\n", "  'close': 16,\n", "  'volume': 1553200,\n", "  'oi': 333600},\n", " {'date': datetime.datetime(2025, 4, 28, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 14.85,\n", "  'high': 16.6,\n", "  'low': 11.75,\n", "  'close': 14.4,\n", "  'volume': 588000,\n", "  'oi': 289600},\n", " {'date': datetime.datetime(2025, 4, 29, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 14.25,\n", "  'high': 20.85,\n", "  'low': 12.6,\n", "  'close': 17,\n", "  'volume': 794400,\n", "  'oi': 302800},\n", " {'date': datetime.datetime(2025, 4, 30, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 17.2,\n", "  'high': 19.9,\n", "  'low': 15,\n", "  'close': 17.2,\n", "  'volume': 497600,\n", "  'oi': 321600},\n", " {'date': datetime.datetime(2025, 5, 2, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 18.9,\n", "  'high': 22.45,\n", "  'low': 15.8,\n", "  'close': 16.6,\n", "  'volume': 1091200,\n", "  'oi': 429600},\n", " {'date': datetime.datetime(2025, 5, 5, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 18.9,\n", "  'high': 20.85,\n", "  'low': 14.85,\n", "  'close': 15.45,\n", "  'volume': 591200,\n", "  'oi': 381200},\n", " {'date': datetime.datetime(2025, 5, 6, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 15.3,\n", "  'high': 17.4,\n", "  'low': 13.4,\n", "  'close': 15.95,\n", "  'volume': 529600,\n", "  'oi': 419600},\n", " {'date': datetime.datetime(2025, 5, 7, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 13.85,\n", "  'high': 16.15,\n", "  'low': 12.9,\n", "  'close': 13.95,\n", "  'volume': 530400,\n", "  'oi': 419600},\n", " {'date': datetime.datetime(2025, 5, 8, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 14.65,\n", "  'high': 15.5,\n", "  'low': 11.95,\n", "  'close': 14.25,\n", "  'volume': 596000,\n", "  'oi': 444800},\n", " {'date': datetime.datetime(2025, 5, 9, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 13.5,\n", "  'high': 15.3,\n", "  'low': 11.45,\n", "  'close': 11.95,\n", "  'volume': 750000,\n", "  'oi': 553200},\n", " {'date': datetime.datetime(2025, 5, 12, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 15,\n", "  'high': 71.9,\n", "  'low': 15,\n", "  'close': 66.8,\n", "  'volume': 4574400,\n", "  'oi': 438400},\n", " {'date': datetime.datetime(2025, 5, 13, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 48.05,\n", "  'high': 51.1,\n", "  'low': 29.1,\n", "  'close': 30.35,\n", "  'volume': 4152800,\n", "  'oi': 944000},\n", " {'date': datetime.datetime(2025, 5, 14, 0, 0, tzinfo=tzoffset(None, 19800)),\n", "  'open': 30.45,\n", "  'high': 40.75,\n", "  'low': 30,\n", "  'close': 39.45,\n", "  'volume': 3148000,\n", "  'oi': 687200}]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "markdown", "id": "e9963411", "metadata": {}, "source": ["## picking up options"]}, {"cell_type": "code", "execution_count": null, "id": "e708ed37", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}