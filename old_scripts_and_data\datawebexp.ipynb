{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pandas as pd    \n", "import requests "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["token = 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDAwOTg5IiwianRpIjoiOWEwZjY5MjgtNDM0MC00Y2NiLWI5NDYtODQ0ODEzZGNiMThkIiwiaXNzIjoiZGF0YXdlYiIsImlhdCI6MTc0MzU4NDI1NiwiZXhwIjoxNzQ0NzkzODU2fQ.SmhfqV5TIl1miKxGgXt3-gdxt6e9UZL5eStpyTc4tiReQyvbxheRpAYcXw-IdodYZ_7xy33sN-TiRnfv9QPLxw'\n", "baseUrl = 'https://datawebws.usitc.gov/dataweb'\n", "headers = {\n", "    \"Content-Type\": \"application/json; charset=utf-8\", \n", "    \"Authorization\": \"Bearer \" + token\n", "}\n", "\n", "requests.packages.urllib3.disable_warnings() "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Basic query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["basicQuery = {\n", "    \"savedQueryName\":\"\",\n", "    \"savedQueryDesc\":\"\",\n", "    \"isOwner\":True,\n", "    \"runMonthly\":<PERSON><PERSON><PERSON>,\n", "    \"reportOptions\":{\n", "        \"tradeType\":\"Import\",\n", "        \"classificationSystem\":\"HTS\"\n", "    },\n", "    \"searchOptions\":{\n", "        \"MiscGroup\":{\n", "            \"districts\":{\n", "                \"aggregation\":\"Aggregate District\",\n", "                \"districtGroups\":{\n", "                    \"userGroups\":[]\n", "                },\n", "                \"districts\":[],\n", "                \"districtsExpanded\":\n", "                    [\n", "                        {\n", "                            \"name\":\"All Districts\",\n", "                            \"value\":\"all\"\n", "                        }\n", "                    ],\n", "                \"districtsSelectType\":\"all\"\n", "            },\n", "            \"importPrograms\":{\n", "                \"aggregation\":None,\n", "                \"importPrograms\":[],\n", "                \"programsSelectType\":\"all\"\n", "            },\n", "            \"extImportPrograms\":{\n", "                \"aggregation\":\"Aggregate CSC\",\n", "                \"extImportPrograms\":[],\n", "                \"extImportProgramsExpanded\":[],\n", "                \"programsSelectType\":\"all\"\n", "            },\n", "            \"provisionCodes\":{\n", "                \"aggregation\":\"Aggregate RPCODE\",\n", "                \"provisionCodesSelectType\":\"all\",\n", "                \"rateProvisionCodes\":[],\n", "                \"rateProvisionCodesExpanded\":[]\n", "            }\n", "        },\n", "        \"commodities\":{\n", "            \"aggregation\":\"Aggregate Commodities\",\n", "            \"codeDisplayFormat\":\"YES\",\n", "            \"commodities\":[],\n", "            \"commoditiesExpanded\":[],\n", "            \"commoditiesManual\":\"\",\n", "            \"commodityGroups\":{\n", "                \"systemGroups\":[],\n", "                \"userGroups\":[]\n", "            },\n", "            \"commoditySelectType\":\"all\",\n", "            \"granularity\":\"2\",\n", "            \"groupGranularity\":None,\n", "            \"searchGranularity\":None\n", "        },\n", "        \"componentSettings\":{\n", "            \"dataToReport\":\n", "                [\n", "                    \"CONS_FIR_UNIT_QUANT\"\n", "                ],\n", "            \"scale\":\"1\",\n", "            \"timeframeSelectType\":\"fullYears\",\n", "            \"years\":\n", "                [\n", "                    \"2022\",\"2023\"\n", "                ],\n", "            \"startDate\":None,\n", "            \"endDate\":None,\n", "            \"startMonth\":None,\n", "            \"endMonth\":None,\n", "            \"yearsTimeline\":\"Annual\"\n", "        },\n", "        \"countries\":{\n", "            \"aggregation\":\"Aggregate Countries\",\n", "            \"countries\":[],\n", "            \"countriesExpanded\":\n", "                [\n", "                    {\n", "                        \"name\":\"All Countries\",\n", "                        \"value\":\"all\"\n", "                    }\n", "                ],\n", "            \"countriesSelectType\":\"all\",\n", "            \"countryGroups\":{\n", "                \"systemGroups\":[],\n", "                \"userGroups\":[]\n", "            }\n", "        }\n", "    },\n", "    \"sortingAndDataFormat\":{\n", "        \"DataSort\":{\n", "            \"columnOrder\":[],\n", "            \"fullColumnOrder\":[],\n", "            \"sortOrder\":[]\n", "        },\n", "        \"reportCustomizations\":{\n", "            \"exportCombineTables\":False,\n", "            \"showAllSubtotal\":True,\n", "            \"subtotalRecords\":\"\",\n", "            \"totalRecords\":\"20000\",\n", "            \"exportRawData\":False\n", "        }\n", "    }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["payload from the interface"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import json\n", "query = json.loads(\"\"\"{\"savedQueryType\":\"\",\"isOwner\":true,\"unitConversion\":\"0\",\"manualConversions\":[],\"reportOptions\":{\"tradeType\":\"Import\",\"classificationSystem\":\"NAIC\"},\"searchOptions\":{\"MiscGroup\":{\"districts\":{\"aggregation\":\"Aggregate District\",\"districtGroups\":{},\"districts\":[],\"districtsExpanded\":[{\"name\":\"All Districts\",\"value\":\"all\"}],\"districtsSelectType\":\"all\"},\"importPrograms\":{\"aggregation\":null,\"importPrograms\":[],\"programsSelectType\":\"all\"},\"extImportPrograms\":{\"aggregation\":\"Aggregate CSC\",\"extImportPrograms\":[],\"extImportProgramsExpanded\":[],\"programsSelectType\":\"all\"},\"provisionCodes\":{\"aggregation\":\"Aggregate RPCODE\",\"provisionCodesSelectType\":\"all\",\"rateProvisionCodes\":[],\"rateProvisionCodesExpanded\":[],\"rateProvisionGroups\":{\"systemGroups\":[]}}},\"commodities\":{\"aggregation\":\"Break Out Commodities\",\"codeDisplayFormat\":\"YES\",\"commodities\":[\"31\",\"32\",\"33\"],\"commoditiesExpanded\":[{\"name\":\"MANUFACTURING, PART 1\",\"value\":\"31\"},{\"name\":\"MANUFACTURING, PART 2\",\"value\":\"32\"},{\"name\":\"MANUFACTURING, PART 3\",\"value\":\"33\"}],\"commoditiesManual\":\"31,32,33\",\"commodityGroups\":{\"systemGroups\":[],\"userGroups\":[]},\"commoditySelectType\":\"list\",\"granularity\":\"4\",\"groupGranularity\":null,\"searchGranularity\":null,\"showHTSValidDetails\":\"\"},\"componentSettings\":{\"dataToReport\":[\"CONS_CUSTOMS_VALUE\",\"CONS_FIR_UNIT_QUANT\",\"CONS_SEC_UNIT_QUANT\"],\"scale\":\"1\",\"timeframeSelectType\":\"fullYears\",\"years\":[\"2024\",\"2023\"],\"startDate\":null,\"endDate\":null,\"startMonth\":null,\"endMonth\":null,\"yearsTimeline\":\"Annual\"},\"countries\":{\"aggregation\":\"Aggregate Countries\",\"countries\":[],\"countriesExpanded\":[{\"name\":\"All Countries\",\"value\":\"all\"}],\"countriesSelectType\":\"all\",\"countryGroups\":{\"systemGroups\":[],\"userGroups\":[]}}},\"sortingAndDataFormat\":{\"DataSort\":{\"columnOrder\":[\"NAICS4 & DESCRIPTION\"],\"fullColumnOrder\":[{\"hasChildren\":false,\"name\":\"NAICS4 & DESCRIPTION\",\"value\":\"NAICS4 & DESCRIPTION\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"}],\"sortOrder\":[{\"sortData\":\"NAICS4 & DESCRIPTION\",\"orderBy\":\"asc\",\"year\":\"\"}]},\"reportCustomizations\":{\"exportCombineTables\":false,\"totalRecords\":\"20000\",\"exportRawData\":false}},\"deletedCountryUserGroups\":[],\"deletedCommodityUserGroups\":[],\"deletedDistrictUserGroups\":[]}\"\"\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'savedQueryType': '',\n", " 'isOwner': True,\n", " 'unitConversion': '0',\n", " 'manualConversions': [],\n", " 'reportOptions': {'tradeType': 'Import', 'classificationSystem': 'NAIC'},\n", " 'searchOptions': {'MiscGroup': {'districts': {'aggregation': 'Aggregate District',\n", "    'districtGroups': {},\n", "    'districts': [],\n", "    'districtsExpanded': [{'name': 'All Districts', 'value': 'all'}],\n", "    'districtsSelectType': 'all'},\n", "   'importPrograms': {'aggregation': None,\n", "    'importPrograms': [],\n", "    'programsSelectType': 'all'},\n", "   'extImportPrograms': {'aggregation': 'Aggregate CSC',\n", "    'extImportPrograms': [],\n", "    'extImportProgramsExpanded': [],\n", "    'programsSelectType': 'all'},\n", "   'provisionCodes': {'aggregation': 'Aggregate RPCODE',\n", "    'provisionCodesSelectType': 'all',\n", "    'rateProvisionCodes': [],\n", "    'rateProvisionCodesExpanded': [],\n", "    'rateProvisionGroups': {'systemGroups': []}}},\n", "  'commodities': {'aggregation': 'Break Out Commodities',\n", "   'codeDisplayFormat': 'YES',\n", "   'commodities': ['31', '32', '33'],\n", "   'commoditiesExpanded': [{'name': 'MANUFACTURING, PART 1', 'value': '31'},\n", "    {'name': 'MANUFACTURING, PART 2', 'value': '32'},\n", "    {'name': 'MANUFACTURING, PART 3', 'value': '33'}],\n", "   'commoditiesManual': '31,32,33',\n", "   'commodityGroups': {'systemGroups': [], 'userGroups': []},\n", "   'commoditySelectType': 'list',\n", "   'granularity': '4',\n", "   'groupGranularity': None,\n", "   'searchGranularity': None,\n", "   'showHTSValidDetails': ''},\n", "  'componentSettings': {'dataToReport': ['CONS_CUSTOMS_VALUE',\n", "    'CONS_FIR_UNIT_QUANT',\n", "    'CONS_SEC_UNIT_QUANT'],\n", "   'scale': '1',\n", "   'timeframeSelectType': 'fullYears',\n", "   'years': ['2024', '2023'],\n", "   'startDate': None,\n", "   'endDate': None,\n", "   'startMonth': None,\n", "   'endMonth': None,\n", "   'yearsTimeline': 'Annual'},\n", "  'countries': {'aggregation': 'Aggregate Countries',\n", "   'countries': [],\n", "   'countriesExpanded': [{'name': 'All Countries', 'value': 'all'}],\n", "   'countriesSelectType': 'all',\n", "   'countryGroups': {'systemGroups': [], 'userGroups': []}}},\n", " 'sortingAndDataFormat': {'DataSort': {'columnOrder': ['NAICS4 & DESCRIPTION'],\n", "   'fullColumnOrder': [{'hasChildren': False,\n", "     'name': 'NAICS4 & DESCRIPTION',\n", "     'value': 'NAICS4 & DESCRIPTION',\n", "     'classificationSystem': '',\n", "     'groupUUID': '',\n", "     'items': [],\n", "     'tradeType': ''}],\n", "   'sortOrder': [{'sortData': 'NAICS4 & DESCRIPTION',\n", "     'orderBy': 'asc',\n", "     'year': ''}]},\n", "  'reportCustomizations': {'exportCombineTables': False,\n", "   'totalRecords': '20000',\n", "   'exportRawData': False}},\n", " 'deletedCountryUserGroups': [],\n", " 'deletedCommodityUserGroups': [],\n", " 'deletedDistrictUserGroups': []}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["query"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["requestData = query"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["response = requests.post(baseUrl+'/api/v2/report2/runReport', \n", "                         headers=headers, json=requestData, verify=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response.json())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def getData(dataGroups):\n", "    data = []\n", "    for row in dataGroups:\n", "        rowData = []\n", "        for field in row['rowEntries']:\n", "            rowData.append(field['value'])\n", "        data.append(rowData)\n", "    return data"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def getColumns(columnGroups, prevCols = None):\n", "    if prevCols is None:\n", "        columns = []\n", "    else:\n", "        columns = prevCols\n", "    for group in columnGroups:\n", "        if isinstance(group, dict) and 'columns' in group.keys():\n", "            getColumns(group['columns'], columns)\n", "        elif isinstance(group, dict) and 'label' in group.keys():\n", "            columns.append(group['label'])\n", "        elif isinstance(group, list):\n", "            getColumns(group, columns)\n", "    return columns"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def printQueryResults(headers, requestData):\n", "    response = requests.post(baseUrl+\"/api/v2/report2/runReport\", \n", "                            headers=headers, json=requestData, verify=False)\n", "\n", "    columns = getColumns(response.json()['dto']['tables'][0]['column_groups'])\n", "\n", "    data = getData(response.json()['dto']['tables'][0]['row_groups'][0]['rowsNew'])\n", "\n", "    df = pd.DataFrame(data, columns = columns)\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>Quantity Description</th>\n", "      <th>2023</th>\n", "      <th>2024</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3111</td>\n", "      <td>ANIMAL FOODS</td>\n", "      <td>Value for: kilograms</td>\n", "      <td>2,161,986,991</td>\n", "      <td>2,712,053,284</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3111</td>\n", "      <td>ANIMAL FOODS</td>\n", "      <td>Value for: metric tons</td>\n", "      <td>435,832,623</td>\n", "      <td>468,911,867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3112</td>\n", "      <td>GRAIN &amp; OILSEED MILLING PRODUCTS</td>\n", "      <td>Value for: kilograms</td>\n", "      <td>19,468,594,473</td>\n", "      <td>20,746,847,819</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3112</td>\n", "      <td>GRAIN &amp; OILSEED MILLING PRODUCTS</td>\n", "      <td>Value for: metric tons</td>\n", "      <td>107,757,998</td>\n", "      <td>91,144,188</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3113</td>\n", "      <td>SUGAR &amp; CONFECTIONERY PRODUCTS</td>\n", "      <td>Value for: kilograms</td>\n", "      <td>11,307,081,196</td>\n", "      <td>13,176,946,911</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number                       Description    Quantity Description  \\\n", "0        3111                      ANIMAL FOODS    Value for: kilograms   \n", "1        3111                      ANIMAL FOODS  Value for: metric tons   \n", "2        3112  GRAIN & OILSEED MILLING PRODUCTS    Value for: kilograms   \n", "3        3112  GRAIN & OILSEED MILLING PRODUCTS  Value for: metric tons   \n", "4        3113    SUGAR & CONFECTIONERY PRODUCTS    Value for: kilograms   \n", "\n", "             2023            2024  \n", "0   2,161,986,991   2,712,053,284  \n", "1     435,832,623     468,911,867  \n", "2  19,468,594,473  20,746,847,819  \n", "3     107,757,998      91,144,188  \n", "4  11,307,081,196  13,176,946,911  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, requestData).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Read the fields <br>\n", "create more query <br>\n", "write infinite loop around hitting api function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Query related to import for consumption"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import json\n", "query = json.loads(\"\"\"{\"savedQueryType\":\"\",\"savedQueryName\":\"query1\",\"savedQueryDesc\":\"Related to automotive, metals and fabric\",\"isOwner\":true,\"runMonthly\":false,\"unitConversion\":\"0\",\"manualConversions\":[],\"reportOptions\":{\"tradeType\":\"Import\",\"classificationSystem\":\"NAIC\"},\"searchOptions\":{\"MiscGroup\":{\"districts\":{\"aggregation\":\"Aggregate District\",\"districtGroups\":{\"userGroups\":[]},\"districts\":[],\"districtsExpanded\":[{\"name\":\"All Districts\",\"value\":\"all\"}],\"districtsSelectType\":\"all\"},\"importPrograms\":{\"aggregation\":null,\"importPrograms\":[],\"programsSelectType\":\"all\"},\"extImportPrograms\":{\"aggregation\":\"Aggregate CSC\",\"extImportPrograms\":[],\"extImportProgramsExpanded\":[],\"programsSelectType\":\"all\"},\"provisionCodes\":{\"aggregation\":\"Aggregate RPCODE\",\"provisionCodesSelectType\":\"all\",\"rateProvisionCodes\":[],\"rateProvisionCodesExpanded\":[],\"rateProvisionGroups\":{\"systemGroups\":[]}}},\"commodities\":{\"aggregation\":\"Break Out Commodities\",\"codeDisplayFormat\":\"YES\",\"commodities\":[\"3131\",\"31311\",\"31331\",\"3311\",\"33111\",\"3312\",\"33121\",\"33122\",\"3313\",\"33131\",\"336110\",\"336112\"],\"commoditiesExpanded\":[{\"name\":\"FIBERS, YARNS & THREADS\",\"value\":\"3131\"},{\"name\":\"FIBERS, YARNS & THREADS\",\"value\":\"31311\"},{\"name\":\"TEXTILE AND FABRIC FINISHING MILL PRODUCTS\",\"value\":\"31331\"},{\"name\":\"IRON & STEEL & FERROALLOY\",\"value\":\"3311\"},{\"name\":\"IRON AND STEEL AND FERROALLOY STEEL PRODUCTS\",\"value\":\"33111\"},{\"name\":\"STEEL PRODUCTS FROM PURCHASED STEEL\",\"value\":\"3312\"},{\"name\":\"IRON AND STEEL PIPE AND TUBE FROM PURCHASED STEEL\",\"value\":\"33121\"},{\"name\":\"ROLLING & DRAWING OF PURCHASED STEEL\",\"value\":\"33122\"},{\"name\":\"ALUMINA & ALUMINUM  & PROCESSING\",\"value\":\"3313\"},{\"name\":\"ALUMINA & ALUMINUM  & PROCESSING\",\"value\":\"33131\"},{\"name\":\"AUTOMOBILE AND LIGHT DUTY MOTOR VEHICLE MANUFAC\",\"value\":\"336110\"},{\"name\":\"LIGHT TRUCK AND UTILITY VEHICLE\",\"value\":\"336112\"}],\"commoditiesManual\":\"3131,31311,31331,3311,33111,3312,33121,33122,3313,33131,336110,336112\",\"commodityGroups\":{\"systemGroups\":[],\"userGroups\":[]},\"commoditySelectType\":\"list\",\"granularity\":\"6\",\"groupGranularity\":null,\"searchGranularity\":null,\"showHTSValidDetails\":\"\"},\"componentSettings\":{\"dataToReport\":[\"CONS_CUSTOMS_VALUE\"],\"scale\":\"1\",\"timeframeSelectType\":\"fullYears\",\"years\":[\"2025\",\"2024\",\"2023\"],\"startDate\":null,\"endDate\":null,\"startMonth\":null,\"endMonth\":null,\"yearsTimeline\":\"Year-to-Date\"},\"countries\":{\"aggregation\":\"Aggregate Countries\",\"countries\":[],\"countriesExpanded\":[{\"name\":\"All Countries\",\"value\":\"all\"}],\"countriesSelectType\":\"all\",\"countryGroups\":{\"systemGroups\":[],\"userGroups\":[]}}},\"sortingAndDataFormat\":{\"DataSort\":{\"columnOrder\":[\"NAICS6 & DESCRIPTION\"],\"fullColumnOrder\":[{\"hasChildren\":false,\"name\":\"NAICS6 & DESCRIPTION\",\"value\":\"NAICS6 & DESCRIPTION\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"}],\"sortOrder\":[{\"sortData\":\"NAICS6 & DESCRIPTION\",\"orderBy\":\"asc\",\"year\":\"\"}]},\"reportCustomizations\":{\"exportCombineTables\":false,\"totalRecords\":\"20000\",\"exportRawData\":false}},\"deletedCountryUserGroups\":[]}\"\"\")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'savedQueryType': '',\n", " 'savedQueryName': 'query1',\n", " 'savedQueryDesc': 'Related to automotive, metals and fabric',\n", " 'isOwner': True,\n", " 'runMonthly': <PERSON><PERSON><PERSON>,\n", " 'unitConversion': '0',\n", " 'manualConversions': [],\n", " 'reportOptions': {'tradeType': 'Import', 'classificationSystem': 'NAIC'},\n", " 'searchOptions': {'MiscGroup': {'districts': {'aggregation': 'Aggregate District',\n", "    'districtGroups': {'userGroups': []},\n", "    'districts': [],\n", "    'districtsExpanded': [{'name': 'All Districts', 'value': 'all'}],\n", "    'districtsSelectType': 'all'},\n", "   'importPrograms': {'aggregation': None,\n", "    'importPrograms': [],\n", "    'programsSelectType': 'all'},\n", "   'extImportPrograms': {'aggregation': 'Aggregate CSC',\n", "    'extImportPrograms': [],\n", "    'extImportProgramsExpanded': [],\n", "    'programsSelectType': 'all'},\n", "   'provisionCodes': {'aggregation': 'Aggregate RPCODE',\n", "    'provisionCodesSelectType': 'all',\n", "    'rateProvisionCodes': [],\n", "    'rateProvisionCodesExpanded': [],\n", "    'rateProvisionGroups': {'systemGroups': []}}},\n", "  'commodities': {'aggregation': 'Break Out Commodities',\n", "   'codeDisplayFormat': 'YES',\n", "   'commodities': ['3131',\n", "    '31311',\n", "    '31331',\n", "    '3311',\n", "    '33111',\n", "    '3312',\n", "    '33121',\n", "    '33122',\n", "    '3313',\n", "    '33131',\n", "    '336110',\n", "    '336112'],\n", "   'commoditiesExpanded': [{'name': 'FIBERS, YARNS & THREADS',\n", "     'value': '3131'},\n", "    {'name': 'FIBERS, YARNS & THREADS', 'value': '31311'},\n", "    {'name': 'T<PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON> FINISHING MILL PRODUCTS', 'value': '31331'},\n", "    {'name': 'IRON & STEEL & FERROALLOY', 'value': '3311'},\n", "    {'name': '<PERSON>RON AND STEEL AND FERROALLOY STEEL PRODUCTS', 'value': '33111'},\n", "    {'name': 'STEEL PRODUCTS FROM PURCHASED STEEL', 'value': '3312'},\n", "    {'name': '<PERSON>RON AND STEEL PIPE AND TUBE FROM PURCHASED STEEL',\n", "     'value': '33121'},\n", "    {'name': 'ROLLING & DRAWING OF PURCHASED STEEL', 'value': '33122'},\n", "    {'name': 'ALUMINA & ALUMINUM  & PROCESSING', 'value': '3313'},\n", "    {'name': 'ALUMINA & ALUMINUM  & PROCESSING', 'value': '33131'},\n", "    {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON> AND LIGHT DUTY MOTOR VEHICLE MANUFAC',\n", "     'value': '336110'},\n", "    {'name': 'LIGHT TRUCK AND UTILITY VEHICLE', 'value': '336112'}],\n", "   'commoditiesManual': '3131,31311,31331,3311,33111,3312,33121,33122,3313,33131,336110,336112',\n", "   'commodityGroups': {'systemGroups': [], 'userGroups': []},\n", "   'commoditySelectType': 'list',\n", "   'granularity': '6',\n", "   'groupGranularity': None,\n", "   'searchGranularity': None,\n", "   'showHTSValidDetails': ''},\n", "  'componentSettings': {'dataToReport': ['CONS_CUSTOMS_VALUE'],\n", "   'scale': '1',\n", "   'timeframeSelectType': 'fullYears',\n", "   'years': ['2025', '2024', '2023'],\n", "   'startDate': None,\n", "   'endDate': None,\n", "   'startMonth': None,\n", "   'endMonth': None,\n", "   'yearsTimeline': 'Year-to-Date'},\n", "  'countries': {'aggregation': 'Aggregate Countries',\n", "   'countries': [],\n", "   'countriesExpanded': [{'name': 'All Countries', 'value': 'all'}],\n", "   'countriesSelectType': 'all',\n", "   'countryGroups': {'systemGroups': [], 'userGroups': []}}},\n", " 'sortingAndDataFormat': {'DataSort': {'columnOrder': ['NAICS6 & DESCRIPTION'],\n", "   'fullColumnOrder': [{'hasChildren': False,\n", "     'name': 'NAICS6 & DESCRIPTION',\n", "     'value': 'NAICS6 & DESCRIPTION',\n", "     'classificationSystem': '',\n", "     'groupUUID': '',\n", "     'items': [],\n", "     'tradeType': ''}],\n", "   'sortOrder': [{'sortData': 'NAICS6 & DESCRIPTION',\n", "     'orderBy': 'asc',\n", "     'year': ''}]},\n", "  'reportCustomizations': {'exportCombineTables': False,\n", "   'totalRecords': '20000',\n", "   'exportRawData': False}},\n", " 'deletedCountryUserGroups': []}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["query"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>2023</th>\n", "      <th>2024</th>\n", "      <th>2025</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>313110</td>\n", "      <td>FIBERS, YARNS, &amp; THREADS</td>\n", "      <td>706,078,579</td>\n", "      <td>723,529,334</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>331110</td>\n", "      <td>IRON AND STEEL AND FERROALLOY STEEL  PRODUCTS</td>\n", "      <td>37,742,663,098</td>\n", "      <td>36,622,305,030</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>331210</td>\n", "      <td>IRON AND STEEL PIPE AND TUBE FROM PURCHASED STEEL</td>\n", "      <td>1,881,596,422</td>\n", "      <td>55,819,855</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>331221</td>\n", "      <td>ROLLED STEEL SHAPE</td>\n", "      <td>223,013,627</td>\n", "      <td>192,995,510</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>331222</td>\n", "      <td>STEEL WIRE DRAWING</td>\n", "      <td>2,112,010,882</td>\n", "      <td>2,179,942,229</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number                                        Description  \\\n", "0      313110                           FIBERS, YARNS, & THREADS   \n", "1      331110      IRON AND STEEL AND FERROALLOY STEEL  PRODUCTS   \n", "2      331210  IRON AND STEEL PIPE AND TUBE FROM PURCHASED STEEL   \n", "3      331221                                 ROLLED STEEL SHAPE   \n", "4      331222                                 STEEL WIRE DRAWING   \n", "\n", "             2023            2024 2025  \n", "0     706,078,579     723,529,334    0  \n", "1  37,742,663,098  36,622,305,030    0  \n", "2   1,881,596,422      55,819,855    0  \n", "3     223,013,627     192,995,510    0  \n", "4   2,112,010,882   2,179,942,229    0  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, query).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Query related to foreign exports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "query2 =json.loads( \"\"\"{\"savedQueryType\":\"\",\"savedQueryID\":\"70ba3a1d-f17a-4fdf-8c4b-afa9bdcd271a\",\"savedQueryName\":\"foreign exp query\",\"savedQueryDesc\":\"Related to computer equipments\",\"isOwner\":true,\"runMonthly\":false,\"unitConversion\":\"0\",\"manualConversions\":[],\"reportOptions\":{\"tradeType\":\"ForeignExp\",\"classificationSystem\":\"NAIC\"},\"searchOptions\":{\"MiscGroup\":{\"districts\":{\"aggregation\":\"Aggregate District\",\"districtGroups\":{\"userGroups\":[]},\"districts\":[],\"districtsExpanded\":[],\"districtsSelectType\":\"all\"},\"importPrograms\":{\"aggregation\":null,\"importPrograms\":[],\"programsSelectType\":\"all\"},\"extImportPrograms\":{\"aggregation\":\"Aggregate CSC\",\"extImportPrograms\":[],\"extImportProgramsExpanded\":[],\"programsSelectType\":\"all\"},\"provisionCodes\":{\"aggregation\":\"Aggregate RPCODE\",\"provisionCodesSelectType\":\"all\",\"rateProvisionCodes\":[],\"rateProvisionCodesExpanded\":[],\"rateProvisionGroups\":{\"systemGroups\":[]}}},\"commodities\":{\"aggregation\":\"Break Out Commodities\",\"codeDisplayFormat\":\"YES\",\"commodities\":[\"334\"],\"commoditiesExpanded\":[{\"name\":\"COMPUTER & ELECTRONIC PRODUCTS\",\"value\":\"334\"}],\"commoditiesManual\":\"334\",\"commodityGroups\":{\"systemGroups\":[],\"userGroups\":[]},\"commoditySelectType\":\"list\",\"granularity\":\"5\",\"groupGranularity\":null,\"searchGranularity\":null,\"showHTSValidDetails\":false},\"componentSettings\":{\"dataToReport\":[\"FAS_VALUE\"],\"scale\":\"1\",\"timeframeSelectType\":\"fullYears\",\"years\":[\"2025\",\"2024\",\"2023\"],\"startDate\":null,\"endDate\":null,\"startMonth\":null,\"endMonth\":null,\"yearsTimeline\":\"Annual\"},\"countries\":{\"aggregation\":\"Aggregate Countries\",\"countries\":[],\"countriesExpanded\":[],\"countriesSelectType\":\"all\",\"countryGroups\":{\"systemGroups\":[],\"userGroups\":[]}}},\"sortingAndDataFormat\":{\"DataSort\":{\"columnOrder\":[\"NAICS5 & DESCRIPTION\"],\"fullColumnOrder\":[{\"hasChildren\":false,\"name\":\"NAICS5 & DESCRIPTION\",\"value\":\"NAICS5 & DESCRIPTION\",\"classificationSystem\":\"\",\"groupUUID\":\"\",\"items\":[],\"tradeType\":\"\"}],\"sortOrder\":[{\"sortData\":\"NAICS5 & DESCRIPTION\",\"orderBy\":\"asc\",\"year\":\"0\"}]},\"reportCustomizations\":{\"exportCombineTables\":false,\"totalRecords\":\"20000\",\"exportRawData\":false}},\"deletedCountryUserGroups\":[],\"deletedCommodityUserGroups\":[],\"deletedDistrictUserGroups\":[]}\"\"\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'savedQueryType': '',\n", " 'savedQueryID': '70ba3a1d-f17a-4fdf-8c4b-afa9bdcd271a',\n", " 'savedQueryName': 'foreign exp query',\n", " 'savedQueryDesc': 'Related to computer equipments',\n", " 'isOwner': True,\n", " 'runMonthly': <PERSON><PERSON><PERSON>,\n", " 'unitConversion': '0',\n", " 'manualConversions': [],\n", " 'reportOptions': {'tradeType': 'ForeignExp', 'classificationSystem': 'NAIC'},\n", " 'searchOptions': {'MiscGroup': {'districts': {'aggregation': 'Aggregate District',\n", "    'districtGroups': {'userGroups': []},\n", "    'districts': [],\n", "    'districtsExpanded': [],\n", "    'districtsSelectType': 'all'},\n", "   'importPrograms': {'aggregation': None,\n", "    'importPrograms': [],\n", "    'programsSelectType': 'all'},\n", "   'extImportPrograms': {'aggregation': 'Aggregate CSC',\n", "    'extImportPrograms': [],\n", "    'extImportProgramsExpanded': [],\n", "    'programsSelectType': 'all'},\n", "   'provisionCodes': {'aggregation': 'Aggregate RPCODE',\n", "    'provisionCodesSelectType': 'all',\n", "    'rateProvisionCodes': [],\n", "    'rateProvisionCodesExpanded': [],\n", "    'rateProvisionGroups': {'systemGroups': []}}},\n", "  'commodities': {'aggregation': 'Break Out Commodities',\n", "   'codeDisplayFormat': 'YES',\n", "   'commodities': ['334'],\n", "   'commoditiesExpanded': [{'name': 'COMPUTER & ELECTRONIC PRODUCTS',\n", "     'value': '334'}],\n", "   'commoditiesManual': '334',\n", "   'commodityGroups': {'systemGroups': [], 'userGroups': []},\n", "   'commoditySelectType': 'list',\n", "   'granularity': '5',\n", "   'groupGranularity': None,\n", "   'searchGranularity': None,\n", "   'showHTSValidDetails': False},\n", "  'componentSettings': {'dataToReport': ['FAS_VALUE'],\n", "   'scale': '1',\n", "   'timeframeSelectType': 'fullYears',\n", "   'years': ['2025', '2024', '2023'],\n", "   'startDate': None,\n", "   'endDate': None,\n", "   'startMonth': None,\n", "   'endMonth': None,\n", "   'yearsTimeline': 'Annual'},\n", "  'countries': {'aggregation': 'Aggregate Countries',\n", "   'countries': [],\n", "   'countriesExpanded': [],\n", "   'countriesSelectType': 'all',\n", "   'countryGroups': {'systemGroups': [], 'userGroups': []}}},\n", " 'sortingAndDataFormat': {'DataSort': {'columnOrder': ['NAICS5 & DESCRIPTION'],\n", "   'fullColumnOrder': [{'hasChildren': False,\n", "     'name': 'NAICS5 & DESCRIPTION',\n", "     'value': 'NAICS5 & DESCRIPTION',\n", "     'classificationSystem': '',\n", "     'groupUUID': '',\n", "     'items': [],\n", "     'tradeType': ''}],\n", "   'sortOrder': [{'sortData': 'NAICS5 & DESCRIPTION',\n", "     'orderBy': 'asc',\n", "     'year': '0'}]},\n", "  'reportCustomizations': {'exportCombineTables': False,\n", "   'totalRecords': '20000',\n", "   'exportRawData': False}},\n", " 'deletedCountryUserGroups': [],\n", " 'deletedCommodityUserGroups': [],\n", " 'deletedDistrictUserGroups': []}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["query2"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAIC Number</th>\n", "      <th>Description</th>\n", "      <th>2023</th>\n", "      <th>2024</th>\n", "      <th>2025</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33411</td>\n", "      <td>COMPUTER EQUIPMENT</td>\n", "      <td>29,198,601,787</td>\n", "      <td>41,043,535,496</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33421</td>\n", "      <td>TELEPHONE APPARATUS</td>\n", "      <td>550,975,667</td>\n", "      <td>469,946,567</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>33422</td>\n", "      <td>RADIO/TV BROADCAST &amp; WIRELESS COMMUNICATION EQUIP</td>\n", "      <td>25,694,563,655</td>\n", "      <td>26,897,837,424</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33429</td>\n", "      <td>OTHER COMMUNICATIONS EQUIPMENT</td>\n", "      <td>232,324,540</td>\n", "      <td>235,321,204</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>33431</td>\n", "      <td>AUDIO &amp; VIDEO EQUIPMENT</td>\n", "      <td>4,229,420,368</td>\n", "      <td>4,186,307,959</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NAIC Number                                        Description  \\\n", "0       33411                                 COMPUTER EQUIPMENT   \n", "1       33421                                TELEPHONE APPARATUS   \n", "2       33422  RADIO/TV BROADCAST & WIRELESS COMMUNICATION EQUIP   \n", "3       33429                     OTHER COMMUNICATIONS EQUIPMENT   \n", "4       33431                            AUDIO & VIDEO EQUIPMENT   \n", "\n", "             2023            2024 2025  \n", "0  29,198,601,787  41,043,535,496    0  \n", "1     550,975,667     469,946,567    0  \n", "2  25,694,563,655  26,897,837,424    0  \n", "3     232,324,540     235,321,204    0  \n", "4   4,229,420,368   4,186,307,959    0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["printQueryResults(headers, query2).head()"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}