{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import beaapi"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["beakey = '0CB6FB71-8009-4FF4-B57F-184021F1A352'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get Metadata <br>\n", "The BEA data is distributed across various datasets that each contain many tables. To get a list of the datasets available on the API, use get_data_set_list():\n", "\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DatasetName</th>\n", "      <th>DatasetDescription</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NIPA</td>\n", "      <td>Standard NIPA tables</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NIUnderlyingDetail</td>\n", "      <td>Standard NI underlying detail tables</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MNE</td>\n", "      <td>Multinational Enterprises</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FixedAssets</td>\n", "      <td>Standard Fixed Assets tables</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ITA</td>\n", "      <td>International Transactions Accounts</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>IIP</td>\n", "      <td>International Investment Position</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>InputOutput</td>\n", "      <td>Input-Output Data</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>IntlServTrade</td>\n", "      <td>International Services Trade</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>IntlServSTA</td>\n", "      <td>International Services Supplied Through Affili...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>GDPbyIndustry</td>\n", "      <td>GDP by Industry</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Regional</td>\n", "      <td>Regional data sets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>UnderlyingGDPbyIndustry</td>\n", "      <td>Underlying GDP by Industry</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>APIDatasetMetaData</td>\n", "      <td>Metadata about other API datasets</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                DatasetName                                 DatasetDescription\n", "0                      NIPA                               Standard NIPA tables\n", "1        NIUnderlyingDetail               Standard NI underlying detail tables\n", "2                       MNE                          Multinational Enterprises\n", "3               FixedAssets                       Standard Fixed Assets tables\n", "4                       ITA                International Transactions Accounts\n", "5                       IIP                  International Investment Position\n", "6               InputOutput                                  Input-Output Data\n", "7             IntlServTrade                       International Services Trade\n", "8               IntlServSTA  International Services Supplied Through Affili...\n", "9             GDPbyIndustry                                    GDP by Industry\n", "10                 Regional                                 Regional data sets\n", "11  UnderlyingGDPbyIndustry                         Underlying GDP by Industry\n", "12       APIDatasetMetaData                  Metadata about other API datasets"]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_sets = beaapi.get_data_set_list(beakey)\n", "display(list_of_sets)  # Note the last dataset is only for speeding up metadata queries\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Queries to the different datasets take different parameters. To get a list of the paramaters for a given dataset, use <br> get_parameter_list(); for example, to get a list of the parameters for the NIPA dataset, use:"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ParameterName</th>\n", "      <th>ParameterDataType</th>\n", "      <th>ParameterDescription</th>\n", "      <th>ParameterIsRequiredFlag</th>\n", "      <th>ParameterDefaultValue</th>\n", "      <th>MultipleAcceptedFlag</th>\n", "      <th>AllValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Frequency</td>\n", "      <td>string</td>\n", "      <td>A - Annual, Q-Quarterly</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Industry</td>\n", "      <td>string</td>\n", "      <td>List of industries to retrieve (ALL for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TableID</td>\n", "      <td>integer</td>\n", "      <td>The unique GDP by Industry table identifier (A...</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Year</td>\n", "      <td>integer</td>\n", "      <td>List of year(s) of data to retrieve (ALL for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ParameterName ParameterDataType  \\\n", "0     Frequency            string   \n", "1      Industry            string   \n", "2       TableID           integer   \n", "3          Year           integer   \n", "\n", "                                ParameterDescription  ParameterIsRequiredFlag  \\\n", "0                            A - Annual, Q-Quarterly                        1   \n", "1       List of industries to retrieve (ALL for All)                        1   \n", "2  The unique GDP by Industry table identifier (A...                        1   \n", "3  List of year(s) of data to retrieve (ALL for All)                        1   \n", "\n", "  ParameterDefaultValue  MultipleAcceptedFlag AllValue  \n", "0                                           1      ALL  \n", "1                                           1      ALL  \n", "2                                           1      ALL  \n", "3                                           1      ALL  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_params = beaapi.get_parameter_list(beakey, 'GDPbyIndustry')\n", "display(list_of_params)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To get a list of the values for a given parameter, use get_parameter_values(); for example, to get a list of the parameter values <br>for the Frequency parameter of the NIPA dataset, use:"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Key</th>\n", "      <th>Desc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11</td>\n", "      <td>Agriculture, forestry, fishing, and hunting (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>111CA</td>\n", "      <td>Farms (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>113FF</td>\n", "      <td>Forestry, fishing, and related activities (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21</td>\n", "      <td>Mining (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>ORE</td>\n", "      <td>Other real estate (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>PGOOD</td>\n", "      <td>Private goods-producing industries (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>PROF</td>\n", "      <td>Professional and business services (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>PSERV</td>\n", "      <td>Private services-producing industries (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>PVT</td>\n", "      <td>Private industries (A,Q)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>104 rows × 2 columns</p>\n", "</div>"], "text/plain": ["       Key                                               Desc\n", "0       11  Agriculture, forestry, fishing, and hunting (A,Q)\n", "1    111CA                                        Farms (A,Q)\n", "2    113FF    Forestry, fishing, and related activities (A,Q)\n", "3       21                                       Mining (A,Q)\n", "4      211                       Oil and gas extraction (A,Q)\n", "..     ...                                                ...\n", "99     ORE                            Other real estate (A,Q)\n", "100  PGOOD           Private goods-producing industries (A,Q)\n", "101   PROF           Professional and business services (A,Q)\n", "102  PSERV        Private services-producing industries (A,Q)\n", "103    PVT                           Private industries (A,Q)\n", "\n", "[104 rows x 2 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_param_vals = beaapi.get_parameter_values(beakey, 'GDPbyIndustry', 'Industry')\n", "display(list_of_param_vals)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A few datasets (ITA, IIP, InputOutput, IntlServTrade, GDPbyIndustry, Regional, and UnderlyingGDPbyIndustry) allow you to filter <br>the values based on what will be passed in for other parameters.\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Key</th>\n", "      <th>Desc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BalCapAcct</td>\n", "      <td>Balance on capital account</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>BalCurrAcct</td>\n", "      <td>Balance on current account</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BalGds</td>\n", "      <td>Balance on goods</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BalGdsServ</td>\n", "      <td>Balance on goods and services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BalPrimInc</td>\n", "      <td>Balance on primary income</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Key                           Desc\n", "0   BalCapAcct     Balance on capital account\n", "1  BalCurrAcct     Balance on current account\n", "2       BalGds               Balance on goods\n", "3   BalGdsServ  Balance on goods and services\n", "4   BalPrimInc      Balance on primary income"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tbl = beaapi.get_parameter_values_filtered(beakey, 'ITA', targetparameter='Indicator', AreaOrCountry=\"China\",Frequency=\"A\", Year=\"2011\")\n", "display(tbl.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A few of the datasets publish metadata tables that can be queried for particular strings using search_metadata(). This method <br>allows you to search for BEA data by keyword. For example, to find all datasets in which the term \"personal consumption\" <br>appears, use the following:"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created directory: beaapi_data\n", "Creating first-time local copy of metadata for all datasets - only done once in working directory.\n", "Datasets will be updated only if timestamps indicate metadata obsolete in future searches, and only obsolete metadata sets will be updated.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SeriesCode</th>\n", "      <th>RowNumber</th>\n", "      <th>LineDescription</th>\n", "      <th>LineNumber</th>\n", "      <th>ParentLineNumber</th>\n", "      <th>Tier</th>\n", "      <th>Path</th>\n", "      <th>TableId</th>\n", "      <th>Datasetname</th>\n", "      <th>TableName</th>\n", "      <th>ReleaseDate</th>\n", "      <th>NextReleaseDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A191RL</td>\n", "      <td>10</td>\n", "      <td>Gross domestic product</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>T10101</td>\n", "      <td>NIPA</td>\n", "      <td>Table 1.1.1. Percent Change From Preceding Per...</td>\n", "      <td>Feb 28 2019  8:30AM</td>\n", "      <td>Mar 28 2019  8:30AM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>A191RP</td>\n", "      <td>280</td>\n", "      <td>Gross domestic product, current dollars</td>\n", "      <td>27</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>27</td>\n", "      <td>T10101</td>\n", "      <td>NIPA</td>\n", "      <td>Table 1.1.1. Percent Change From Preceding Per...</td>\n", "      <td>Feb 28 2019  8:30AM</td>\n", "      <td>Mar 28 2019  8:30AM</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   SeriesCode RowNumber                          LineDescription LineNumber  \\\n", "0      A191RL        10                   Gross domestic product          1   \n", "26     A191RP       280  Gross domestic product, current dollars         27   \n", "\n", "   ParentLineNumber Tier Path TableId Datasetname  \\\n", "0                      0    1  T10101        NIPA   \n", "26                     0   27  T10101        NIPA   \n", "\n", "                                            TableName          ReleaseDate  \\\n", "0   Table 1.1.1. Percent Change From Preceding Per...  Feb 28 2019  8:30AM   \n", "26  Table 1.1.1. Percent Change From Preceding Per...  Feb 28 2019  8:30AM   \n", "\n", "        NextReleaseDate  \n", "0   Mar 28 2019  8:30AM  \n", "26  Mar 28 2019  8:30AM  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["search_data = beaapi.search_metadata('Gross domestic', beakey)\n", "search_data.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once you have identified the TableId number and other information, you can use get_data() to access the data. The following code, for example, returns the NIPA table with 2015 data for Table T20305."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Regarding the table T20305 you've referenced in your code:​\n", "\n", "Table Naming Convention: NIPA tables are labeled using a three-number system separated by periods, which indicate their classification within the accounts:​\n", "\n", "First Number: Denotes the account area. For example, '2' corresponds to 'Personal Income and Outlays'.​\n", "\n", "Second Number: Sequences the tables within the account area and groups tables with similar subjects.​\n", "\n", "Third Number: Indicates the form of the data presented, such as current-dollar levels, real (chained) dollar levels, price indexes, etc.​\n", "\n", "\n", "Table T20305: This specific table falls under the 'Personal Income and Outlays' account area and presents data on 'Personal Consumption Expenditures by Major Type of Product'. The third digit '5' signifies that the table provides current-dollar levels. In your code, you're querying this table to retrieve quarterly data for the year 2015."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>SeriesCode</th>\n", "      <th>LineNumber</th>\n", "      <th>LineDescription</th>\n", "      <th>TimePeriod</th>\n", "      <th>METRIC_NAME</th>\n", "      <th>CL_UNIT</th>\n", "      <th>UNIT_MULT</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>T20305</td>\n", "      <td>DPCERC</td>\n", "      <td>1</td>\n", "      <td>Personal consumption expenditures (PCE)</td>\n", "      <td>2015Q1</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>T20305</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>T20305</td>\n", "      <td>DPCERC</td>\n", "      <td>1</td>\n", "      <td>Personal consumption expenditures (PCE)</td>\n", "      <td>2015Q2</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>T20305</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  TableName SeriesCode  LineNumber                          LineDescription  \\\n", "0    T20305     DPCERC           1  Personal consumption expenditures (PCE)   \n", "1    T20305     DPCERC           1  Personal consumption expenditures (PCE)   \n", "\n", "  TimePeriod      METRIC_NAME CL_UNIT  UNIT_MULT  DataValue NoteRef  \n", "0     2015Q1  Current Dollars   Level          6   ********  T20305  \n", "1     2015Q2  Current Dollars   Level          6   ********  T20305  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["bea_tbl = beaapi.get_data(beakey, datasetname='NIPA', TableName='T20305', Frequency='Q', Year='2015')\n", "display(bea_tbl.head(2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The data you've retrieved from the BEA's National Income and Product Accounts (NIPA) Table T20305 pertains to **Personal Consumption Expenditures (PCE)**, which measures consumer spending on goods and services in the U.S. economy.\n", "\n", "Here's a breakdown of the columns in your dataset:\n", "\n", "- **TableName:** Identifies the source table, in this case, 'T20305', which focuses on PCE.\n", "\n", "- **SeriesCode:** Represents a unique code for the data series; 'DPCERC' corresponds to the current-dollar value of PCE.\n", "\n", "- **LineNumber:** Indicates the specific line item within the table; '1' refers to the aggregate PCE.\n", "\n", "- **LineDescription:** Provides a description of the line item; here, it denotes 'Personal consumption expenditures (PCE)'.\n", "\n", "- **TimePeriod:** Specifies the time frame of the data; for example, '2015Q1' represents the first quarter of 2015.\n", "\n", "- **METRIC_NAME:** Denotes the measurement metric; 'Current Dollars' indicates that the values are in nominal terms, not adjusted for inflation.\n", "\n", "- **CL_UNIT:** Stands for 'Classification Unit', showing the data's unit; 'Level' implies the data represents absolute figures.\n", "\n", "- **UNIT_MULT:** Represents the unit multiplier; a value of '6' means the data values are in millions.\n", "\n", "- **DataValue:** Shows the actual expenditure value; for instance, '12083904' signifies $12,083,904 million (or $12.08 trillion) spent in PCE for that quarter.\n", "\n", "- **NoteRef:** References any notes related to the data; 'T20305' points back to the source table.\n", "\n", "In summary, this dataset provides quarterly figures for total Personal Consumption Expenditures in the U.S., measured in millions of current dollars. For example, in the first quarter of 2015 (2015Q1), the PCE was $12.08 trillion, and in the second quarter (2015Q2), it was $12.22 trillion. This information is crucial for analyzing consumer spending trends and assessing economic health. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["We store in the meta-data the index columns so that you can create a unique index on the data-frame."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>SeriesCode</th>\n", "      <th>LineDescription</th>\n", "      <th>METRIC_NAME</th>\n", "      <th>CL_UNIT</th>\n", "      <th>UNIT_MULT</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "    <tr>\n", "      <th>LineNumber</th>\n", "      <th>TimePeriod</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">1</th>\n", "      <th>2015Q1</th>\n", "      <td>T20305</td>\n", "      <td>DPCERC</td>\n", "      <td>Personal consumption expenditures (PCE)</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>T20305</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015Q2</th>\n", "      <td>T20305</td>\n", "      <td>DPCERC</td>\n", "      <td>Personal consumption expenditures (PCE)</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>T20305</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      TableName SeriesCode  \\\n", "LineNumber TimePeriod                        \n", "1          2015Q1        T20305     DPCERC   \n", "           2015Q2        T20305     DPCERC   \n", "\n", "                                               LineDescription  \\\n", "LineNumber TimePeriod                                            \n", "1          2015Q1      Personal consumption expenditures (PCE)   \n", "           2015Q2      Personal consumption expenditures (PCE)   \n", "\n", "                           METRIC_NAME CL_UNIT  UNIT_MULT  DataValue NoteRef  \n", "LineNumber TimePeriod                                                         \n", "1          2015Q1      Current Dollars   Level          6   ********  T20305  \n", "           2015Q2      Current Dollars   Level          6   ********  T20305  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(bea_tbl.set_index(bea_tbl.attrs['index_cols']).head(2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Extra meta-data from the API is returned in a dictionary in the attributes called detail and can vary based on the dataset."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extra detail keys:dict_keys(['Statistic', 'UTCProductionTime', 'Dimensions', 'Notes'])\n", "Let's look at some interesting ones.\n", "Statistic: NIPA Table\n", "Notes corresponding to NoteRef:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NoteRef</th>\n", "      <th>NoteText</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>T20305</td>\n", "      <td>Table 2.3.5. Personal Consumption Expenditures...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>T20305.1</td>\n", "      <td>1. Net expenses of NPISHs, defined as their gr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>T20305.2</td>\n", "      <td>2. Gross output is net of unrelated sales, sec...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>T20305.3</td>\n", "      <td>3. Excludes unrelated sales, secondary sales, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>T20305.4</td>\n", "      <td>4. Food consists of food and beverages purchas...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    NoteRef                                           NoteText\n", "0    T20305  Table 2.3.5. Personal Consumption Expenditures...\n", "1  T20305.1  1. Net expenses of NPISHs, defined as their gr...\n", "2  T20305.2  2. Gross output is net of unrelated sales, sec...\n", "3  T20305.3  3. Excludes unrelated sales, secondary sales, ...\n", "4  T20305.4  4. Food consists of food and beverages purchas..."]}, "metadata": {}, "output_type": "display_data"}], "source": ["print('Extra detail keys:' + str(bea_tbl.attrs['detail'].keys()))\n", "print(\"Let's look at some interesting ones.\")\n", "print('Statistic: ' + bea_tbl.attrs['detail']['Statistic'])\n", "print(\"Notes corresponding to NoteRef:\")\n", "display(bea_tbl.attrs['detail']['Notes'].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fixed Assets"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DatasetName</th>\n", "      <th>DatasetDescription</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NIPA</td>\n", "      <td>Standard NIPA tables</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NIUnderlyingDetail</td>\n", "      <td>Standard NI underlying detail tables</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MNE</td>\n", "      <td>Multinational Enterprises</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FixedAssets</td>\n", "      <td>Standard Fixed Assets tables</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ITA</td>\n", "      <td>International Transactions Accounts</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>IIP</td>\n", "      <td>International Investment Position</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>InputOutput</td>\n", "      <td>Input-Output Data</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>IntlServTrade</td>\n", "      <td>International Services Trade</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>IntlServSTA</td>\n", "      <td>International Services Supplied Through Affili...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>GDPbyIndustry</td>\n", "      <td>GDP by Industry</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Regional</td>\n", "      <td>Regional data sets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>UnderlyingGDPbyIndustry</td>\n", "      <td>Underlying GDP by Industry</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>APIDatasetMetaData</td>\n", "      <td>Metadata about other API datasets</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                DatasetName                                 DatasetDescription\n", "0                      NIPA                               Standard NIPA tables\n", "1        NIUnderlyingDetail               Standard NI underlying detail tables\n", "2                       MNE                          Multinational Enterprises\n", "3               FixedAssets                       Standard Fixed Assets tables\n", "4                       ITA                International Transactions Accounts\n", "5                       IIP                  International Investment Position\n", "6               InputOutput                                  Input-Output Data\n", "7             IntlServTrade                       International Services Trade\n", "8               IntlServSTA  International Services Supplied Through Affili...\n", "9             GDPbyIndustry                                    GDP by Industry\n", "10                 Regional                                 Regional data sets\n", "11  UnderlyingGDPbyIndustry                         Underlying GDP by Industry\n", "12       APIDatasetMetaData                  Metadata about other API datasets"]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_sets = beaapi.get_data_set_list(beakey)\n", "display(list_of_sets)  # Note the last dataset is only for speeding up metadata queries\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## List of parameters for fixed assets"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ParameterName</th>\n", "      <th>ParameterDataType</th>\n", "      <th>ParameterDescription</th>\n", "      <th>ParameterIsRequiredFlag</th>\n", "      <th>ParameterDefaultValue</th>\n", "      <th>MultipleAcceptedFlag</th>\n", "      <th>AllValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TableName</td>\n", "      <td>string</td>\n", "      <td>The new Fixed Assets identifier</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Year</td>\n", "      <td>integer</td>\n", "      <td>List of year(s) of data to retrieve (X for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>X</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ParameterName ParameterDataType  \\\n", "0     TableName            string   \n", "1          Year           integer   \n", "\n", "                              ParameterDescription  ParameterIsRequiredFlag  \\\n", "0                  The new Fixed Assets identifier                        1   \n", "1  List of year(s) of data to retrieve (X for All)                        1   \n", "\n", "  ParameterDefaultValue  MultipleAcceptedFlag AllValue  \n", "0                                           0           \n", "1                                           1        X  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_params = beaapi.get_parameter_list(beakey, 'FixedAssets')\n", "display(list_of_params)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>FirstAnnualYear</th>\n", "      <th>LastAnnualYear</th>\n", "      <th>FirstQuarterlyYear</th>\n", "      <th>LastQuarterlyYear</th>\n", "      <th>FirstMonthlyYear</th>\n", "      <th>LastMonthlyYear</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAAt101</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAAt102</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAAt103</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAAt104</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAAt105</td>\n", "      <td>1901</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>FAAt809</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>FAAt810</td>\n", "      <td>1925</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>FAAt901</td>\n", "      <td>2007</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>FAAt902</td>\n", "      <td>2007</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>FAAt903</td>\n", "      <td>2007</td>\n", "      <td>2023</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>109 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    TableName FirstAnnualYear LastAnnualYear FirstQuarterlyYear  \\\n", "0     FAAt101            1925           2023                  0   \n", "1     FAAt102            1925           2023                  0   \n", "2     FAAt103            1925           2023                  0   \n", "3     FAAt104            1925           2023                  0   \n", "4     FAAt105            1901           2023                  0   \n", "..        ...             ...            ...                ...   \n", "104   FAAt809            1925           2023                  0   \n", "105   FAAt810            1925           2023                  0   \n", "106   FAAt901            2007           2023                  0   \n", "107   FAAt902            2007           2023                  0   \n", "108   FAAt903            2007           2023                  0   \n", "\n", "    LastQuarterlyYear FirstMonthlyYear LastMonthlyYear  \n", "0                   0                0               0  \n", "1                   0                0               0  \n", "2                   0                0               0  \n", "3                   0                0               0  \n", "4                   0                0               0  \n", "..                ...              ...             ...  \n", "104                 0                0               0  \n", "105                 0                0               0  \n", "106                 0                0               0  \n", "107                 0                0               0  \n", "108                 0                0               0  \n", "\n", "[109 rows x 7 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_param_vals = beaapi.get_parameter_values(beakey, 'FixedAssets', 'Year')\n", "display(list_of_param_vals)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["list_of_param_vals.to_csv('data.csv', index=False)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>Description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAAt101</td>\n", "      <td>Table 1.1. Current-Cost Net Stock of Fixed Ass...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAAt102</td>\n", "      <td>Table 1.2. Chain-Type Quantity Indexes for Net...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAAt103</td>\n", "      <td>Table 1.3. Current-Cost Depreciation of Fixed ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAAt104</td>\n", "      <td>Table 1.4. Chain-Type Quantity Indexes for Dep...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAAt105</td>\n", "      <td>Table 1.5. Investment in Fixed Assets and Cons...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>FAAt809</td>\n", "      <td>Table 8.9. Current-Cost Average Age at Yearend...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>FAAt810</td>\n", "      <td>Table 8.10. Historical-Cost Average Age at Yea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>FAAt901</td>\n", "      <td>Table 9.1. Real Net Stock of Fixed Assets and ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>FAAt902</td>\n", "      <td>Table 9.2. Real Depreciation of Fixed Assets a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>FAAt903</td>\n", "      <td>Table 9.3. Real Investment in Fixed Assets and...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>109 rows × 2 columns</p>\n", "</div>"], "text/plain": ["    TableName                                        Description\n", "0     FAAt101  Table 1.1. Current-Cost Net Stock of Fixed Ass...\n", "1     FAAt102  Table 1.2. Chain-Type Quantity Indexes for Net...\n", "2     FAAt103  Table 1.3. Current-Cost Depreciation of Fixed ...\n", "3     FAAt104  Table 1.4. Chain-Type Quantity Indexes for Dep...\n", "4     FAAt105  Table 1.5. Investment in Fixed Assets and Cons...\n", "..        ...                                                ...\n", "104   FAAt809  Table 8.9. Current-Cost Average Age at Yearend...\n", "105   FAAt810  Table 8.10. Historical-Cost Average Age at Yea...\n", "106   FAAt901  Table 9.1. Real Net Stock of Fixed Assets and ...\n", "107   FAAt902  Table 9.2. Real Depreciation of Fixed Assets a...\n", "108   FAAt903  Table 9.3. Real Investment in Fixed Assets and...\n", "\n", "[109 rows x 2 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_tables = beaapi.get_parameter_values(beakey, 'FixedAssets', 'TableName')\n", "display(list_of_tables)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["list_of_tables.to_csv('tables.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>SeriesCode</th>\n", "      <th>LineNumber</th>\n", "      <th>LineDescription</th>\n", "      <th>TimePeriod</th>\n", "      <th>METRIC_NAME</th>\n", "      <th>CL_UNIT</th>\n", "      <th>UNIT_MULT</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ptotl1es00</td>\n", "      <td>1</td>\n", "      <td>Private fixed assets</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ptotl1eq00</td>\n", "      <td>2</td>\n", "      <td>Equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>8731602</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1eq00</td>\n", "      <td>3</td>\n", "      <td>Nonresidential equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>8629807</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep00</td>\n", "      <td>4</td>\n", "      <td>Information processing equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>1995151</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep11</td>\n", "      <td>5</td>\n", "      <td>Computers and peripheral equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>344701</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep20</td>\n", "      <td>6</td>\n", "      <td>Communication equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>740566</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep33</td>\n", "      <td>7</td>\n", "      <td>Medical equipment and instruments</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>590865</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep36</td>\n", "      <td>8</td>\n", "      <td>Nonmedical instruments</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>263561</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep31</td>\n", "      <td>9</td>\n", "      <td>Photocopy and related equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>40647</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep12</td>\n", "      <td>10</td>\n", "      <td>Office and accounting equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>14811</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  TableName    SeriesCode  LineNumber                     LineDescription  \\\n", "0   FAAt201  k1ptotl1es00           1                Private fixed assets   \n", "1   FAAt201  k1ptotl1eq00           2                           Equipment   \n", "2   FAAt201  k1ntotl1eq00           3            Nonresidential equipment   \n", "3   FAAt201  k1ntotl1ep00           4    Information processing equipment   \n", "4   FAAt201  k1ntotl1ep11           5  Computers and peripheral equipment   \n", "5   FAAt201  k1ntotl1ep20           6             Communication equipment   \n", "6   FAAt201  k1ntotl1ep33           7   Medical equipment and instruments   \n", "7   FAAt201  k1ntotl1ep36           8              Nonmedical instruments   \n", "8   FAAt201  k1ntotl1ep31           9     Photocopy and related equipment   \n", "9   FAAt201  k1ntotl1ep12          10     Office and accounting equipment   \n", "\n", "  TimePeriod      METRIC_NAME CL_UNIT  UNIT_MULT  DataValue  NoteRef  \n", "0       2022  Current Dollars   Level          6   ********  FAAt201  \n", "1       2022  Current Dollars   Level          6    8731602  FAAt201  \n", "2       2022  Current Dollars   Level          6    8629807  FAAt201  \n", "3       2022  Current Dollars   Level          6    1995151  FAAt201  \n", "4       2022  Current Dollars   Level          6     344701  FAAt201  \n", "5       2022  Current Dollars   Level          6     740566  FAAt201  \n", "6       2022  Current Dollars   Level          6     590865  FAAt201  \n", "7       2022  Current Dollars   Level          6     263561  FAAt201  \n", "8       2022  Current Dollars   Level          6      40647  FAAt201  \n", "9       2022  Current Dollars   Level          6      14811  FAAt201  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["bea_tbl = beaapi.get_data(beakey, TableName = 'FAAt201', datasetname='FixedAssets', Year='2022')\n", "display(bea_tbl.head(10))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["is_present = (bea_tbl['TimePeriod'] == '2022').any()\n", "print(is_present)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Multiple years"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableName</th>\n", "      <th>SeriesCode</th>\n", "      <th>LineNumber</th>\n", "      <th>LineDescription</th>\n", "      <th>TimePeriod</th>\n", "      <th>METRIC_NAME</th>\n", "      <th>CL_UNIT</th>\n", "      <th>UNIT_MULT</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ptotl1es00</td>\n", "      <td>1</td>\n", "      <td>Private fixed assets</td>\n", "      <td>2021</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>59312969</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ptotl1es00</td>\n", "      <td>1</td>\n", "      <td>Private fixed assets</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ptotl1eq00</td>\n", "      <td>2</td>\n", "      <td>Equipment</td>\n", "      <td>2021</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>8118582</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ptotl1eq00</td>\n", "      <td>2</td>\n", "      <td>Equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>8731602</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1eq00</td>\n", "      <td>3</td>\n", "      <td>Nonresidential equipment</td>\n", "      <td>2021</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>8020875</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1eq00</td>\n", "      <td>3</td>\n", "      <td>Nonresidential equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>8629807</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep00</td>\n", "      <td>4</td>\n", "      <td>Information processing equipment</td>\n", "      <td>2021</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>1844785</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep00</td>\n", "      <td>4</td>\n", "      <td>Information processing equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>1995151</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep11</td>\n", "      <td>5</td>\n", "      <td>Computers and peripheral equipment</td>\n", "      <td>2021</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>311451</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>FAAt201</td>\n", "      <td>k1ntotl1ep11</td>\n", "      <td>5</td>\n", "      <td>Computers and peripheral equipment</td>\n", "      <td>2022</td>\n", "      <td>Current Dollars</td>\n", "      <td>Level</td>\n", "      <td>6</td>\n", "      <td>344701</td>\n", "      <td>FAAt201</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  TableName    SeriesCode  LineNumber                     LineDescription  \\\n", "0   FAAt201  k1ptotl1es00           1                Private fixed assets   \n", "1   FAAt201  k1ptotl1es00           1                Private fixed assets   \n", "2   FAAt201  k1ptotl1eq00           2                           Equipment   \n", "3   FAAt201  k1ptotl1eq00           2                           Equipment   \n", "4   FAAt201  k1ntotl1eq00           3            Nonresidential equipment   \n", "5   FAAt201  k1ntotl1eq00           3            Nonresidential equipment   \n", "6   FAAt201  k1ntotl1ep00           4    Information processing equipment   \n", "7   FAAt201  k1ntotl1ep00           4    Information processing equipment   \n", "8   FAAt201  k1ntotl1ep11           5  Computers and peripheral equipment   \n", "9   FAAt201  k1ntotl1ep11           5  Computers and peripheral equipment   \n", "\n", "  TimePeriod      METRIC_NAME CL_UNIT  UNIT_MULT  DataValue  NoteRef  \n", "0       2021  Current Dollars   Level          6   59312969  FAAt201  \n", "1       2022  Current Dollars   Level          6   ********  FAAt201  \n", "2       2021  Current Dollars   Level          6    8118582  FAAt201  \n", "3       2022  Current Dollars   Level          6    8731602  FAAt201  \n", "4       2021  Current Dollars   Level          6    8020875  FAAt201  \n", "5       2022  Current Dollars   Level          6    8629807  FAAt201  \n", "6       2021  Current Dollars   Level          6    1844785  FAAt201  \n", "7       2022  Current Dollars   Level          6    1995151  FAAt201  \n", "8       2021  Current Dollars   Level          6     311451  FAAt201  \n", "9       2022  Current Dollars   Level          6     344701  FAAt201  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["bea_tbl_1 = beaapi.get_data(beakey, TableName = 'FAAt201', datasetname='FixedAssets', Year='2021,2022')\n", "display(bea_tbl_1.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Getting some extra metadata"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extra detail keys:dict_keys(['Statistic', 'UTCProductionTime', 'Dimensions', 'Notes'])\n"]}], "source": ["print('Extra detail keys:' + str(bea_tbl.attrs['detail'].keys()))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notes corresponding to NoteRef:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NoteRef</th>\n", "      <th>NoteText</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>FAAT201</td>\n", "      <td>Table 2.1. Current-Cost Net Stock of Private F...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>FAAT201.1</td>\n", "      <td>1. Consists of office buildings, except those ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FAAT201.10</td>\n", "      <td>10. Includes private universities and colleges...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FAAT201.11</td>\n", "      <td>n.e.c. Not elsewhere classified</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FAAT201.2</td>\n", "      <td>2. Includes buildings and structures used by t...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      NoteRef                                           NoteText\n", "0     FAAT201  Table 2.1. Current-Cost Net Stock of Private F...\n", "1   FAAT201.1  1. Consists of office buildings, except those ...\n", "2  FAAT201.10  10. Includes private universities and colleges...\n", "3  FAAT201.11                    n.e.c. Not elsewhere classified\n", "4   FAAT201.2  2. Includes buildings and structures used by t..."]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"Notes corresponding to NoteRef:\")\n", "display(bea_tbl.attrs['detail']['Notes'].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# GDP by Industry"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## list of parameters for GDP by industry"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ParameterName</th>\n", "      <th>ParameterDataType</th>\n", "      <th>ParameterDescription</th>\n", "      <th>ParameterIsRequiredFlag</th>\n", "      <th>ParameterDefaultValue</th>\n", "      <th>MultipleAcceptedFlag</th>\n", "      <th>AllValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Frequency</td>\n", "      <td>string</td>\n", "      <td>A - Annual, Q-Quarterly</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Industry</td>\n", "      <td>string</td>\n", "      <td>List of industries to retrieve (ALL for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TableID</td>\n", "      <td>integer</td>\n", "      <td>The unique GDP by Industry table identifier (A...</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Year</td>\n", "      <td>integer</td>\n", "      <td>List of year(s) of data to retrieve (ALL for All)</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>ALL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ParameterName ParameterDataType  \\\n", "0     Frequency            string   \n", "1      Industry            string   \n", "2       TableID           integer   \n", "3          Year           integer   \n", "\n", "                                ParameterDescription  ParameterIsRequiredFlag  \\\n", "0                            A - Annual, Q-Quarterly                        1   \n", "1       List of industries to retrieve (ALL for All)                        1   \n", "2  The unique GDP by Industry table identifier (A...                        1   \n", "3  List of year(s) of data to retrieve (ALL for All)                        1   \n", "\n", "  ParameterDefaultValue  MultipleAcceptedFlag AllValue  \n", "0                                           1      ALL  \n", "1                                           1      ALL  \n", "2                                           1      ALL  \n", "3                                           1      ALL  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["list_of_params_gdp = beaapi.get_parameter_list(beakey, 'GDPbyIndustry')\n", "display(list_of_params_gdp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Industry parameter"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Key</th>\n", "      <th>Desc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11</td>\n", "      <td>Agriculture, forestry, fishing, and hunting (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>111CA</td>\n", "      <td>Farms (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>113FF</td>\n", "      <td>Forestry, fishing, and related activities (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21</td>\n", "      <td>Mining (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>ORE</td>\n", "      <td>Other real estate (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>PGOOD</td>\n", "      <td>Private goods-producing industries (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>PROF</td>\n", "      <td>Professional and business services (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>PSERV</td>\n", "      <td>Private services-producing industries (A,Q)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>PVT</td>\n", "      <td>Private industries (A,Q)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>104 rows × 2 columns</p>\n", "</div>"], "text/plain": ["       Key                                               Desc\n", "0       11  Agriculture, forestry, fishing, and hunting (A,Q)\n", "1    111CA                                        Farms (A,Q)\n", "2    113FF    Forestry, fishing, and related activities (A,Q)\n", "3       21                                       Mining (A,Q)\n", "4      211                       Oil and gas extraction (A,Q)\n", "..     ...                                                ...\n", "99     ORE                            Other real estate (A,Q)\n", "100  PGOOD           Private goods-producing industries (A,Q)\n", "101   PROF           Professional and business services (A,Q)\n", "102  PSERV        Private services-producing industries (A,Q)\n", "103    PVT                           Private industries (A,Q)\n", "\n", "[104 rows x 2 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["industry_param_vals = beaapi.get_parameter_values(beakey, 'GDPbyIndustry', 'Industry')\n", "display(industry_param_vals)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["industry_param_vals.to_csv('industry.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Table_id parameter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tableid_param_vals = beaapi.get_parameter_values(beakey, 'GDPbyIndustry', 'TableID')\n", "display(tableid_param_vals)\n", "tableid_param_vals.to_csv('tableid.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["year parameter"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Key</th>\n", "      <th>Desc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1997</td>\n", "      <td>1997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1998</td>\n", "      <td>1998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1999</td>\n", "      <td>1999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2000</td>\n", "      <td>2000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2001</td>\n", "      <td>2001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2002</td>\n", "      <td>2002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2003</td>\n", "      <td>2003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2004</td>\n", "      <td>2004</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2005</td>\n", "      <td>2005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2006</td>\n", "      <td>2006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2007</td>\n", "      <td>2007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2008</td>\n", "      <td>2008</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2009</td>\n", "      <td>2009</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2010</td>\n", "      <td>2010</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2011</td>\n", "      <td>2011</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2012</td>\n", "      <td>2012</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2013</td>\n", "      <td>2013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2014</td>\n", "      <td>2014</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2015</td>\n", "      <td>2015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2016</td>\n", "      <td>2016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2017</td>\n", "      <td>2017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2018</td>\n", "      <td>2018</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2019</td>\n", "      <td>2019</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2020</td>\n", "      <td>2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2021</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2022</td>\n", "      <td>2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2023</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2024</td>\n", "      <td>2024</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Key  Desc\n", "0   1997  1997\n", "1   1998  1998\n", "2   1999  1999\n", "3   2000  2000\n", "4   2001  2001\n", "5   2002  2002\n", "6   2003  2003\n", "7   2004  2004\n", "8   2005  2005\n", "9   2006  2006\n", "10  2007  2007\n", "11  2008  2008\n", "12  2009  2009\n", "13  2010  2010\n", "14  2011  2011\n", "15  2012  2012\n", "16  2013  2013\n", "17  2014  2014\n", "18  2015  2015\n", "19  2016  2016\n", "20  2017  2017\n", "21  2018  2018\n", "22  2019  2019\n", "23  2020  2020\n", "24  2021  2021\n", "25  2022  2022\n", "26  2023  2023\n", "27  2024  2024"]}, "metadata": {}, "output_type": "display_data"}], "source": ["year_param_vals = beaapi.get_parameter_values(beakey, 'GDPbyIndustry', 'Year')\n", "display(year_param_vals)\n", "year_param_vals.to_csv('year_gdp.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Quarterly"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableID</th>\n", "      <th>Frequency</th>\n", "      <th>Year</th>\n", "      <th>Quarter</th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Q</td>\n", "      <td>2022</td>\n", "      <td>I</td>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction</td>\n", "      <td>283.9</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Q</td>\n", "      <td>2022</td>\n", "      <td>II</td>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction</td>\n", "      <td>363.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>Q</td>\n", "      <td>2022</td>\n", "      <td>III</td>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction</td>\n", "      <td>342.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>Q</td>\n", "      <td>2022</td>\n", "      <td>IV</td>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction</td>\n", "      <td>286.9</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   TableID Frequency  Year Quarter Industry     IndustrYDescription  \\\n", "0        1         Q  2022       I      211  Oil and gas extraction   \n", "1        1         Q  2022      II      211  Oil and gas extraction   \n", "2        1         Q  2022     III      211  Oil and gas extraction   \n", "3        1         Q  2022      IV      211  Oil and gas extraction   \n", "\n", "   DataValue NoteRef  \n", "0      283.9       1  \n", "1      363.5       1  \n", "2      342.0       1  \n", "3      286.9       1  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["bea_tbl_gdp = beaapi.get_data(beakey, datasetname ='GDPbyIndustry', Frequency = 'Q', Industry ='211', TableID = 1, Year = 2022 )\n", "display(bea_tbl_gdp.head(5))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extra detail keys:dict_keys(['Statistic', 'UTCProductionTime', 'Dimensions', 'Notes'])\n"]}], "source": ["print('Extra detail keys:' + str(bea_tbl_gdp.attrs['detail'].keys()))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NoteRef</th>\n", "      <th>NoteText</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.1.Q</td>\n", "      <td>1. Consists of agriculture, forestry, fishing,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.2.Q</td>\n", "      <td>2. Consists of utilities; wholesale trade; ret...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.3.Q</td>\n", "      <td>3. Consists of computer and electronic product...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.0.Q</td>\n", "      <td>Note. Detail may not add to total due to round...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>Value Added by Industry [Billions of dollars]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  NoteRef                                           NoteText\n", "0   1.1.Q  1. Consists of agriculture, forestry, fishing,...\n", "1   1.2.Q  2. Consists of utilities; wholesale trade; ret...\n", "2   1.3.Q  3. Consists of computer and electronic product...\n", "3   1.0.Q  Note. Detail may not add to total due to round...\n", "4       1      Value Added by Industry [Billions of dollars]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(bea_tbl_gdp.attrs['detail']['Notes'].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Annual with multiple years and Industry"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TableID</th>\n", "      <th>Frequency</th>\n", "      <th>Year</th>\n", "      <th>Quarter</th>\n", "      <th>Industry</th>\n", "      <th>IndustrYDescription</th>\n", "      <th>DataValue</th>\n", "      <th>NoteRef</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>2022</td>\n", "      <td>2022</td>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction</td>\n", "      <td>319.1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>2022</td>\n", "      <td>2022</td>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>298.9</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>2023</td>\n", "      <td>211</td>\n", "      <td>Oil and gas extraction</td>\n", "      <td>257.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>A</td>\n", "      <td>2023</td>\n", "      <td>2023</td>\n", "      <td>334</td>\n", "      <td>Computer and electronic products</td>\n", "      <td>308.1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   TableID Frequency  Year Quarter Industry               IndustrYDescription  \\\n", "0        1         A  2022    2022      211            Oil and gas extraction   \n", "1        1         A  2022    2022      334  Computer and electronic products   \n", "2        1         A  2023    2023      211            Oil and gas extraction   \n", "3        1         A  2023    2023      334  Computer and electronic products   \n", "\n", "   DataValue NoteRef  \n", "0      319.1       1  \n", "1      298.9       1  \n", "2      257.0       1  \n", "3      308.1       1  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["bea_tbl_gdp_2 = beaapi.get_data(beakey, datasetname ='GDPbyIndustry', Frequency = 'A', Industry ='211,334', TableID = 1, Year = '2022,2023' )\n", "display(bea_tbl_gdp_2.head(5))"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}