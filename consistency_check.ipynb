import pandas as pd


df_1 = pd.read_excel("manuf_score_4.1_turn1.xlsx")

df_1.head()

df_2 = pd.read_excel("manuf_score_4.1_turn2.xlsx")

df_2.head()

df_3 = pd.read_excel("manuf_score_4.1_turn3.xlsx")

# Create a new DataFrame to store company names and std deviation
result_df = pd.DataFrame()
result_df['company'] = df_1['company']

# Stack the scores from all three runs
scores = pd.concat([
    df_1[['capacity_expansion_score']],
    df_2[['capacity_expansion_score']],
    df_3[['capacity_expansion_score']]
], axis=1)

# Rename columns for clarity
scores.columns = ['capacity_exp_score_1', 'capacity_exp_score_2', 'capacity_exp_score_3']

# Compute standard deviation row-wise
result_df['capacity_score_std'] = scores.std(axis=1)



scores.head(10)

result_df.head()

# --- Employment Generation Score Std ---
employment_scores = pd.concat([
    df_1[['employment_generation_score']],
    df_2[['employment_generation_score']],
    df_3[['employment_generation_score']]
], axis=1)

employment_scores.columns = ['employment_score_1', 'employment_score_2', 'employment_score_3']
result_df['employment_score_std'] = employment_scores.std(axis=1)

# --- Production Output Score Std ---
production_scores = pd.concat([
    df_1[['production_output_score']],
    df_2[['production_output_score']],
    df_3[['production_output_score']]
], axis=1)

production_scores.columns = ['production_score_1', 'production_score_2', 'production_score_3']
result_df['production_score_std'] = production_scores.std(axis=1)



production_scores.head(10)

result_df.head(10)

from sentence_transformers import SentenceTransformer, util
import pandas as pd

# Load embedding model
model = SentenceTransformer('all-MiniLM-L6-v2')

# Initialize lists to store similarity scores
sim12_list = []
sim23_list = []
sim13_list = []

# Loop through each company
for i in range(len(df_1)):
    # Extract rationales
    r1 = df_1.loc[i, 'capacity_expansion_rationale']
    r2 = df_2.loc[i, 'capacity_expansion_rationale']
    r3 = df_3.loc[i, 'capacity_expansion_rationale']

    # Embed the rationales
    embs = model.encode([r1, r2, r3], convert_to_tensor=True)

    # Compute cosine similarities
    sim12 = util.cos_sim(embs[0], embs[1]).item()
    sim23 = util.cos_sim(embs[1], embs[2]).item()
    sim13 = util.cos_sim(embs[0], embs[2]).item()

    # Store results
    sim12_list.append(sim12)
    sim23_list.append(sim23)
    sim13_list.append(sim13)

# Add to result_df
result_df['cap_exp_sim12'] = sim12_list
result_df['cap_exp_sim23'] = sim23_list
result_df['cap_exp_sim13'] = sim13_list



result_df.head(10)

# --- Employment Generation Rationale Similarity ---
emp_sim12_list = []
emp_sim23_list = []
emp_sim13_list = []

for i in range(len(df_1)):
    r1 = df_1.loc[i, 'employment_generation_rationale']
    r2 = df_2.loc[i, 'employment_generation_rationale']
    r3 = df_3.loc[i, 'employment_generation_rationale']
    
    embs = model.encode([r1, r2, r3], convert_to_tensor=True)
    
    sim12 = util.cos_sim(embs[0], embs[1]).item()
    sim23 = util.cos_sim(embs[1], embs[2]).item()
    sim13 = util.cos_sim(embs[0], embs[2]).item()
    
    emp_sim12_list.append(sim12)
    emp_sim23_list.append(sim23)
    emp_sim13_list.append(sim13)

result_df['emp_gen_sim12'] = emp_sim12_list
result_df['emp_gen_sim23'] = emp_sim23_list
result_df['emp_gen_sim13'] = emp_sim13_list

# --- Production Output Rationale Similarity ---
prod_sim12_list = []
prod_sim23_list = []
prod_sim13_list = []

for i in range(len(df_1)):
    r1 = df_1.loc[i, 'production_output_rationale']
    r2 = df_2.loc[i, 'production_output_rationale']
    r3 = df_3.loc[i, 'production_output_rationale']
    
    embs = model.encode([r1, r2, r3], convert_to_tensor=True)
    
    sim12 = util.cos_sim(embs[0], embs[1]).item()
    sim23 = util.cos_sim(embs[1], embs[2]).item()
    sim13 = util.cos_sim(embs[0], embs[2]).item()
    
    prod_sim12_list.append(sim12)
    prod_sim23_list.append(sim23)
    prod_sim13_list.append(sim13)

result_df['prod_out_sim12'] = prod_sim12_list
result_df['prod_out_sim23'] = prod_sim23_list
result_df['prod_out_sim13'] = prod_sim13_list




result_df.head(10)

df_1['avg_score'] = df_1[[
    'capacity_expansion_score',
    'employment_generation_score',
    'production_output_score'
]].mean(axis=1)

df_1.head()

df_2['avg_score'] = df_2[[
    'capacity_expansion_score',
    'employment_generation_score',
    'production_output_score'
]].mean(axis=1)

df_3['avg_score'] = df_3[[
    'capacity_expansion_score',
    'employment_generation_score',
    'production_output_score'
]].mean(axis=1)

avg_scores = pd.concat([
    df_1[['avg_score']],
    df_2[['avg_score']],
    df_3[['avg_score']]
], axis=1)

avg_scores.columns = ['avg_score_1', 'avg_score_2', 'avg_score_3']
result_df['avg_score_std'] = avg_scores.std(axis=1)

result_df['avg_score_std'] = avg_scores.std(axis=1)

result_df.head(10)

result_df.drop(columns=['avg_score_std'], inplace=True)

result_df.to_excel("consistency_check.xlsx", index=False)