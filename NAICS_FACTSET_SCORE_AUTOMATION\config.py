from pathlib import Path
# ------ API KEYS ------
DATAWEB_TOKEN = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDAwOTg5IiwianRpIjoiYjM2ZDlkZTAtOTRmMS00MjljLTk1YTItN2VmMDlmZmUxYWZmIiwiaXNzIjoiZGF0YXdlYiIsImlhdCI6MTc0OTc5ODMyOSwiZXhwIjoxNzUxMDA3OTI5fQ.jzG-bZScQFdpR0TEQoVT6kdQ_uSNzVnA66DRBNGe6aJbq9GhbfqwsoG-kvnSHcTrTQKEyhhyAMwUCFJym_Basg"
BEA_API_KEY = "0CB6FB71-8009-4FF4-B57F-184021F1A352"
FRED_API_KEY = "51c0e7ec1da836c20ce6d40a9e50f5d1"
BLS_API_KEY = "77631563995c4764bcfe327957cb19cf"

PROJECT_ROOT = Path(__file__).resolve().parent
#----- Master sheet path-----
MASTER_SHEET_PATH = PROJECT_ROOT / "master_sheet.csv"

# --- DATA FOLDER PATHS ---
RAW_DATA_DIR = PROJECT_ROOT / "data" / "raw"
PROCESSED_DATA_DIR = PROJECT_ROOT / "data" / "processed"
FINAL_REPORTS_DIR = PROJECT_ROOT / "data" / "final_reports"

