{"cells": [{"cell_type": "code", "execution_count": 9, "id": "d8fdca7d", "metadata": {}, "outputs": [], "source": ["fred_api_key = \"51c0e7ec1da836c20ce6d40a9e50f5d1\""]}, {"cell_type": "code", "execution_count": 10, "id": "68504cff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total series retrieved: 2646\n"]}], "source": ["import requests\n", "\n", "API_KEY = fred_api_key\n", "SEARCH_TEXT = 'Capacity Utilization'\n", "BASE_URL = 'https://api.stlouisfed.org/fred/series/search'\n", "LIMIT = 1000\n", "offset = 0\n", "all_series = []\n", "\n", "while True:\n", "    params = {\n", "        'search_text': SEARCH_TEXT,\n", "        'api_key': API_KEY,\n", "        'file_type': 'json',\n", "        'limit': LIMIT,\n", "        'offset': offset\n", "    }\n", "    response = requests.get(BASE_URL, params=params)\n", "    data = response.json()\n", "    series = data.get('seriess', [])\n", "    if not series:\n", "        break\n", "    all_series.extend(series)\n", "    offset += LIMIT\n", "\n", "print(f\"Total series retrieved: {len(all_series)}\")\n"]}, {"cell_type": "code", "execution_count": 11, "id": "eecb025d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>realtime_start</th>\n", "      <th>realtime_end</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "      <th>notes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TCU</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Total Index</td>\n", "      <td>1967-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:20:27-05</td>\n", "      <td>69</td>\n", "      <td>69</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CAPUTLB50001SQ</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Total Index</td>\n", "      <td>1967-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Q</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:24:40-05</td>\n", "      <td>12</td>\n", "      <td>69</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CAPUTLB50001A</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Total Index</td>\n", "      <td>1967-01-01</td>\n", "      <td>2024-01-01</td>\n", "      <td>Annual</td>\n", "      <td>A</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:24:38-05</td>\n", "      <td>2</td>\n", "      <td>69</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>INDPRO</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Total Index</td>\n", "      <td>1919-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:27:54-05</td>\n", "      <td>79</td>\n", "      <td>80</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>IPB50001N</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Industrial Production: Total Index</td>\n", "      <td>1919-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Index 2017=100</td>\n", "      <td>Not Seasonally Adjusted</td>\n", "      <td>NSA</td>\n", "      <td>2025-04-16 08:28:20-05</td>\n", "      <td>39</td>\n", "      <td>80</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               id realtime_start realtime_end  \\\n", "0             TCU     2025-05-08   2025-05-08   \n", "1  CAPUTLB50001SQ     2025-05-08   2025-05-08   \n", "2   CAPUTLB50001A     2025-05-08   2025-05-08   \n", "3          INDPRO     2025-05-08   2025-05-08   \n", "4       IPB50001N     2025-05-08   2025-05-08   \n", "\n", "                                title observation_start observation_end  \\\n", "0   Capacity Utilization: Total Index        1967-01-01      2025-03-01   \n", "1   Capacity Utilization: Total Index        1967-01-01      2025-01-01   \n", "2   Capacity Utilization: Total Index        1967-01-01      2024-01-01   \n", "3  Industrial Production: Total Index        1919-01-01      2025-03-01   \n", "4  Industrial Production: Total Index        1919-01-01      2025-03-01   \n", "\n", "   frequency frequency_short           units     units_short  \\\n", "0    Monthly               M         Percent               %   \n", "1  Quarterly               Q         Percent               %   \n", "2     Annual               A         Percent               %   \n", "3    Monthly               M  Index 2017=100  Index 2017=100   \n", "4    Monthly               M  Index 2017=100  Index 2017=100   \n", "\n", "       seasonal_adjustment seasonal_adjustment_short            last_updated  \\\n", "0      Seasonally Adjusted                        SA  2025-04-16 08:20:27-05   \n", "1      Seasonally Adjusted                        SA  2025-04-16 08:24:40-05   \n", "2      Seasonally Adjusted                        SA  2025-04-16 08:24:38-05   \n", "3      Seasonally Adjusted                        SA  2025-04-16 08:27:54-05   \n", "4  Not Seasonally Adjusted                       NSA  2025-04-16 08:28:20-05   \n", "\n", "   popularity  group_popularity  \\\n", "0          69                69   \n", "1          12                69   \n", "2           2                69   \n", "3          79                80   \n", "4          39                80   \n", "\n", "                                               notes  \n", "0  explanatory notes (https://www.federalreserve....  \n", "1  explanatory notes (https://www.federalreserve....  \n", "2  explanatory notes (https://www.federalreserve....  \n", "3  explanatory notes (https://www.federalreserve....  \n", "4  explanatory notes (https://www.federalreserve....  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "capacity_df = pd.DataFrame(all_series)\n", "capacity_df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "3be7ac76", "metadata": {}, "outputs": [], "source": ["capacity_df.to_excel(\"capacity_search_results.xlsx\", index=False)"]}, {"cell_type": "markdown", "id": "448ded5f", "metadata": {}, "source": ["## Filtering capacity utilization manufacturing"]}, {"cell_type": "code", "execution_count": 12, "id": "92a76507", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>realtime_start</th>\n", "      <th>realtime_end</th>\n", "      <th>title</th>\n", "      <th>observation_start</th>\n", "      <th>observation_end</th>\n", "      <th>frequency</th>\n", "      <th>frequency_short</th>\n", "      <th>units</th>\n", "      <th>units_short</th>\n", "      <th>seasonal_adjustment</th>\n", "      <th>seasonal_adjustment_short</th>\n", "      <th>last_updated</th>\n", "      <th>popularity</th>\n", "      <th>group_popularity</th>\n", "      <th>notes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>MCUMFN</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:22:28-05</td>\n", "      <td>46</td>\n", "      <td>46</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>CUMFN</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-01-01</td>\n", "      <td>Quarterly</td>\n", "      <td>Q</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:22:33-05</td>\n", "      <td>5</td>\n", "      <td>46</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>CAPUTLGMFA</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Manufacturing (NAICS)</td>\n", "      <td>1972-01-01</td>\n", "      <td>2024-01-01</td>\n", "      <td>Annual</td>\n", "      <td>A</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:18:45-05</td>\n", "      <td>1</td>\n", "      <td>46</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>CAPUTLG3311A2S</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Manufacturing: Durable G...</td>\n", "      <td>1972-01-01</td>\n", "      <td>2025-03-01</td>\n", "      <td>Monthly</td>\n", "      <td>M</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:24:51-05</td>\n", "      <td>31</td>\n", "      <td>33</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>CAPUTLG3311A2A</td>\n", "      <td>2025-05-08</td>\n", "      <td>2025-05-08</td>\n", "      <td>Capacity Utilization: Manufacturing: Durable G...</td>\n", "      <td>1972-01-01</td>\n", "      <td>2024-01-01</td>\n", "      <td>Annual</td>\n", "      <td>A</td>\n", "      <td>Percent</td>\n", "      <td>%</td>\n", "      <td>Seasonally Adjusted</td>\n", "      <td>SA</td>\n", "      <td>2025-04-16 08:24:43-05</td>\n", "      <td>9</td>\n", "      <td>33</td>\n", "      <td>explanatory notes (https://www.federalreserve....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                id realtime_start realtime_end  \\\n", "8           MCUMFN     2025-05-08   2025-05-08   \n", "9            CUMFN     2025-05-08   2025-05-08   \n", "10      CAPUTLGMFA     2025-05-08   2025-05-08   \n", "16  CAPUTLG3311A2S     2025-05-08   2025-05-08   \n", "17  CAPUTLG3311A2A     2025-05-08   2025-05-08   \n", "\n", "                                                title observation_start  \\\n", "8         Capacity Utilization: Manufacturing (NAICS)        1972-01-01   \n", "9         Capacity Utilization: Manufacturing (NAICS)        1972-01-01   \n", "10        Capacity Utilization: Manufacturing (NAICS)        1972-01-01   \n", "16  Capacity Utilization: Manufacturing: Durable G...        1972-01-01   \n", "17  Capacity Utilization: Manufacturing: Durable G...        1972-01-01   \n", "\n", "   observation_end  frequency frequency_short    units units_short  \\\n", "8       2025-03-01    Monthly               M  Percent           %   \n", "9       2025-01-01  Quarterly               Q  Percent           %   \n", "10      2024-01-01     Annual               A  Percent           %   \n", "16      2025-03-01    Monthly               M  Percent           %   \n", "17      2024-01-01     Annual               A  Percent           %   \n", "\n", "    seasonal_adjustment seasonal_adjustment_short            last_updated  \\\n", "8   Seasonally Adjusted                        SA  2025-04-16 08:22:28-05   \n", "9   Seasonally Adjusted                        SA  2025-04-16 08:22:33-05   \n", "10  Seasonally Adjusted                        SA  2025-04-16 08:18:45-05   \n", "16  Seasonally Adjusted                        SA  2025-04-16 08:24:51-05   \n", "17  Seasonally Adjusted                        SA  2025-04-16 08:24:43-05   \n", "\n", "    popularity  group_popularity  \\\n", "8           46                46   \n", "9            5                46   \n", "10           1                46   \n", "16          31                33   \n", "17           9                33   \n", "\n", "                                                notes  \n", "8   explanatory notes (https://www.federalreserve....  \n", "9   explanatory notes (https://www.federalreserve....  \n", "10  explanatory notes (https://www.federalreserve....  \n", "16  explanatory notes (https://www.federalreserve....  \n", "17  explanatory notes (https://www.federalreserve....  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["prefix_to_filter = \"Capacity Utilization: Manufacturing\"\n", "starts_with_prefix_mask = capacity_df['title'].astype(str).str.startswith(prefix_to_filter)\n", "filtered_df = capacity_df[starts_with_prefix_mask]\n", "filtered_df.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "ff9c25cd", "metadata": {}, "outputs": [], "source": ["filtered_df.to_excel(\"capacity_search_results_manuf.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": 6, "id": "0448fafe", "metadata": {}, "outputs": [{"data": {"text/plain": ["117"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(filtered_df)"]}, {"cell_type": "code", "execution_count": 7, "id": "980c4378", "metadata": {}, "outputs": [{"data": {"text/plain": ["(117, 7)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_columns = ['id', 'title', 'observation_start', 'observation_end', 'frequency', 'units', 'seasonal_adjustment']\n", "capac_df = filtered_df[selected_columns].copy()\n", "\n", "\n", "capac_df.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "1ab9c031", "metadata": {}, "outputs": [], "source": ["capac_df.to_excel(\"capacity_search_results_manuf.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}