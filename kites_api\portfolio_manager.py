import pandas as pd
import os

from config import ACTIVE_POSITIONS_FILE, TRANSACTION_LOG_FILE

# note: need to handle those cases where active positions and transaction_log are not present in the first run.
# perhaps taking the output from strategy_handler and expiry_handler and putting it in data folder is fine. or in 
# main orchestrator we will create this two files.


def update_active_positions(new_positions_df):
    """
    Adds new unique options from `new_positions_df` to `active_positions.xlsx`.

    Parameters:
    - new_positions_df: DataFrame containing new options (with 'tradingsymbol' column).
    """

    file_path = ACTIVE_POSITIONS_FILE
    active_df = pd.read_excel(file_path)

    # Filter out already existing tradingsymbols
    existing_symbols = set(active_df['tradingsymbol'].unique())
    new_df_filtered = new_positions_df[~new_positions_df['tradingsymbol'].isin(existing_symbols)]

    if new_df_filtered.empty:
        print("[INFO] No new positions to add. All options already exist.")
        return

    active_df = pd.concat([active_df, new_df_filtered], ignore_index=True)
    active_df.to_excel(file_path, index=False)
    print(f"[INFO] Added {len(new_df_filtered)} new positions to active_positions.xlsx.")


def remove_expired_positions(expired_df: pd.DataFrame):
    """
    Removes squared-off positions from `active_positions.xlsx` based on `tradingsymbol`.

    Parameters:
    - expired_df: DataFrame containing expired/squared-off options (must have 'tradingsymbol').
    """
    file_path = ACTIVE_POSITIONS_FILE

    try:
        active_df = pd.read_excel(file_path)
    except FileNotFoundError:
        print("[ERROR] active_positions.xlsx not found.")
        return

    # Set of symbols that were squared off
    expired_symbols = set(expired_df['tradingsymbol'].unique())

    # Remove rows from active positions that are in expired_symbols
    initial_count = len(active_df)
    active_df = active_df[~active_df['tradingsymbol'].isin(expired_symbols)]
    final_count = len(active_df)

    removed = initial_count - final_count
    if removed == 0:
        print("[INFO] No matching positions found to remove.")
    else:
        print(f"[INFO] Removed {removed} expired positions from active_positions.xlsx.")

    active_df.to_excel(file_path, index=False)


# Transaction log management

def append_to_transaction_log(expired_df: pd.DataFrame):
    """
    Appends monthly expiry data to transaction_log.xlsx.

    Parameters:
    - expired_df: DataFrame containing options that were squared off.
    """
    file_path = TRANSACTION_LOG_FILE
    transaction_log = pd.read_excel(file_path)
    if transaction_log.empty:
            combined_df = expired_df.copy()
    else:
        combined_df = pd.concat([transaction_log, expired_df], ignore_index=True)
    # combined_df = pd.concat([transaction_log, expired_df], ignore_index=True)
    print(f"[INFO] Appended {len(expired_df)} records to transaction_log.xlsx.")
    combined_df.to_excel(file_path, index=False)


