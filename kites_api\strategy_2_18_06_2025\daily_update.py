import pandas as pd
from logger import log
import config
import data_manager
import kite_utils
import strategy

def update_positions():
    """
    Main function to update current prices and P&L for all active positions.
    """
    log.info("--- Starting Daily Update Process ---")

    # --- 1. Initialization and Data Loading ---
    try:
        kite = kite_utils.initialize_kite()
        active_options_df = data_manager.get_active_options()
        stock_holdings_df = data_manager.get_stock_holdings()
    except Exception as e:
        log.error(f"Initialization or data loading failed: {e}", exc_info=True)
        return

    # --- 2. Check if there's anything to update ---
    if active_options_df.empty and stock_holdings_df.empty:
        log.info("No active options or stock holdings to update. Exiting.")
        return

    # --- 3. Gather all instruments for a single API call ---
    instruments_to_quote = []
    
    # Get instruments from active options
    if not active_options_df.empty:
        # We need the option's tradingsymbol (e.g., 'NFO:ICICIBANK24AUG1050PE')
        option_symbols = [f"{row['exchange']}:{row['tradingsymbol']}" for _, row in active_options_df.iterrows()]
        instruments_to_quote.extend(option_symbols)

        # We also need the underlying stock's price (e.g., 'NSE:ICICIBANK')
        stock_symbols_for_options = [f"NSE:{row['name']}" for _, row in active_options_df.iterrows()]
        instruments_to_quote.extend(stock_symbols_for_options)
        
    # Get instruments from stock holdings
    if not stock_holdings_df.empty:
        stock_symbols_for_holdings = [f"NSE:{row['name']}" for _, row in stock_holdings_df.iterrows()]
        instruments_to_quote.extend(stock_symbols_for_holdings)

    # Remove duplicates
    instruments_to_quote = sorted(list(set(instruments_to_quote)))

    if not instruments_to_quote:
        log.info("No instruments found to fetch quotes for. Exiting.")
        return
        
    # --- 4. Fetch quotes from Kite API ---
    log.info(f"Fetching quotes for {len(instruments_to_quote)} instruments...")
    quotes = kite_utils.get_quotes(kite, instruments_to_quote)
    if not quotes:
        log.error("Could not fetch quotes. Aborting update.")
        return

    # --- 5. Update Active Options DataFrame ---
    if not active_options_df.empty:
        log.info("Updating active options P&L...")
        for index, row in active_options_df.iterrows():
            option_instrument = f"{row['exchange']}:{row['tradingsymbol']}"
            stock_instrument = f"NSE:{row['name']}"

            # Update current option price
            if option_instrument in quotes:
                current_option_price = quotes[option_instrument]['last_price']
                active_options_df.loc[index, 'current_option_price'] = current_option_price
                
                # Update current stock price
                if stock_instrument in quotes:
                    active_options_df.loc[index, 'current_stock_price'] = quotes[stock_instrument]['last_price']
                
                # Calculate and update P&L using our strategy function
                original_premium_per_share = row['price_of_option_at_option_sell']
                lot_size = row['lot_size']
                
                pnl = strategy.calculate_rollover_profit_or_loss(
                    current_option_price=current_option_price,
                    original_option_premium_per_share=original_premium_per_share,
                    lot_size=lot_size
                )
                active_options_df.loc[index, 'current_profit_or_loss_on_option'] = pnl
                active_options_df.loc[index, 'premium_paid_if_today_squareoff'] = current_option_price * lot_size


    # --- 6. Update Stock Holdings DataFrame ---
    if not stock_holdings_df.empty:
        log.info("Updating stock holdings P&L...")
        for index, row in stock_holdings_df.iterrows():
            stock_instrument = f"NSE:{row['name']}"
            
            if stock_instrument in quotes:
                current_price = quotes[stock_instrument]['last_price']
                stock_holdings_df.loc[index, 'current_price'] = current_price
                
                # Calculate P&L for stocks
                purchase_price = row['purchase_price']
                quantity = row['quantity']
                pnl = (current_price - purchase_price) * quantity
                stock_holdings_df.loc[index, 'current_profit_loss_on_stocks'] = pnl

    # --- 7. Save updated data ---
    try:
        if not active_options_df.empty:
            data_manager.save_active_options(active_options_df)
        if not stock_holdings_df.empty:
            data_manager.save_stock_holdings(stock_holdings_df)
        log.info("Daily update process completed successfully.")
    except Exception as e:
        log.error(f"Failed to save updated data: {e}", exc_info=True)



if __name__ == "__main__":
    update_positions()