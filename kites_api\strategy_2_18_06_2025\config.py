import os
# -----Kite API credentials-----
API_KEY = "3q0r6l1fb689slhk" 
ACCESS_TOKEN = "S7GLzIKtVvNaEr3GBazzgXMJn9hHtaNF"

# -----Target companies-----
TARGET_STOCKS = [
    'HDFCBANK',
    'ICICIBANK',
    'SBIN']
# TARGET_STOCKS = [
#     'ICICIBANK',
#     'HDFCBANK',
#     'SBIN',
#     'AXISBANK',
#     'KOTAKBANK',
#     'MUTHOOTFIN',
#     'MANAPPURAM'
# ]

# -----Intrinsic values-----
INTRINSIC_VALUES ={
    'HDFCBANK': 1900,
    'ICICIBANK': 1400, 
    'SBIN': 750  
}
# INTRINSIC_VALUES = {
#     'ICICIBANK': 1100,
#     'HDFCBANK': 1500,
#     'SBIN': 800,
#     'AXISBANK': 1150,
#     'KOTAKBANK': 1700,
#     'MUTHOOTFIN': 1300,
#     'MANAPPURAM': 180,
# }

MIN_PREMIUM_PERCENT = 0.01

# -----File paths-----
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, 'data')
os.makedirs(DATA_DIR, exist_ok=True)
ACTIVE_OPTIONS_FILE = os.path.join(DATA_DIR, 'active_options.xlsx')
STOCK_SHEET_FILE = os.path.join(DATA_DIR, 'stock_sheet.xlsx')
TRANSACTION_SHEET_FILE = os.path.join(DATA_DIR, 'transaction_sheet.xlsx')


