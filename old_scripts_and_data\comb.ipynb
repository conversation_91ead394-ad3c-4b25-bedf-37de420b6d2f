{"cells": [{"cell_type": "markdown", "id": "79e08e22", "metadata": {}, "source": ["## Adding factset to NAICS mapping "]}, {"cell_type": "code", "execution_count": 1, "id": "364c4506", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "584cc659", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NAICS Code</th>\n", "      <th>NAICS Title</th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "      <th>Match Rationale</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1111</td>\n", "      <td>Oilseed and Grain Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS involves growing oilseed and grain crops...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1112</td>\n", "      <td>Vegetable and Melon Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS covers growing vegetables and melons; Fa...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1113</td>\n", "      <td>Fruit and Tree Nut Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS involves growing fruit and tree nut crop...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1114</td>\n", "      <td>Greenhouse, Nursery, and Floriculture Production</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS includes growing crops under cover and n...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1119</td>\n", "      <td>Other Crop Farming</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "      <td>NAICS covers growing crops like tobacco, cotto...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NAICS Code                                       NAICS Title  \\\n", "0        1111                         Oilseed and Grain Farming   \n", "1        1112                       Vegetable and Melon Farming   \n", "2        1113                        Fruit and Tree Nut Farming   \n", "3        1114  Greenhouse, Nursery, and Floriculture Production   \n", "4        1119                                Other Crop Farming   \n", "\n", "   Subgroup Code                    Subgroup Title  \\\n", "0         2225.0  Agricultural Commodities/Milling   \n", "1         2225.0  Agricultural Commodities/Milling   \n", "2         2225.0  Agricultural Commodities/Milling   \n", "3         2225.0  Agricultural Commodities/Milling   \n", "4         2225.0  Agricultural Commodities/Milling   \n", "\n", "                                     Match Rationale  \n", "0  NAICS involves growing oilseed and grain crops...  \n", "1  NAICS covers growing vegetables and melons; Fa...  \n", "2  NAICS involves growing fruit and tree nut crop...  \n", "3  NAICS includes growing crops under cover and n...  \n", "4  NAICS covers growing crops like tobacco, cotto...  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["mapping_df = pd.read_excel('NAICS_to_Factset.xlsx')\n", "\n", "mapping_df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "9c82b95e", "metadata": {}, "outputs": [], "source": ["comb_rank_df = pd.read_excel('Combined_NAICS_BEA_Scores_new.xlsx')"]}, {"cell_type": "code", "execution_count": 4, "id": "73b84aba", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code BEA_industry_description  NAICS_code  \\\n", "0     3110       Food manufacturing        3111   \n", "1     3110       Food manufacturing        3112   \n", "2     3110       Food manufacturing        3113   \n", "3     3110       Food manufacturing        3114   \n", "4     3110       Food manufacturing        3115   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.498296   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \n", "0                  0.5                0.115238          0.385857  \n", "1                  0.5                0.159188          0.438410  \n", "2                  0.5                0.153765          0.354900  \n", "3                  0.5                0.167181          0.458670  \n", "4                  0.5                0.117253          0.458316  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["comb_rank_df.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "bafc0157", "metadata": {}, "outputs": [], "source": ["comb_rank_df['NAICS_code'] = comb_rank_df['NAICS_code'].astype(str).str.strip()\n", "mapping_df['NAICS Code'] = mapping_df['NAICS Code'].astype(str).str.strip()"]}, {"cell_type": "code", "execution_count": 6, "id": "c67b727d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["object\n"]}], "source": ["print(comb_rank_df['NAICS_code'].dtype)"]}, {"cell_type": "code", "execution_count": 7, "id": "d3f30611", "metadata": {}, "outputs": [], "source": ["mapping_df.rename(columns={'NAICS Code': 'NAICS_code'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "9f36772c", "metadata": {}, "outputs": [], "source": ["final_df = pd.merge(\n", "    comb_rank_df,\n", "    mapping_df[['NAICS_code', 'Subgroup Code', 'Subgroup Title']],\n", "    on='NAICS_code',\n", "    how='left'\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "deb8d898", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BEA_code</th>\n", "      <th>BEA_industry_description</th>\n", "      <th>NAICS_code</th>\n", "      <th>NAICS_industry_description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Subgroup Code</th>\n", "      <th>Subgroup Title</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3111</td>\n", "      <td>Animal food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.115238</td>\n", "      <td>0.385857</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3112</td>\n", "      <td>Grain and oilseed milling</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.159188</td>\n", "      <td>0.438410</td>\n", "      <td>2225.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3113</td>\n", "      <td>Sugar and confectionery product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.153765</td>\n", "      <td>0.354900</td>\n", "      <td>2410.0</td>\n", "      <td>Agricultural Commodities/Milling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3114</td>\n", "      <td>Fruit and vegetable preserving and specialty f...</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.167181</td>\n", "      <td>0.458670</td>\n", "      <td>2410.0</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3115</td>\n", "      <td>Dairy product manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.117253</td>\n", "      <td>0.458316</td>\n", "      <td>2415.0</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3116</td>\n", "      <td>Animal slaughtering and processing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.137305</td>\n", "      <td>0.866124</td>\n", "      <td>2415.0</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3117</td>\n", "      <td>Seafood product preparation and packaging</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.127220</td>\n", "      <td>0.352433</td>\n", "      <td>2415.0</td>\n", "      <td>Food: Meat/Fish/Dairy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3118</td>\n", "      <td>Bakeries and tortilla manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.149352</td>\n", "      <td>0.725256</td>\n", "      <td>2410.0</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3110</td>\n", "      <td>Food manufacturing</td>\n", "      <td>3119</td>\n", "      <td>Other food manufacturing</td>\n", "      <td>0.498296</td>\n", "      <td>0.5</td>\n", "      <td>0.157582</td>\n", "      <td>0.519237</td>\n", "      <td>2410.0</td>\n", "      <td>Food: Specialty/Candy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3120</td>\n", "      <td>Beverage and tobacco product manufacturing</td>\n", "      <td>3121</td>\n", "      <td>Beverage manufacturing</td>\n", "      <td>0.228889</td>\n", "      <td>0.5</td>\n", "      <td>0.206382</td>\n", "      <td>0.659114</td>\n", "      <td>2420.0</td>\n", "      <td>Beverages: Non-Alcoholic</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  BEA_code                    BEA_industry_description NAICS_code  \\\n", "0     3110                          Food manufacturing       3111   \n", "1     3110                          Food manufacturing       3112   \n", "2     3110                          Food manufacturing       3113   \n", "3     3110                          Food manufacturing       3114   \n", "4     3110                          Food manufacturing       3115   \n", "5     3110                          Food manufacturing       3116   \n", "6     3110                          Food manufacturing       3117   \n", "7     3110                          Food manufacturing       3118   \n", "8     3110                          Food manufacturing       3119   \n", "9     3120  Beverage and tobacco product manufacturing       3121   \n", "\n", "                          NAICS_industry_description  Fixed_Assets_Score  \\\n", "0                          Animal food manufacturing            0.498296   \n", "1                          Grain and oilseed milling            0.498296   \n", "2      Sugar and confectionery product manufacturing            0.498296   \n", "3  Fruit and vegetable preserving and specialty f...            0.498296   \n", "4                        Dairy product manufacturing            0.498296   \n", "5                 Animal slaughtering and processing            0.498296   \n", "6          Seafood product preparation and packaging            0.498296   \n", "7                Bakeries and tortilla manufacturing            0.498296   \n", "8                           Other food manufacturing            0.498296   \n", "9                             Beverage manufacturing            0.228889   \n", "\n", "   Economic_Heft_Score  Import_Intensity_Score  Employment_Score  \\\n", "0                  0.5                0.115238          0.385857   \n", "1                  0.5                0.159188          0.438410   \n", "2                  0.5                0.153765          0.354900   \n", "3                  0.5                0.167181          0.458670   \n", "4                  0.5                0.117253          0.458316   \n", "5                  0.5                0.137305          0.866124   \n", "6                  0.5                0.127220          0.352433   \n", "7                  0.5                0.149352          0.725256   \n", "8                  0.5                0.157582          0.519237   \n", "9                  0.5                0.206382          0.659114   \n", "\n", "   Subgroup Code                    Subgroup Title  \n", "0         2225.0  Agricultural Commodities/Milling  \n", "1         2225.0  Agricultural Commodities/Milling  \n", "2         2410.0  Agricultural Commodities/Milling  \n", "3         2410.0             Food: Specialty/Candy  \n", "4         2415.0             Food: Meat/Fish/Dairy  \n", "5         2415.0             Food: Meat/Fish/Dairy  \n", "6         2415.0             Food: Meat/Fish/Dairy  \n", "7         2410.0             Food: Specialty/Candy  \n", "8         2410.0             Food: Specialty/Candy  \n", "9         2420.0          Beverages: Non-Alcoholic  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df.head(10)"]}, {"cell_type": "code", "execution_count": 10, "id": "0e3a7def", "metadata": {}, "outputs": [], "source": ["final_df['Subgroup Code'] = final_df['Subgroup Code'].astype('Int64')"]}, {"cell_type": "code", "execution_count": 11, "id": "d28ad072", "metadata": {}, "outputs": [], "source": ["final_df.rename(columns={'Subgroup Code': 'Factset_code', 'Subgroup Title': 'Factset_title' }, inplace=True)"]}, {"cell_type": "code", "execution_count": 12, "id": "24712cbf", "metadata": {}, "outputs": [], "source": ["final_df.to_excel('Combined_NAICS_BEA_Scores_factset_new.xlsx', index=False)"]}, {"cell_type": "markdown", "id": "b486b3bb", "metadata": {}, "source": ["## SEC 10 Q section extraction"]}, {"cell_type": "code", "execution_count": null, "id": "a85ed9dc", "metadata": {}, "outputs": [], "source": ["3"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}