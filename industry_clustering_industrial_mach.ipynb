{"cells": [{"cell_type": "markdown", "id": "c9914f9d", "metadata": {}, "source": ["## K means clustering"]}, {"cell_type": "markdown", "id": "a91324cc", "metadata": {}, "source": ["### load data"]}, {"cell_type": "code", "execution_count": 1, "id": "c4dff0f6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "37f65fe1", "metadata": {}, "outputs": [], "source": ["us_data_df = pd.read_excel(\"US_Data_Matched_new.xlsx\")"]}, {"cell_type": "code", "execution_count": 3, "id": "557c4278", "metadata": {}, "outputs": [{"data": {"text/plain": ["(809, 46)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["us_data_df.shape"]}, {"cell_type": "code", "execution_count": 4, "id": "b6c67118", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Fsym_ID</th>\n", "      <th>Fsym_Security_ID</th>\n", "      <th>Ticker</th>\n", "      <th>ISIN</th>\n", "      <th>SEDOL</th>\n", "      <th>Entity_ID</th>\n", "      <th>Entity_Name</th>\n", "      <th>Security_Name</th>\n", "      <th>Exchange_Code</th>\n", "      <th>Exchange_Name</th>\n", "      <th>...</th>\n", "      <th>Last_Trade_Date</th>\n", "      <th>ADTV_6M</th>\n", "      <th>ADTV_3M</th>\n", "      <th>Security_Mcap_USD</th>\n", "      <th>Business Description</th>\n", "      <th>Fixed_Assets_Score</th>\n", "      <th>Economic_Heft_Score</th>\n", "      <th>Import_Intensity_Score</th>\n", "      <th>Employment_Score</th>\n", "      <th>Combined_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MH33D6-R</td>\n", "      <td>R85KLC-S</td>\n", "      <td>AAPL-US</td>\n", "      <td>US0378331005</td>\n", "      <td>2046251</td>\n", "      <td>000C7F-E</td>\n", "      <td>Apple, Inc.</td>\n", "      <td>Apple Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>11780.832445</td>\n", "      <td>12683.983288</td>\n", "      <td>3.207062e+06</td>\n", "      <td>Apple Inc. designs, manufactures and markets s...</td>\n", "      <td>0.5</td>\n", "      <td>0.02</td>\n", "      <td>0.55</td>\n", "      <td>0.22</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>K7TPSX-R</td>\n", "      <td>QDYJZC-S</td>\n", "      <td>NVDA-US</td>\n", "      <td>US67066G1040</td>\n", "      <td>2379504</td>\n", "      <td>00208X-E</td>\n", "      <td>NVIDIA Corp.</td>\n", "      <td>NVIDIA Corporation</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>33115.052807</td>\n", "      <td>34618.656093</td>\n", "      <td>2.968748e+06</td>\n", "      <td>NVIDIA Corporation is a full-stack computing i...</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>B81TLL-R</td>\n", "      <td>MDX5ZL-S</td>\n", "      <td>AVGO-US</td>\n", "      <td>US11135F1012</td>\n", "      <td>BDZ78H9</td>\n", "      <td>0JCLYY-E</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>Broadcom Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>5828.494482</td>\n", "      <td>7560.204036</td>\n", "      <td>9.194190e+05</td>\n", "      <td>Broadcom Inc. is a global technology firm that...</td>\n", "      <td>0.5</td>\n", "      <td>0.11</td>\n", "      <td>0.53</td>\n", "      <td>0.62</td>\n", "      <td>0.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Q2YN1N-R</td>\n", "      <td>WWDPYB-S</td>\n", "      <td>TSLA-US</td>\n", "      <td>US88160R1014</td>\n", "      <td>B616C79</td>\n", "      <td>006XY7-E</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>Tesla, Inc.</td>\n", "      <td>NAS</td>\n", "      <td>NASDAQ</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>29106.313789</td>\n", "      <td>32312.138650</td>\n", "      <td>8.040649e+05</td>\n", "      <td>Tesla, Inc. designs, develops, manufactures, s...</td>\n", "      <td>0.3</td>\n", "      <td>0.58</td>\n", "      <td>1.00</td>\n", "      <td>0.64</td>\n", "      <td>0.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>B19ST9-R</td>\n", "      <td>W38FV3-S</td>\n", "      <td>LLY-US</td>\n", "      <td>US5324571083</td>\n", "      <td>2516152</td>\n", "      <td>000P56-E</td>\n", "      <td>Eli Lilly &amp; Co.</td>\n", "      <td>Eli Lilly and Company</td>\n", "      <td>NYS</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>...</td>\n", "      <td>2025-03-20</td>\n", "      <td>3030.989148</td>\n", "      <td>3017.401099</td>\n", "      <td>7.711702e+05</td>\n", "      <td>Eli Lilly and Company is a medicine company. T...</td>\n", "      <td>0.5</td>\n", "      <td>0.28</td>\n", "      <td>0.85</td>\n", "      <td>0.62</td>\n", "      <td>0.67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 46 columns</p>\n", "</div>"], "text/plain": ["    Fsym_ID Fsym_Security_ID   Ticker          ISIN    SEDOL Entity_ID  \\\n", "0  MH33D6-R         R85KLC-S  AAPL-US  US0378331005  2046251  000C7F-E   \n", "1  K7TPSX-R         QDYJZC-S  NVDA-US  US67066G1040  2379504  00208X-E   \n", "2  B81TLL-R         MDX5ZL-S  AVGO-US  US11135F1012  BDZ78H9  0JCLYY-E   \n", "3  Q2YN1N-R         WWDPYB-S  TSLA-US  US88160R1014  B616C79  006XY7-E   \n", "4  B19ST9-R         W38FV3-S   LLY-US  US5324571083  2516152  000P56-E   \n", "\n", "       Entity_Name          Security_Name Exchange_Code  \\\n", "0      Apple, Inc.             Apple Inc.           NAS   \n", "1     NVIDIA Corp.     NVIDIA Corporation           NAS   \n", "2    Broadcom Inc.          Broadcom Inc.           NAS   \n", "3      Tesla, Inc.            Tesla, Inc.           NAS   \n", "4  Eli Lilly & Co.  Eli Lilly and Company           NYS   \n", "\n", "             Exchange_Name  ... Last_Trade_Date       ADTV_6M       ADTV_3M  \\\n", "0                   NASDAQ  ...      2025-03-20  11780.832445  12683.983288   \n", "1                   NASDAQ  ...      2025-03-20  33115.052807  34618.656093   \n", "2                   NASDAQ  ...      2025-03-20   5828.494482   7560.204036   \n", "3                   NASDAQ  ...      2025-03-20  29106.313789  32312.138650   \n", "4  New York Stock Exchange  ...      2025-03-20   3030.989148   3017.401099   \n", "\n", "  Security_Mcap_USD                               Business Description  \\\n", "0      3.207062e+06  Apple Inc. designs, manufactures and markets s...   \n", "1      2.968748e+06  NVIDIA Corporation is a full-stack computing i...   \n", "2      9.194190e+05  Broadcom Inc. is a global technology firm that...   \n", "3      8.040649e+05  Tesla, Inc. designs, develops, manufactures, s...   \n", "4      7.711702e+05  Eli Lilly and Company is a medicine company. T...   \n", "\n", "  Fixed_Assets_Score Economic_Heft_Score  Import_Intensity_Score  \\\n", "0                0.5                0.02                    0.55   \n", "1                0.5                0.11                    0.53   \n", "2                0.5                0.11                    0.53   \n", "3                0.3                0.58                    1.00   \n", "4                0.5                0.28                    0.85   \n", "\n", "  Employment_Score Combined_Score  \n", "0             0.22           0.40  \n", "1             0.62           0.48  \n", "2             0.62           0.48  \n", "3             0.64           0.76  \n", "4             0.62           0.67  \n", "\n", "[5 rows x 46 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["us_data_df.head()"]}, {"cell_type": "markdown", "id": "2b0a0006", "metadata": {}, "source": ["### Filtering Industrial machinery companies"]}, {"cell_type": "code", "execution_count": 5, "id": "bcc344d9", "metadata": {}, "outputs": [], "source": ["industrial_machinery_df = us_data_df[us_data_df['Factset_Industry'] == 'Industrial Machinery'][['Entity_Name', 'Company_Mcap_USD']]"]}, {"cell_type": "code", "execution_count": 6, "id": "b538dc24", "metadata": {}, "outputs": [{"data": {"text/plain": ["(52, 2)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_machinery_df.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "20328376", "metadata": {}, "outputs": [], "source": ["industrial_machinery_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": 8, "id": "bd288c16", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Applied Materials, Inc.</td>\n", "      <td>126464.546766</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Lam Research Corp.</td>\n", "      <td>101011.364915</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Carrier Global Corp.</td>\n", "      <td>57481.090569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ingersoll Rand, Inc.</td>\n", "      <td>33000.426945</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Xylem, Inc.</td>\n", "      <td>29836.354348</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Entity_Name  Company_Mcap_USD\n", "0  Applied Materials, Inc.     126464.546766\n", "1       Lam Research Corp.     101011.364915\n", "2     Carrier Global Corp.      57481.090569\n", "3     Ingersoll Rand, Inc.      33000.426945\n", "4              Xylem, Inc.      29836.354348"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_machinery_df.head()"]}, {"cell_type": "markdown", "id": "ff67e4ec", "metadata": {}, "source": ["### visulaizing raw market cap data"]}, {"cell_type": "code", "execution_count": 10, "id": "21f7aa1b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(industrial_machinery_df['Company_Mcap_USD'], bins=15, kde=True)\n", "plt.title('Raw Market Cap Distribution')\n", "plt.xlabel('Market Cap (USD Millions)')\n", "plt.ylabel('Frequency')\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "ae1216f5", "metadata": {}, "source": ["Distribution is right skewed"]}, {"cell_type": "markdown", "id": "cd173a56", "metadata": {}, "source": ["### log transform to handle skewness"]}, {"cell_type": "code", "execution_count": 11, "id": "0e054beb", "metadata": {}, "outputs": [], "source": ["industrial_machinery_df['LogMCap'] = np.log1p(industrial_machinery_df['Company_Mcap_USD'])  # log1p(x) = log(1 + x)"]}, {"cell_type": "code", "execution_count": 12, "id": "6101fd97", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Applied Materials, Inc.</td>\n", "      <td>126464.546766</td>\n", "      <td>11.747725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Lam Research Corp.</td>\n", "      <td>101011.364915</td>\n", "      <td>11.522998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Carrier Global Corp.</td>\n", "      <td>57481.090569</td>\n", "      <td>10.959229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ingersoll Rand, Inc.</td>\n", "      <td>33000.426945</td>\n", "      <td>10.404306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Xylem, Inc.</td>\n", "      <td>29836.354348</td>\n", "      <td>10.303516</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Entity_Name  Company_Mcap_USD    LogMCap\n", "0  Applied Materials, Inc.     126464.546766  11.747725\n", "1       Lam Research Corp.     101011.364915  11.522998\n", "2     Carrier Global Corp.      57481.090569  10.959229\n", "3     Ingersoll Rand, Inc.      33000.426945  10.404306\n", "4              Xylem, Inc.      29836.354348  10.303516"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_machinery_df.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "dbf02e02", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 6))\n", "sns.histplot(industrial_machinery_df['LogMCap'], bins=20, kde=True)\n", "plt.title('Log Market Cap Distribution')\n", "plt.xlabel('LogMcap')\n", "plt.ylabel('Frequency')\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "1da9aa9f", "metadata": {}, "source": ["### Apply K means clustering"]}, {"cell_type": "code", "execution_count": 15, "id": "d259403a", "metadata": {}, "outputs": [], "source": ["from sklearn.cluster import KMeans\n", "\n", "X = industrial_machinery_df[['LogMCap']] # KMeans expects a 2D array\n", "\n", "kmeans = KMeans(n_clusters=3, random_state=42)\n", "industrial_machinery_df['Cluster'] = kmeans.fit_predict(X)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "9a54e6ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["kmeans.n_iter_\n"]}, {"cell_type": "code", "execution_count": 17, "id": "31351d74", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "      <th>Cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Applied Materials, Inc.</td>\n", "      <td>126464.546766</td>\n", "      <td>11.747725</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Lam Research Corp.</td>\n", "      <td>101011.364915</td>\n", "      <td>11.522998</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Carrier Global Corp.</td>\n", "      <td>57481.090569</td>\n", "      <td>10.959229</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ingersoll Rand, Inc.</td>\n", "      <td>33000.426945</td>\n", "      <td>10.404306</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Xylem, Inc.</td>\n", "      <td>29836.354348</td>\n", "      <td>10.303516</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Entity_Name  Company_Mcap_USD    LogMCap  Cluster\n", "0  Applied Materials, Inc.     126464.546766  11.747725        2\n", "1       Lam Research Corp.     101011.364915  11.522998        2\n", "2     Carrier Global Corp.      57481.090569  10.959229        2\n", "3     Ingersoll Rand, Inc.      33000.426945  10.404306        2\n", "4              Xylem, Inc.      29836.354348  10.303516        2"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_machinery_df.head()"]}, {"cell_type": "markdown", "id": "2d3a4951", "metadata": {}, "source": ["### Assigning cluster names"]}, {"cell_type": "code", "execution_count": 18, "id": "a6f702f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cluster\n", "1     1375.767755\n", "0     7692.552205\n", "2    52169.229575\n", "Name: Company_Mcap_USD, dtype: float64\n"]}], "source": ["cluster_avg = industrial_machinery_df.groupby('Cluster')['Company_Mcap_USD'].mean().sort_values()\n", "print(cluster_avg)"]}, {"cell_type": "code", "execution_count": 19, "id": "20f82344", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1: 'Small', 0: 'Medium', 2: 'Large'}\n"]}], "source": ["cluster_to_size = {cluster: size for cluster, size in zip(cluster_avg.index, ['Small', 'Medium', 'Large'])}\n", "print(cluster_to_size)"]}, {"cell_type": "code", "execution_count": 20, "id": "3197c87e", "metadata": {}, "outputs": [], "source": ["industrial_machinery_df['SizeCategory'] = industrial_machinery_df['Cluster'].map(cluster_to_size)"]}, {"cell_type": "code", "execution_count": 21, "id": "8ffd66d4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "      <th>Cluster</th>\n", "      <th>SizeCategory</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Applied Materials, Inc.</td>\n", "      <td>126464.546766</td>\n", "      <td>11.747725</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Lam Research Corp.</td>\n", "      <td>101011.364915</td>\n", "      <td>11.522998</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Carrier Global Corp.</td>\n", "      <td>57481.090569</td>\n", "      <td>10.959229</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ingersoll Rand, Inc.</td>\n", "      <td>33000.426945</td>\n", "      <td>10.404306</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Xylem, Inc.</td>\n", "      <td>29836.354348</td>\n", "      <td>10.303516</td>\n", "      <td>2</td>\n", "      <td>Large</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Entity_Name  Company_Mcap_USD    LogMCap  Cluster SizeCategory\n", "0  Applied Materials, Inc.     126464.546766  11.747725        2        Large\n", "1       Lam Research Corp.     101011.364915  11.522998        2        Large\n", "2     Carrier Global Corp.      57481.090569  10.959229        2        Large\n", "3     Ingersoll Rand, Inc.      33000.426945  10.404306        2        Large\n", "4              Xylem, Inc.      29836.354348  10.303516        2        Large"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_machinery_df.head()"]}, {"cell_type": "markdown", "id": "0dc3c36a", "metadata": {}, "source": ["### number of industries in each cluster"]}, {"cell_type": "code", "execution_count": 22, "id": "c0e5afa8", "metadata": {}, "outputs": [{"data": {"text/plain": ["SizeCategory\n", "Medium    27\n", "Small     17\n", "Large      8\n", "Name: count, dtype: int64"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_machinery_df['SizeCategory'].value_counts()\n"]}, {"cell_type": "markdown", "id": "66fd3d7a", "metadata": {}, "source": ["## Quantile based clustering"]}, {"cell_type": "code", "execution_count": 23, "id": "10a47f82", "metadata": {}, "outputs": [], "source": ["industrial_machinery_df['QuantileCluster'] = pd.qcut(industrial_machinery_df['LogMCap'], q=3, labels=['Small', 'Medium', 'Large'])"]}, {"cell_type": "markdown", "id": "4d46d74e", "metadata": {}, "source": ["Calculates 33 percentile and 66 percentile"]}, {"cell_type": "code", "execution_count": 24, "id": "0f54471a", "metadata": {}, "outputs": [{"data": {"text/plain": ["QuantileCluster\n", "Medium    18\n", "Small     17\n", "Large     17\n", "Name: count, dtype: int64"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_machinery_df['QuantileCluster'].value_counts()"]}, {"cell_type": "code", "execution_count": 25, "id": "0606c57d", "metadata": {}, "outputs": [], "source": ["industrial_machinery_df.to_excel(\"industrial_machinery_clusters.xlsx\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}