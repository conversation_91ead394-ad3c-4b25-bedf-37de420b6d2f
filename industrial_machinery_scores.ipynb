{"cells": [{"cell_type": "markdown", "id": "fff1aa8c", "metadata": {}, "source": ["```\n", "1. Date - 16/05/2025\n", "Adding employment details in sec data retrieval prompt.\n", "2. Date - 20-05-2025\n", "Retrieving production data.\n", "3. Date - 28-05-2025\n", "Using for Industrial machinery companies\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "70ee55d1", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "2570c75f", "metadata": {}, "outputs": [], "source": ["api_key = \"********************************************************************************************************************************************************************\""]}, {"cell_type": "code", "execution_count": 3, "id": "09c0703e", "metadata": {}, "outputs": [], "source": ["client = OpenAI(api_key=api_key)"]}, {"cell_type": "markdown", "id": "ae288479", "metadata": {}, "source": ["Giving average confidence score as 0.9 for sec prompt\n"]}, {"cell_type": "markdown", "id": "f7b581ff", "metadata": {}, "source": ["## SEC data retreival"]}, {"cell_type": "code", "execution_count": 4, "id": "4f4e4906", "metadata": {}, "outputs": [], "source": ["sec_data_retrieval_prompt = \"\"\"\\\n", "You are an expert in extracting information from SEC filings of companies.\n", "The company of interest is {company_name}. You need to extract all the information related to following fields from the SEC filings of the {company_name} and return the output in defined JSON format:\n", "1. Plans regarding manufacturing capacity expansion or new plant openings in the United States as per the SEC filings of the {company_name} from January 1, 2024, through today.\n", "\n", "2. Employment generation or hiring commitments of the {company_name} in the United States as per it's SEC filings from January 1, 2024, through today.\n", "\n", "3. Actual production output (units or volumes produced) per quarter of the {company_name} in the United States as reported in it's SEC filings from January 1, 2024, through today.\n", "\n", "While answering, please adhere strictly to these rules:\n", "\n", "1. Web search:\n", "   - Search only the Official SEC filings (10-K/10-Q/8-K) of the target company to gather authoritative sources.\n", "   - Do not search any other source apart from the official SEC filings.\n", "\n", "2. Plan Identification:\n", "   2a. Identify Capacity-Expansion Plans: \n", "         - Locate statements or paragraphs in the SEC filings describing intentions, plans, or commitments to:\n", "            - Expand existing factories in the United States\n", "            - Build or commission new manufacturing facilities in the United States.\n", "         - Quantify Every Plan:\n", "            - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the SEC filings. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "   2b. Identify Employment-Generation Plans:  \n", "         - Locate any statements in the SEC filings describing hiring targets, job-creation commitments, or intended workforce increases in the United States.  \n", "         - Quantify by citing numeric metrics if available in the SEC filings.(e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "   2c. Identify Actual Production Output:\n", "         - Locate quarterly production output metrics in the United States, disclosed in the SEC filings from January 1, 2024, through today.\n", "         - Extract details such as number of units produced, volume of goods manufactured, or any other production quantity disclosed on a per-quarter basis.\n", "\n", "3. Time-<PERSON><PERSON>:\n", "   - Include only those sources dated on or after January 1, 2024, up to the current date.\n", "   - Exclude any sources from earlier periods.\n", "\n", "4. Output Structure:\n", "   - Present your findings in JSON format with following fields:\n", "     - `company`: name of the target company.\n", "     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, publishing_agency, title, section, url).\n", "     - `employment_generation_plans`: list of objects with `plan_details` regarding employment generation, `source` (publishing_date, publishing_agency, title, section, url).\n", "     - `production_output`: list of objects with `output_details` (e.g., units/volume produced), `source` (publishing_date, publishing_agency, title, section, url).\n", "     - `summary`: a concise overview of `plan_details`from both `capacity_expansion_plans` and `employment_generation_plans`, and  `output_details` from `production_output`.\n", "   - Order `plan_details` chronologically (earliest → latest).\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 5, "id": "93251a32", "metadata": {}, "outputs": [], "source": ["output_format_sec = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"company\":{\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Name of the company about which the information is being asked by the user.\"\n", "                },\n", "                \"capacity_expansion_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Manufacturing capacity expansion or new plant opening plan details of the company in the United States as per the SEC filings (10-K/10-Q/8-K).\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the SEC filing (10-K/10-Q/8-K) was filed by the company (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the publishing agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the SEC filing (10-K/10-Q/8-K)\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the SEC filing where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"employment_generation_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Employment generation plan details of the company in the United States as per the SEC filings (10-K/10-Q/8-K).\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the SEC filing was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the publishing agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the SEC filing (10-K/10-Q/8-K)\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the SEC filing where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"production_output\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"output_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Actual production output (units or volumes produced) per quarter of the company in the United States as reported in SEC filings.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the SEC filing was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the publishing agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the SEC filing (10-K/10-Q/8-K)\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the SEC filing where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"output_details\",\n", "                            \"source\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"summary\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Short overview of all plan details and output details.\"\n", "                }\n", "            },\n", "            \"required\": [\"company\",\"capacity_expansion_plans\",\"employment_generation_plans\", \"production_output\",\n", "                \"summary\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "markdown", "id": "a55d010b", "metadata": {}, "source": ["## company press release, news articles and other relevant websites."]}, {"cell_type": "code", "execution_count": 6, "id": "4abd9102", "metadata": {}, "outputs": [], "source": ["user_prompt_other_sources = \"\"\"\\\n", "You are a research assistant with access to web-search.\n", "The company of interest is {company_name}. You need to extract all information related to following fields from relevant web sources and return the output in defined JSON format:\n", "1. Plans regarding manufacturing capacity expansion or new plant openings of the {company_name} in the United States from relevant web sources, covering the period from January 1, 2024, to the present.\n", "\n", "2. Employment generation or hiring commitments of the {company_name} in the United States from relevant web sources, covering the period from January 1, 2024, to the present.\n", "\n", "3. Actual production output (units or volumes produced) per quarter of the {company_name} in the United States as per the relevant web sources, covering the period from January 1, 2024, to the present.\n", " \n", "When responding, adhere strictly to these rules:\n", "\n", "1. Web search: \n", "   - Search the company's press releases, relevant websites, news articles etc. to gather authoritative sources.\n", "\n", "2. Plan Identification:\n", "   2a. Identify Capacity-Expansion Plans: \n", "         - Locate statements or paragraphs describing intentions, plans, or commitments to:\n", "            - Expand existing factories in the United States.\n", "            - Build or commission new manufacturing facilities in the United States.\n", "         - Quantify Every Plan:\n", "            - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures. (e.g., \"...investing $X billion to expand factory\", \"...setting up a new Y sq ft facility\" etc.)\n", "   2b. Identify Employment-Generation Plans:  \n", "         - Locate any statements in the sources describing hiring targets, job-creation commitments, or intended workforce increases in the United States.  \n", "         - Quantify by citing numeric metrics (e.g., \"1,000 new employees\", \"2,500 jobs over three years\" etc.).\n", "   2c. Identify Actual Production Output:\n", "         - Locate reported or disclosed production output metrics by the company per quarter in the United States, covering the period from January 1, 2024, to the present.\n", "         - Extract information such as number of units produced, volume of goods manufactured, or other quarterly production quantity data disclosed in press releases, earnings calls or news articles etc.\n", "\n", "4. Time-<PERSON><PERSON>: \n", "   - Include only sources dated on or after 2024-01-01, up to today's date.  \n", "   - Exclude any sources published before 2024-01-01.\n", "\n", "5. Restrict to Company's Own Data: \n", "   - Only include target company's own plans to increase its manufacturing capacity or opening a new plant, employment generation plans and production output.\\\n", "     Do not include any supplier, vendor, or third‑party manufacturing partner plans or information.\n", "\n", "6. Output Structure:\n", "   - Return results in JSON with these fields:\n", "     - `company`: name of the target company.\n", "     - `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `source` (publishing_date, publishing_agency, title, section, url), and `confidence_score`.\n", "     - `employment_generation_plans`: list of objects with `plan_details` regarding employment generation, `source` (publishing_date, publishing_agency, title, section, url), and `confidence_score`.  \n", "     - `production_output`: list of objects with `output_details` (e.g., units/volume produced), `source` (publishing_date, publishing_agency, title, section, url) and `confidence_score`.\n", "     - `summary`: a concise overview of `plan_details`from both `capacity_expansion_plans` and `employment_generation_plans`, and `output_details` from `production_output`.\n", "   - Order `plan_details` chronologically (earliest → latest).\n", "\n", "7. Confidence Score Calculation: \n", "   - A score between 0 and 0.9 suggesting confidence in the information given in plan details and source, calculated as follows:  \n", "      - Source Reliability (0.0–0.4):  \n", "         0.4=Company's official press release; 0.3=Tier-1 industry press; 0.2=Blog/aggregator; 0.0=Unverified forum/social media  \n", "      - Specificity Score(0.0–0.5):  \n", "         0.5=Direct management quote with numeric metric; 0.4=Exact excerpt paraphrased; 0.3=Summary statement without numbers; 0.2=Analyst commentary; 0.0= pure Speculation  \n", "      - `confidence_score = Source Reliability + Specificity Score`\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 7, "id": "61301811", "metadata": {}, "outputs": [], "source": ["output_format_other_sources = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_expansion_details\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"company\":{\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Name of the company about which the information is being asked by the user.\"\n", "                },\n", "                \"capacity_expansion_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Capacity expansion or new plant opening plan details of the company under consideration in the United States as per the found sources.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the source was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the document or article\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"confidence_score\": {\n", "                                \"type\": \"number\",\n", "                                \"description\": \"A score between 0 and 0.9 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\",\n", "                            \"confidence_score\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"employment_generation_plans\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"plan_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Employment generation plan details of the company under consideration in the United States as per the found sources.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the source was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the document or article\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"confidence_score\": {\n", "                                \"type\": \"number\",\n", "                                \"description\": \"A score between 0 and 1 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"plan_details\",\n", "                            \"source\",\n", "                            \"confidence_score\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"production_output\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"output_details\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Actual production output (units or volumes produced) per quarter of the company in the United States as reported in relevant web sources.\"\n", "                            },\n", "                            \"source\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"publishing_date\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Date when the source was published (YYYY-MM-DD)\"\n", "                                    },\n", "                                    \"publishing_agency\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Name of the agency or website\"\n", "                                    },\n", "                                    \"title\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Title of the document or article\"\n", "                                    },\n", "                                    \"section\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"Section or heading within the source where the excerpt is found.\"\n", "                                    },\n", "                                    \"url\": {\n", "                                        \"type\": \"string\",\n", "                                        \"description\": \"URL of the source\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"publishing_date\",\n", "                                    \"publishing_agency\",\n", "                                    \"title\",\n", "                                    \"section\",\n", "                                    \"url\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"confidence_score\": {\n", "                                \"type\": \"number\",\n", "                                \"description\": \"A score between 0 and 1 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"output_details\",\n", "                            \"source\",\n", "                            \"confidence_score\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                },\n", "                \"summary\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": \"Short overview of all plan details and production output details.\"\n", "                }\n", "            },\n", "            \"required\": [\"company\",\"capacity_expansion_plans\",\"employment_generation_plans\", \"production_output\",\n", "                \"summary\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "markdown", "id": "6f360bb3", "metadata": {}, "source": ["## retrieving data for all the companies"]}, {"cell_type": "code", "execution_count": 8, "id": "70bac86f", "metadata": {}, "outputs": [], "source": ["def all_company_details(company_names:list[str]) -> tuple[list[dict], float, float, float]:\n", "    company_results = []\n", "    INPUT_COST_PER_TOKEN = 2.00 / 1_000_000\n", "    OUTPUT_COST_PER_TOKEN = 8.00 / 1_000_000\n", "    WEB_SEARCH_COST_PER_CALL = 50.00 / 1_000  \n", "    sec_cost = 0\n", "    other_cost = 0\n", "    for company_name in company_names:\n", "        # SEC data retrieval\n", "        response_sec = client.responses.create(\n", "            model=\"gpt-4.1\",\n", "            tools=[{\n", "                    \"type\": \"web_search_preview\",\n", "                    \"search_context_size\": \"high\",\n", "                }],\n", "            input=sec_data_retrieval_prompt.format(company_name = company_name),\n", "            text=output_format_sec\n", "        )\n", "        # Cost calculation\n", "        input_tokens_sec = response_sec.usage.input_tokens\n", "        output_tokens_sec = response_sec.usage.output_tokens\n", "        token_cost_sec = (input_tokens_sec * INPUT_COST_PER_TOKEN) + (output_tokens_sec * OUTPUT_COST_PER_TOKEN)\n", "        total_cost_sec = token_cost_sec + WEB_SEARCH_COST_PER_CALL\n", "        sec_cost += total_cost_sec\n", "\n", "        data_sec = json.loads(response_sec.output_text)\n", "        # Assinging confidence score of 0.9\n", "        for plan in data_sec[\"capacity_expansion_plans\"]:\n", "            plan[\"confidence_score\"] = 0.9\n", "\n", "        for plan in data_sec[\"employment_generation_plans\"]:\n", "            plan[\"confidence_score\"] = 0.9\n", "\n", "        for output in data_sec[\"production_output\"]:\n", "            output[\"confidence_score\"] = 0.9\n", "        \n", "        # Other sources data retrieval\n", "        response_other = client.responses.create(\n", "            model=\"gpt-4.1\",\n", "            tools=[{\n", "                    \"type\": \"web_search_preview\",\n", "                    \"search_context_size\": \"high\",\n", "            }],\n", "            input=user_prompt_other_sources.format(company_name = company_name),\n", "            text=output_format_other_sources\n", "        )\n", "        # Cost calculation\n", "        input_tokens_other = response_other.usage.input_tokens\n", "        output_tokens_other = response_other.usage.output_tokens\n", "        token_cost_other = (input_tokens_other * INPUT_COST_PER_TOKEN) + (output_tokens_other * OUTPUT_COST_PER_TOKEN)\n", "        total_cost_other = token_cost_other + WEB_SEARCH_COST_PER_CALL\n", "        other_cost += total_cost_other\n", "\n", "        data_other = json.loads(response_other.output_text)\n", "        # appending other sources data to SEC data to make final data\n", "        for plan in data_other[\"capacity_expansion_plans\"]:\n", "            data_sec[\"capacity_expansion_plans\"].append(plan)\n", "\n", "        for plan in data_other[\"employment_generation_plans\"]:\n", "            data_sec[\"employment_generation_plans\"].append(plan)\n", "\n", "        for output in data_other[\"production_output\"]:\n", "            data_sec[\"production_output\"].append(output)\n", "\n", "        data_sec[\"summary\"]=data_sec[\"summary\"]+' '+data_other[\"summary\"]\n", "        # final data\n", "        company_results.append(data_sec)\n", "    return company_results, sec_cost, other_cost, sec_cost + other_cost\n"]}, {"cell_type": "markdown", "id": "32386f55", "metadata": {}, "source": ["## loading company names"]}, {"cell_type": "code", "execution_count": 9, "id": "f7788b03", "metadata": {}, "outputs": [], "source": ["industrial_mach_df = pd.read_excel(\"industrial_machinery_clusters.xlsx\")"]}, {"cell_type": "code", "execution_count": 10, "id": "d1724390", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity_Name</th>\n", "      <th>Company_Mcap_USD</th>\n", "      <th>LogMCap</th>\n", "      <th>SizeCategory</th>\n", "      <th>QuantileCluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Applied Materials, Inc.</td>\n", "      <td>126464.546766</td>\n", "      <td>11.747725</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Lam Research Corp.</td>\n", "      <td>101011.364915</td>\n", "      <td>11.522998</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Carrier Global Corp.</td>\n", "      <td>57481.090569</td>\n", "      <td>10.959229</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ingersoll Rand, Inc.</td>\n", "      <td>33000.426945</td>\n", "      <td>10.404306</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Xylem, Inc.</td>\n", "      <td>29836.354348</td>\n", "      <td>10.303516</td>\n", "      <td>Large</td>\n", "      <td>Large</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Entity_Name  Company_Mcap_USD    LogMCap SizeCategory  \\\n", "0  Applied Materials, Inc.     126464.546766  11.747725        Large   \n", "1       Lam Research Corp.     101011.364915  11.522998        Large   \n", "2     Carrier Global Corp.      57481.090569  10.959229        Large   \n", "3     Ingersoll Rand, Inc.      33000.426945  10.404306        Large   \n", "4              Xylem, Inc.      29836.354348  10.303516        Large   \n", "\n", "  QuantileCluster  \n", "0           Large  \n", "1           Large  \n", "2           Large  \n", "3           Large  \n", "4           Large  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["industrial_mach_df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "c895ca5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["17\n"]}], "source": ["small_indus_mach_q = industrial_mach_df[industrial_mach_df['QuantileCluster'] == 'Small']['Entity_Name'].tolist()\n", "print(len(small_indus_mach_q))"]}, {"cell_type": "code", "execution_count": 12, "id": "c7e0a272", "metadata": {}, "outputs": [], "source": ["manuf_comp_data, sec_cost, other_cost, total_cost = all_company_details(small_indus_mach_q)"]}, {"cell_type": "code", "execution_count": 13, "id": "84f885e1", "metadata": {}, "outputs": [{"data": {"text/plain": ["17"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["len(manuf_comp_data)"]}, {"cell_type": "code", "execution_count": 14, "id": "0eea90a8", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"industr_mach_small_q.json\", \"w\") as f:\n", "    json.dump(manuf_comp_data, f, indent=4)\n", "\n"]}, {"cell_type": "markdown", "id": "95d5d91f", "metadata": {}, "source": ["```\n", "potential improvemnt\n", "sec_sum = data_sec.get(\"summary\", \"\")\n", "other_sum = data_other.get(\"summary\", \"\")\n", "data_sec[\"summary\"] = f\"{sec_sum} {other_sum}\".strip()\n", "```"]}, {"cell_type": "markdown", "id": "79430d09", "metadata": {}, "source": ["## Final data"]}, {"cell_type": "markdown", "id": "7fe2c478", "metadata": {}, "source": ["## Different scores"]}, {"cell_type": "code", "execution_count": 15, "id": "30d0f40d", "metadata": {}, "outputs": [], "source": ["scores_system_prompt = \"\"\"\\\n", "You are an expert research analyst evaluating and scoring manufacturing companies based on their manufacturing capacity expansion plans or new plant opening plans, employment generation plans, and actual production output in the United States. You have been provided with a structured JSON list containing details for multiple companies.\n", "\n", "For each company:\n", "1. <PERSON>oughly analyze the `capacity_expansion_plans`, `employment_generation_plans`, and `production_output` fields in in the input structured JSON.\n", "2. Consider all `plan_details` from `capacity_expansion_plans`, `employment_generation_plans`, `output_details` from `production_output`, and their corresponding `confidence_score`s while assessing each company.\n", "\n", "Assign the following scores to each company in a comparative manner on a scale from 0 to 10:\n", "- `capacity_expansion_score`: Based on the magnitude of manufacturing capacity expansion or new manufacturing facilities of companies. The magnitude can be dollar value or in terms of units of production or other relevant figures. Give more weightage to specific, quantifiable plans as compared to vague, non-specific information.\n", "- `employment_generation_score`: Based on the number of new job creation or hiring plans of the respective companies.\n", "- `production_output_score`: Based on the level and trend of actual production output (in units or volume) over the quarters of respective companies. Use the following rules while assigning production_output_score to companies:\n", "    a. If production output data is available for only one, two or three quarters, make an educated estimate of the annual output based on the available data, assuming consistent production unless indicated otherwise.\n", "    b. When comparing companies for production output scoring, compare only similar types of commodities within the same primary product category. Focus on the company's core manufacturing output — i.e., its primary product line. For instance, Company X's annual car production can be compared with Company Y's motorcycle output, as both fall under the broad 'vehicle' category. However, X's energy storage production should not be compared with vehicles or any other unrelated product categories from other companies. Always align comparisons based on the principal manufactured product of each company.\n", "\n", "Output structure:\n", "- Return the output in json format with following schema:\n", "    - \"companies\": list of objects with following fields:\n", "        - \"company\": name of the company.\n", "        - \"scores\": list of objects with \"capacity_expansion_score\", \"employment_generation_score\", \"production_output_score\".\n", "        - \"Rationale\": <PERSON><PERSON><PERSON> explaining the assigned scores.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 16, "id": "97c14d0a", "metadata": {}, "outputs": [], "source": ["scores_output_format = {\n", "    \"format\": {\n", "        \"type\": \"json_schema\",\n", "        \"name\": \"company_scores\",\n", "        \"schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"companies\": {\n", "                    \"type\": \"array\",\n", "                    \"items\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"company\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Name of the company being evaluated.\"\n", "                            },\n", "                            \"scores\": {\n", "                                \"type\": \"object\",\n", "                                \"properties\": {\n", "                                    \"capacity_expansion_score\": {\n", "                                        \"type\": \"number\",\n", "                                        \"description\": \"Score (0–10) based on magnitude of capacity expansion or new plant opening.\"\n", "                                    },\n", "                                    \"employment_generation_score\": {\n", "                                        \"type\": \"number\",\n", "                                        \"description\": \"Score (0–10) based on the scale of employment generation or hiring.\"\n", "                                    },\n", "                                    \"production_output_score\": {\n", "                                        \"type\": \"number\",\n", "                                        \"description\": \"Score (0–10) based on the level and trend of actual production output.\"\n", "                                    }\n", "                                },\n", "                                \"required\": [\n", "                                    \"capacity_expansion_score\",\n", "                                    \"employment_generation_score\",\n", "                                    \"production_output_score\"\n", "                                ],\n", "                                \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                            },\n", "                            \"capacity_expansion_rationale\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Explanation of how the capacity_expansion_score was determined, referencing plan details and confidence scores.\"\n", "                            },\n", "                            \"employment_generation_rationale\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Explanation of how the employment_generation_score was determined, referencing plan details and confidence scores.\"\n", "                            },\n", "                            \"production_output_rationale\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"Explanation of how the production_output_score was determined, referencing output details and confidence scores.\"\n", "                            }\n", "                        },\n", "                        \"required\": [\n", "                            \"company\",\n", "                            \"scores\",\n", "                            \"capacity_expansion_rationale\",\n", "                            \"employment_generation_rationale\",\n", "                            \"production_output_rationale\"\n", "                        ],\n", "                        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "                    }\n", "                }\n", "            },\n", "            \"required\": [\n", "                \"companies\"\n", "            ],\n", "            \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 17, "id": "00c33053", "metadata": {}, "outputs": [], "source": ["response = client.responses.create(\n", "            model=\"o4-mini\",\n", "            reasoning={\"effort\": \"high\"},\n", "            input=[\n", "                {\"role\": \"system\", \"content\": scores_system_prompt},\n", "                {\"role\": \"user\", \"content\": json.dumps(manuf_comp_data) }\n", "            ],\n", "            text=scores_output_format\n", "        )\n", "\n", "INPUT_COST_PER_TOKEN = 1.10 / 1_000_000\n", "OUTPUT_COST_PER_TOKEN = 4.40 / 1_000_000\n", "\n", "input_tokens = response.usage.input_tokens\n", "output_tokens = response.usage.output_tokens\n", "total_scoring_cost = (input_tokens * INPUT_COST_PER_TOKEN) + (output_tokens * OUTPUT_COST_PER_TOKEN)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "id": "3ff99e4c", "metadata": {}, "outputs": [{"data": {"text/plain": ["14912"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["input_tokens"]}, {"cell_type": "code", "execution_count": 19, "id": "0882a786", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"companies\": [\n", "        {\n", "            \"company\": \"Enerpac Tool Group Corp.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 2\n", "            },\n", "            \"capacity_expansion_rationale\": \"Enerpac disclosed no manufacturing capacity expansion or new plant opening plans in its filings (capacity_expansion_plans empty, summary confirms; confidence N/A).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans were disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Reported net sales of $158.7 M in Q4 FY2024 and $145.2 M in Q1 FY2025. Annualizing (~$600 M) places Enerpac in the lower quartile among peers with output data.\"\n", "        },\n", "        {\n", "            \"company\": \"Worthington Enterprises, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 6,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"Initiated a $X M modernization project including a new 58,000 sq ft building and automation upgrades at Chilton, WI (confidence 0.9), a moderate-size facility expansion.\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; confidence N/A).\",\n", "            \"production_output_rationale\": \"No production output data available in the period.\"\n", "        },\n", "        {\n", "            \"company\": \"Oceaneering International, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 2,\n", "                \"production_output_score\": 10\n", "            },\n", "            \"capacity_expansion_rationale\": \"No new facility openings or capacity expansions disclosed\\u2014contract manufacturing at existing Panama City, FL facility (capacity_expansion_plans empty; confidence N/A).\",\n", "            \"employment_generation_rationale\": \"Earned the 2025 Gold Military Friendly\\u00ae Employer designation, indicating a commitment to hire veterans, but no headcount figures provided (confidence 0.9).\",\n", "            \"production_output_rationale\": \"Reported Q3 2024 revenue of $680 M. Annualizing (~$2.72 B) yields the highest output among peers.\"\n", "        },\n", "        {\n", "            \"company\": \"Kulicke & Soffa Industries, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 3\n", "            },\n", "            \"capacity_expansion_rationale\": \"No manufacturing capacity expansion or new plant openings disclosed (capacity_expansion_plans empty; summary confirms).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Q2 FY2024\\u2013Q1 FY2025 net revenues sum to ~$701 M annualized, placing it in the lower mid-range among peers.\"\n", "        },\n", "        {\n", "            \"company\": \"Axcelis Technologies, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 8,\n", "                \"employment_generation_score\": 2,\n", "                \"production_output_score\": 5\n", "            },\n", "            \"capacity_expansion_rationale\": \"Opened a 101,800 sq ft logistics and flex manufacturing center in Beverly, MA in June 2024 (confidence 0.8), a substantial new facility.\",\n", "            \"employment_generation_rationale\": \"New facility is expected to generate significant jobs locally and globally, but no numbers provided (confidence 0.8; vague).\",\n", "            \"production_output_rationale\": \"Reported revenues of ~$252\\u2013256 M in Q1\\u2013Q4 2024 (3 quarters); annualizing to ~$1.015 B, a solid mid-tier output.\"\n", "        },\n", "        {\n", "            \"company\": \"ACM Research, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 5,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"Acquired a 39,500 sq ft facility (including a 5,200 sq ft clean room) in Hillsboro, OR for $7.75 M to expand R&D/demo capabilities (confidence 0.9).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"No production output data available for the period.\"\n", "        },\n", "        {\n", "            \"company\": \"Kennametal Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 8\n", "            },\n", "            \"capacity_expansion_rationale\": \"Announced a $100 M Operational Excellence initiative involving plant closures and inventory optimization, but no expansions (confidence 0.9).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Q4 FY2024\\u2013Q3 FY2025 quarterly sales of $482\\u2013$543 M annualize to ~$1.99 B, among the highest outputs in the group.\"\n", "        },\n", "        {\n", "            \"company\": \"Tennant Co.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 7\n", "            },\n", "            \"capacity_expansion_rationale\": \"No manufacturing capacity expansion or new plant openings disclosed (capacity_expansion_plans empty; summary confirms).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Net sales grew from $311 M in Q1 2024 to $328.9 M in Q3 2024, projecting ~1.27 B annualized, above mid-tier.\"\n", "        },\n", "        {\n", "            \"company\": \"Helios Technologies, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 6,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"Expanded Mishawaka, IN facility by 50,000 sq ft to create a Hydraulic Manifold Solutions Center of Excellence (confidence 0.9), a moderate expansion.\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"No production output data available for the period.\"\n", "        },\n", "        {\n", "            \"company\": \"Ultra Clean Holdings, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 4,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 9\n", "            },\n", "            \"capacity_expansion_rationale\": \"Commenced a 10-year lease on new manufacturing space in Austin, TX in October 2024 (confidence 0.9), expanding U.S. production capacity with unspecified square footage.\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Q2\\u2013Q4 2024 revenues of $516\\u2013563 M annualize to ~$2.16 B, the second-highest output among peers.\"\n", "        },\n", "        {\n", "            \"company\": \"The Gorman-Rupp Company\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 7,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 2\n", "            },\n", "            \"capacity_expansion_rationale\": \"Planned $18\\u201320 M in 2024 and 2025 capital expenditures for building improvements and machinery, and tripled Fill-Rite\\u2019s Lenexa, KS facility size in 2023 (confidence 0.9).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Reported net sales of ~$660 M for 2024, placing it in the lower tier among peers with output data.\"\n", "        },\n", "        {\n", "            \"company\": \"Ichor Holdings Ltd.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 5,\n", "                \"employment_generation_score\": 2,\n", "                \"production_output_score\": 4\n", "            },\n", "            \"capacity_expansion_rationale\": \"Invested $17.6 M (2.1% of sales) in 2024 capital expenditures to support capacity and new product development (confidence 0.8).\",\n", "            \"employment_generation_rationale\": \"Ramped up headcount in Q4 2024 to meet increased demand, but no specific hiring figures provided (confidence 0.8; vague).\",\n", "            \"production_output_rationale\": \"Q1\\u2013Q4 2024 revenues of $201\\u2013233 M annualize to ~$842 M, a mid-range output among peers.\"\n", "        },\n", "        {\n", "            \"company\": \"Energy Recovery, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 0,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"No capacity expansion or plant opening plans disclosed (capacity_expansion_plans empty; summary confirms).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"No production output data available for the period.\"\n", "        },\n", "        {\n", "            \"company\": \"CECO Environmental Corp.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 6,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 1\n", "            },\n", "            \"capacity_expansion_rationale\": \"Acquired EnviroCare International and Profire Energy in 2024\\u20132025 to broaden processing solutions and increased its credit facility to $400 M to support organic/inorganic growth (confidence 0.9).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Reported 2024 net sales of $557.9 M, placing it in the lower tier among peers with output data.\"\n", "        },\n", "        {\n", "            \"company\": \"Hyster-Yale, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 3,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 0\n", "            },\n", "            \"capacity_expansion_rationale\": \"Announced plans to expand U.S. manufacturing for high-capacity electric models under the BABA Act, determining location and sourcing, but provided no scale metrics (confidence 0.9; non-specific).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"No production output data available for the period.\"\n", "        },\n", "        {\n", "            \"company\": \"Power Solutions International, Inc.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 2,\n", "                \"employment_generation_score\": 0,\n", "                \"production_output_score\": 1\n", "            },\n", "            \"capacity_expansion_rationale\": \"CEO stated in Q3 2024 that PSI is working on projects to expand manufacturing capacity for data center demand, but details and scale were not disclosed (confidence 0.9; vague).\",\n", "            \"employment_generation_rationale\": \"No employment generation plans disclosed (employment_generation_plans empty; summary confirms).\",\n", "            \"production_output_rationale\": \"Reported net sales of $125.8\\u2013$144.3 M across Q2 2024\\u2013Q1 2025, annualizing to ~$531 M, the lowest among companies with output data.\"\n", "        },\n", "        {\n", "            \"company\": \"Eastman Kodak Co.\",\n", "            \"scores\": {\n", "                \"capacity_expansion_score\": 7,\n", "                \"employment_generation_score\": 10,\n", "                \"production_output_score\": 6\n", "            },\n", "            \"capacity_expansion_rationale\": \"Invested $20 M to construct a new cGMP pharmaceutical and diagnostic reagent facility at Eastman Business Park, plus a temporary film production shutdown for plant upgrades, significantly boosting capacity (confidence 0.9).\",\n", "            \"employment_generation_rationale\": \"Hired hundreds of employees over three years (50% of its manufacturing workforce are new hires) and maintains dozens of open positions, representing a substantial and quantifiable hiring effort (confidence 0.7).\",\n", "            \"production_output_rationale\": \"Reported consolidated revenues of $266 M in Q4 2024 and $247 M in Q1 2025, annualizing to ~$1.026 B, positioning it in the mid-tier among peers.\"\n", "        }\n", "    ]\n", "}\n"]}], "source": ["final_result = json.loads(response.output_text)\n", "print(json.dumps(final_result, indent=4))"]}, {"cell_type": "code", "execution_count": 20, "id": "cd1d3f3e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "companies_data = final_result[\"companies\"]\n", "\n", "df = pd.DataFrame([\n", "    {\n", "        \"company\": entry[\"company\"],\n", "        \"capacity_expansion_score\": entry[\"scores\"][\"capacity_expansion_score\"],\n", "        \"employment_generation_score\": entry[\"scores\"][\"employment_generation_score\"],\n", "        \"production_output_score\": entry[\"scores\"][\"production_output_score\"],\n", "        \"capacity_expansion_rationale\": entry[\"capacity_expansion_rationale\"],\n", "        \"employment_generation_rationale\": entry[\"employment_generation_rationale\"],\n", "        \"production_output_rationale\": entry[\"production_output_rationale\"]\n", "    }\n", "    for entry in companies_data\n", "])\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "33bc63a7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>company</th>\n", "      <th>capacity_expansion_score</th>\n", "      <th>employment_generation_score</th>\n", "      <th>production_output_score</th>\n", "      <th>capacity_expansion_rationale</th>\n", "      <th>employment_generation_rationale</th>\n", "      <th>production_output_rationale</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Enerpac Tool Group Corp.</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>Enerpac disclosed no manufacturing capacity ex...</td>\n", "      <td>No employment generation plans were disclosed ...</td>\n", "      <td>Reported net sales of $158.7 M in Q4 FY2024 an...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Worthington Enterprises, Inc.</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Initiated a $X M modernization project includi...</td>\n", "      <td>No employment generation plans disclosed (empl...</td>\n", "      <td>No production output data available in the per...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Oceaneering International, Inc.</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>10</td>\n", "      <td>No new facility openings or capacity expansion...</td>\n", "      <td>Earned the 2025 Gold Military Friendly® Employ...</td>\n", "      <td>Reported Q3 2024 revenue of $680 M. Annualizin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Kulicke &amp; Soffa Industries, Inc.</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>No manufacturing capacity expansion or new pla...</td>\n", "      <td>No employment generation plans disclosed (empl...</td>\n", "      <td>Q2 FY2024–Q1 FY2025 net revenues sum to ~$701 ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Axcelis Technologies, Inc.</td>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>Opened a 101,800 sq ft logistics and flex manu...</td>\n", "      <td>New facility is expected to generate significa...</td>\n", "      <td>Reported revenues of ~$252–256 M in Q1–Q4 2024...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>ACM Research, Inc.</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Acquired a 39,500 sq ft facility (including a ...</td>\n", "      <td>No employment generation plans disclosed (empl...</td>\n", "      <td>No production output data available for the pe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Kennametal Inc.</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>Announced a $100 M Operational Excellence init...</td>\n", "      <td>No employment generation plans disclosed (empl...</td>\n", "      <td>Q4 FY2024–Q3 FY2025 quarterly sales of $482–$5...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Tennant Co.</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>No manufacturing capacity expansion or new pla...</td>\n", "      <td>No employment generation plans disclosed (empl...</td>\n", "      <td>Net sales grew from $311 M in Q1 2024 to $328....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Helios Technologies, Inc.</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Expanded Mishawaka, IN facility by 50,000 sq f...</td>\n", "      <td>No employment generation plans disclosed (empl...</td>\n", "      <td>No production output data available for the pe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Ultra Clean Holdings, Inc.</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>Commenced a 10-year lease on new manufacturing...</td>\n", "      <td>No employment generation plans disclosed (empl...</td>\n", "      <td>Q2–Q4 2024 revenues of $516–563 M annualize to...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            company  capacity_expansion_score  \\\n", "0          Enerpac Tool Group Corp.                         0   \n", "1     Worthington Enterprises, Inc.                         6   \n", "2   Oceaneering International, Inc.                         0   \n", "3  Kulicke & Soffa Industries, Inc.                         0   \n", "4        Axcelis Technologies, Inc.                         8   \n", "5                ACM Research, Inc.                         5   \n", "6                   Kennametal Inc.                         0   \n", "7                       Tennant Co.                         0   \n", "8         Helios Technologies, Inc.                         6   \n", "9        Ultra Clean Holdings, Inc.                         4   \n", "\n", "   employment_generation_score  production_output_score  \\\n", "0                            0                        2   \n", "1                            0                        0   \n", "2                            2                       10   \n", "3                            0                        3   \n", "4                            2                        5   \n", "5                            0                        0   \n", "6                            0                        8   \n", "7                            0                        7   \n", "8                            0                        0   \n", "9                            0                        9   \n", "\n", "                        capacity_expansion_rationale  \\\n", "0  Enerpac disclosed no manufacturing capacity ex...   \n", "1  Initiated a $X M modernization project includi...   \n", "2  No new facility openings or capacity expansion...   \n", "3  No manufacturing capacity expansion or new pla...   \n", "4  Opened a 101,800 sq ft logistics and flex manu...   \n", "5  Acquired a 39,500 sq ft facility (including a ...   \n", "6  Announced a $100 M Operational Excellence init...   \n", "7  No manufacturing capacity expansion or new pla...   \n", "8  Expanded Mishawaka, IN facility by 50,000 sq f...   \n", "9  Commenced a 10-year lease on new manufacturing...   \n", "\n", "                     employment_generation_rationale  \\\n", "0  No employment generation plans were disclosed ...   \n", "1  No employment generation plans disclosed (empl...   \n", "2  Earned the 2025 Gold Military Friendly® Employ...   \n", "3  No employment generation plans disclosed (empl...   \n", "4  New facility is expected to generate significa...   \n", "5  No employment generation plans disclosed (empl...   \n", "6  No employment generation plans disclosed (empl...   \n", "7  No employment generation plans disclosed (empl...   \n", "8  No employment generation plans disclosed (empl...   \n", "9  No employment generation plans disclosed (empl...   \n", "\n", "                         production_output_rationale  \n", "0  Reported net sales of $158.7 M in Q4 FY2024 an...  \n", "1  No production output data available in the per...  \n", "2  Reported Q3 2024 revenue of $680 M. Annualizin...  \n", "3  Q2 FY2024–Q1 FY2025 net revenues sum to ~$701 ...  \n", "4  Reported revenues of ~$252–256 M in Q1–Q4 2024...  \n", "5  No production output data available for the pe...  \n", "6  Q4 FY2024–Q3 FY2025 quarterly sales of $482–$5...  \n", "7  Net sales grew from $311 M in Q1 2024 to $328....  \n", "8  No production output data available for the pe...  \n", "9  Q2–Q4 2024 revenues of $516–563 M annualize to...  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(10)"]}, {"cell_type": "code", "execution_count": 22, "id": "009cac99", "metadata": {}, "outputs": [], "source": ["df.to_excel(\"industr_mach_small_q_score.xlsx\", index=False)"]}, {"cell_type": "markdown", "id": "58579239", "metadata": {}, "source": ["## Cost Analysis"]}, {"cell_type": "code", "execution_count": 27, "id": "610f25af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sec_data_cost: 47.712121120000006,\n", "       \n", " other_data_cost: 51.656816639999995,\n", "       \n", " total_data_cost: 99.36893776000001,\n", "        \n", "total_scoring_cost: 3.4355094400000006\n"]}], "source": ["print(f\"\"\"sec_data_cost: {sec_cost*85.52},\n", "       \\n other_data_cost: {other_cost*85.52},\n", "       \\n total_data_cost: {total_cost*85.52},\n", "        \\ntotal_scoring_cost: {total_scoring_cost*85.52}\"\"\")"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>z", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}